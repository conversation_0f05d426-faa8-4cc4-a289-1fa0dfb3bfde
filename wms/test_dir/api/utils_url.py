# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  utils_url.py
@Description    :  
@CreateTime     :  2025/7/3 16:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/3 16:01
"""

from wms.qa_config import global_data


class UtilsUrl:
    """seller统一不同URL的方法"""

    special_url = "https://api.seller.tb1.sayweee.net"

    if global_data.is_prod:
        special_erpurl = "https://erp.merch.sayweee.net"
    else:
        special_erpurl = "https://tb1.sayweee.net"


