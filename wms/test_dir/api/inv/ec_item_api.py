import urllib.parse
import weeeTest
from wms.test_dir.api.wms.wms import header
import json
from time import sleep


class EcItem(weeeTest.TestCase):
    """
    ec item 相关接口
    """

    def query_categroy_product(self, zipcode, date, filters='{"delivery_type":"delivery_type_pantry"}', filter_sub_category='sale'):
        """
        按分类查询pantry/global等商品
        """
        # 定义基础路径和固定参数
        base_path = '/ec/item/v5/content/v1/catalogue'
        fixed_params = {
            'page': 1,
            'limit': 12,
            'zipcode': zipcode,
            'date': date,
            'lang': 'zh',
            'sort': 'recommend',
            'filter_sub_category': filter_sub_category,
            'filters': filters
        }
        # 构造完整的 URL
        query_string = urllib.parse.urlencode(fixed_params)
        url = f"{base_path}?{query_string}"
        self.get(url=url, headers=header)
        sleep(5)
        weeeTest.log.info(f'接口返回详情打印：{self.response}')  # 增加response打印
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pantry_transform(self, product_list, zipcode):
        """
        自营转pantry同步接口
        """
        url = '/central/im/deal/pantry/transform'
        header["weee-zipcode"] = str(zipcode)
        body = {
            "product_ids": product_list
        }
        self.post(url=url, headers=header, data=json.dumps(body))
        assert self.status_code == 200, f"{url}接口请求失败,详情：{self.response}"
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
