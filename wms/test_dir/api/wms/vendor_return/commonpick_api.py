# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  commonpick_api.py
@Description    :  通用拣货相关API
@CreateTime     :  2024/8/8 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/8 17:01
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class CommonPickAPI(weeeTest.TestCase):

    def scan_Equipment(self, warehouseNumber,storageType,userId,Equipment_code):
        """
        扫描设备
        Args:
            Equipment_code: 设备编码
        Returns:
            dict: 响应结果
                    {
            "messageId": "10000",
            "success": true,
            "body": {
                "warehouseNumber": null,
                "bizType": 70,
                "taskTotal": 1,
                "taskFinishNum": 0,
                "itemTotal": 1501,
                "itemFinishNum": 0,
                "get_task_dtm": **********,
                "estimate_pick_dtm": 0,
                "palletNo": null,
                "palletType": 38,
                "taskDispatchList": [
                    {
                        "recId": null,
                        "warehouseNumber": "25",
                        "bizTaskId": 98510,
                        "bizType": 70,
                        "taskNo": null,
                        "pickupDate": null,
                        "referenceNo": "EP70",
                        "storageType": 1,
                        "status": 20,
                        "palletNo": null,
                        "palletType": 38,
                        "itemNumber": "88382",
                        "locationNo": "A0510-6-4",
                        "locationType": 4,
                        "itemQuantity": 1501,
                        "pickQuantity": 0,
                        "piecesPerPack": null,
                        "pickingUserId": null,
                        "batchNo": "240513072437",
                        "checkShelfLife": null,
                        "globalPriority": null,
                        "workType": 2,
                        "memo": null,
                        "dispatchDtm": null,
                        "dispatchRuleType": null,
                        "finishDtm": null,
                        "inUser": null,
                        "inDtm": null,
                        "editUser": null,
                        "editDtm": null,
                        "newWorkType": null,
                        "uniqueKey": null,
                        "releasePallet": null,
                        "releasePickingUser": null,
                        "bizSubType": 70,
                        "bizOrderNo": "EP70"
                    }
                ]
            }
        }
        """
        url = "/wms/commonPick/scanEquipment"
        body = {
            "warehouseNumber": warehouseNumber,
            "storageType": storageType,
            "userId": userId,
            "equipmentNo": Equipment_code
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def scan_pallet(self, bizType,warehouseNumber,storageType,userId,pallet_code):
        """
        扫描托盘
        Args:
            pallet_code: 托盘编码
        Returns:
            dict: 响应结果
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "type": 70,
                "bizOrderId": "EP70",
                "commonTaskType": "Expiration Control Picking"
            }
        }
        """
        url = "/wms/commonPick/scanPallet"
        body = {
            "bizType": bizType,
            "warehouseNumber": warehouseNumber,
            "storageType": storageType,
            "userId": userId,
            "pickingPalletNo": pallet_code
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]
    
    def get_upc_pick_detail(self, warehouseNumber,taskRecId,locationNo,bizType,bizOrderId,pallet_code,userId):
        """
        获取UPC拣货详情
        Args:
            order_no: 订单号
        Returns:
            dict: 响应结果
            {
                "messageId": "10000",
                "success": true,
                "body": {
                    "taskRecId": 99615,
                    "warehouseNumber": "25",
                    "returnNo": "EP70",
                    "type": 70,
                    "locationNo": "A0208-5-4",
                    "itemNumber": "91481",
                    "ename": "BIMBO Cuernitos - Croissants",
                    "name": "BIMBO Cuernitos - 可颂面包",
                    "image": "https://img06.weeecdn.com/description/image/786/071/4197D260E6A01272.png",
                    "itemVolume": 50.23,
                    "caseVolume": 49.14,
                    "upcs": [
                        "74323091571",
                        "074323091571"
                    ],
                    "upcCode": "074323091571",
                    "recommendTotalQty": 1501,
                    "recommendTotalBox": 1501,
                    "piecesPerPack": 1,
                    "checkShelfLife": true,
                    "expireDtm": **********,
                    "receiveDtm": **********
                }
            }
        """
        url = "/wms/commonPick/pickDetail"
        body = {
            "warehouseNumber": warehouseNumber,
            "taskRecId": taskRecId,
            "locationNo": locationNo,
            "type": bizType,
            "bizOrderId": bizOrderId,
            "pickingPalletNo": pallet_code,
            "userId": userId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]
    
    def get_lpn_pick_detail(self, warehouseNumber,taskRecId,locationNo,type,bizOrderId,pickingPalletNo,userId):
        """
        获取LPN拣货详情
        Args:
            {
                "warehouseNumber": "25",
                "taskRecId": 94142,
                "locationNo": "B2601-1-1",
                "type": 70,
                "bizOrderId": "EP70",
                "pickingPalletNo": "PLT0689",
                "userId": 10953840
            }
        Returns:
            dict: 响应结果
                        {
                "messageId": "10000",
                "success": true,
                "body": {
                    "taskRecId": 94142,
                    "warehouseNumber": "25",
                    "returnNo": "EP70",
                    "type": 70,
                    "locationNo": "B2601-1-1",
                    "itemNumber": "49180",
                    "ename": "Vinh Thuan Brand Rice Flour ",
                    "name": "Vinh Thuan Brand Rice Flour ",
                    "image": "https://img06.weeecdn.com/item/image/162/959/777F42597788B18A.jpg",
                    "itemVolume": 49.98,
                    "caseVolume": 0,
                    "itemQuantity": 50,
                    "piecesPerPack": 10,
                    "checkShelfLife": true,
                    "expireDtm": 1727679600,
                    "receiveDtm": 1725174000,
                    "showMasterLpnFlag": true,
                    "masterLpnNoList": [
                        "G2409-49180-M2",
                        "G2409-49180-M3"
                    ],
                    "allLpnList": [
                        {
                            "rec_id": null,
                            "warehouse_number": "25",
                            "item_number": "49180",
                            "location_no": "B2601-1-1",
                            "location_type": 3,
                            "lpn_id": 103159,
                            "lpn_no": "G2409-49180-M1-1",
                            "quantity": 10,
                            "is_lock": 1,
                            "in_user": null,
                            "in_dtm": null,
                            "edit_user": null,
                            "edit_dtm": null,
                            "pieces_per_pack": 10,
                            "is_as_bin": false,
                            "lpnLocation": true,
                            "original_quantity": 10,
                            "before_quantity": null,
                            "before_allocated": null,
                            "before_total_qty": null,
                            "availableQty": null,
                            "location_total_qty": null,
                            "location_available_qty": null,
                            "isFullPallet": false,
                            "allocated_qty": null,
                            "restock_type": null,
                            "flag_extend": null,
                            "inv_rec_id": null,
                            "master_lpn_no": "G2409-49180-M2",
                            "whole_case": true,
                            "must_pick": false,
                            "batch_no": "240902000026",
                            "picked": null,
                            "isOneItemOneSpec": false,
                            "pieces_per_pack_str": null,
                            "lock_reason": 10,
                            "ob_pallet_no": null,
                            "piecesPerPack": "10.00",
                            "batchNoFormat": "240902000026"
                        }
                    ]
                }
            }
        """
        url = "/wms/commonPick/pickDetailLpn"
        body = {
            "warehouseNumber": warehouseNumber,
            "taskRecId": taskRecId,
            "locationNo": locationNo,
            "type": type,
            "bizOrderId": bizOrderId,
            "pickingPalletNo": pickingPalletNo,
            "userId": userId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def confirm_upc_pick(self, item_number, picking_action, picked_quantity, warehouse_number, task_rec_id, location_no, biz_type, biz_order_id, user_id, picking_pallet_no):
        """
        确认UPC拣货
        Args:
                {
            "itemNumber": "91481",
            "pickingAction": 0,
            "pickedQuantity": 1501,
            "warehouseNumber": "25",
            "taskRecId": "99615",
            "locationNo": "A0208-5-4",
            "type": 70,
            "bizOrderId": "EP70",
            "userId": 10953840,
            "pickingPalletNo": "PLT0689"
        }
            dict: 响应结果
            {"messageId":"10000","success":true,"body":"success"}
        """
        url = "/wms/commonPick/pickConfirm"
        body = {
            "itemNumber": item_number,
            "pickingAction": picking_action,
            "pickedQuantity": picked_quantity,
            "warehouseNumber": warehouse_number,
            "taskRecId": task_rec_id,
            "locationNo": location_no,
            "type": biz_type,
            "bizOrderId": biz_order_id,
            "userId": user_id,
            "pickingPalletNo": picking_pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]
    
    def confirm_lpn_pick(self, lpnNoList, itemNumber, pickingPalletNo, pickingAction, pickedQuantity, warehouseNumber, taskRecId, locationNo, type, bizOrderId, userId):
        """
        确认LPN拣货
        Args:
            {
                "lpnNoList": [
                    "G2409-49180-M1-5",
                ],
                "itemNumber": "49180",
                "pickingPalletNo": "PLT0689",
                "pickingAction": 0,
                "pickedQuantity": 50,
                "warehouseNumber": "25",
                "taskRecId": "94142",
                "locationNo": "B2601-1-1",
                "type": 70,
                "bizOrderId": "EP70",
                "userId": 10953840
            }
        Returns:
            dict: 响应结果
            {"messageId":"10000","success":true,"body":"success"}
        """
        url = "/wms/commonPick/pickConfirmLpn"
        body = {
            "lpnNoList": lpnNoList,
            "itemNumber": itemNumber,
            "pickingPalletNo": pickingPalletNo,
            "pickingAction": pickingAction,
            "pickedQuantity": pickedQuantity,
            "warehouseNumber": warehouseNumber,
            "taskRecId": taskRecId,
            "locationNo": locationNo,
            "type": type,
            "bizOrderId": bizOrderId,
            "userId": userId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pick_complete(self, warehouse_number, picking_pallet_no, storage_type, user_id, type, biz_order_id):
        """
        拣货完成
        Args:
            order_no: 订单号
        Returns:
            dict: 响应结果
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "viewSlot": true,
                "slotNo": "SLOT999",
                "locationType": 28,
                "viewSlotFull": false
            }
        }
        """
        url = "/wms/commonPick/pickComplete"
        body = {
            "warehouseNumber": warehouse_number,
            "pickingPalletNo": picking_pallet_no,
            "storageType": storage_type,
            "userId": user_id,
            "type": type,
            "bizOrderId": biz_order_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def scan_slot(self, slotNo, warehouseNumber, storageType, userId, type, bizOrderId, pickingPalletNo):
        """
        扫描货位
        Args:
            slot_code: 货位编码
        Returns:
            dict: 响应结果
            {"messageId":"10000","success":true,"body":"success"}
        """
        url = "/wms/commonPick/scanSlot"
        body = {
            "slotNo": slotNo,
            "warehouseNumber": warehouseNumber,
            "storageType": storageType,
            "userId": userId,
            "type": type,
            "bizOrderId": bizOrderId,
            "pickingPalletNo": pickingPalletNo
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    #调整优先级
    def adjust_priority(self, task_rec_id, priority, warehouseNumber):
        """
        调整优先级
        Args:
            task_rec_id: 任务记录ID
            priority: 优先级
            user_id: 用户ID
        Returns:
            dict: 响应结果
            {"messageId":"10000","success":true,"body":"success"}
        """
        url = "/wms/commonPick/central/taskDispatch/priority"
        body = {
            "priority": priority,
            "recId": task_rec_id,
            "warehouseNumber": warehouseNumber
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def start_pick(self, recId, warehouseNumber):
        """
        开始拣货
        Args:
            recId: 任务记录ID
            warehouseNumber: 仓库编号
        Returns:
            dict: 响应结果
        """
        url = "/wms/commonPick/central/taskDispatch/startPick"
        body = {
            "recId": recId,
            "warehouseNumber": warehouseNumber
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def get_task_pages(self, warehouseNumber, bizTypeList=None, storageTypeList=None, statusList=None,pickno='', startColumn=0, pageSize=15, order=None,itemNumber="", dateType=1):
        """
        获取任务分页列表
        Args:
            warehouseNumber: 仓库编号
            bizTypeList: 业务类型列表
            storageTypeList: 存储类型列表
            statusList: 状态列表
            startColumn: 起始列
            pageSize: 页面大小
            order: 排序
            dateType: 日期类型
        Returns:
            dict: 响应结果
        """
        url = "/wms/commonPick/central/taskDispatch/pages"
        body = {
            "warehouseNumber": warehouseNumber,
            "bizTypeList": bizTypeList or [],
            "storageTypeList": storageTypeList or [],
            "statusList": statusList or [],
            "taskNo": pickno,
            "startColumn": startColumn,
            "pageSize": pageSize,
            "order": order,
            "itemNumber": itemNumber,
            "dateType": dateType
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pick_list(self, bizOrderId, warehouseNumber, storageType, userId, pickingPalletNo,type=2):
        """获取拣货列表"""
        url = "/wms/commonPick/pickList"
        body = {
            "type": type,
            "bizOrderId": bizOrderId,
            "warehouseNumber": warehouseNumber,
            "storageType": storageType,
            "userId": userId,
            "pickingPalletNo": pickingPalletNo,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]