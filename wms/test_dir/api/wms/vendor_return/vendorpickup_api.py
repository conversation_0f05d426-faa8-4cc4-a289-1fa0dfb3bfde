# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  vendorpickup_api.py
@Description    :  
@CreateTime     :  2024/11/26 13:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/11/26 13:22
"""

import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class VendorpickupAPI(weeeTest.TestCase):

    def queryCompulsoryList(self, warehouseNumber, userId):
        # 根据货主获取Compulsory待提货退仓单单信息
        url = '/wms/pickup/pda/queryCompulsoryList'
        body = {
            "warehouse_number": warehouseNumber,
            "in_user": userId,
            "vendor_id": 9012,
            "seller_id": 'null'
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def getpickupPallet(self, warehouseNumber, userId, RecId):
        # 扫描待提货退仓单号，获取待提货pallet信息
        url = '/wms/pickup/pda/getPallet'
        body = {
            "warehouse_number": warehouseNumber,
            "in_user": userId,
            "returnRecId": RecId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickupload(self, warehouseNumber, userId, RecId, pallets):
        # pallet装车
        url = '/wms/pickup/pda/load'
        body = {
            "warehouse_number": warehouseNumber,
            "in_user": userId,
            "returnRecId": RecId,
            "loadPallets": pallets
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def createbol(self, warehouseNumber, userId, RecId, orderNO, pallets):
        """
        创建BOL
        :return:
        """
        url = '/wms/pickup/pda/createBol'
        body = {
            "warehouse_number": warehouseNumber,
            "in_user": userId,
            "dock_id": "Dock002",
            "detailList": [
                {
                    "returnRecId": RecId,
                    "returnNo": orderNO,
                    "palletNoList": pallets,
                    "ticketType": 2
                }
            ],
            "truck_size": 3,
            "truck_type": 1,
            "record_type": 2,
            "comments": ""
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]