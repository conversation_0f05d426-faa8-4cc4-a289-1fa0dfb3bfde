# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  returncentral_api.py
@Description    :  
@CreateTime     :  2024/8/8 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/8 17:01
"""
import json
import weeeTest

from wms.test_dir.api.wms import mkplheader
from wms.test_dir.api.wms.wms import header

class ReturnCentralAPI(weeeTest.TestCase):

    def createOrUpdate(self,orderNO,pickUpDate,deliveryType,itemNumber1):
        #创建、更新订单
        url = "/wms/vendorReturnOrder/createOrUpdate"
        body = {
        "orderNo": orderNO,
        "type": 2,
        "warehouseNumber": "25",
        "sellerId": 9012,
        "sellerName": "Globa+ Vendor0328 New",
        "vendorId": "",
        "vendorName": "",
        "pickUpDate": pickUpDate,
        "deliveryType": deliveryType,
        "createUserId": "10953840",
        "createUserName": "hao.fan",
        "itemList": [
            {
                "detailUniqueId": 1,
                "itemNumber": itemNumber1,
                "quantity": 2,
                "itemLevel": 1,
                "requireNumber": "",
                "partial": True,
                "outboundPolicy": 2,
                "storageType": 2
            }
        ]
    }
        self.post(url=url, headers=header, json=body)
        '''response body
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050011",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def cancelorder(self,orderNO):
        #取消订单
        url = "/wms/vendorReturnOrder/cancel"
        body ={
            "warehouseNumber": "41",
            "orderNo": orderNO,
            "type": 2,
            "userId": "10953840",
            "userName": "hao.fan"
        }
        self.post(url=url, headers=header, json=body)
        '''response body
           {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050002",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def startpreoccupy(self,orderNO):
        #自动预占
        url = "/wms/vendorReturn/preoccupy/start"
        body = {
            "returnNo": orderNO,
            "type": 2,
            "warehouseNumber": "41"
        }
        self.post(url=url, headers=header, json=body)
        '''response body
           {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050002",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    #预占结果获取
    def getpreoccupyresult(self, orderNO):
        """
        获取预占结果。

        本函数通过发送HTTP POST请求，获取给定退货单号的预占结果。
        参数:
        - orderNO (str): 退货单号，用于查询预占结果。

        返回:
        - dict: 包含预占结果的响应体。
        """
        # 定义请求URL
        url = "/wms/vendorReturn/preoccupy/result"
        # 构建请求体，包含退货单号、类型和仓库编号
        body = {
            "returnNo": orderNO,
            "type": 2,
            "warehouseNumber": "41"
        }
        # 发送POST请求
        self.post(url=url, headers=header, json=body)
        '''
        预期的响应示例：
        {
            "messageId": "",
            "success": false,
            "body": {
                "completed": false,
                "hasTask": false
            }
        }
        '''
        # 断言响应中的'success'字段为True，确保请求成功
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # 返回响应体
        return self.response["body"]

    def getOrderDetail(self, warehouseNumber, returnNo, type):
        """
        获取供应商退货订单详情。

        本函数通过发送HTTP POST请求，获取给定仓库编号、退货单号和类型的订单详情。

        参数:
        - warehouseNumber (str): 仓库编号。
        - returnNo (str): 退货单号。
        - type (int): 类型。

        返回:
        - dict: 包含订单详情的响应体。
        """
        url = "/wms/vendorReturn/orderDetail"
        body = {
            "warehouseNumber": warehouseNumber,
            "returnNo": returnNo,
            "type": type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def createReturnOrder(self, seller_id, warehouse_number, return_type, return_date, product_id, quantity, product_level, outbound_policy):
        #创建ROS
        url = "/central/mkpl/global/fbw/sellers/ros/create"
        body = {
            "seller_id": seller_id,
            "warehouse_number": warehouse_number,
            "return_type": return_type,
            "return_date": return_date,
            "products": [
                {
                    "product_id": product_id,
                    "quantity": quantity,
                    "product_level": product_level,
                    "outbound_policy": outbound_policy
                }
            ]
        }
        self.post(special_url="https://api.seller.tb1.sayweee.net",url=url, headers=mkplheader, json=body)
        '''response body
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050011",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['result'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["object"]

    def confirmReturnOrder(self, return_no):
        #确认ROS
        url = f"/central/mkpl/global/fbw/sellers/ros/{return_no}/confirm"
        self.post(special_url="https://api.seller.tb1.sayweee.net",url=url, headers=mkplheader)
        '''response body
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050011",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['result'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["object"]

    def getPickUpInfoPage(self, warehouse_number, start_column=0, page_size=15, start_time=None, end_time=None, business_no=None, in_user=None):
        """获取拣货信息分页数据"""
        url = "/wms/pickup/ctrl/getPickUpInfoPage"
        body = {
            "warehouse_number": warehouse_number,  # 仓库编号
            "startColumn": start_column,  # 起始列
            "pageSize": page_size,  # 页面大小
            "order": {"orderColumn": "", "orderRule": "desc"},  # 排序规则
            "startTime": start_time,  # 开始时间戳
            "endTime": end_time,  # 结束时间戳
            "businessNo": business_no,  # 业务编号
            "pickUpTicketTypeEnum": [],  # 拣货单类型枚举
            "pickUpStatusEnum": [],  # 拣货状态枚举
            "in_user": in_user,  # 操作用户
            "sellerOrVendorId": None  # 卖家或供应商ID
        }
        self.post(url=url, headers=header, json=body)
        '''
        响应示例:
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recordsTotal": 0,
                "recordsFiltered": 0,
                "draw": 0,
                "data": [],
                "flag": false
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

