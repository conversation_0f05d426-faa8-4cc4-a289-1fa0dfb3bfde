# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  returncentral_api.py
@Description    :
@CreateTime     :  2024/8/8 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/8 17:01
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class ReturnPickAPI(weeeTest.TestCase):

    def gettasklist(self,action,workType,warehouseNumber,userId):
        #领取任务/查询任务/二次领取任务
        url = '/wms/vendorReturnPick/getList'
        body = {
            "workType": workType,
            "action": action,
            "warehouseNumber": warehouseNumber,
            "storageType": 2,
            "userId": userId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def checkpallet(self,pallet_no,orderid,itemLevel,workType,userId):
        #扫描pallet
        url = '/wms/vendorReturnPick/checkPallet'
        body = {
            "warehouseNumber": "25",
            "pickingPalletNo": pallet_no,
            "vendorReturnOrderId": orderid,
            "itemLevel": itemLevel,
            "workType": workType,
            "storageType": 2,
            "userId":userId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickDetailLpn(self,orderNO,recid,locationNo):
        url = '/wms/vendorReturnPick/pickDetailLpn'
        body = {
                "warehouseNumber":"25",
                "returnNo":orderNO,
                "taskRecId":recid,
                "locationNo":locationNo
                }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickDetail(self,orderNO,recid,locationNo):
        url = '/wms/vendorReturnPick/pickDetail'
        body = {"warehouseNumber":"25",
                "returnNo":orderNO,
                "taskRecId":recid,
                "locationNo":locationNo
                }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickConfirmLpn(self,itemNumber,pallet,lpnNoList,warehouseNumber,returnNo,recid,location):
        #LPN pick confirm
        url = '/wms/vendorReturnPick/pickConfirmLpn'
        body = {
                "itemNumber":itemNumber,
                "palletNo": pallet,
                "lpnPickingAction": 0,
                "lpnNoList": lpnNoList,
                "warehouseNumber": warehouseNumber,
                "returnNo": returnNo,
                "taskRecId": recid,
                "locationNo": location
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickConfirm(self,itemNumber,pallet,quantity,orderNO,recid,locationNo):
        #upc pick confirm
        url = '/wms/vendorReturnPick/pickConfirm'
        body = {
            "itemNumber": itemNumber,
            "palletNo": pallet,
            "quantity": quantity,
            "warehouseNumber": "25",
            "returnNo": orderNO,
            "taskRecId": recid,
            "locationNo": locationNo,
            "pickingAction": 0
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pickComplete(self,pallet_no):
        url = '/wms/vendorReturnPick/pickComplete'
        body = {
            "warehouseNumber":"25",
            "palletNo":pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def scanSlot(self,orderNO,slotNo,pallet_no):
        url = '/wms/vendorReturnPick/scanSlot'
        body = {
            "warehouseNumber":"25",
            "returnNo":orderNO,
            "type":2,
            "slotNo":slotNo,
            "palletNo":pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def releaseTask(self,userid,):
        # 释放任务
        url = '/wms/vendorReturnPick/releaseTask'
        body = {
            "warehouseNumber": "25",
            "storageType": 2,
            "userId":userid
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def currentTask(self,userid):
        # 获取当前任务
        url = '/wms/vendorReturnPick/currentTask'
        body = {
            "warehouseNumber": "25",
            "storageType": 2,
            "userId":userid
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]