# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json 
from wms.test_dir.api.wms.wms import *


class MofPacking(weeeTest.TestCase):
    user = ""
    warehouse = "33"

    # 获取打包任务类型
    def query_packing_task(self, tote_no, station="8-1"):
        url = "/wms/packing/queryPackingTask"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 获取打包任务信息
    def query_mof_packing_info(self, tote_no, station="8-1"):
        url = "/wms/mailOrderMix/packing/queryMixPackingInfo"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

        items = []
        mix_items = self.response['body']['mix_items']
        if "dry" in mix_items.keys():
            items.extend(mix_items["dry"])
        if "fresh" in mix_items.keys():
            items.extend(mix_items["fresh"])
        if "frozen" in mix_items.keys():
            items.extend(mix_items["frozen"])
        order_id = self.response['body']['order_id']
        recommend_package = self.response['body']['recommendBoxes']
        return items, order_id, recommend_package

    # 开始QC商品
    def mix_packing_qc(self, items, order_id):
        url = "/wms/mailOrderMix/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 12,
            "is_skip_current_storage_type": False
        }
        for item in items:
            body["upc"] = item["upc"]
            body["item_number"] = item["item_number"]
            body["quantity"] = item["item_quantity"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def mix_oos_qc(self, items, order_id):
        url = "/wms/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 12,
            "is_skip_current_storage_type": False
        }
        for i in range(len(items) - 1):
            body["upc"] = items[i]["upc"]
            body["item_number"] = items[i]["item_number"]
            body["quantity"] = items[i]["item_quantity"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # 返回未QC的Item
        return items[-1:][0]

    # 选择包材
    def mix_scan_box(self, recommend_package, order_id):
        url = "/wms/mailOrderMix/packing/scanBox"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "box_barcode": "",
            "box_num": None
        }
        ices = []
        for box in recommend_package:
            if box["type"] in (1, 2):
                barcode = box["barcode"]
                break
        for box in recommend_package:
            if box["type"] == 3:
                ices.append(box)
        body["box_barcode"] = barcode
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        parent_id = self.response["body"]
        for ice in ices:
            body["box_barcode"] = ice["barcode"]
            body["parent_id"] = parent_id
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 封箱
    def box_full(self, order_id):
        url = "/wms/mailOrderMix/packing/boxFull"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "weight": 0.2,
            "supervisor_view": False,
            "previous_overweight": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def mix_packing_force_stock(self, tote_no, order_id):
        url = f"/wms/mailOrderMix/packing/forcestock/{order_id}"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "edit_user": self.user,
            "tote_from": tote_no,
            "scanSupervisorPassword": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 打印label
    def mof_label_create(self, order_id):
        url = f"/wms/mailOrderMix/packing/{order_id}/merge_mail_order/label/create"
        body = {
            "order_id": order_id,
            "warehouse_number": self.warehouse,
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 出库
    def mof_ship(self, tote_no, order_id):
        url = f"/wms/mailOrderMix/packing/{order_id}/ship"
        body = {
            "order_id": order_id,
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 缺发QC
    def mof_oos_qc(self, items, order_id):
        url = "/wms/mailOrderMix/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 12,
            "is_skip_current_storage_type": False
        }

        for i in range(len(items) - 1):
            body["upc"] = items[i]["upc"]
            body["item_number"] = items[i]["item_number"]
            body["quantity"] = items[i]["item_quantity"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def query_replenish_quantity(self, tote_no,order_id):
        url = "/wms/packing/queryReplenishItemQuantity"
        body = {
            "tote_from": tote_no,
            "edit_user": self.user,
            "order_id": order_id,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # MOF转补单
    def mof_packing_replenish(self, tote_no, order_id, oos_item):
        url = "/wms/mailOrderMix/packing/packingReplenish"
        replenish_list = list()
        replenish_item_details = dict()
        for item in oos_item:
            replenish_item_details["item_number"] = item["item_number"]
            replenish_item_details["miss_quantity"] = item["item_quantity"]
            replenish_item_details["quality_quantity"] = 1
            replenish_item_details["lost_quantity"] = item["item_quantity"] - 1
            replenish_list.append(replenish_item_details)

        body = {
            "warehouse_number": self.warehouse,
            "tote_from": tote_no,
            "edit_user": self.user,
            "order_id": order_id,
            "replenishItemDetails": replenish_list
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 缺发出库
    def mof_packing_force_stock(self, tote_no, order_id):
        url = f"/wms/mailOrderMix/packing/forcestock/{order_id}"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "edit_user": self.user,
            "tote_from": tote_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def mof_move_to_cot(self, tote_no, order_id):
        """
        MOF订单取消，move to cot

        Args:
            tote_no (_type_): tote number
            order_id (_type_): wms order id
        """        
        url = "/wms/mailOrderMix/packing/cancel/moveToCOT"
        body = {
            "warehouse_number": self.warehouse,
            "tote_from": tote_no,
            "edit_user": self.user,
            "order_id": order_id,
            "work_station": "8-1"
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def mof_create_cot_label(self, order_id):
        """
        MOF 订单取消，转移库存到COT Tote后打印COT Label

        Args:
            order_id (_type_): wms order id
        """        
        url = f"/wms/cancel/order/label/create/{order_id}"
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"