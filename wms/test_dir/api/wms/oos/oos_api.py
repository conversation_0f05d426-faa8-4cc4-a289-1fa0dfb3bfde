import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class OrderOOSItem(weeeTest.TestCase):
    """
    central 发起订单oos相关接口
    """

    # OOS List
    def get_order_oos_list_page(self,warehouse_number, delivery_dtm, order_type=0, pageSize=100, item_number="",wave=""):
        """
        获取订单项列表
        :param delivery_dtm: 配送日期
        :param draw:
        :param in_dtm_moment:
        :param item_number: 商品编号
        :param page_size: 每页大小
        :param order_column: 排序列
        :param order_rule: 排序规则
        :param start_column: 起始列
        :param warehouse_number: 仓库编号
        :param wave: 波次
        :param order_type: 订单类型
        :param check_result: 是否检查结果
        :return: 响应
        """
        url="/wms/order/item/unpre/page"
        body = {
            "delivery_dtm": delivery_dtm,
            "draw": 3,
            "in_dtm_moment": "2025-08-08T16:00:00.000Z",
            "item_number": item_number,
            "pageSize": pageSize,
            "order": {"orderColumn": "oos_quantity", "orderRule": "desc"},
            "startColumn": 0,
            "warehouse_number": warehouse_number,
            "wave": wave,
            "order_type": order_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def get_order_oos_list_detail(self, delivery_dtm, item_number, warehouse_number, order_type, wave=""):
        """
        获取订单项列表详情
        :param delivery_dtm: 配送日期
        :param item_number: 商品编号
        :param warehouse_number: 仓库编号
        :param wave: 波次
        :param order_type: 订单类型
        :return: 响应
        """
        url="/wms/order/item/wave/unpre"
        body = {
            "delivery_dtm": delivery_dtm,
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "wave": wave,
            "order_type": order_type,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def order_oos_items(self, warehouse_number, edit_user, delivery_dtm, order_list, order_type):
        """
        item oos
        """
        url="/wms/order/item/oospre"
        body = {
            "warehouse_number": warehouse_number,
            "edit_user": edit_user,
            "delivery_dtm": delivery_dtm,
            "orderList": order_list,
            "order_type": order_type,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
    # CS cancel order
    def cs_cancel_order(self, in_user, warehouse_number, order_id, order_type):
        """CS 取消订单

        Args:
            warehouse_number: 仓库编号
            order_id: 订单ID
            order_type: 订单类型
        """
        url="/wms/outbound/orders/update"
        body = [
                {
                    "edit_user": in_user,
                    "items": [],
                    "order_id": order_id,
                    "order_type": order_type,
                    "reason": "cancel_reason: 自动化测试取消",
                    "warehouse_number": warehouse_number,
                }
            ]
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    #OOS Refund
    def order_oos_refund_list(self, warehouse_number, order_id):
        """
        获取订单下可做OOS refund的Item
        """
        url="/wms/route/itemList"    
        body = {
            "order_id": order_id,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def order_oos_refund(self, warehouse_number, order_id, item_list, user_id, order_type):
        """
        对单个/多个商品做OOS Refund

        Args:
            item_list (list): [{
                "item_number": "95442",
                "quantity": 1
            }]
        """
        url="/wms/route/oosRefund"
        body = {
            "type": 1,
            "order_id": order_id,
            "item_list": item_list,
            "user_id": user_id,
            "warehouse_number": warehouse_number,
            "order_type": order_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def central_move_to_cot(self, warehouse_number, order_id):
        """
        central cancel order management: move to cot
        """
        url="/wms/cancel/order/moveToCOT"
        body = {
            "warehouse_number" : warehouse_number,
            "order_id": order_id
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def oos_refund_order_list(self, warehouse_number, delivery_date, in_user):
        """
        query oos refund order list
        """
        url="/wms/route/orderList"
        body = {
            "warehouse_number": warehouse_number,
            "route_ids": [],
            "delivery_ids": [],
            "delivery_date": delivery_date,
            "order_packing_num": "",
            "order_type": "",
            "startColumn": 0,
            "pageSize": 100,
            "in_user": in_user,
            "order_id": ""
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]