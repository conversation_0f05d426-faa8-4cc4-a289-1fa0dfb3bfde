# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
from wms.test_dir.api.wms.wms import *


class CollectExtraAPI(weeeTest.TestCase):
    """
    CollectEXtra相关接口
    """
    def collect_extra_search_item(self, warehouse_number, upc):
        """collect extra search
        """
        url = "/wms/extra/fresh/scan/moreitem"
        body = {
            "warehouse_number": warehouse_number,
            "param": upc
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def collect_extra_complete(self, warehouse_number, location_no, item_number, quantity, pieces_per_pack, expire_dtm_str, receive_dtm_str):
        """collect extra confirm 
        """
        url = "/wms/extra/fresh/complete"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "item_number": item_number,
            "quantity": quantity,
            "pieces_per_pack": pieces_per_pack,
            "date_type": 0,
            "expire_dtm_str": expire_dtm_str,
            "receive_dtm_str": receive_dtm_str
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response