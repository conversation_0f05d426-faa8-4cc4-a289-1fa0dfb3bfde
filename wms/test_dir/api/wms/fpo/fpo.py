# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  fpo.py
@Description    :  
@CreateTime     :  2023/11/24 13:57
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/24 13:57
"""
import weeeTest
from weeeTest import jmespath, log
from tms.test_dir.api.tms import central_header, so_header
from tms.test_dir.api_case.tms.tms_sql import TmsDB
from tms.test_dir.api_case.tms import utils


class FPO(weeeTest.TestCase):
    """
    FPO方法封装
    """
    tms_db = TmsDB()
    util = utils

    def down_order_to_fpo(self, order_id):
        """
        so同步fpo订单
        @param order_id: 订单ID
        @return:
        """
        body = [
            {
                "order_id": order_id,
                "type": "down_order_queue_ack"
            }
        ]
        self.put(url='/central/so/order/downByOrderId', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'so订单{order_id}同步fpo成功!')
        else:
            log.error(f'so订单{order_id}同步fpo失败,Response:{self.response}')
        return self.response

    def down_order_to_wms(self, last_push_time):
        """
        FPO下发订单到WMS(job)
        @param last_push_time: 1700848800
        @return:
        """

        self.get(url=f'/wms-order/job/fpo/autoGenerateAndSend?time={last_push_time}', headers=central_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'FPO下发订单到WMS成功!')
        else:
            log.error(f'FPO下发订单到WMS失败,Response:{self.response}')
        return self.response

    # SO相关接口
    def so_anon_auth(self):
        """
        获取匿名token
        @return:
        """
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'so匿名auth:{auth}')
        so_header["authorization"] = 'Bearer ' + auth
        return auth

    def so_customer_signup(self, email, passwd='1234abcd'):
        """
        用户账户注册
        :param email: 地址
        :param passwd:  密码
        :return:
        """
        body = {
            "email": email,
            "password": passwd,
            "referral_id": 0,
            "ep_partner": ""
        }
        self.post(url=f'/ec/customer/signup', headers=so_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'用户{email}账户注册成功!')
        else:
            log.error(f'用户{email}账户注册失败,Response:{self.response}')
        return self.response

    def so_login(self, email, passwd='1234abcd'):
        """
        用户账户登录
        :param email: 地址
        :param passwd:  密码
        :return:
        """
        body = {
            "email": email,
            "password": passwd,
            "referral_id": 0
        }
        self.so_anon_auth()
        self.post(url=f'/ec/customer/login/email', headers=so_header, json=body)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'用户{email}账户登录成功!')
            so_header["authorization"] = 'Bearer ' + auth
        else:
            raise Exception(f'用户{email}账户登录失败,Response:{self.response}')
        return self.response

    def so_update_points(self, user_id):
        """
        给用户积分
        :param user_id:
        :return:
        """
        body = {
            "pointsIssueItemList": [
                {
                    "userId": user_id,
                    "points": 10000,
                    "typeId": 34,
                    "refId": "2133243",
                    "uniqBizId": "FD8FBA2E-C28C-4B30-9ABC-A5611CBt0Et8"
                }
            ]
        }
        self.post(url=f'/central/customer/points_new/issue', headers=so_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'给用户{user_id}积分成功!')
        else:
            log.error(f'给用户{user_id}积分失败,Response:{self.response}')
        return self.response

    def so_active_points(self, user_id):
        """
        给用户激活积分
        :param user_id:
        :return:
        """
        body = {}
        self.post(url=f'/ec/customer/points_new/active', headers=so_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'给用户{user_id}激活积分成功!')
        else:
            log.error(f'给用户{user_id}积分积分失败,Response:{self.response}')
        return self.response

    def so_porder_zipcode(self, zipcode):
        """
        设置zipcode购物车
        :param zipcode:
        :return:
        """
        body = {
            "zipcode": zipcode
        }
        self.put(url=f'/ec/so/porder/zipcode', headers=so_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'设置zipcode{zipcode}购物车成功!')
        else:
            log.error(f'设置zipcode{zipcode}购物车失败,Response:{self.response}')
        return self.response

    def so_get_porder_simple(self):
        """
        获取购物车信息
        :return:
        """

        self.get(url=f'/ec/so/porder/simple', headers=so_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'获取购物车信息成功!')
        else:
            log.error(f'获取购物车信息失败,Response:{self.response}')
        return self.response

    def so_get_user_addresses(self):
        """
        获取当前用户的地址
        :return:
        """

        self.post(url=f'/ec/customer/addresses', headers=so_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'获取当前用户的地址成功!')
        else:
            log.error(f'获取当前用户的地址失败,Response:{self.response}')
        return self.response

    def so_add_user_addresses(self, addr_firstname, addr_lastname, phone, email, addr_address, addr_apt, addr_city,
                              addr_state, addr_zipcode, addr_country):
        """
        添加地址到地址簿
        :param addr_firstname:
        :param addr_lastname:
        :param phone:
        :param email:
        :param addr_address:
        :param addr_apt:
        :param addr_city:
        :param addr_state:
        :param addr_zipcode:
        :param addr_country:
        :return:
        """
        body = {
            "addr_firstname": addr_firstname,
            "addr_lastname": addr_lastname,
            "phone": phone,
            "email": email,
            "comment": "Api test order, please cancel after verification",
            "addr_address": addr_address,
            "addr_apt": addr_apt,
            "addr_city": addr_city,
            "addr_state": addr_state,
            "addr_zipcode": addr_zipcode,
            "addr_country": addr_country,
            "force": True
        }
        self.post(url=f'/ec/customer/addresses', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'添加地址到地址簿成功!')
        else:
            log.error(f'添加地址到地址簿失败,Response:{self.response}')
        return self.response

    def so_change_delivery_date(self, delivery_date):
        """
        切换配送日期
        :param delivery_date:
        :return:
        """
        body = {
            "delivery_pickup_date": delivery_date,
            "force": 0
        }

        self.post(url=f'/ec/so/porder/date', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'切换配送日期至{delivery_date}成功!')
        else:
            log.error(f'切换配送日期至{delivery_date}失败,Response:{self.response}')
        return self.response

    def so_add_product_quantity(self, product_id, delivery_date, quantity):
        """
        增加商品数量
        :param product_id:
        :param delivery_date:
        :param quantity:
        :return:
        """
        body = [
            {
                "product_id": product_id,
                "source": "categories-undefined-all",
                "refer_type": "normal",
                "refer_value": "",
                "delivery_date": delivery_date,
                "min_order_quantity": 1,
                "is_pantry": False,
                "is_alcohol": False,
                "item_type": "",
                "is_mkpl": False,
                "positionInfoT2": {
                    "modSecPos": {
                        "mod_nm": "item_list",
                        "mod_pos": 2
                    },
                    "prodPos": 0,
                    "context": {
                        "filter_sub_category": "sale",
                        "catalogue_num": "all",
                        "sort": "recommend",
                        "filters": {
                            "delivery": True
                        },
                        "global_vendor": None
                    }
                },
                "ctx": {
                    "filter_sub_category": "sale",
                    "catalogue_num": "all",
                    "sort": "recommend",
                    "filters": {
                        "delivery": True
                    },
                    "global_vendor": None
                },
                "quantity": quantity,
                "new_source": "{\"mod_nm\":\"item_list\",\"mod_pos\":2,\"prod_pos\":0,\"page_key\":\"mweb_category\",\"referer_page_key\":\"mweb_sort_filter\",\"view_id\":\"7f94ebce8c2a40568b6e858b9d800900\"}"
            }
        ]

        self.put(url=f'/ec/so/porder/items/v2', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'增加商品{product_id}数量{quantity}成功!')
        else:
            log.error(f'增加商品{product_id}数量{quantity}失败,Response:{self.response}')
        return self.response

    def so_get_porder_v3(self):
        """
        获取购物车porder V3
        :return:
        """

        self.get(url=f'/ec/so/porder/v3', headers=so_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'获取购物车porder V3成功!')
        else:
            log.error(f'获取购物车porder V3失败,Response:{self.response}')
        return self.response

    def so_pre_checkout(self, refer_type, deal_id):
        """
        预结算
        :param refer_type:
        :param deal_id:
        :return:
        """
        body = {
            "type": refer_type,
            "deal_id": deal_id,
            "vendor_id": 6880
        }
        self.post(url=f'/ec/so/order/checkout/pre', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'{refer_type}预结算成功!')
        else:
            log.error(f'{refer_type}预结算失败,Response:{self.response}')
        return self.response

    def so_apply_address(self, address_id, delivery_date):
        """
        选择地址
        :param address_id:
        :param delivery_date:
        :return:
        """
        body = {
            "address_id": address_id,
            "force": False,
            "type": "normal",
            "date": delivery_date
        }
        self.put(url=f'/ec/so/address/apply', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'选择地址成功!')
        else:
            log.error(f'选择地址失败,Response:{self.response}')
        return self.response

    def so_payment_category(self, payment_category="P"):
        """
        选择支付方式
        :param payment_category:
        :return:
        """
        body = {
            "payment_category": payment_category,
            "points": True
        }
        self.put(url=f'/ec/so/porder/payment_category', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'选择支付方式{payment_category}成功!')
        else:
            log.error(f'选择支付方式{payment_category}失败,Response:{self.response}')
        return self.response

    def so_order_checkout(self, refer_type, deal_id, vendor_id, tip, referral_id):
        """
        订单结算
        :param refer_type:
        :param deal_id:
        :param vendor_id:
        :param tip:
        :param referral_id:
        :return:
        """
        body = {
            "order_window": None,
            "type": refer_type,
            "deal_id": deal_id,
            "vendor_id": vendor_id,
            "tip": tip,
            "referral_id": referral_id,
            "is_pick_up": None,
            "order_note": "",
            "braintree_device_data": "",
            "page_time": 1692956134831
        }
        self.post(url=f'/ec/so/order/checkout', headers=so_header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'订单结算成功!')
        else:
            log.error(f'订单结算失败,Response:{self.response}')
        return self.response

    def so_my_order(self):
        """
        验证待支付页面存在该订单${order_id}
        :return:
        """
        self.post(url=f'/ec/so/order/query/listMyOrder', headers=so_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'订单结算成功!')
        else:
            log.error(f'订单结算失败,Response:{self.response}')
        return self.response

    def so_logout(self):
        """
        退出登录
        :return:
        """
        self.get(url='/ec/customer/logout', headers=so_header)
        result = jmespath(self.response, "success")
        if result:
            so_header["authorization"] = None
            log.info(f'当前账户退出登录成功!')
        else:
            log.error(f'当前账户退出登录失败,Response:{self.response}')
        return self.response


fpo = FPO()
