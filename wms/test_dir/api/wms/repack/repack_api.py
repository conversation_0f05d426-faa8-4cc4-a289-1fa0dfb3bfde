# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  repack_api.py
@Description    :
@CreateTime     :  2024/8/12 11:47
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/12 11:47
"""

import json
import weeeTest
from wms.test_dir.api.wms.wms import header
import datetime

class Repack(weeeTest.TestCase):

    #创建任务
    def RepackCreateTask(self,delivery_date,warehouse):
        url = "/wms/repack/test/task/create/manual"
        body = {
                "delivery_date": delivery_date,
                "warehouse_number": warehouse
        }
        self.post(url=url,headers=header,json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    #预占
    def RepackPre(self, warehouse):
        url = f"/wms/job/repackTask/preoccupancy?warehouse_number={warehouse}"
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def CheckLocation(self, warehouse, locationno, taskId):
        url = "/wms/repackPick/checkLocation"
        body = {
            "warehouse_number": warehouse,
            "type": 0,
            "locationNum": locationno,
            "task_id": taskId
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


    def pickListWitchLpn(self,action,warehouse,palletno,taskId,type):
        url = "/wms/repackPick/pickListWitchLpn"
        body ={
            "action": action,
            "warehouse_number": warehouse,
            "pallet_no": palletno,
            "task_id": taskId,
            "type": type
        }
        self.post(url=url, headers=header,json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pickingDetailWitchLpn(self,locationno,picking_task_id,warehouse,new_lpn_no,**kwargs):
        url = "/wms/repackPick/pickingDetailWitchLpn"
        body ={
            "location_no": locationno,
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse,
            "pickedLpnList": [],
            "new_lpn_no": new_lpn_no,
            "undo": None
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pickingConfirmWitchLpn(self,locationno,PalletNo,skuQuantity,picking_task_id,warehouse,pickedlpnlist):
        url = "/wms/repackPick/pickingConfirmWitchLpn"
        body ={
            "warehouse_number": warehouse,
            "source_location": locationno,
            "target_location": PalletNo,
            "oriQuantity": skuQuantity,
            "picking_task_id": picking_task_id,
            "lpnPickingErr": 0,
            "pickedLpnList": pickedlpnlist
}
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_current_date(self):
        today = datetime.datetime.now()
        return today.strftime("%Y-%m-%d")

    def getRepackConfig(self):
        url = "/wms/common/queryConfigList"
        body = {
            "warehouse_number": "000",
            "config_key": "wms_job_mapping_config"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def getRepackTaskDetail(self,warehouse,task_id):
        url = "/wms/repackPick/task/detail"
        body = {
            "warehouse_number": warehouse,
            "task_id": task_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def addRepackItem(self,itemNumber,piecesPerPack,targetItemNumber,targetPiecesPerPack,targetQuantity,taskId,warehouse_number):
        url = "/wms/repackPick/task/detail/add"
        body = {
            "itemNumber": itemNumber,
            "piecesPerPack": piecesPerPack,
            "targetItemNumber": targetItemNumber,
            "targetPiecesPerPack": targetPiecesPerPack,
            "targetQuantity": targetQuantity,
            "taskId": taskId,
            "warehouseNumber": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        # 业务中使用状态码，此处不断言接口是否成功
        return self.response

    def deleteRepackItem(self,ItemRecId):
        url = f"/wms/repackPick/task/detail/{ItemRecId}"
        self.delete(url=url, headers=header)
        # 业务中使用状态码，此处不断言接口是否成功
        return self.response

    def updateRepackItem(self,warehouse_number,task_id,rec_id,quantity):
        url = "/wms/repackPick/task/detail/update"
        body = {
            "warehouse_number": warehouse_number,
            "task_id": task_id,
            "rec_id": rec_id,
            "quantity": quantity
        }
        self.post(url=url, headers=header, json=body)
        # 业务中使用状态码，此处不断言接口是否成功
        return self.response


    def queryReceiveList(self,**kwargs):
        url = "/wms/receive/queryReceiveList"
        self.post(url=url, headers=header, json=kwargs)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryRepackItemDetail(self, reference_no, item_number, warehouse_number, user_id):
        url = "/wms/receive/queryRepackItemDetail"
        body = {
                "reference_no": reference_no,
                "item_number": item_number,
                "warehouse_number": warehouse_number,
                "in_user": user_id
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryRepackPickBatchList(self, reference_no, item_number, warehouse_number, user_id):
        url = "/wms/receive/queryRepackPickBatchList"
        body = {
            "reference_no": reference_no,
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "in_user": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def receiveConfirm(self,**kwargs):
        url = "/wms/receive/lpnConfirm"
        self.post(url=url, headers=header, json=kwargs)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryUndoTaskList(self,warehouse_number,user_id,po_number):
        url = "/wms/putaway/app/queryUndoTaskList"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": user_id,
            "reference_no": po_number,
            "storage_type": "2"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def todoList(self,warehouse_number,po_number,storage_type,user_id):
        url = "/wms/putaway/app/todoList"
        body = {
            "warehouse_number": warehouse_number,
            "reference_no": po_number,
            "storage_type": storage_type,
            "in_user": user_id,
            "startColumn": 0,
            "pageSize": 10
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def scanAndCheckTask(self,warehouse_number,po_number,user_id,lpn_no):
        url = "/wms/putaway/app/scanAndCheckTask"
        body ={
            "warehouse_number": warehouse_number,
            "in_user": user_id,
            "reference_no": po_number,
            "label_no": lpn_no,
            "storage_type": "2"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def confirmAndScheduleTasks(self,warehouse_number,user_id,putaway_task_id,pallet_no):
        url = "/wms/putaway/app/confirmAndScheduleTasks"
        body ={
            "warehouse_number": warehouse_number,
            "in_user": user_id,
            "task_id_list": [putaway_task_id],
            "pallet_no": pallet_no,
            "module_name": "repack_putaway"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryPutawayInfo(self,putaway_task_id,warehouse_number,user_id):
        url = "/wms/putaway/app/queryPutawayInfo"
        body ={
            "task_id": putaway_task_id,
            "warehouse_number": warehouse_number,
            "in_user": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryTaskConfirmDetail(self,putaway_task_id,location_no,warehouse_number,user_id):
        url = "/wms/putaway/app/queryTaskConfirmDetail"
        body ={
            "task_id": putaway_task_id,
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "in_user": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def confirmPutaway(self,**kwargs):
        url = "/wms/putaway/app/confirmPutaway"
        self.post(url=url, headers=header, json=kwargs)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def partialComplete(self, warehouse_number, task_id, in_user):
        url = "/wms/putaway/app/partialComplete"
        body = {
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "module_name": "repack_putaway"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def queryCentralRepackTask(self, warehouse_number, delivery_date):
        url = "/wms/repackPick/list/task"
        body = {
            "warehouse_number": warehouse_number,
            "delivery_date": delivery_date,
            "startColumn": 0,
            "pageSize": 10,
            "task_id": ""
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def FinishRepackTask(self, warehouse_number, task_id):
        url = "/wms/repackPick/task/finish"
        body = {
                "task_id": task_id,
                "warehouse_number": warehouse_number
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


if __name__ == '__main__':
    repack = Repack()
    # current_date = repack.get_current_date()
    # print(current_date)
    resp2 = repack.todoList(warehouse_number='20',po_number='R2024120102',user_id='7251010')
    print(resp2['body'][0]['item_number'])
