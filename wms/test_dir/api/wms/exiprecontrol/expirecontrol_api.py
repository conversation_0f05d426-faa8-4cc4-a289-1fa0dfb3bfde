# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  expirecontrol_api.py
@Description    :
@CreateTime     :  2025/8/3 15:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/8/3 15:13
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class ExpireControlAPI(weeeTest.TestCase):

    def expire_control_create(self):
        """生成任务"""
        url = "/wms/job/expiration/removal"
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def cancelRemovalTask(self, warehouse_number, rec_ids, edit_user):
        """取消过期商品移除任务"""
        url = "/wms/expirationremoval/cancelRemovalTask"
        body = {
            "warehouse_number": warehouse_number,  # 仓库编号
            "rec_ids": rec_ids,  # 记录ID列表
            "edit_user": edit_user  # 编辑用户
        }
        self.post(url=url, headers=header, json=body)
        '''
        响应示例:
        {
            "messageId": "10000",
            "success": true,
            "body": "success"
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def queryRemovalInfoList(self, warehouse_number, status_list=None, item_number=None, location_no="", start_column=0,
                             page_size=15):
        """查询过期商品移除信息列表"""
        url = "/wms/expirationremoval/queryRemovalInfoList"
        body = {
            "warehouse_number": warehouse_number,  # 仓库编号
            "status_list": status_list or [10, 20, 30, 40, 45, 55],  # 状态列表
            "item_number": item_number,  # 商品编号
            "location_no": location_no,
            "startColumn": start_column,  # 起始列
            "pageSize": page_size  # 页面大小
        }
        self.post(url=url, headers=header, json=body)
        '''
        响应示例:
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recordsTotal": 2,
                "recordsFiltered": 2,
                "draw": 0,
                "data": [...],
                "flag": false
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

