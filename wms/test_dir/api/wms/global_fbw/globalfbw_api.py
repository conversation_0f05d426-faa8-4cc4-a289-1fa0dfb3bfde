# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  globalfbw_api.py
@Description    :  
@CreateTime     :  2024/12/26 16:02
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/26 16:02
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header, mkplheader


class GlobalFbwAPI(weeeTest.TestCase):

    def createinboundorder(self, po_number):
        # wms创建订单
        url = '/wms/inbound/orders'
        body ={
        "direct_import": "N",
        "email": "",
        "expected_dt": 1735344000,
        "items": [
            {
                "boxes": 1,
                "case_spec": "30.00 lb",
                "gift_quantity": 0,
                "is_variable_quantity": "N",
                "item_number": "2071603",
                "max_case_spec": "0.00",
                "max_overage_quantity": 4,
                "min_case_spec": "0.00",
                "pieces_per_pack": "2.00",
                "quantity": 2,
                "skip_check_shelf_life": 0,
                "unit_spec": "15.00 lb",
                "vendor_item_name": "paomo"
            }
        ],
        "latest_dt": 1735257600,
        "pm_name": "System",
        "pm_user_id": 8888,
        "po_number": po_number,
        "po_type": "ZNB1",
        "reference_type": 7,
        "status": 0,
        "trade_no": "4102654444",
        "vendor_email": "<EMAIL>",
        "vendor_ename": "Glob8test0327111",
        "vendor_id": 9012,
        "vendor_name": "Globa+ Vendor0328 New",
        "warehouse_number": "25"
    }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def mkplcreateinboundorder(self, delivery_date,item_number):
        # mkpl创建订单
        url = '/central/mkpl/global/fbw/sellers/7596/pos'
        body =[
                {
                    "warehouse_number": "25",
                    "products": [
                        {
                            "product_id": item_number,
                            "list": [
                                {
                                    "box_quantity": 1,
                                    "sku_quantity": 22,
                                    "expiration_date": "2026-01-01"
                                }
                            ]
                        }
                    ],
                    "est_delivery_date": delivery_date
                }
            ]
        self.post(url=url, headers=mkplheader, json=body)
        assert self.response['result'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["object"]

    def mkplcreatelpn(self,refenceno):
        #下发lpn
        url = f'''/central/mkpl/global/fbw/sellers/7596/pos/{refenceno}/shipment/create'''
        body = {
                "urls": [],
                "tracking_infos": [
                    {
                        "carrier_id": "22",
                        "carrier_name": "4PX",
                        "tracking_number": "4455667"
                    }
                ]
            }
        self.post(url=url, headers=mkplheader, json=body)
        assert self.response['result'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["object"]
