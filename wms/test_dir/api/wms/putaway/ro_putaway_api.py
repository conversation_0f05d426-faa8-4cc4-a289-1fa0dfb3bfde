# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json

import weeeTest

from wms.test_dir.api.wms.wms import header


class RoPutAwayAPI(weeeTest.TestCase):
    account = ''
    user = ''
    in_user = "jinyu.xu(9946725)"

    #  ——扫描pallet
    def ro_check_pallet(self, warehouse_number, pallet_no):
        url = "/wms/putaway/app/checkPallet"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "pallet_no": pallet_no,
            "storage_type": "1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True


    # ——获取pallet下的待上架单
    def ro_list(self, pallet_no, warehouse_number):
        url = "/wms/putaway/app/roList"
        body = {
            "pallet_no": pallet_no,
            "warehouse_number": warehouse_number,
            "storage_type": "1",
            "in_user": self.in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    # ——点击confirm跳转到上架详情页面
    def ro_schedule_tasks(self, warehouse_number, task_id_list, pallet_no):
        url = "/wms/putaway/app/ROScheduleTasks"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "task_id_list": task_id_list,
            "pallet_no": pallet_no,
            "module_name": "put_away"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # ——获取待上架单的详细信息
    def ro_query_putaway_info(self, task_id, warehouse_number):
        url = "/wms/putaway/app/queryPutawayInfo"
        body = {
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response['body']

    # ——扫描要上架的库位（获取上架单的详情）
    def query_ro_task_confirm_detail(self, task_id, location_no, warehouse_number):
        url = "/wms/putaway/app/queryROTaskConfirmDetail"
        body = {
            "task_id": task_id,
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response['body']

    # ——点击confirm,提交上架数据
    def ro_confirm_putaway(self, putaway_qty, warehouse_number, task_id, location_no, actual_location_recommend_qty):
        url = "/wms/putaway/app/confirmPutaway"
        body = {
            "module_name": "put_away",
            "putaway_qty": putaway_qty,
            "warehouse_number": warehouse_number,
            "task_id": task_id,
            "location_no": location_no,
            "in_user": self.in_user,
            "actual_location_recommend_qty": actual_location_recommend_qty
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"]== True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
