# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from wms.test_dir.api.wms.wms import header


class PutAwayAPI(weeeTest.TestCase):
    account = ''
    user = ''
    in_user = "jinyu.xu(9946725)"

    def query_central_putaway_task_list(self, warehouse_number, reference_no, create_time="", end_time="", pallet_no="", status_list=[]):
        url = "/wms/putaway/central/taskList"
        body = {
            "warehouse_number": warehouse_number,
            "startColumn": 0,
            "pageSize": 15,
            "pallet_no": pallet_no,
            "reference_type_list": [],
            "status_list": status_list,
            "reference_no": reference_no,
            "start_date_str": "",
            "end_date_str": "",
            "create_time": create_time,
            "end_time": end_time
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    #  ——扫描扫描pallet（检查扫描pallet是否被占用）
    def check_Pallet(self, warehouse_number, pallet_no):
        url = "/wms/putaway/app/checkPallet"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "pallet_no": pallet_no,
            "storage_type": "1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "The pallet is already occupied. Please scan for other pallets"

    # ——扫描待上架的MLPN  NO
    def scan_And_Check_Task(self, warehouse_number, label_no, storage_type):
        url = "/wms/putaway/app/scanAndCheckTask"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "label_no": label_no,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "扫描待上架的MLPN NO无效"
        return self.response["body"]

    # ——点击confirm跳转到上架详情页面
    def confirm_And_Schedule_Tasks(self, warehouse_number, task_id_list, pallet_no):
        url = "/wms/putaway/app/confirmAndScheduleTasks"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "task_id_list": task_id_list,
            "pallet_no": pallet_no,
            "module_name": "put_away"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "领取mlpn失败"

    # ——获取待上架mlpn的详细信息
    def query_Putaway_Info(self, task_id, warehouse_number):
        url = "/wms/putaway/app/queryPutawayInfo"
        body = {
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "获取待上架mlpn的详细信息失败"
        return self.response['body']

    # ——扫描要上架的库位（获取MLPN 下所有未上架的lpn）
    def query_Task_Confirm_Detail(self, task_id, location_no, warehouse_number, process):
        url = "/wms/putaway/app/queryTaskConfirmDetail"
        body = {
            "task_id": task_id,
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "process": process
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "获取MLPN下所有未上架的lpn失败"
        return self.response['body']

    # ——点击confirm,提交上架数据
    def confirm_Putaway(self, item_number, location_no, task_id, warehouse_number, rec_id, lpn_no, original_quantity, actual_location_recommend_qty):
        url = "/wms/putaway/app/confirmPutaway"
        body = {
            "item_number": item_number,
            "location_no": location_no,
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "module_name": "put_away",
            "scan_mlpn": True,
            "lpnInfoList": [
                {
                    "rec_id": rec_id,
                    "warehouse_number": None,
                    "item_number": None,
                    "lpn_no": lpn_no,
                    "original_quantity": original_quantity
                }
            ]
            ,
            "actual_location_recommend_qty": actual_location_recommend_qty
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "提交上架数据失败"

    def confirm_Putaway_po(self, item_number, location_no, task_id, warehouse_number, lpnInfoList, actual_location_recommend_qty):
        url = "/wms/putaway/app/confirmPutaway"
        body = {
            "item_number": item_number,
            "location_no": location_no,
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "module_name": "put_away",
            "scan_mlpn": True,
            "lpnInfoList": lpnInfoList
            ,
            "actual_location_recommend_qty": actual_location_recommend_qty
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "提交上架数据失败"

        #  —— 将as属性同步kpi

    def update_items_as_attributes(self, warehouse_number, item_numbers):
        url = "/wms/items/management/updateItemsAttributes"
        body = {
            "warehouse_number": warehouse_number,
            "item_numbers": [item_numbers],
            "details": [
                {
                    "attributeCode": 78,
                    "attributeView": "as_sku",
                    "updateValue": "true"
                }
            ],
            "edit_user": self.in_user,
            "secondConfirm": True
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "as属性同步kpi失败"

    #  —— AS系统上架完成回调
    def autostore_api_host_receive(self, automation_order_no, pallet, replenTaskId, item_number, inventory_batch_no):
        url = "/wms/v1/AutoStoreApi/HostReceive"
        body = {
            "action": "ReplenResult",
            "instanceId": "SayWeee",
            "messageId": "45282701",
            "params": {
                "listComplete": False,
                "replenListId": automation_order_no,
                "lpn": pallet,
                "replenTaskInformation": [
                    {
                        "replenTaskId": replenTaskId,
                        "replenTaskComplete": True,
                        "itemId": item_number,
                        "clientId": "SayWeee",
                        "compartmentInformation": [
                            {
                                "binId": "AS110153",
                                "compartmentId": "A1",
                                "replenDate": "2024-10-18",
                                "fifoDate": "2024-09-27",
                                "expiryDate": "2024-12-01",
                                "group1": inventory_batch_no,
                                "quantity": 1,
                                "portId": 3,
                                "portType": "CONVEYOR",
                                "startDatetime": "2024-10-18T10:18:08-07:00",
                                "endDatetime": "2024-10-18T10:18:25-07:00",
                                "userId": "8400874",
                                "sortBarPositionId": "A1"
                            }
                        ]
                    }
                ]
            },
            "timestamp": "2024-10-18T10:18:25-07:00",
            "version": "2.0.0"
        }
        self.post(url=url, headers=header, json=body)

    #  —— 将geek属性同步kpi
    def update_items_geek_attributes(self, warehouse_number, item_numbers):
        url = "/wms/items/management/updateItemsAttributes"
        body = {
            "warehouse_number": warehouse_number,
            "item_numbers": [item_numbers],
            "details": [
                {
                    "attributeCode": 96,
                    "attributeView": "geekPlus_sku",
                    "updateValue": "true"
                }
            ],
            "edit_user": self.in_user,
            "secondConfirm": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, "as属性同步kpi失败"

    #  —— GEEK系统上架完成回调
    def autostore_geek_host_receive(self, pallet, lpn_no, item_number, plan_qty, inventory_batch_no, putaway_no):
        url = "/wms/geekplus/callback"
        body = {
            "body": {
                "size": 1,
                "receipt_list": [
                    {
                        "operator": "",
                        "operateTime": 0,
                        "isReady": 0,
                        "resetTimes": 0,
                        "isSync2": 0,
                        "sync2Times": 0,
                        "skuTypeAmount": 2,
                        "id": 1697,
                        "receipt_code": "hv90cj4o06n00009",
                        "warehouse_code": "41",
                        "status": 4,
                        "type": 0,
                        "pallet_code": pallet,
                        "asn_code": "A20240923-0000002",
                        "completion_time": 1727080587902,
                        "receiptor": self.in_user,
                        "receipt_status": 0,
                        "supplier_code": "",
                        "carrier_code": "",
                        "plan_sku_amount": 33,
                        "plan_sku_type_amount": 2,
                        "sku_amount": 3,
                        "sku_type_amount": 2,
                        "confirm_type": 0,
                        "owner_code": "SayWeee",
                        "is_manual_close": 1,
                        "remark": "",
                        "sync_date": 0,
                        "is_cancel": 0,
                        "sku_list": [

                            {
                                "status": 4,
                                "colValPair": {
                                    "externalCode": "hv90cj4o06n00009",
                                    "column": "id",
                                    "value": 39183
                                },
                                "tableName": "receipt_note_back_details",
                                "lastDetail": False,
                                "id": 39183,
                                "input_date": 1727079991000,
                                "item": "2",
                                "receipt_note_back_id": 1697,
                                "receipt_code": "hv90cj4o06n00009",
                                "container_code": lpn_no,
                                "sku_id": item_number,
                                "wms_id": 259,
                                "is_sequence_sku": 0,
                                "sku_code": "67693",
                                "bar_code": "",
                                "sku_name": "【wangzhihe】Soy Bean Sauce 300g",
                                "owner_code": "SayWeee",
                                "plan_amount": plan_qty,
                                "amount": 1,
                                "production_date": -63072000000,
                                "expiration_date": 0,
                                "sku_level": 0,
                                "receipt_flag": 0,
                                "out_batch_code": inventory_batch_no,
                                "packing_spec": "",
                                "shelf_code": "",
                                "shelf_bin_code": "",
                                "operator": "FISHER",
                                "refuse_reason": "",
                                "detail_sub": [],
                                "shelf_bin_list": [
                                    {
                                        "reservation1": "",
                                        "reservation2": "",
                                        "reservation3": "",
                                        "quantity": 1,
                                        "shelf_code": "P00005",
                                        "shelf_bin_code": "P00005F04B",
                                        "operator": "9946725",
                                        "logic_area_code": ""
                                    }
                                ],
                                "updated_batch_property_list": [
                                    {
                                        "amount": 1,
                                        "updated_sku_level": 0
                                    }
                                ],
                                "sequence_list": [],
                                "product_batch": "",
                                "batch_property01": "-63072000000",
                                "batch_property02": "0",
                                "batch_property03": "240923000021",
                                "batch_property04": "2653820-67693-M2",
                                "batch_property05": "",
                                "batch_property06": "",
                                "batch_property07": "",
                                "batch_property08": "",
                                "batch_property09": "",
                                "batch_property10": putaway_no,
                                "batch_property11": "SayWeee",
                                "batch_property12": "",
                                "sku_reservation_1": "",
                                "sku_reservation_2": "",
                                "sku_reservation_3": "",
                                "sku_reservation_4": "",
                                "sku_reservation_5": "",
                                "damaged_amount": 0,
                                "miss_amount": 0,
                                "is_batch": 0
                            }
                        ],
                        "reservation1": "",
                        "reservation2": "",
                        "reservation3": "",
                        "reservation4": "",
                        "reservation5": "",
                        "fstime": 1727080680032,
                        "start_time": 1727080426441,
                        "workstation_no": "101",
                        "data_source_platform": 200,
                        "feedback_strategy": "5",
                        "total_line": 0
                    }
                ],
                "receipt_amount": 1
            },
            "header": {
                "interface_code": "feedback_receipt_note",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": "41"
            }
        }
        self.post(url=url, headers=header, json=body)
