from time import sleep
import json
import weeeTest
from wms.test_dir.api.wms.wms import header
from wms.test_dir.api_case.wms.utils import DataUtils


class Adjust(weeeTest.TestCase):
    # 库存查询
    def query_location_inv(self, item_number, location_no, warehouse_number, lpn_no=None):
        """单个商品库存查询"""
        url = "/wms/adjust/queryLocationInvDetail"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_no": location_no,
            "lpn_no": lpn_no
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_location_inv_list(self, warehouse_number, location_no="", item_number="", location_types=[]):
        """多个商品库存查询"""
        url = "/wms/adjust/queryLocationInvList"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_no": location_no,
            "location_types": location_types
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    # 库存调整
    def create_batch_inv(self, item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn=False, pieces_per_pack=1, expire_dtm=0, batch_no=None,
                         lpn_list=[], receive_date=None, input_dtm_type=0, lpn_qty=None):
        url = "/wms/adjust/confirmLocationInv"
        body = {
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "reason": "other",
            "comment": "自动化测试test------",
            "adjustList": [
                {
                    "location_no": location_no,
                    "adjust_number": adjust_number,
                    "is_lpn": is_lpn,
                    "pieces_per_pack": pieces_per_pack,
                    "expire_dtm": expire_dtm,
                    "adjustLpnList": lpn_list,
                    "receive_date": DataUtils.get_special_date() if not receive_date else receive_date,
                    "input_dtm_str": DataUtils.get_special_date() if not receive_date else receive_date,
                    "input_dtm_type": input_dtm_type,
                    "lpn_qty": lpn_qty
                }
            ]
        }
        if batch_no:
            for adjust in body["adjustList"]:
                adjust["batch_no"] = batch_no
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True or self.response['messageId'] == "99004", f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 修改保质期
    def modify_shelf_life(self, warehouse_number, item_number, location_nos, lpn_nos, is_all_lpn, batch_inv_infos, new_expire_date, input_dtm_str):
        url = "/wms/adjust/update/item/expireDate"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_nos": location_nos,
            "lpn_nos": lpn_nos,
            "is_all_lpn": is_all_lpn,
            "batch_inv_infos": batch_inv_infos,
            "new_expire_date": new_expire_date,
            "input_dtm_type": 0,
            "input_dtm_str": input_dtm_str
        }
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # Lock/Unlock库存
    def lock_inv(self, is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no):
        url = "/wms/location/lockLocation"
        body = {
            "is_lock": is_lock,
            "item_number": item_number,
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "is_lpn": is_lpn,
            "lpn_nos": lpn_nos,
            "batch_no": batch_no
        }
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_approve_task(self, warehouse_number, item_number, location_no, start_time, end_time, action_types=None, status_list=None):
        if status_list is None:
            status_list = [20, 21]
        url = "/wms/check/queryDailyReportList"
        body = {
            "warehouse_number": warehouse_number,
            "start_time": start_time,
            "end_time": end_time,
            "category_ids": [],
            "location_no": location_no,
            "startColumn": 0,
            "pageSize": 100,
            "status_list": status_list,
            "aisles": [],
            "action_types": action_types
        }
        if action_types:
            body["action_types"] = action_types
        if item_number:
            body["item_number"] = item_number
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    # approve库存
    def approve_task(self, item_number, approve_type, in_user, warehouse_number, rec_id, action_type, info_id, detail_id, location_no):
        url = "/wms/approval/batchApproveTask"
        body = [{
            "item_number": item_number,
            "type": approve_type,  # 1:通过 0:拒绝
            "in_user": in_user,
            "warehouse_number": warehouse_number,
            "dailyReportItem": [
                {
                    "rec_id": rec_id,  # rec_id审核任务ID
                    "info_id": info_id,  # 盘点必传
                    "detail_id": detail_id,  # 盘点必传
                    "action_type": action_type,  # 业务类型 1:盘点 2:central调整 3:通用工具 4:work order 5:internal order return;
                    "issue_type": None,
                    "adjustment_analysis": "自动化测试审核数据......",
                    "warehouse_number": warehouse_number,
                    "location_from": None,
                    "location_no": location_no,
                    "item_number": item_number,
                    "bpmApproveDTO": {
                        "inventory_loss": False,
                        "approve": False,
                        "packaging_material": False,
                        "director_approval": None,
                        "exec_approval": None,
                        "item_number": None,
                        "name": None,
                        "ename": None,
                        "warehouse_id": warehouse_number,
                        "warehouse": None,
                        "time_zone": None,
                        "spoilage_qty": None,
                        "spoilage_cost": None,
                        "month_total_spoilage_qty": None,
                        "month_total_spoilage_cost": None,
                        "manager": None,
                        "director": None,
                        "exec": None
                    }
                }
            ]
        }]

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 库存调整
    def transfer_inv(self, item_number, warehouse_number, in_user, from_location, to_location):
        url = "/wms/general/backEndBatchConfirmMove"
        body = {
            "warehouseNumber": warehouse_number,
            "in_user": in_user,
            "comment": "自动化测试test------",
            "moveLocationInfoList": [
                {
                    "locationFrom": from_location,
                    "item_numbers": [item_number],
                    "locationTo": to_location
                }
            ]
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 库存调整
    def query_inventory_log(self, warehouse_number, start_time, end_time, item_number="", reference_id="", type_code_list=[]):
        url = "/wms/inventory/logs"
        body = {
            "end_time": end_time,
            "from_location": "",
            "item_number": item_number,
            "type_code_list": type_code_list,
            "order": {
                "orderColumn": "in_dtm",
                "orderRule": "desc"
            },
            "pageSize": 15,
            "reference_id": reference_id,
            "startColumn": 0,
            "start_time": start_time,
            "to_location": "",
            "user_id": "",
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
