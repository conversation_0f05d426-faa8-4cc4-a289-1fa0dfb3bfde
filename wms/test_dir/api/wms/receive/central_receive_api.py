# !/usr/bin/python3
# -*- coding: utf-8 -*-
from contextlib import nullcontext

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.login import Login
from wms.test_dir.api.wms.wms import header
from wms.test_dir.api.utils_url import UtilsUrl


class CentralReceiveAPI(weeeTest.TestCase):
    user = 'janine.cheng.1(7226349)'
    # 1、central receive
    # central端——检查po
    def central_query_po(self, warehouse_number, po_number):
        url = "/wms/receive/central/centralQueryPo"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number,
            "in_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, 'PO单无效，不能进行收货'
        return self.response["body"]

    # central端——获取PO单下的商品信息
    def receive_list(self, warehouse_number, po_number):
        url = "/wms/receive/central/centralQueryReceiveList"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '获取PO单下的商品信息失败'
        return self.response["body"]

    # central端——扫描upc/item number
    def receive_detail(self, warehouse_number, po_number, item_number_or_upc):
        url = "/wms/receive/central/receiveDetail"
        body = {
            "warehouse_number": warehouse_number,
            "po_number": po_number,
            "item_number_or_upc": item_number_or_upc
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, 'upc/item number不正确'
        return self.response["body"]

    # central端——检查完成后点击 create&print task 生成receive task
    def create_task(self, warehouse_number, po_number, item_number, pieces_per_pack, expire_date):
        url = "/wms/receive/central/centralReceiveCreateTask"
        if expire_date != "":
            qc_ExpiredDate = expire_date.replace("-","/") + " " + "00:00:00"
        else:
            qc_ExpiredDate = ""
        body = {
            "lp": "",
            "po_number": po_number,
            "item_number": item_number,
            "pieces_per_pack": pieces_per_pack,
            "weight_error": True,
            "image_error": False,
            "qc_ExpiredDate": qc_ExpiredDate,
            "ng_ExpiredDate": "",
            "rec_memo": "",
            "in_user": self.user,
            "warehouse_number": warehouse_number,
            "rec_Id": 0,
            "location_no": "",
            "images": [],
            "is_recount": 0,
            "temperature": "0.00F",
            "only_check_request": False,
            "input_dtm_str": expire_date,
            "input_dtm_type": 0
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '生成receive task失败'

    # 2、APP端receive

    # APP端——检查po信息
    def query_po(self, po_number, warehouse_number, status):
        url = "/wms/common/queryPO"
        body = {
            "po_number": po_number,
            "warehouse_number": warehouse_number,
            "status": status
        }
        self.post(url=url, headers=header, json=body)
        # assert self.response["success"] == True
        return self.response["body"]

    # APP端——获取PO单下的receive task 信息、扫描receive task
    def query_receive_task_list(self, reference_no, status, warehouse_number, upc_code=None, flag=True):
        print(f"==================={flag}=======================")
        url = "/wms/receive/central/queryReceiveTaskList"
        body = {
            "reference_no": reference_no,
            "status": status,
            "pageSize": 5,
            "startColumn": 0,
            "warehouse_number": warehouse_number
        }
        if flag:
            body["is_search_all"] = False
        else:
            body["is_search_all"] = True
            body["upc_code"] = upc_code
        print(f'**********{body}*********')
        self.post(url=url, headers=header, json=body)
        print(f"----------------{self.response['success']}---------------------")
        assert self.response["success"] == True, '获取PO单下的receive task 信息失败'
        return self.response["body"]

    #  APP端——进入收货详情页面

    def app_receive_detail(self, po_number, item_number):
        url = "/wms/receive/receiveDetail"
        body = {
            "po_number": po_number,
            "item_number": item_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True

    #  APP端——输入收货数据点击confirm提交收货
    def central_receive_confirm(self, warehouse_number,good_qty,reference_no,item_number,receive_task_no,only_check=True):
        url = "/wms/receive/central/centralReceiveConfirm"
        body = {
            "warehouse_number": warehouse_number,
            "good_cases": 0,
            "good_qty": good_qty,
            "reference_no": reference_no,
            "item_number": item_number,
            "receive_task_no": receive_task_no,
            "in_user": self.user,
            "images": [],
            "only_check": only_check,
            "module_name": 'receive'
        }
        # only_check == False 才是最终交互，only_check == True只是检查数据
        if only_check == False:
            body["copies"] = 2
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '点击confirm提交收货失败'


 #  APP端——错收冲减
    def delete_receive(self, warehouse_number, receive_task_no):
        url = "/wms/receive/central/resetTask"
        body = {
                  "warehouse_number": warehouse_number,
                  "receive_task_no": receive_task_no,
                  "in_user":self.user
                }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '错收冲减失败'

    # 创建PO单接口
    def create_purchase_order(self, vendor_id,warehouse_number,est_delivery_date, item):
        """
        创建PO单
        :param vendor_id: 供应商ID
        :param inbound_inventory_id: 入库库存ID
        :param third_delivery_inventroy_id: 第三方配送库存ID
        :param est_delivery_date: 预计交货日期 (格式: "2025-05-27")
        :param eta_connection_date: ETA连接日期 (格式: "2022-05-28")
        :param shipping: 运费
        :param container_offload_fee: 集装箱卸货费
        :param return_process_fee: 退货处理费
        :param purchase_org_code: 采购组织代码
        :param payment_mode: 付款方式 ("wire", "check", etc.)
        :param delivery_mode: 交货方式 ("delivery", "pickup", etc.)
        :param business_type: 业务类型 ("bulk_buy", "regular", etc.)
        :param creator_id: 创建者ID
        :param source: 来源 ("S", "M", etc.)
        :param internal_comment: 内部备注
        :param comment: 备注
        :param items: 商品列表 [{"product_id": 338, "purchase_quantity": 12, "purchase_price": 100.00}, ...]
        :return: 响应结果
        """

        product_items = [
            {
                "product_id": item,
                "purchase_quantity": 1,
                "purchase_price": 0.5
            }
        ]
        url = "/central/merch/v1/fns/ApiMerch/createPurchaseOrder"

        # 构建请求体
        body = {
            "vendor_id": vendor_id,
            "inbound_inventory_id": warehouse_number,
            "third_delivery_inventroy_id": 55,
            "est_delivery_date": est_delivery_date,
            "eta_connection_date": est_delivery_date,
            "shipping": 1,
            "container_offload_fee": 2.05,
            "return_process_fee": 3,
            "purchase_org_code": "",
            "payment_mode": "wire",
            "delivery_mode": "delivery",
            "business_type": "bulk_buy",
            "creator_id": 7226349,
            "source": "S",
            "internal_comment": "test internal_comment",
            "comment": "tt comment",
            "items": product_items
        }

        # 发送POST请求
        self.post(url=url, headers=header, json=body)
        assert self.response["result"] == True, '创建PO单失败'
        return self.response["object"]

    def confirm_po_order(self, po_data):
        """
        确认生效PO订单
        :param po_data: 入参
        :return:
        """
        erp_header=CentralReceiveAPI().erp_header()
        data = {
            "order_id": "",
            "confirm_inventory_volume": "0"
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_erpurl, url="/admin_gb_po/api_confirm_order", headers=erp_header, data=data)
        return self.response

    def erp_header(self, account=None, password='123456'):
        header = {}
        if account is None:
            if weeeTest.weeeConfig.base_url != 'https://api.sayweee.net':
                account = '********'
                login = Login().erp_login(account=account, password=password)
                header = {
                    'Cookie': f'keycloak_user_email=<EMAIL>; keycloak_user_id=********; keycloak_token={login[0]}',
                }
            else:
                account = '7226349'
                login = Login().erp_login(account=account, password=password)
                header = {
                    'Cookie': f'keycloak_user_email=<EMAIL>; keycloak_user_id=7226349; keycloak_token={login[0]}',
                }
        return header

    # 3、供应商退货相关接口

    def vendor_return_page(self, warehouse_number, status_list=None, start_column=0, page_size=15, order_column="createTime", order_rule="desc"):
        """
        供应商退货分页查询接口
        :param warehouse_number: 仓库编号 (如: "41")
        :param status_list: 状态列表 (如: [1,10,20,50,40,60,70], 1-待处理, 10-处理中, 20-已完成, 40-已取消, 50-已暂停, 60-已分配, 70-已关闭)
        :param start_column: 起始列 (分页用, 默认0)
        :param page_size: 每页大小 (默认15)
        :param order_column: 排序字段 (默认"createTime"创建时间)
        :param order_rule: 排序规则 (默认"desc"降序, "asc"升序)
        :return: 响应结果
        """
        if status_list is None:
            status_list = [1, 10, 20, 50, 40, 60, 70]

        url = "/wms/vendorReturn/page"
        body = {
            "warehouseNumber": warehouse_number,
            "statusList": status_list,
            "startColumn": start_column,
            "pageSize": page_size,
            "order": {
                "orderColumn": order_column,
                "orderRule": order_rule
            }
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '获取供应商退货分页数据失败'
        return self.response["body"]

    def vendor_return_preoccupy_start(self, warehouse_number, return_no, return_type):
        """
        开始预占用退货单接口
        :param warehouse_number: 仓库编号 (如: "41")
        :param return_no: 退货单号 (如: "100394")
        :param return_type: 退货类型 (如: 2)
        :return: 响应结果
        """
        url = "/wms/vendorReturn/preoccupy/start"
        body = {
            "warehouseNumber": warehouse_number,
            "returnNo": return_no,
            "type": return_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '开始预占用退货单失败'
        return self.response["body"]

    def vendor_return_preoccupy_result(self, warehouse_number, return_no, return_type):
        """
        查询预占用结果接口
        :param warehouse_number: 仓库编号 (如: "41")
        :param return_no: 退货单号 (如: "100394")
        :param return_type: 退货类型 (如: 2)
        :return: 响应结果
        """
        url = "/wms/vendorReturn/preoccupy/result"
        body = {
            "warehouseNumber": warehouse_number,
            "returnNo": return_no,
            "type": return_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, '查询预占用结果失败'
        return self.response["body"]

    def vendor_return_cancel(self, warehouse_number, return_no, return_type):
        """
        取消供应商退货单接口
        :param warehouse_number: 仓库编号 (如: "41")
        :param return_no: 退货单号 (如: "100394")
        :param return_type: 退货类型 (如: 2)
        :return: 响应结果
        """
        url = "/wms/vendorReturn/cancel"
        body = {
            "warehouseNumber": warehouse_number,
            "returnNo": return_no,
            "type": return_type
        }
        # 注意：这里使用DELETE方法
        self.delete(url=url, headers=header, json=body)
        assert self.response["success"] == True, '取消供应商退货单失败'
        return self.response["body"]

