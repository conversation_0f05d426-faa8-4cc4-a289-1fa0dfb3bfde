# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json

import weeeTest

from wms.test_dir.api.wms.wms import header


class RoReceiveAPI(weeeTest.TestCase):
    account = ''
    user = ''
    in_user = "jinyu.xu(9946725)"
    """
    Ro Receive功能相关接口
    """

    # 扫描pallet
    def ro_scan_pallet(self, warehouse_number, pallet_no):
        url = "/wms/ro/receive/scanPallet"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "pallet_no": pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 扫描RO单
    def query_ro(self, warehouse_number, reference_no, ):
        url = "/wms/ro/receive/queryRo"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "reference_no": reference_no,
            "operation_page": "10"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    #  获取RO单下的商品信息
    def query_ro_itemlist(self, reference_no, warehouse_number):
        url = "/wms/ro/receive/queryRoItemList"
        body = {
            "reference_no": reference_no,
            "operation": 1,
            "pageSize": 5,
            "startColumn": 0,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "is_search_all": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    #  扫描upc
    def query_ro_item_list(self, reference_no, warehouse_number, upc):
        url = "/wms/ro/receive/queryRoItemList"
        body = {
            "reference_no": reference_no,
            "operation": 1,
            "pageSize": 5,
            "startColumn": 0,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "upc": upc,
            "is_search_all": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    # 获取收货详情
    def query_ro_item_detail(self, reference_no, item_number, warehouse_number):
        url = "/wms/ro/receive/queryRoItemDetail"
        body = {
            "reference_no": reference_no,
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "in_user": self.in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    # 输入收货数据点击confirm提交收货
    def ro_receive_confirm(self, pallet_no, reference_no, item_number, expire_date, warehouse_number, pieces_per_pack):
        url = "/wms/ro/receive/roReceiveConfirm"
        if expire_date != "":
            qc_expire_date = expire_date.replace("-", "/") + " " + "00:00:00"
        else:
            qc_expire_date = ""
        body = {
            "pallet_no": pallet_no,
            "reference_no": reference_no,
            "item_number": item_number,
            "rec_qty": 1,
            "ng_qty": 0,
            "expire_date": qc_expire_date,
            "ng_expire_date": qc_expire_date,
            "input_dtm_str": expire_date,
            "input_dtm_type": 0,
            "comments": "",
            "images": [],
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "printer_no": "",
            "pieces_per_pack": pieces_per_pack
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    # 生成上架单
    def switch_pallet(self, warehouse_number, pallet_no):
        url = "/wms/ro/receive/switchPallet"
        body = {
            "warehouse_number": warehouse_number,
            "in_user": self.in_user,
            "pallet_no": pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response["success"] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
