# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
from wms.test_dir.api.wms.wms import *


class LabelReceiveAPI(weeeTest.TestCase):
    """
    Label Receive功能相关接口
    """

    def query_inbound_number(self, warehouse_number, reference_type, start_date, end_date, in_user, reference_no=None):
        """
        获取待收货订单
        reference_type：1：PO， 5: RO, 6：Internal order, 7:FBW Global, 14: FBW Local
        status: 1: waiting to receive, 2: partial received, 3: receive completed, 4: canceled
        """
        url = "/wms/inboundOrderManagement/queryInboundList"
        body = {
            "start_date_str": start_date,
            "end_date_str": end_date,
            "reference_type": reference_type,
            "status": 1,
            "in_user": in_user,
            "warehouse_number":  warehouse_number,
            "startColumn": 0,
            "pageSize": 100,
            "order": None
        }
        if reference_no:
            body["reference_no"] = reference_no
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    def query_po(self, po_number, warehouse_number, status, reference_type):
        url = "/wms/common/queryPO"
        body = {
                "po_number": po_number,
                "warehouse_number": warehouse_number,
                "status": status,
                "reference_type": reference_type
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def query_receive_list(self, po_number, status, warehouse_number,pageSize=0):
        """
        获取当前业务单下TODO List / Done List
        status (int): 1: Todo List, 2: Done L:ist
        """
        url = "/wms/receive/queryReceiveList"
        body ={
            "po_number": po_number,
            "status": status,
            "pageSize": pageSize,
            "startColumn": 0,
            "warehouse_number": warehouse_number,
            "is_search_all": True
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def label_receive_scan_lpn(self, warehouse_number, lpn_no, in_user,reference_no=None,item_number=None):
        """
        扫描LPN开始收货
        """
        url = "/wms/receive/scanLpnNo"
        body ={
            "lpn_no": lpn_no,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "reference_no":reference_no,
            "item_number":item_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def label_receive_detail(self, po_number, item_number, expire_dtm, receive_dtm, pieces_per_pack, pallet_no=None):
        """
        进入收货详情页
        """
        url = "/wms/receive/receiveDetail"
        body = {
                "po_number": po_number,
                "item_number": item_number,
                "expire_dtm": expire_dtm,
                "receive_dtm": receive_dtm,
                "pieces_per_pack": pieces_per_pack
            }
        # 扫LPN进入时，会有pallet_no， 长按SKU进入或填写的LPN QTY不等于当前LPN所属pallet_no上的LPN QTY，进入详情页时没有pallet_no
        if pallet_no:
            body["pallet_no"] = pallet_no
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def label_receive_confirm(self, warehouse_number, po_number, item_number, rec_qty, pieces_per_pack, qc_ExpiredDate, in_user, lpnInfoList):
        """
        收货确认
        "lpnInfoList": [{
                "lpn_no": "G2411-44218-M7-1",
                "original_quantity": 1
            }]
        """
        url = "/wms/receive/receiveConfirm"
        body = {
            "module_name": "label_receive",
            "po_number": po_number,
            "item_number": item_number,
            "rec_qty": rec_qty,
            "pieces_per_pack": pieces_per_pack,
            "rec_memo": "Auto test",
            "rec_Id": 0,
            "images": [],
            "qc_ExpiredDate": qc_ExpiredDate,
            "in_user": in_user,
            "warehouse_number": warehouse_number,
            "lpnInfoList": lpnInfoList
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def label_receive_print_label(self, warehouse_number, inbound_batch_id, in_user):
        """
        收货完成打印Label
        """
        url = "/wms/receive/printLpnLabel"
        body = {
            "rec_id": inbound_batch_id,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "copies": 2,
            "printer_no": 163
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def label_receive_delete_batch(self, warehouse_number, inbound_batch_id, po_number, in_user):
        """
        错收冲减
        """
        url = "/wms/receive/deleteBatch"
        body = {
            "rec_id": inbound_batch_id,
            "warehouse_number": warehouse_number,
            "reference_no": po_number,
            "in_user": in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]




