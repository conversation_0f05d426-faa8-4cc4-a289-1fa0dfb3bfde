import json
import weeeTest

from wms.test_dir.api.wms.wms import header


class Count(weeeTest.TestCase):
    # 领取Bin任务接口
    def count_get_bin_task(self, warehouse_number, edit_user, storage_type, location_type, status, module_name):
        url = "/wms/check/getList4Count"
        body = {
            "warehouse_number": warehouse_number,
            "edit_user": edit_user,
            "storage_type": storage_type,  # 1/2/3
            "location_type": location_type,  # stock3/bin4
            "status": status,  # 0：待一盘/1待二盘/2待三盘
            "module_name": module_name,  # stock_cycle_count/random_bin_cycle_count
            "flag": 1
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        assert self.response['body'] != [], f"{url}没领到盘点任务：{json.dumps(self.response)}"
        return self.response

    # 领取stock任务接口
    def count_get_stock_task(self, warehouse_number, edit_user, storage_type, location_type, status, module_name,
                             restock_type):
        url = "/wms/check/getList4Count"
        body = {
            "warehouse_number": warehouse_number,
            "edit_user": edit_user,
            "storage_type": storage_type,  # 1/2/3
            "location_type": location_type,  # stock3/bin4
            "status": status,
            "module_name": module_name,  # stock_cycle_count/bin_cycle_count
            "flag": 1,
            "restock_type": restock_type  # 1底层,2高层
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        assert self.response['body'] != [], f"{url}没领到盘点任务：{json.dumps(self.response)}"
        return self.response

    # 查询已领取任务接口
    def count_task_list(self, warehouse_number, edit_user, storage_type, location_type, status, module_name):
        url = "/wms/check/getList4Count"
        body = {
            "warehouse_number": warehouse_number,
            "edit_user": edit_user,
            "storage_type": storage_type,  # 1/2/3
            "location_type": location_type,  # stock3/bin4
            "status": status,  # 0：待一盘/1待二盘/2待三盘
            "module_name": module_name,  # stock_cycle_count/random_bin_cycle_count
            "flag": 0  # 0已领取，1领取
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 任务释放接口
    def count_release_task(self, edit_user):
        url = '/wms/check/releaseCountTask'
        body = {
            "editUser": edit_user
        }
        self.put(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # *****scan_location_cycle_count*****
    # 扫描location查询location状态
    def query_location_count_status(self, location_no, in_user, warehouse_number):
        url = '/wms/check/queryLocationCountStatus'
        body = {
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "type": 0
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response  # 根据返回的count_id,盘点是否有盘点任务

    # createCountTaskByLocation接口,没有盘点任务时调用该接口生成location级别任务
    def create_count_task_by_location(self, location_no, warehouse_number, in_user):
        url = '/wms/check/createCountTaskByLocation'
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "in_user": in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 进入详情页面goods接口
    def goods(self, location_no, warehouse_number, item_number, rec_id):
        url = '/wms/check/goods'
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_no": location_no,
            "rec_id": rec_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # upc数量confirm接口
    def confirm_num(self, location_no, warehouse_number, item_number, input_qty, in_user, status, module_name):
        """
		只盘数量
		:param location_no:
		:param warehouse_number:
		:param item_number:
		:param input_qty:
		:param in_user:
		:param status:
		:return:
		"""
        url = '/wms/check/confirm'
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "item_number": item_number,
            "inputQty": input_qty,
            "in_user": in_user,
            "count1Auth": True,
            "count2Auth": True,
            "count3Auth": True,
            "status": status,
            "module_name": module_name
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # empty接口
    def empty_scan_location(self, count_id, in_user, status, module_name):
        # 取queryLocationCountStatus返回的status
        if status == 1:
            in_user = "yu.yang.20(7644738)"  # 指定二盘in_user
        if status == 2:
            in_user = "jing.wang55(7396768)"  # 指定三盘in_user
        url = '/wms/check/empty'
        body = {
            "rec_id": count_id,
            "edit_user": in_user,
            "status": status,
            "module_name": module_name
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # *****stock/bin盘点接口*****
    # 扫描库位接口
    def scan_location(self, count_id, status, module_name):
        url = '/wms/check/scanLocation/' + str(count_id) + '/' + str(status) + '/' + str(module_name)
        # count_id取queryLocationCountStatus/createCountTaskByLocation接口返回的count_id
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # finish接口,bin/stock
    def count_finish(self, count_id, in_user, sku_list, module_name, status):
        """
		:param count_id: 盘点ID
		:param in_user: 盘点人
		:param sku_list: 盘点SKU个数
		:param module_name: 模块名称
		:param status: 盘点任务状态
		:return:
		"""
        url = '/wms/check/completeCount/' + str(sku_list) + '/' + str(module_name)
        body = {
            "rec_id": count_id,
            "in_user": in_user,
            "status": status
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # lpn_confirm接口：保质期在lpnList里面
    def lpn_confirm(self, warehouse_number, location_no, item_number, in_user, status, module_name,
                    lpn_list):
        url = '/wms/check/lpnConfirm'
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "item_number": item_number,
            "in_user": in_user,
            "count1Auth": True,
            "count2Auth": True,
            "count3Auth": True,
            "status": status,
            "module_name": module_name,
            "lpnList": lpn_list
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 盘点任务查询接口
    def query_countinfo_list(self, warehouse_number, location_no):
        url = '/wms/check/queryCountInfoList'
        body = {
            "warehouse_number": warehouse_number,
            "start_time": "",
            "end_time": "",
            "type": "",
            "status": "",
            "location_type": "",
            "flag": "",
            "item_number": "",
            "location_no": location_no,
            "order": {
                "orderColumn": "in_dtm",
                "orderRule": "desc"
            },
            "startColumn": 0,
            "pageSize": 15,
            "user_id": "",
            "aisles": [],
            "category_ids": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    # 盘点任务分配接口
    def assign_count_task(self, warehouse_number, count_id, user_id):
        url = '/wms/check/assignCountTask'
        body = {
            "warehouse_number": warehouse_number,
            "rec_id": count_id,
            "edit_user": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 库存审核接口
    def approve_inv(self):
        url = '/wms/check/queryDailyReportList'
        body = {
            "warehouse_number": "29",
            "start_time": "",
            "end_time": "",
            "item_number": "40972",
            "location_no": "B0105-2-3",
            "startColumn": 0,
            "pageSize": 25,
            "status_list": [
                20,
                21
            ],
            "action_types": [
                "1"
            ],
            "aisles": [

            ]
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True
        rest = self.response
        # 根据查询结果判断是否审核
        assert rest['body']['data'] != []
        item_number = rest['body']['data'][0]['item_number']
        url = '/wms/check/batchApproveCheck'
        body = [
            {
                "item_number": item_number,
                "type": 1,
                "in_user": "yu.yang.20(7644738)",
                "warehouse_number": "29",
                "dailyReportItem": [
                    {
                        "rec_id": 76785,
                        "info_id": 1228833,
                        "detail_id": 1516261,
                        "action_type": 1,
                        "issue_type": None,
                        "adjustment_analysis": None
                    }
                ]
            }
        ]
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
