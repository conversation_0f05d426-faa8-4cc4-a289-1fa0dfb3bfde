# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
from wms.test_dir.api.wms.wms import *


class RouteCheckAPI(weeeTest.TestCase):
    user = ''

    def get_route_package(self, warehouse_number, delivery_dtm, route_id, storage_type):
        url = "/wms/route/package"
        body = {
            "warehouse_number": warehouse_number,
            "delivery_dtm": delivery_dtm,
            "route_id": route_id,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    def package_confirm(self, warehouse_number, delivery_dtm, route_id, tracking_num, storage_type):
        url = "/wms/route/confirm"
        body = {
            "order_id": "",
            "box_index": "",
            "tracking_num": tracking_num,
            "route_id": route_id,
            "edit_user": self.user,
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "delivery_dtm": delivery_dtm
        }
        self.post(url=url, headers=header, json=body)
        if self.response["body"] != "Repeat scan" and self.response["body"] != "The order was canceled by user!":
            assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def supervisor_cancel(self, warehouse_number, delivery_dtm, route_id, tracking_num, storage_type, edit_user):
        url = "/wms/cancel/order/scan/supervisorPassword"
        body = {
            "order_id": "",
            "box_index": "",
            "tracking_num": tracking_num,
            "route_id": route_id,
            "edit_user": edit_user,
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "delivery_dtm": delivery_dtm
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def query_route_check_details(self, warehouse_number, delivery_dtm, route_id, delivery_id, order_type):
        """
        获取订单RouteCheck信息
        status:路线维度,是否完成Route Check, 0:unfinish,1:finish
        check_status: 订单维度, 0: unchecked, 1:cancel, 2:blocked, 3:checked
        """
        url = "/wms/dashboard/routeCheck/orderDetailPages"
        body = {
            "check_status": 0,
            "status": 0,
            "order_type": order_type,
            "route_id": route_id,
            "warehouse_number": warehouse_number,
            "delivery_date_str": delivery_dtm,
            "delivery_id": delivery_id,
            "pageSize": 100,
            "startColumn": 0,
            "order": None
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]