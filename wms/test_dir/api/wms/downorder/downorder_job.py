# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  downOrderJOB.py
@Description    :
@CreateTime     :  2023/11/26 20:32
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/26 20:32
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class DownOrderJOB(weeeTest.TestCase):
    """
    FPO JOB下发订单到WMS相关接口
    """

    def put_order_from_so(self, so_order_id):
        """
        so同步订单到fpo_order
        """
        url = "/central/so/order/downByOrderId"
        body = [{
            "order_id": so_order_id,
            "type": "down_order_queue_ack"}]
        self.put(url=url, json=body, headers= header)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def JobDownOrder(self,last_push_time):
        """
        根据last_push_time JOB下发订单，生成后获取发货批次信息 (生产环境禁止调用)
        """
        url='/wms-order/job/fpo/autoGenerateAndSend?time=' + str(last_push_time)
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def cutoff_config(self,delivery_date,region_id,warehouse_no):
        """
        :param delivery_date:
        :param region_id:
        :param warehouse_no:
        :return:
        """
        url='/wms-order/fpo/order/cutoff'
        body = {
            "delivery_dtm": delivery_date,
            "region_id": region_id,
            "warehouse_num": warehouse_no
        }
        self.post(url=url, json=body, headers=header)
        #assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def checkInvoice(self,delivery_id):
        url=f'/wms-order/job/checkInvoice?deliveryIds={delivery_id}'
        self.post(url=url,headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_performance_date(self,zipcode):
        """
        根据zipcode获取履约日期
        """
        url = f'/wms-order/fpo/zipcode/fulfill/dates?zipcode={zipcode}'
        self.get(url=url, headers=header)
        if isinstance(self.response, dict):
            assert self.response.get('success') is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        else:
            print(f"警告：接口返回的响应格式不符合预期。响应内容：{self.response}")
            raise ValueError(f"接口返回的响应格式不符合预期：{self.response}")
        return self.response

    def editDeliveryConfig(self,config_id, cutoff_time):
        url = '/wms-order/fcs/editDeliveryConfig'
        body = {
                    "capacity": 600,
                    "orderCutoffTime": cutoff_time,
                    "firstPushTime": "",
                    "lastPushTime": cutoff_time,
                    "id": config_id
                }
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_date_config(self,params):
        url = f'/wms-order/fcs/getConfig/page?{params}'
        self.get(url=url, headers=header)
        #assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

if __name__ == '__main__':
    downorder = DownOrderJOB()
    res = downorder.get_date_config(params='start_date_str=2025-06-29&end_date_str=2025-08-24&region_type=0&region_id=20&pageSize=15&startColumn=0')
    print(res)

