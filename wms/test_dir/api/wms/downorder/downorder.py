import time
import json

import weeeTest
from weeeTest import log

from wms.test_dir.api.wms.wms import header


class WmsDownOrder(weeeTest.TestCase):
    """
    downorder 相关接口
    """

    def query_fpo_config(self, delivery_date, region_type, region_id):
        """
        获取FPO config相关信息
        """
        url = f'/wms-order/fcs/getConfig/page?start_date_str={delivery_date}&end_date_str={delivery_date}&region_type={region_type}&region_id={region_id}&pageSize=15&startColumn=0'
        self.get(url=url, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    def pull_order_from_so(self):
        """
          从SO拉取订单到WMS
        """
        url='/wms-order/fpo/order/pullOrderFromSO'
        self.get(url=url, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        time.sleep(5)
        return self.response

    def fpo_region_by_warehouse(self, warehouse_number, delivery_date):
        """
          获取region 和配送日信息
        """
        url='/wms-order/fpo/region/byWarehouse/%s/%s' % (warehouse_number, delivery_date)
        self.get(url=url, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]

    def invoice_generate(self, delivery_date, region_ids, warehouse_number, user_id):
        """
        生成发货批次
        """
        url='/wms-order/fpo/invoice/generate'
        body = {
            "delivery_date": delivery_date,
            "region_ids": region_ids,
            "warehouse_number": warehouse_number,
            "user_id": user_id
        }
        self.post(url=url, json=body, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        log.info(self.response)
        return self.response

    def list_delivery(self, region_id_list, delivery_start_date, delivery_end_date):
        """
        central 发货批次列表
        """
        url='/wms-order/fpo/delivery/listDelivery'
        body = {
            "regionIdList": region_id_list,
            "deliveryStartDate": delivery_start_date,
            "deliveryEndDate": delivery_end_date,
            "startColumn": 0,
            "pageSize": 10,
            "status": "A"
        }
        self.post(url=url, json=body, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    def fpo_order_page(self, order_id, delivery_date=''):
        """
         central fpo order management page
        """
        url=f'/wms-order/fpo/order/page?delivery_date={delivery_date}&order_id={order_id}&startColumn=0&pageSize=10'
        self.get(url=url, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    def fpo_invoice_page(self, delivery_id="", start_date="", end_date=""):
        """
        central fpo invoice management page
        """
        url=f'/wms-order/fpo/invoice/page?delivery_id={delivery_id}&start_date={start_date}&end_date={end_date}&startColumn=0&pageSize=100'
        self.get(url=url, headers=header)
        assert self.response["success"] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response["body"]["data"]

    def bulk_sku_tag(self,warehouse_number,item_number,user_id,attributeCode,attributeView,updateValue):
        """
        bulk商品标记
        """
        body = {
                "warehouse_number": warehouse_number,
                "item_numbers": [item_number],
                "details": [{
                    "attributeCode": attributeCode,
                    "attributeView": attributeView,
                    "updateValue": updateValue
                }],
                "edit_user": user_id,
                "secondConfirm": False
            }
        url = '/wms/items/management/updateItemsAttributes'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def set_redis(self,warehouse_number,region,delivery_dtm):
        body = {
            "key": "fpo:readyForPicking:{}:{}:{}".format(warehouse_number, region, delivery_dtm),
            "value": "1"
        }
        url = '/wms/redis/set'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_redis(self,warehouse_number,region,delivery_dtm):
        url = f'/wms/redis/getValue/fpo:readyForPicking:{warehouse_number}:{region}:{delivery_dtm}'
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def delete_redis(self,warehouse_number,region,delivery_dtm):
        url = f'/wms/redis/fpo:readyForPicking:{warehouse_number}:{region}:{delivery_dtm}'
        self.get(url=url, headers=header)
        return self.response

    def ready_for_picking(self, warehouse_number, delivery_date, region_name):
        """
        Ready for picking
        :param delivery_data: "2024-01-06"
        :param region_name: "BA-R2"
        :param warehouse_num: "25"
        :return:
        """
        url = "/wms/outbound/orders/readyForPicking"
        body = {
            "delivery_dtm": delivery_date,
            "region_name": region_name,
            "warehouse_num": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def update_order_route_info(self, deliveryIdList, warehouse_number, invoiceId, orderPackingNum, startLocationId, wsOrderId):
        """
        模拟TMS排车,通知WMS更新Route
        """
        body = {
            "deliveryIdList": deliveryIdList,
            "fpoTmsDeliveryDtoList": [{
                "exsd_infos": [{
                    "warehouse_number": warehouse_number
                }],
                "flexFlag": 1,
                "groupPointId": 506,
                "invoiceId": invoiceId,
                "orderPackingNum": orderPackingNum,
                "pointCode": "[13]M420789",
                "seq": 1,
                "startLocationId": startLocationId,
                "wave_num": 10,
                "wsOrderId": wsOrderId
            }]
        }
        url = '/wms/tmsDelivery/sendWmsDeliveryPlan'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_invoice_order(self,invoice_id):
        """
        根据invoice_id获取invoice_id对应的wms order
        """
        url = f"/wms-order/fpo/invoice/detail/wmsInfo/{invoice_id}"
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["orderTypeInfoList"]

    def query_fulfill_date(self, zipcode):
        """
        根据zipcode查询可以履约的日期
        """
        url = f"/wms-order/fpo/zipcode/fulfill/dates?zipcode={zipcode}"
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["delivery_dates"]



if __name__ == '__main__':
    WmsDownOrder()
