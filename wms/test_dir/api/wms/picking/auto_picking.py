# !/usr/bin/python3
# -*- coding: utf-8 -*-


import json
import weeeTest
from weeeTest import log
from wms.test_dir.api.wms.wms import header


class AutoPickingApi(weeeTest.TestCase):
    def autoStoreApi(self,data):
        url = "/wms/v1/AutoStoreApi/HostReceive"
        self.post(url=url, headers=header, json=data)
        #assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def geekPlusApi(self, data):
        url = "/wms/geekplus/callback"
        self.post(url=url, headers=header, json=data)
        assert self.response['body']['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def autoStoreJob(self):
        url = "/wms/job/automation/mergePick"
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def geekPlusJob(self):
        url = "/wms/job/automation/geekplus/orderSync"
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 创建拣货任务
    def create_picking_task(self, cart_no: str, warehouse_number: str, storage_type):
        url = "/wms/orderPick/createPickingTask"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "picking_channel": 10
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    def bind_tote_number(self, picking_task_id, tote_no, order_id, shipping_type,picking_order_rec_id, warehouse_number):
        """
        绑定拣货框
        :param picking_task_id:
        :param tote_no:
        :param order_id:
        :param shipping_type:
        :param warehouse_number:
        :return:
        """
        body = {
                "picking_task_id": picking_task_id,
                "tote_no": tote_no,
                "order_id": order_id,
                "shipping_type": shipping_type,
                "picking_order_rec_id": picking_order_rec_id,
                "warehouse_number": warehouse_number,
                "picking_channel": 10
            }
        url = "/wms/orderPick/bindToteNumber"
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def updateDestinationStatus(self, warehouseNumber,moduleName,destinationId,destinationStatus):
        body = {
                "warehouseNumber": warehouseNumber,
                "moduleName": moduleName,
                "kpiUpdateRequestParam": {
                    "destinationId": destinationId,
                    "destinationStatus": destinationStatus
                }
            }
        url = "/wms/automation/v1/updateDestinationStatus"
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def adjustLocation(self, warehouse_number,tote_no,chute_id,order_id,picking_type,picking_task_id,long_press,scanLocationDtm,scanUpcDtm,scanToteDtm,picking_channel):
        body = {
                "warehouse_number": warehouse_number,
                "tote_no": tote_no,
                "location_no": chute_id,
                "order_id": order_id,
                "picking_type": picking_type,
                "picking_task_id": picking_task_id,
                "long_press": long_press,
                "scanLocationDtm": scanLocationDtm,
                "scanUpcDtm": scanUpcDtm,
                "scanToteDtm": scanToteDtm,
                "picking_channel": picking_channel
            }
        url = "/wms/orderPick/adjustLocation"
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


    def pickingTaskFinish(self, picking_task_id,picking_type,packing_line,warehouse_number,picking_channel):
        body = {
                "picking_task_id": picking_task_id,
                "picking_type": picking_type,
                "packing_line": packing_line,
                "warehouse_number": warehouse_number,
                "picking_channel": picking_channel
            }
        url = "/wms/orderPick/pickingTaskFinish"
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response



