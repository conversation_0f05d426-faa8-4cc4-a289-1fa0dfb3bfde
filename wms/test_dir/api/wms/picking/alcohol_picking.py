# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""

"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class AlcoholPicking(weeeTest.TestCase):

    # 创建拣货任务
    def create_picking_task(self, cart_no: str, warehouse_number: str):
        url = "/wms/orderPick/createPickingTask"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": "1",
            "picking_channel": 2
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 库存调整
    def adjust_location(self, warehouse_number, tote_no, location_no, item_number, order_id, picking_quantity,
                        picking_type, picking_task_id):
        url = "/wms/orderPick/adjustLocation"
        body = {
            "warehouse_number": warehouse_number,
            "tote_no": tote_no,
            "location_no": location_no,
            "item_number": item_number,
            "order_id": order_id,
            "picking_quantity": picking_quantity,
            "picking_type": picking_type,
            "picking_task_id": picking_task_id,
            "long_press": 1,
            "scanLocationDtm": 1661243552,
            "scanUpcDtm": 1661243556

        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def picking_task_finish(self, picking_task_id, picking_type, packing_line, warehouse_number, picking_task_status):
        url = "/wms/orderPick/pickingTaskFinish"
        body = {
            "picking_task_id": picking_task_id,
            "picking_type": picking_type,
            "packing_line": packing_line,
            "picking_task_status": picking_task_status,
            "warehouse_number": warehouse_number
        }
        self.post(url = url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def putwall_scan_cart(self, cart_no, warehouse_number):
        url = "/wms/putwall/cart"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 绑定拣货框
    def bind_tote_number(self, box_size, tote_no, order_id, warehouse_number):
        url = "/wms/putwall/order/tote"
        body = {
            "box_size": box_size,
            "tote_no": tote_no,
            "order_id": order_id,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # PICK ITEMS
    def pick_items(self, picking_task_id, warehouse_number):
        url = "/wms/putwall/item"
        body = {
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # SORTING ITEMS
    def sorting_items(self, item_number, order_id, picking_task_id, sorting_quantity):
        url = "/wms/putwall/item"
        body = {
            "item_number": item_number,
            "order_id": order_id,
            "picking_task_id": picking_task_id,
            "sorting_quantity": sorting_quantity,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 再次调用/putwall/cart，返回packing line，结束任务
    def sorting(self, item_number, order_id, picking_task_id, sorting_quantity, action=1):
        url = "/wms/putwall/sorting"
        body = {
            "action": action,
            "item_number": item_number,
            "picking_task_id": picking_task_id,
            "order_id": order_id,
            "sorting_quantity": sorting_quantity,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def task_finish(self, cart_no, packing_line, picking_task_id, warehouse_number):
        url = "/wms/putwall/task"
        body = {
            "cart_no": cart_no,
            "packing_line": packing_line,
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse_number,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
