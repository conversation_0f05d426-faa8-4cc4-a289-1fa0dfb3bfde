# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :
@CreateTime     :  2023/5/15 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/15 11:06
"""
import json
import weeeTest

from wms.test_dir.api.wms.wms import header


class MofPicking(weeeTest.TestCase):

    # 进入mail order模块
    def enter_module(self, warehouse_number: str):
        url = "/wms/common/record/module/log"
        body = {
            "warehouse_number": warehouse_number,
            "module_name": "mail_order_mix",
            "action": 1
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 创建拣货任务
    def create_picking_task(self, cart_no: str, warehouse_number: str):
        url = "/wms/orderPick/createPickingTask"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": "6",
            "picking_channel": 1
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 绑定拣货框
    def bind_tote_number(self, picking_task_id, tote_no, order_id, shipping_type, warehouse_number):
        url = "/wms/orderPick/bindToteNumber"
        body = {
            "picking_task_id": picking_task_id,
            "tote_no": tote_no,
            "order_id": order_id,
            "shipping_type": shipping_type,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 库存调整
    def adjust_location(self, warehouse_number, tote_no, location_no, item_number, order_id, picking_quantity,
                        picking_type, picking_task_id):
        url = "/wms/orderPick/adjustLocation"
        body = {
            "warehouse_number": warehouse_number,
            "tote_no": tote_no,
            "location_no": location_no,
            "item_number": item_number,
            "order_id": order_id,
            "picking_quantity": picking_quantity,
            "picking_type": picking_type,
            "picking_task_id": picking_task_id,
            "long_press": 1,
            "scanLocationDtm": **********,
            "scanUpcDtm": **********,
            "scanToteDtm": **********

        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 生鲜拣货任务完成，扫描打包线
    def scan_picking_region(self, picking_region_no, picking_task_id, warehouse_number):
        url = "/wms/orderPick/scanPickingRegion"
        body = {
            "picking_region_no": picking_region_no,
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    def picking_task_finish(self, picking_task_id, picking_type, packing_line, warehouse_number):
        url = "/wms/orderPick/pickingTaskFinish"
        # {"picking_task_id":1290497,"picking_type":1,"packing_line":1,"warehouse_number":"35"}
        body = {
            "picking_task_id": picking_task_id,
            "picking_type": picking_type,
            "packing_line": packing_line,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        return self.response
