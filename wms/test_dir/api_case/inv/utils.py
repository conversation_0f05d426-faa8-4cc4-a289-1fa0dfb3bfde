import json
import math

from jsonpath import jsonpath
from time import sleep
from weeeTest import jmespath
from wms.test_dir.api.inv.ec_inventory_api import EcInventory
from wms.test_dir.api.inv.central_inventory_api import CentralInventory
from wms.test_dir.api.wms.wms import wms


class InvDataUtils(object):
    # ec_inv接口......
    def assert_create_order_v5(self, date, product_id, product_type, sales_org_id, zipcode, inventory_mode="available"):
        # 查询zipcode履约日期
        performance_date_res = wms.down_order_job.get_performance_date(zipcode)
        performance_date = jmespath(performance_date_res, "body.delivery_dates")
        # 自营和MOF：delivery_dates、MO：查shipping_dates
        assert any(item["delivery_date"] == date for item in performance_date), f'zipcode:{zipcode}在delivery_date: {date} 下履约信息不存在'
        # 查询下单日期的可售库存
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id, deliveryQty=True)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv[0] > 2, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}在date:{date}可售库存为{product_inv}"
        # 调用下单接口
        res = EcInventory().create_order(date, product_id, product_type, sales_org_id, zipcode, inventory_mode)
        failed_products = jmespath(res, "object.failedProducts")
        order_id = jmespath(res, "object.successProducts[0].message_id")
        assert failed_products is None, f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "P":
                    print(f"订单明细{order_id}写入DB成功，状态为P")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_cancel_order_v5(self, date, order_id, product_id, product_type, sales_org_id, zipcode, inventory_mode="available"):
        # 取消订单
        # 调用取消订单接口
        res1 = EcInventory().cancel_order_v5(date, order_id, product_id, product_type, sales_org_id, zipcode, inventory_mode)

        # 提取失败产品列表
        failed_products = jmespath(res1, "object.failedProducts")
        if failed_products is not None:
            raise ValueError(f"取消订单失败: {failed_products}")

        # 提取成功产品的 message_id
        success_products = jmespath(res1, "object.successProducts")
        if not success_products or "message_id" not in success_products[0]:
            raise KeyError("取消订单成功但未返回 message_id")
        order_id = success_products[0]["message_id"]

        # 动态轮询检查订单状态，取消订单MQ消息有延迟
        max_retries = 10
        retry_interval = 6  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if not order_detail:
                raise ValueError(f"查询订单{order_id}明细结果为空")

            status = order_detail[0].get("status")
            if status == "X":
                print("订单状态已更新为X")
                return

            print(f"订单状态尚未更新为X，当前状态: {status}")
            sleep(retry_interval)
        raise TimeoutError("订单状态更新超时，未变为X")

    def assert_inventory_query_v5(self, date, zipcode, product_id, sales_warehouse_number, inv_qty, sales_model="inventory"):
        # inventory query v5接口
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        assert jsonpath(v5_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v5_res, "$.object[0].warehouse_number") == [sales_warehouse_number], f'期望售卖仓库为{sales_warehouse_number}, 实际为{v5_res}'
        assert jsonpath(v5_res, "$.object[0].qty") == [inv_qty], f'期望售卖数量为{inv_qty}, 实际为{v5_res}'
        if inv_qty > 0:
            assert jsonpath(v5_res, "$.object[0].is_sold_out") == [False], f'售卖数量为{inv_qty}, 但是售罄字段不是False'

    def assert_inventory_query_v4(self, date, sales_org_id, product_id, sales_warehouse_number, inv_qty, sales_model="inventory"):
        # inventory query v4接口
        v4_res = wms.ec_inv_api.ec_inventory_query_v4(date, sales_org_id, product_id)
        assert jsonpath(v4_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v4_res, "$.object[0].warehouse_number") == [sales_warehouse_number], f'期望售卖仓库为{sales_warehouse_number}, 实际为{v4_res}'
        assert jsonpath(v4_res, "$.object[0].qty") == [inv_qty], f'期望售卖数量为{inv_qty}, 实际为{v4_res}'
        if inv_qty > 0:
            assert jsonpath(v4_res, "$.object[0].is_sold_out") == [False], f'售卖数量为{inv_qty}, 但是售罄字段不是False'

    def assert_product_all_region_inv(self, date, product_id, region_id, product_inv):
        # query_product_region接口
        query_product_region_res = wms.ec_inv_api.query_product_region(product_ids=[product_id])
        region_inv_res = jsonpath(query_product_region_res, f'$.object[?(@.region_id=="{region_id}")].inventory_infos[?('
                                                            f'@.date=="{date}")].qty')
        assert region_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{region_inv_res}'

    def assert_ec_inventory_query_product_inv(self, date, sales_org_id, product_id, product_inv, zipcode, warehouse_number):
        # ec_inventory_query_product_inv接口
        res = wms.ec_inv_api.ec_inventory_query_product_inv(sales_org_id, product_id, zipcode)
        query_product_inv_res = jsonpath(res, f'$.object[?(@.date=="{date}")].qty')
        assert query_product_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{query_product_inv_res}'

    def assert_ec_inventory_fulfillment_query(self, date, sales_org_id, product_id, zipcode, product_inv):
        # ec_inventory_fulfillment_query接口
        res = wms.ec_inv_api.ec_inventory_fulfillment_query(date, zipcode, product_id, sales_org_id)
        fulfillment_query_inv_res = jsonpath(res, f'$.object[0].qty')
        delivery_windows = jsonpath(res, f'$.object[0].delivery_windows')
        assert fulfillment_query_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{fulfillment_query_inv_res}'
        assert delivery_windows, f'期望有delivery_windows, 实际为{delivery_windows}'

    def assert_query_zipcode_region(self, zipcode, region_id):
        # query_zipcode_region接口
        region_res = wms.ec_inv_api.query_zipcode_region([zipcode])
        region_ids_res = jsonpath(region_res, f'$.object[0].region_ids[*]')
        assert region_ids_res, f"zipcode={zipcode}绑定的Region为空,请检查!"
        assert region_id in region_ids_res, f'region_id：{region_id}, 不在{region_ids_res}里面'

    def assert_query_seller_region(self, seller_id, biz_type, region_id):
        # query_seller_region接口
        region_res = wms.ec_inv_api.query_seller_region(seller_id, biz_type)
        region_ids_res = jsonpath(region_res, f'$.object[*]')
        assert region_ids_res, f"seller_id={seller_id}绑定的Region为空,请检查!"
        assert region_id in region_ids_res, f'region_id：{region_id}, 不在{region_ids_res}里面'

    # central_inv接口......
    def assert_inventory_daily_sales(self, sales_org_id, date, zipcode, product_id):
        # 查询每日可售库存接口
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}无可售库存"
        return product_inv[0]

    def assert_redis_inv(self, product_id, warehouse_number, product_inv):
        # Redis库存查询
        if product_inv > 0:
            inv_res = wms.central_inv_api.redis_detail_query(product_id)
            productInvs = jsonpath(inv_res, '$.object.productInvs')[0]
            for inv in productInvs:
                if f"inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}" in inv:
                    value = inv.split("->")[1]
                    print(int(value))  # 打印Redis库存
                    assert int(value) == product_inv, f"Redis库存查询结果与预期不符, 期望为{product_inv}, 实际为{value}"
                    return

    def assert_local_inv(self, product_id, date, warehouse_number, product_inv, sales_org_id=None, is_pantry=None):
        # local库存查询校验
        if product_inv > 0:
            tpa_res = wms.central_inv_api.query_product_local_inv(productIds=[product_id], dates=[date])
            qty = jsonpath(tpa_res,
                           f'$...inventory_list[?(@.warehouse_number == "{warehouse_number}")].available_qty')
            # 校验直邮商品在本地org=10下面的库存为0,该接口不查直邮
            if is_pantry:
                inventory_list = jsonpath(tpa_res, '$.object.data[0].inventory_list[*]')
                for item in inventory_list:
                    if item['sales_org_ids'] and {sales_org_id} in item['sales_org_ids']:
                        pantry_qty = item['available_qty']
                        assert pantry_qty[0] == 0, f"本地库存查询结果与预期不符, 期望为0, 实际为{qty}"
                        break
            else:
                # 非pantry商品校验
                assert max(qty[0], 0) == product_inv, f"本地库存查询结果与预期不符, 期望为{product_inv}, 实际为{qty}"

    def assert_sales_inv(self, sales_org_id, date, product_id, product_inv, is_pantry=None):
        # local库存查询校验
        tpa_res1 = wms.central_inv_api.query_product_sales_inv(sales_org_id, date, product_ids=[product_id])
        qty1 = jsonpath(tpa_res1, "$.object[0].qty")
        # 校验直邮商品在本地org=10下面的库存为0
        if is_pantry:
            assert qty1 == [0], f"本地库存查询结果与预期不符, 期望为0, 实际为{qty1}"
        else:
            # 非pantry商品校验
            assert qty1 == [product_inv], f"本地库存查询结果与预期不符, 期望为{product_inv}, 实际为{qty1}"

    def assert_all_date_v4(self, sales_org_id, date, zipcode, product_id, product_inv):
        """
        /central/inventory/query/all/date/v4接口查询库存
        """
        # 查询仓库库存
        tpa_res4 = wms.central_inv_api.query_all_date_inv_v4(product_id)
        qty4 = jsonpath(tpa_res4, f'$.object.[?(@.sales_org_id == {sales_org_id})].date_sold_status[?(@.date=="{date}")].remaining_count')
        assert qty4 == [
            product_inv], f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}可售库存与V5接口返回不一致,V5:{product_inv}, 实际:{qty4}"

    def assert_org_inv(self, sales_org_id, sales_model, product_id, product_inv, is_pantry=None):
        """
        /query/sales_org_id/inv接口查询库存
        """
        # 查询仓库库存
        if product_inv > 0:
            tpa_res = wms.central_inv_api.query_org_inv(sales_model, sales_org_id)
            product_ids = jsonpath(tpa_res, f'$.object[*]')
            # 校验org=10查不到直邮商品ID
            if is_pantry:
                assert product_id not in product_ids, f"销售组织:{sales_org_id}下, product_id:{product_id}存在"
            else:
                # 非pantry商品校验
                assert product_id in product_ids, f"销售组织:{sales_org_id}下, product_id:{product_id}不存在"

    def assert_all_region_inv(self, sales_org_id, region_id, product_ids, date, product_inv):
        """
        /central/inventory/query/all/region/inv接口查询库存
        """
        # 查询仓库库存
        tpa_res = wms.central_inv_api.query_all_region_inv(product_ids)
        if region_id == "0":
            all_region_inv_res = jsonpath(tpa_res, f'$.object[?(@.sales_org_id==None && @.region_id=="{region_id}")].inventory_infos[?('
                                                   f'@.date=="{date}")].qty')
            assert all_region_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{all_region_inv_res}'
        else:
            all_region_inv_res = jsonpath(tpa_res, f'$.object[?(@.sales_org_id=={sales_org_id} && @.region_id=="{region_id}")].inventory_infos[?('
                                                   f'@.date=="{date}")].qty')
            assert all_region_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{all_region_inv_res}'

    def assert_warehouse_inv(self, warehouse_number, sales_model, product_id, product_inv):
        """
        /query/inventory/product/inv接口查询库存
        """
        # 查询仓库库存
        if product_inv > 0:
            tpa_res = wms.central_inv_api.query_warehouse_inv(warehouse_number, sales_model, product_id)
            warehouse_inv_res = jsonpath(tpa_res, f'$.object[0].available_qty')
            assert warehouse_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{warehouse_inv_res}'

    def assert_local_warehouse_inv(self, productIds, warehouseNumber, salesOrgId, product_inv, is_pantry=None):
        """
        local/warehouse/inv接口查询库存
        """
        # 查询仓库库存
        if product_inv > 0:
            tpa_res = wms.central_inv_api.query_local_warehouse_inv(productIds, warehouseNumber, salesOrgId)
            inv_res = jsonpath(tpa_res, f'$.object.data')  # 如果仓库没库存模型date返回的[]
            warehouse_inv_res = jsonpath(tpa_res, f'$.object.data[0].available_qty')
            # 校验直邮商品在本地org=10下面的库存为0
            if is_pantry:
                assert warehouse_inv_res == [0] or inv_res[0] == [], f'期望售卖数量为0, 实际为{inv_res[0]}'
            else:
                # 非pantry商品正常校验本地ORG的库存
                assert warehouse_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{warehouse_inv_res}'

    def assert_query_seller_inv(self, product_id, seller_id, product_inv):
        """
        /central/inventory/query/seller/product/inv接口查询库存
        """
        # 查询仓库库存
        tpa_res = wms.central_inv_api.query_seller_inv(product_id, seller_id)
        seller_inv_res = jsonpath(tpa_res, f'$.object[0].available_qty')
        assert seller_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{seller_inv_res}'

    def assert_query_seller_warehouse_product(self, seller_id, warehouse_number, product_id):
        """
        /central/inventory/query/seller/warehouse/productIds接口查询库存
        """
        # 查询仓库库存
        tpa_res = wms.central_inv_api.query_seller_warehouse_product(seller_id, warehouse_number)
        product_ids_res = jsonpath(tpa_res, f'$.object[?(@.seller_id=={seller_id})].sku_count_list[?('
                                            f'@.warehouse_number=="{warehouse_number}")].product_ids[*]')
        assert product_id in product_ids_res, f'查询的SKU：{product_id}不在{product_ids_res}里面'

    def assert_query_global_fbw_inv_detail(self, warehouse_number, product_id, product_inv):
        """
        /central/inventory/query/product/inventory/detail接口查询库存
        """
        # 查询仓库库存
        if product_inv > 0:
            tpa_res = wms.central_inv_api.query_global_fbw_inv_detail(warehouse_number, product_id)
            inv_detail_res = jsonpath(tpa_res, f'$.object.data[?(@.product_id=={product_id})].data[?('
                                               f'@.warehouse_number=="{warehouse_number}")].available_qty')
            assert inv_detail_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{inv_detail_res}'

    def assert_query_fbw_inv_summary(self, product_id, seller_id, region_id, date, warehouse_number):
        """
        /central/inventory/inventory_summary接口查询库存
        """
        # 查询仓库库存
        tpa_res = wms.central_inv_api.query_fbw_inv_summary(seller_id, region_id, date)
        sold_count_res = jsonpath(tpa_res, f'$.object.products[?(@.product_id=={product_id})].inventory_infos[?('
                                           f'@.date=="{date}")].sold_count')
        # Redis查询sold_count
        inv_res = wms.central_inv_api.redis_detail_query(product_id)
        productInvs = jsonpath(inv_res, '$.object.productInvs')[0]
        for inv in productInvs:
            if f"inv:inventory:presaleSellable:sku_{product_id}_wh_{warehouse_number}_date_{date}" in inv:
                available_qty = inv.split("->")[1]
                print(int(available_qty))  # 打印Redis.presaleSellable库存
                if f"inv:inventory:totalPresaleSellable:sku_{product_id}_wh_{warehouse_number}_date_{date}" in inv:
                    total_qty = inv.split("->")[1]
                    print(int(total_qty))  # 打印Redis.totalPresaleSellable库存
                    assert int(total_qty) - int(
                        available_qty) == sold_count_res, f"Redis库存查询结果与预期不符, 期望为{sold_count_res}, 实际为{int(total_qty) - int(available_qty)}"
                return None
            return None
        return None

    def del_inv_redis_key_del(self, product_id, warehouse_number):
        """
        拼接Redis key进行删除
        """
        data = wms.central_inv_api.redis_detail_query(item_number=product_id)
        key_list = []

        for sales_model in data['object']['salesModels']:
            try:
                # 检查 sales_model 是否为有效字符串
                if not isinstance(sales_model, str) or '->' not in sales_model:
                    raise ValueError("Invalid sales_model format. Expected a string containing '->'.")

                # 分割字符串并提取第二部分
                parts = sales_model.split('->')
                if len(parts) < 2 or not parts[1].strip():
                    raise ValueError("Invalid sales_model format. The part after '->' is missing or empty.")

                target_data = parts[1].strip()

                # 使用 jmespath 进行搜索
                # 确保 target_data 是合法的 JSON 格式
                try:
                    sales_info_list = json.loads(target_data)
                except (ValueError, TypeError) as e:
                    raise ValueError(f"Failed to parse data: {e}")

                # 遍历 sales_info_list 并检查每个元素
                for sales_info in sales_info_list:
                    if sales_info is not None and sales_info.get('product_id') == product_id and sales_info.get('warehouse_number') == str(warehouse_number):
                        key_list.append(sales_model.split('->')[0])

            except Exception as e:
                # 统一异常处理，记录错误信息
                print(f"Error processing sales_model: {e}")

        for product_inv in data['object']['productInvs']:
            # 解析键值对
            key, value = product_inv.split('->')
            if key.startswith(f'inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}'):
                key_list.append(key)

        print(key_list)
        # 删除 Redis key
        wms.central_inv_api.inv_redis_key_del(key_list)

    def reserve_start(self, product_id, reserve_qty, sales_org_id):
        # 赠品活动库存预占
        start_date = wms.util.get_special_date(days=0)
        end_date = wms.util.get_special_date(days=3)
        reserve_start_res = CentralInventory().reserve_start(start_date, end_date, product_id, reserve_qty, sales_org_id)
        wms.util.wait(sleep_time=1, reason='Redis同步延迟')
        real_reserve_qty = jsonpath(reserve_start_res, "$.object[0].real_reserve_qty")
        res1 = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        assert jsonpath(res1, "$.object.data[0].reserved_qty") == real_reserve_qty, f"预占数量不正确, 期望{real_reserve_qty}, 实际{res1}"

    def reserve_end(self, product_id, sales_org_id):
        # 赠品活动库存预占
        start_date = wms.util.get_special_date(days=0)
        end_date = wms.util.get_special_date(days=3)
        reserve_qty_res = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        reserve_qty = jsonpath(reserve_qty_res, "$.object.data[0].reserved_qty")[0]
        CentralInventory().reserve_end(start_date, end_date, product_id, reserve_qty, sales_org_id)  # 退还所有的reserve_qty
        wms.util.wait(sleep_time=1, reason='Redis同步延迟')
        res1 = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        assert jsonpath(res1, "$.object.data[0].reserved_qty")[0] == 0, f"预占数量不正确, 期望{0}, 实际{res1}"

    def special_start(self, sales_org_id, product_id, change_qty, start_time, date, zipcode):
        # 秒杀活动开启
        end_time = wms.util.get_current_time(seconds_buffer=3600)
        CentralInventory().special_update(sales_org_id, product_id, change_qty, start_time, end_time)
        # 秒杀库存redis key查询，校验秒杀是否开启成功；
        wms.util.wait(sleep_time=1, reason='Redis同步延迟')
        data = f"inv:inventory:specialLimit:sku_{product_id}_salesOrg_{sales_org_id}"
        res1 = CentralInventory().fulfill_region_query(data)
        special_inv = int(jsonpath(res1, "$.object")[0])
        assert [special_inv] == [change_qty], f"秒杀活动开启后秒杀库存special_inv不等于change_qty"
        # V5接口查询，校验返回是不是秒杀库存；
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        assert jsonpath(v5_res, "$.object[0].qty") == [change_qty], f'期望售卖数量为{change_qty}, 实际为{v5_res}'
        # 调用秒杀库存进度查询接口，查看秒杀库存；
        res2 = CentralInventory().special_inv(product_id, sales_org_id)
        limit_qty = jsonpath(res2, f'$.object[?(@.salesOrgId=={sales_org_id})].products[?(@.productId=={product_id})].limitQty')
        assert limit_qty == [special_inv], f"秒杀库存进度查询接口返回秒杀库存不正确，预期{special_inv}, 实际{limit_qty}"

    def special_end(self, sales_org_id, product_id, change_qty, start_time, date, zipcode):
        # 秒杀活动结束
        end_time = wms.util.get_current_time(seconds_buffer=0)
        CentralInventory().special_update(sales_org_id, product_id, change_qty, start_time, end_time)
        # 秒杀库存redis key查询，校验秒杀是否关闭成功；
        wms.util.wait(sleep_time=1, reason='Redis同步延迟')
        data = f"inv:inventory:specialLimit:sku_{product_id}_salesOrg_{sales_org_id}"
        res1 = CentralInventory().fulfill_region_query(data)
        assert jsonpath(res1, "$.object"), f"秒杀活动结束秒杀key：{res1}没删除"
        # V5接口查询，校验返回是不是仓库可售库存；
        local_res = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        local_inv = jsonpath(local_res, "$.object.data[0].available_qty")[0]
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        v5_inv = jsonpath(v5_res, "$.object[0].qty")
        assert [local_inv] == v5_inv, f"V5接口返回的不是仓库可售库存, 期望{local_inv}, 实际{v5_inv}"

    def assert_priority_product(self, product_id, sales_model, region_id, warehouse_number, date, zipcode):
        # 调用更新售卖优先级的接口，更新为本地/直邮售卖
        wms.central_inv_api.priority_product(product_id)
        wms.util.wait(sleep_time=6, reason='延迟')
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        assert jsonpath(v5_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v5_res, "$.object[0].regionId") == [region_id], f"regionId不是{region_id}"
        assert jsonpath(v5_res, "$.object[0].warehouse_number") == [warehouse_number], f'期望售卖仓库为{warehouse_number}, 实际为{v5_res}'
        assert jsonpath(v5_res, "$.object[0].qty") > [0], f'期望售卖数量大于0, 实际为{v5_res}'

    def assert_create_vendor_return(self, warehouse_number, product_id, types):
        # vendor_return单调用下单接口
        res = CentralInventory().vendor_return_order(warehouse_number, product_id, -2, types, None)
        failed_products = jmespath(res[0], "object.failedProducts")
        order_id = res[1]
        assert failed_products == [], f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "P":
                    print(f"订单明细{order_id}写入DB成功，状态为P")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_cancel_vendor_return(self, warehouse_number, product_id, order_id, types):
        # vendor_return单调用取消订单接口
        res = CentralInventory().vendor_return_order(warehouse_number, product_id, 2, types, order_id)
        failed_products = jmespath(res[0], "object.failedProducts")
        order_id = res[1]
        assert failed_products == [], f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "S":
                    print(f"订单明细{order_id}写入DB成功，状态为S")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_create_transfer_order(self, date, from_warehouse_number, product_id, to_warehouse_number):
        # 调拨单调用取消订单接口
        res = EcInventory().order_transfer(date, from_warehouse_number, product_id, -5, to_warehouse_number, 10, None)
        failed_products = jmespath(res[0], "object.failedProducts")
        order_id = res[1]
        assert failed_products is None, f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "P":
                    print(f"订单明细{order_id}写入DB成功，状态为S")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_cancel_transfer_order(self, date, from_warehouse_number, product_id, to_warehouse_number, order_id):
        # 调拨单调用取消订单接口
        res = EcInventory().order_transfer(date, from_warehouse_number, product_id, 5, to_warehouse_number, 11, order_id)
        failed_products = jmespath(res[0], "object.failedProducts")
        order_id = res[1]
        assert failed_products is None, f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "X":
                    print(f"订单明细{order_id}写入DB成功，状态为S")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_mapping_item_create(self, mappingProductId, subProductId, ratio, sales_org_id, date, zipcode, sales_warehouse_number):
        # 创建Mapping商品
        CentralInventory().mapping_item_create(mappingProductId, subProductId, ratio)
        wms.util.wait(sleep_time=3, reason='等待INV数据写入')
        sup_product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, date, zipcode, subProductId)
        inv_qty = math.floor(sup_product_inv / ratio)  # 计算Mapping商品数量,向下取整
        # 检查Mapping商品是否创建成功
        InvDataUtils().assert_inventory_query_v5(date, zipcode, mappingProductId, sales_warehouse_number, inv_qty, sales_model="inventory")

    def assert_mapping_item_query(self, mappingProductId, subProductId, ratio, sales_org_id, date, zipcode, sales_warehouse_number, quantity,
                                  adjust_type):
        # 商品库存查询
        sup_product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, date, zipcode, subProductId)
        inv_qty = math.floor(sup_product_inv / ratio)  # 计算Mapping商品数量,向下取整
        # 检查Mapping商品库存
        InvDataUtils().assert_inventory_query_v5(date, zipcode, mappingProductId, sales_warehouse_number, inv_qty, sales_model="inventory")
        # 调整原品库存，在检查Mapping商品库存是否更新成功
        CentralInventory().inv_adjust(subProductId, quantity, adjust_type, sales_warehouse_number)
        wms.util.wait(sleep_time=3, reason='等待INV数据写入')
        inv_qty = inv_qty + math.floor(quantity / ratio)  # 库存调整后重新计算Mapping库存
        InvDataUtils().assert_inventory_query_v5(date, zipcode, mappingProductId, sales_warehouse_number, inv_qty, sales_model="inventory")

    def assert_mapping_item_delete(self, mappingProductId, sales_org_id, date, zipcode, subProductId):
        # 删除Mapping商品
        CentralInventory().mapping_item_delete(mappingProductId)
        wms.util.wait(sleep_time=3, reason='等待INV数据写入')
        # 检查Mapping商品是否删除成功
        mapping_product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, date, zipcode, mappingProductId)
        assert mapping_product_inv == 0, f"Mapping商品删除失败，mappingProductId:{mappingProductId},sup_product_inv:{mapping_product_inv}"
