# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json

from weeeTest import log
from config.basic_db import ConnectDatabase
from config.secret import get_secret


class InvDB(object):
    """
    INV相关SQL处理
    """
    db_config = get_secret()

    def __init__(self):
        self.invdb = ConnectDatabase(host='weee.db.tb1.sayweee.net', user=self.db_config['db_wms_username'],
                                     password=self.db_config['db_wms_password'], db_name='weee_inv', return_type="json")

    def select_data_deal(self, sql: str):
        """
        查询结果二次处理,query_data结果是JSON字符串,转换成list
        :param sql: 查询语句
        """
        log.info(f'sql-select_data_deal:{sql}')
        ret = json.loads(self.invdb.query_data(sql))
        log.info(f'retffff:{ret}')
        return ret

    # 库存相关SQL......
    def query_order_detail(self, order_id):
        """
        查询订单明细
        """
        sql = f'select product_id,warehouse_number,reference_no,status,qty from weee_inv.inv_inventory_detail where reference_no="{order_id}";'
        result = self.select_data_deal(sql)
        log.info(f'query_order_detail:{result}')
        return result

    def update_item_sale(self, product_id, status, sale):
        """
        更新item商品状态为直邮售卖
        """
        sql = f'update weee_im.im_product_sale set type="{sale}",status="{status}" where product_id={product_id} and sales_org_id=4;'
        result = self.invdb.update_data(sql)
        log.info(f'update_item_sale:{result}')
        return result

    def query_item_priority(self, product_id):
        """
        查询item商品售卖优先级
        """
        sql = f'select priority from weee_inv.inv_item_region_mapping where product_id ={product_id} and warehouse_number="25" and region_id="R18";'
        result = self.select_data_deal(sql)
        log.info(f'query_item_priority:{result}')
        return result

    def query_item_inventory(self, warehouse_number, product_id):
        """
        查询商品库存
        """
        sql = f'select * from weee_inv.inv_inventory where warehouse_number = "{warehouse_number}" and product_id = {product_id};'
        result = self.select_data_deal(sql)
        log.info(f'item inventory:{result}')
        return result

    def item_inventory_del(self, warehouse_number, product_id):
        """
        删除商品库存
        """
        sql = f'delete from weee_inv.inv_inventory where warehouse_number = "{warehouse_number}" and product_id = {product_id};'
        sql2 = f'delete from weee_comm.gb_inventory_qty where inventory_id = "{warehouse_number}" and product_id = {product_id};'
        self.invdb.update_data(sql)
        self.invdb.update_data(sql2)

    def query_item_region_mapping(self, product_id, warehouse_number=None):
        """
        查询商品与Region关系模型
        """
        sql = f'select * from weee_inv.inv_item_region_mapping where product_id = {product_id}'
        if warehouse_number:
            sql += f' and warehouse_number = "{warehouse_number}"'
        result = self.select_data_deal(sql)
        log.info(f'item_region_mapping:{result}')
        return result

    def delete_item_region_mapping(self, warehouse_number, product_id):
        """
        删除商品与Region关系模型
        """
        sql = f'delete from weee_inv.inv_item_region_mapping where warehouse_number = "{warehouse_number}" and product_id = {product_id};'
        self.invdb.update_data(sql)

    def query_warehouse_region_mapping(self, warehouse_number, work_type):
        """
        查询仓库与Region关系
        """
        sql = f'select * from inv_warehouse_region_config iwrc where warehouse_number ="{warehouse_number}" and work_type ="{work_type}" and sell_mode != "fbw";'
        result = self.select_data_deal(sql)
        log.info(f'warehouse_region_mapping:{result}')
        return result

    def query_region_zipcode_mapping(self, region_id):
        """
        查询Region与zipcode关系
        """
        sql = f'select * from inv_zipcode_region_config where region_id ="{region_id}" and sell_mode !="fbw" limit 1;'
        result = self.select_data_deal(sql)
        log.info(f'zipcode_region_mapping:{result}')
        return result
