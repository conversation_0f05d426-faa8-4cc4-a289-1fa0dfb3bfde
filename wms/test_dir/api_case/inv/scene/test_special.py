import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestSpecial(weeeTest.TestCase):
    """
     秒杀库存相关接口
    """
    def setup_class(self):
        # 登录
        wms.wms_login.common_login()
        self.sales_org_id = global_data.special['sales_org_id']
        self.product_id = global_data.special['product_id']
        self.change_qty = global_data.special['change_qty']
        self.product_type = global_data.special['product_type']
        self.zipcode = global_data.special['zipcode']
        self.start_time = wms.util.get_current_time(seconds_buffer=-10)
        self.date = wms.util.get_special_date(days=3)

    # 秒杀库存测试
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_special_start(self):
        """
        【102291】秒杀_秒杀开启预占库存
        """
        InvDataUtils().special_start(self.sales_org_id, self.product_id, self.change_qty, self.start_time, self.date, self.zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_create_order_inventory(self):
        """
        【102288/102289】秒杀_下单&取消
        """
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(self.date, self.product_id, self.product_type, self.sales_org_id, self.zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(self.date, order_id, self.product_id, self.product_type, self.sales_org_id, self.zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_special_end(self):
        """
        【102895】秒杀_秒杀结束退还库存
        """
        InvDataUtils().special_end(self.sales_org_id, self.product_id, self.change_qty, self.start_time, self.date, self.zipcode)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# 秒杀库存开启
# 秒杀下单/取消
# 秒杀结束
