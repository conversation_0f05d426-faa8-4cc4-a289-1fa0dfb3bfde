# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  yu.yang
@Version        :  V1.0.0
------------------------------------
@File           :  test_mapping_item.py
@Description    :  
@CreateTime     :  2025/8/26 09:56
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/8/26 09:56
"""
import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestMapping(weeeTest.TestCase):
    """
     Mapping商品相关接口
    """

    def setup_class(self):
        # 登录
        wms.wms_login.common_login()
        self.mappingProductId = global_data.mapping['mappingProductId']
        self.subProductId = global_data.mapping['subProductId']
        self.ratio = global_data.mapping['ratio']
        self.zipcode = global_data.mapping['zipcode']
        self.sales_org_id = global_data.mapping['sales_org_id']
        self.sales_warehouse_number = global_data.mapping['warehouse_number']
        self.date = wms.util.get_special_date(days=3)

    # Mapping商品
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_mapping_item_create(self):
        """
        【102958】创建Mapping商品
        """
        InvDataUtils().assert_mapping_item_create(self.mappingProductId, self.subProductId, self.ratio, self.sales_org_id, self.date, self.zipcode,
                                                  self.sales_warehouse_number)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_mapping_item_query(self):
        """
        【103005】Mapping商品库存共享查询
        """
        self.quantity = 20
        self.adjust_type = "701"
        InvDataUtils().assert_mapping_item_query(self.mappingProductId, self.subProductId, self.ratio, self.sales_org_id, self.date, self.zipcode,
                                                 self.sales_warehouse_number, self.quantity,
                                                 self.adjust_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_mapping_item_delete(self):
        """
        【102957】删除Mapping商品
        """
        InvDataUtils().assert_mapping_item_delete(self.mappingProductId, self.sales_org_id, self.date, self.zipcode, self.subProductId)
