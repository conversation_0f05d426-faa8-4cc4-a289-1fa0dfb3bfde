# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestNewProductModel(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_new_product_inventory(self):
        """
        【102724】新商品加库存新建库存模型
        """
        product_id = 110002
        warehouse_number = "48"
        location_no = "A0101-0-1"
        adjust_qty = 1
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        try:
            # 通过wms central 添加库存
            wms.adjust_api.create_batch_inv(
                product_id,
                warehouse_number,
                user_id,
                location_no,
                adjust_qty,
                is_lpn=False,
                pieces_per_pack=1.00,
                expire_dtm=wms.util.get_special_date(days=365),
                receive_date=wms.util.get_special_date(),
            )
            # 最大重试次数
            max_retries = 20
            # 每次重试等待时间（秒）
            wait_time = 6

            for attempt in range(max_retries):
                # 检查库存平台商品库存
                inv_result = wms.inv_db.query_item_inventory(warehouse_number, product_id)
                if jmespath(inv_result, "[*].available_qty") == [1]:
                    break
                else:
                    print(f"Attempt {attempt + 1} failed. Retrying in {wait_time} seconds...")
                    # 等待一段时间后重试
                    wms.util.wait(sleep_time=wait_time)

            # 检查库存平台数据库商品库存
            inv_result = wms.inv_db.query_item_inventory(warehouse_number, product_id)
            assert jmespath(inv_result, "[*].available_qty") == [1], f"商品{product_id} 在inv_inventory表中无库存"

            # 检查库存平台商品与Region模型创建
            wms.util.wait()
            result = wms.inv_db.query_item_region_mapping(product_id, warehouse_number)
            region_list = jmespath(result, "[*].region_id")
            assert len(region_list) > 0
            redis_key_list = [f"inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}"]

            # 检查V5接口，商品可售卖
            for r in result:
                redis_key_list.append(f'inv:fulfill:area:sku_{product_id}_regionId_{r["region_id"]}')
                zipcode = wms.inv_db.query_region_zipcode_mapping(r["region_id"])[0]["zipcode"]
                InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, adjust_qty)

            # 检查库存平台Redis商品库存
            redis_result = wms.central_inv_api.redis_detail_query(product_id)
            assert redis_result["object"] is not None, "INV Redis中未同步库存"
            redis_inv = jmespath(redis_result, "object.productInvs")
            assert f"inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}->{adjust_qty}" in redis_inv, (f"redis中商品库存与实际不一致，实际库存{adjust_qty}, "
                                                                                                                          f"redis库存:{redis_inv}")
            redis_sales_models = jmespath(redis_result, "object.salesModels")
            filtered_list = [item for item in redis_sales_models if any(region in item for region in region_list)]
            assert len(filtered_list) == len(region_list)
        except Exception as e:
            weeeTest.log.info(f"用例执行失败: {e}")
            raise
        finally:
            # 用例执行完成后清理数据
            # 清除WMS商品库存
            wms.moving_api.clear_inv(warehouse_number, user_id, [location_no], operationVirtualLocation=False)
            # 删除库存平台DB中商品数据
            wms.inv_db.delete_item_region_mapping(warehouse_number, product_id)
            wms.inv_db.item_inventory_del(warehouse_number, product_id)
            # 删除库存平台Redis中商品信息
            InvDataUtils().del_inv_redis_key_del(product_id, warehouse_number)


if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
