# !/usr/bin/python3
# -*- coding: utf-8 -*-

from jsonpath import jsonpath
from wms.test_dir.api.wms.wms import wms



class WmsAssertUtils(object):
    """
    WMS断言处理
    """
    def check_location_status(self, warehouse_number, location_type, location_no, flag):
        """location status check"""
        location_info = wms.common_api.get_avaiable_location(warehouse_number, location_type, location_no=location_no)
        location_actual_flag = jsonpath(location_info, "$[*].flag")[0]
        assert location_actual_flag == flag, f"预期仓库{warehouse_number}下库位{location_no}状态为{flag}, 实际为{location_actual_flag}"

    def check_order_status(self, warehouse_number, order_ids, expected_status):
        """Verifies that a list of orders has the expected shipping status."""
        if not isinstance(order_ids, list):
            order_ids = [order_ids]
        for order_id in order_ids:
            shipping_status = wms.batch.query_order_info(warehouse_number, order_id=order_id)[0]["status"]
            assert shipping_status in expected_status if isinstance(expected_status, tuple) else shipping_status == expected_status, \
                f"Order {order_id} status should be {expected_status}, but was {shipping_status}"

    def check_inventory_cleared(self, warehouse_number, items, location_no):
        """Verifies that item inventory has been cleared"""
        for item in items:
            inv_list = wms.adjust_api.query_location_inv(item["item_number"], location_no, warehouse_number)["body"]["invAdjustList"]
            assert len(inv_list) == 0, \
                f"Inventory for item {item['item_number']} in location {location_no} was not cleared after packing."

    def check_inventory_log(self, warehouse_number, item_number, reference_id, type_code):
        """Verifies that the correct inventory logs"""
        log_list = wms.adjust_api.query_inventory_log(
            warehouse_number, wms.util.get_special_date(days=-1), wms.util.get_special_date(),
            item_number=item_number, reference_id=reference_id, type_code_list=[type_code])
        assert len(log_list) > 0, f"Inventory log (type {type_code}) not found for item {item_number} in reference_id {reference_id}."



if __name__ == '__main__':
    w = WmsAssertUtils()
    w.check_location_status("48", 38, "PLT0008", 0)