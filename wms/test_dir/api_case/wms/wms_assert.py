# !/usr/bin/python3
# -*- coding: utf-8 -*-
from yaml import MappingEndEvent

from wms.test_dir.api_case.wms.wms_sql import WmsDB
from wms.test_dir.api_case.wms.utils import DataUtils


class WmsAssert(object):
    """
    WMS断言处理
    """
    wms_db = WmsDB()
    def check_order_ship_status(self, order_id, status):
        """order status check"""
        self.wms_db.check_shipping_status(order_id=order_id, status=status)

    def check_location_status(self, warehouse_number, location_no, status):
        """location status check"""
        self.wms_db.check_tote_status(location_no, warehouse_number, status)

    def check_location_empty(self, warehouse_number, location_no):
        """
        检查库位库存清空

        Args:
            warehouse_number (_type_): 仓库编号
            location_no (_type_): 库位编号
        """
        assert self.wms_db.get_location_inventory(warehouse_number, location_no) == [], f"{warehouse_number}仓库的{location_no}库位库存没有被清空"

    def check_hook_report(self, reference_id, event_key, in_dtm):
        """
        检查业务通过hook上报其他平台数据

        Args:
            order_id (_type_): wms order id
            event_key (_type_): business event key
            in_dtm (_type_): 业务流程操作时间
        """
        DataUtils().wait(sleep_time=1, reason="等待hook表写入上报事件")
        assert self.wms_db.select_hook_task(reference_id, event_key, in_dtm) != [], f"未查到{reference_id}上报{event_key}的hook"


    def check_mq_report(self, warehouse_number, key_str):
        """
        检查业务通通过MQ上报的数据
        """
        mq_list = self.wms_db.select_mq_task(warehouse_number)
        is_include = False
        for i in mq_list:
            if key_str in i["content"]:
                is_include = True
        assert is_include == True, f"未查到上报MQ的记录"

    def check_non_batch_invenotry(self, warehouse_number, location_no, item_number, adjust_qty, reference_no=None, inventory_type=[], old_qty=0, in_dtm=0):
        item_inv = self.wms_db.get_inventory_transaction(warehouse_number, location_no, item_number)
        if item_inv != []:
            assert item_inv["quantity"] == adjust_qty+old_qty, f"""{warehouse_number}仓库{item_number}商品在{location_no}库位上的库存预期为{adjust_qty+old_qty} 实际为{item_inv.get("quantity", 0)}"""


        if inventory_type:
            assert self.wms_db.get_inventory_transaction_log(warehouse_number, location_no, item_number, reference_no, inventory_type=inventory_type, in_dtm=in_dtm) != [], f"{warehouse_number}仓库{item_number}商品在{location_no}库位上的库存扣减上报的inventory log未查到"


    def check_batch_invenotry(self, warehouse_number, location_no, item_number, adjust_qty, batch_no=None, reference_no=None, inventory_type=[], old_qty=0, pieces_per_pack=None, 
                               receive_dtm=None, expire_dtm=0, in_dtm=0):

            if batch_no is None:
                batch_info =  self.wms_db.get_inventory_batch(item_number, reference_no)
                assert batch_info != []
                batch_no = batch_info["batch_no"]
                assert batch_info["pieces_per_pack"] == pieces_per_pack
                assert batch_info["expire_dtm"] == expire_dtm
                assert batch_info["receive_dtm"] == receive_dtm
            item_batch_inv = self.wms_db.get_batch_inventory_transaction(warehouse_number, location_no, item_number, batch_no)
            if item_batch_inv != []:
                assert item_batch_inv.get("quantity", 0) == adjust_qty+old_qty,  f"""库位{location_no}库存调整错误, 预期={adjust_qty+old_qty}, 实际={item_batch_inv.get("quantity", 0)}"""

            if inventory_type:
                assert self.wms_db.get_batch_inventory_transaction_log(warehouse_number, location_no, item_number, reference_no, batch_no, inventory_type=inventory_type, in_dtm=in_dtm) != [], f"未查到inventory_type={inventory_type}的库存记录"

    def check_repack_status(self,task_id,item_number,status):
        """

        :return:
        """
        result = self.wms_db.get_repack_status(task_id,item_number,info='status')

        if result !=[]:
            assert result == status,"repack拣货任务状态异常"


    def check_receive_quantity(self,reference_no,item_number,receive_quantity):
        """
        :return:
        """
        assert self.wms_db.get_receive_quantity(reference_no,item_number)["receive_quantity"] == receive_quantity,"repack收货数量异常"

    def check_putaway_quantity(self,reference_no,item_number,putaway_quantity):
        """

        :param reference_no:
        :param item_number:
        :param putaway_quantity:
        :return:
        """
        assert self.wms_db.get_receive_quantity(reference_no, item_number)["putaway_quantity"] == putaway_quantity

    def check_putaway_status(self,reference_no,item_number,task_id,status):
        """
        :param reference_no: 收货单号
        :param item_number:
        :param status:
        :return:
        """
        task_status = self.wms_db.get_putaway_info(task_id,reference_no,item_number, info='status')
        if task_status != []:
            assert task_status == status,"repack上架单状态异常"
        task_detail_status = self.wms_db.get_putawaydetail_info(task_id, info='status')
        if task_detail_status != []:
            assert task_detail_status == status,"repack上架单明细状态异常"


if __name__ == '__main__':
    w = WmsAssert()
    # w.check_non_batch_invenotry("33", "TM1051", "88609", -1, reference_no="17866123",inventory_type=[261], in_dtm=1730446037)
    #w.check_non_batch_invenotry(warehouse_number = '20', location_no = 'TL0004', item_number = '9819', adjust_qty = -1, reference_no = 1786780502, inventory_type = [261, 801],old_qty = 0, in_dtm = 1730872759)
    #w.check_receive_quantity(reference_no="R2024112901",item_number=10958,item_quantity=945)


