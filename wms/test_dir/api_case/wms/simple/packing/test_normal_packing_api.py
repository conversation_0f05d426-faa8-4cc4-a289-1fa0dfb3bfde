# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestNormalPackingAPI(weeeTest.TestCase):
    """
    普通订单打包出库流程API测试
    """

    def setup_class(self):
        # 打包台相关接口需要的信息
        # warehouse = ""
        # user = ""
        # user_id = ""
        # username = ""
        # tote_no = ""
        # order_id = ""
        # items = []
        # recommend_package = []
        self.packing_info = globals()

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_get_packing_type(self):
        """
        Normal 订单打包获取packing type
        """
        warehouse = global_data.normal_packing['warehouse']
        tote_no = global_data.normal_packing['tote_no']
        order_id = global_data.normal_packing['order_id']
        storage_type = global_data.normal_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)

        # 设置打包所需数据
        self.packing_info["tote_no"] = tote_no
        self.packing_info["warehouse"] = warehouse

        # 登录
        account, username = wms.wms_login.common_login()
        self.packing_info["userid"] = account
        self.packing_info["username"] = username

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 设置用户信息和仓库
        wms.normal_packing.user = username + '(' + account + ')'
        wms.normal_packing.warehouse = warehouse

        # 获取打包任务类型,更新header
        resp = wms.normal_packing.query_packing_task(tote_no)
        assert resp['success'] == True
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_get_packing_info(self):
        """
        Normal 订单打包获取订单详情
        """
        self.packing_info["items"], self.packing_info["order_id"], recommend_package = wms.normal_packing.query_packing_info(self.packing_info["tote_no"])
        self.packing_info["recommend_package"] = list(map(lambda i: i["barcode"], recommend_package))

        # 校验订单状态(shipping_status=60)
        wms.wms_db.check_shipping_status(order_id=self.packing_info["order_id"], status=60)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_qc(self):
        """
        Normal 订单打包QC商品
        """
        wms.normal_packing.packing_qc(self.packing_info["items"], self.packing_info["order_id"])
        # 检查订单下商品是否全部QC
        wms.wms_db.check_all_qc(order_id=self.packing_info["order_id"])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_scan_box(self):
        """
        Normal 订单打包选择包材
        """
        wms.normal_packing.scan_box(self.packing_info["recommend_package"], self.packing_info["order_id"])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_create_lable(self):
        """
        Normal 订单打包打印shpping label
        """
        wms.normal_packing.label_create(self.packing_info["order_id"])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_ship(self):
        """
        Normal 订单打包完成接口
        """
        tote_no = self.packing_info["tote_no"]
        order_id = self.packing_info["order_id"]
        warehouse = self.packing_info["warehouse"]
        wms.normal_packing.normal_ship(tote_no, order_id)
        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=70)

        # check tote status
        wms.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)

        # check inventory
        wms.wms_assert.check_location_empty(warehouse, tote_no)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=self.packing_info["userid"],
                   user_name=self.packing_info["username"])

        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
