import weeeTest

from wms.test_dir.api.wms.wms import wms


class Test(weeeTest.TestCase):
    """
    订单下发相关接口
    """

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_pull_order(self):
        """
        从so获取近两个小时的订单
        """
        # /central_inventory/so/order/downByCreateTime?startTime=?&endTime=?
        wms.wms_login.common_login()
        data = wms.down_order.pull_order_from_so()
        assert data['success'] == True
