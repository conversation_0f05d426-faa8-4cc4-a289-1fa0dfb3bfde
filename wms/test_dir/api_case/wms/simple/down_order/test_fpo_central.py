import weeeTest


class TestFpoCentral(weeeTest.TestCase):
    """
    central fpo management相关接口
    """

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fpo_region(self):
        """
        从so获取近两个小时的订单
        """
        # /central_inventory/so/order/downByCreateTime?startTime=?&endTime=?
        # 此用例未完成，先注释
        # login = Login()
        # wmsDownOrder = WmsDownOrder()
        # login.common_login(account, password)
