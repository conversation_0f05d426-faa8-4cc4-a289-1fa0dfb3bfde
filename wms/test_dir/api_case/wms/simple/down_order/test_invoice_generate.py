import weeeTest

import datetime

from wms.test_dir.api.wms.wms import wms


class TestInvoiceGenerate(weeeTest.TestCase):
    """
    订单下发相关接口
    """

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_invoice(self):
        """
        生成发货批次
        """
        wms.wms_login.common_login()
        # 生成发货批次
        today = datetime.date.today().strftime('%Y-%m-%d')
        data = wms.down_order.invoice_generate(delivery_date=today, region_ids=['5'], warehouse_number='25',
                                               user_id='7226349')
        # 接口返回值校验
        assert data['success'] == True
