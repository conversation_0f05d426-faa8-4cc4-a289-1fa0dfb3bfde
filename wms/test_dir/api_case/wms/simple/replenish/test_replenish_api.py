# # !/usr/bin/python3
# # -*- coding: utf-8 -*-
#
# import weeeTest
# from weeeTest import jmespath
#
# from wms.qa_config import global_data
# from wms.test_dir.api.wms.wms import wms
#
#
# class TestReplenishAPI(weeeTest.TestCase):
#     """
#     补单流程接口测试
#     """
#
#     def setup_class(self):
#         # 补单相关接口需要的信息
#         # warehouse = ""
#         # user = ""
#         # user_id = ""
#         # username = ""
#         # tote_no = ""
#         # order_id = ""
#         # items = []
#         self.rep_info = globals()
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_check_tote(self):
#         """
#         补单扫描tote
#         """
#         warehouse = global_data.normal_packing_to_replenish['warehouse']
#         tote_no = global_data.normal_packing_to_replenish['tote_no']
#         order_id = global_data.normal_packing_to_replenish['order_id']
#         storage_type = global_data.normal_packing_to_replenish['storage_type']
#
#         wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)
#
#         # 设置补单所需数据
#         self.rep_info["tote_no"] = tote_no
#         self.rep_info["warehouse"] = warehouse
#         self.rep_info["order_id"] = order_id
#
#         # 登录
#         account, username = wms.wms_login.common_login()
#         self.rep_info["userid"] = account
#         self.rep_info["username"] = username
#
#         # 设置仓库
#         wms.util.update_header(weee_warehouse=str(warehouse))
#         # 设置storage_type
#         wms.util.update_header(weee_wms_storage_type=str(storage_type))
#
#         # 开始打包
#         wms.normal_packing.user = username + '(' + account + ')'
#         wms.normal_packing.warehouse = warehouse
#
#         # 获取打包任务类型,更新header
#         resp = wms.normal_packing.query_packing_task(tote_no, station="7-1")
#         wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
#         items, order_id, recommend_package = wms.normal_packing.query_packing_info(tote_no, station="7-1")
#
#         # 校验订单状态(shipping_status=60)
#         assert wms.wms_db.check_shipping_status(order_id=order_id) in (60, 61)
#
#         # QC 时OOS
#         oos_item = wms.normal_packing.oos_qc(items, order_id)
#
#         # 转补单
#         wms.normal_packing.packing_replenish(tote_no, order_id, oos_item)
#
#         # check order status
#         wms.wms_db.check_shipping_status(order_id=order_id, status=42)
#
#         # check tote status
#         wms.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=3)
#
#         # 开始补单,设置仓库
#         wms.replenish.warehouse = warehouse
#
#         # replenish order check in
#         wms.replenish.check_tote(tote_no)
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_check_slot(self):
#         """
#         补单扫描slot
#         """
#         slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=self.rep_info["warehouse"], location_type=35,
#                                                           flag=0, info='location_no')
#         self.rep_info["slot_no"] = slot_no
#         wms.replenish.check_slot(slot_no, self.rep_info["order_id"])
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_bind_slot(self):
#         """
#         补单绑定tote与slot
#         """
#         wms.replenish.bind_slot(self.rep_info["tote_no"], self.rep_info["slot_no"])
#         wms.wms_db.check_shipping_status(order_id=self.rep_info["order_id"], status=45)
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_get_pick_list(self):
#         """
#         需要补单商品pick list获取
#         """
#         # replenish item pick
#         hot_tote = wms.wms_db.get_wh_storage_location_info(warehouse=self.rep_info["warehouse"], location_type=26,
#                                                            flag=0, info='location_no')
#         wait_picking_list = wms.replenish.get_pick_list()
#         self.rep_info["hot_tote"] = hot_tote
#         self.rep_info["waitPickingList"] = wait_picking_list
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_scan_hot_tote(self):
#         """
#         补单扫描hot pick tote
#         """
#         wms.replenish.scan_hot_tote(self.rep_info["hot_tote"])
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_pick_confirm(self):
#         """
#         需要补单商品拣货
#         """
#         wms.replenish.pick_confirm(self.rep_info["waitPickingList"], self.rep_info["hot_tote"])
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_query_hot_pick_tote(self):
#         """
#         获取有商品的hot pick tote
#         """
#         # replenish item check in
#         hot_pick_list = wms.replenish.query_hot_pick_tote()["body"]
#         assert self.rep_info["hot_tote"] in list(map(lambda h: h["tote_no"], hot_pick_list))
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_scan_hot_pick_tote(self):
#         """
#         扫描可以check in的hot pick tote
#         """
#         wms.replenish.scan_hot_pick_tote(self.rep_info["hot_tote"])
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_get_checkin_list(self):
#         """
#         获取Hot pick tote下可以check in的商品列表
#         """
#         self.rep_info["checkin_list"] = wms.replenish.get_checkin_list(self.rep_info["hot_tote"])["body"]
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_checkin_confirm(self):
#         """
#         Hot pick tote中的商品转移到slot
#         """
#         wms.replenish.item_checkin_confirm(self.rep_info["hot_tote"], self.rep_info["slot_no"],
#                                            self.rep_info["checkin_list"])
#
#     @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
#     def test_replenish_finish(self):
#         """
#         结束补单
#         """
#         wms.replenish.finish_replenish(self.rep_info["slot_no"], self.rep_info["tote_no"])
#         warehouse = self.rep_info["warehouse"]
#         hot_tote = self.rep_info["hot_tote"]
#         slot_no = self.rep_info["slot_no"]
#         tote_no = self.rep_info["tote_no"]
#         order_id = self.rep_info["order_id"]
#
#         # check order status
#         wms.wms_db.check_shipping_status(order_id=order_id, status=50)
#
#         # check tote status
#         wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=hot_tote, status=0)
#
#         wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)
#
#         wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
#
#         # 退出登录
#         wms.logout(warehouse_number=warehouse, user_id=self.rep_info["userid"],
#                    user_name=self.rep_info["username"])
#
#         # 将order的状态置为结束，避免被其他业务使用
#         wms.wms_db.update_order_ship_status(order_id=order_id, ship_status=70)
#
#
# if __name__ == '__main__':
#     weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
