# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json

from weeeTest import log

from wms.qa_config import global_data
from config.basic_db import ConnectDatabase
from config.secret import get_secret


class WmsDB(object):
    """
    WMS相关SQL处理
    """
    db_config = get_secret()

    def __init__(self):
        self.wmsdb = ConnectDatabase(host='wms.db.tb1.sayweee.net', user=self.db_config['db_wms_username'],
                                     password=self.db_config['db_wms_password'], db_name='wms', return_type="json")

    def select_data_deal(self, sql: str):
        """
        查询结果二次处理
        :param sql: 查询语句
        sql = "SELECT * FROM wms.wh_storage_location WHERE location_type = 50 AND flag = 0 and warehouse = '' LIMIT 1"
        """
        ret = json.loads(self.wmsdb.query_data(sql))
        print(ret)
        if len(ret) == 1:
            return ret[0]
        else:
            return ret

    def update_order_ship_status(self, order_id, ship_status):
        self.wmsdb.update_data(
            f'''update wms.wh_order_info set shipping_status={ship_status} where order_id="{order_id}"''')

    def update_order_status(self, order_id, order_status):
        """
        更新订单状态

        Args:
            order_id (_type_): wms order id
        """
        self.wmsdb.update_data(
            f'''update wms.wh_order_info set order_status={order_status} where order_id="{order_id}"''')

    def update_order_delivery_dtm(self, order_id, delivery_dtm):
        self.wmsdb.update_data(
            f'''update wms.wh_order_info set delivery_dtm={delivery_dtm} where order_id="{order_id}"''')

    def update_location_status(self, warehouse, location_no, flag):
        self.wmsdb.update_data(
            f'''update wms.wh_storage_location set flag={flag} where warehouse_number="{warehouse}" and location_no="{location_no}"''')

    def update_order_item(self, order_id):
        self.wmsdb.update_data(
            f'''update wms.wh_order_item set reject_quantity=0 where order_id="{order_id}"''')

    def update_pick_order(self, order_id):
        self.wmsdb.update_data(
            f'''update wms.wh_picking_order set replenish_tote_no = NULL where order_id="{order_id}"''')
    def update_pick_order_item(self, order_id):
        self.wmsdb.update_data(
            f'''update wms.wh_picking_order_item set miss_quantity = 0 where order_id="{order_id}"''')

    def update_pick_order_status(self, order_id):
        self.wmsdb.update_data(
            f'''update wms.wh_picking_order set status = "50" where order_id="{order_id}"''')

    def select_replenish_tote(self, order_id):
        sql = f'''SELECT * FROM  wms.wh_picking_order where order_id="{order_id}" '''
        result = self.select_data_deal(sql)
        log.info(f"MOF转补单rep,tote_no:{result['replenish_tote_no']}")
        return result['replenish_tote_no']

    def rollback_wait_packing(self, warehouse, order_id, tote, delivery_dtm=0):
        """
        恢复订单到待打包状态
        """
        log.info(f'恢复订单到待打包状态')
        order_list = []
        if isinstance(order_id, list):
            order_list.extend(order_id)
        else:
            order_list.append(order_id)
        self.update_location_status(warehouse, tote, 3)
        for order_id in order_list:
            # 恢复order info为待打包状态
            self.update_order_ship_status(order_id, 50)
            # 恢复order item为待打包状态
            self.update_order_item(order_id)
            # 恢复picking order为待打包状态
            self.update_pick_order_status(order_id)
            if delivery_dtm != 0:
                self.update_order_delivery_dtm(order_id, delivery_dtm)

    def rollback_wait_oos(self, order_id):
        """
        恢复订单到未做Move to cot状态
        """
        log.info(f'恢复订单到未做Move to cot状态')
        sql = f'update wms.wh_packing_order set deal_status=NULL, deal_user=NULL, deal_dtm=NULL, deal_mode=NULL where order_id = {order_id}'
        self.wmsdb.update_data(sql)

    def rollback_wait_routecheck(self, warehouse, delivery_date, route_id, order_type, order_list=[]):
        """
        恢复订单到待route_check状态
        """
        log.info(f'恢复订单到待route_check状态')
        if order_list:
            for order_id in order_list:
                self.wmsdb.update_data(
                    f'''update wms.wh_packing_order set confirm_package_number=NULL,confirm_package=NULL where order_id="{order_id}"''')
        else:
            self.wmsdb.update_data(
                f'''update
                    wms.wh_packing_order
                set
                    confirm_package_number = NULL,
                    confirm_package = NULL
                where
                    order_id in (
                    select
                        order_id
                    from
                        wms.wh_order_info
                    where
                        warehouse_number = "{warehouse}"
                        and delivery_dtm = UNIX_TIMESTAMP("{delivery_date}")
                            and order_type = {order_type}
                            and route_id={route_id});''')

    def check_shipping_status(self, order_id, status=None):
        """
        校验订单状态
        :param status: 订单状态
        :param order_id: wms订单ID
        :return:
        """
        sql = f"select shipping_status from wms.wh_order_info where order_id='{order_id}'"
        result = self.select_data_deal(sql)
        log.info(f'wms_order_id:{order_id}的shipping_status:{result}')
        if status:
            assert result[
                       "shipping_status"] == status, f'''wh_order_info表订单{order_id}的shipping_status预期等于{status},实际等于{result["shipping_status"]}'''
        else:
            return result["shipping_status"]

    def check_inventory_log_location(self, warehouse, reference_id, location = None):
        """
        校验订单状态
        :param location: 库位
        :param reference_id: wms订单ID
        :return:
        """
        sql = f"SELECT location_no FROM wh_batch_inventory_transaction_log WHERE warehouse_number = '{warehouse}' and reference_id = '{reference_id}' and operation_type = 'preoccupy' ORDER by rec_id desc limit 1"
        result = self.select_data_deal(sql)
        log.info(f'wms_order_id:{reference_id}补单预占的location_no:{result}')
        if location:
            assert result[
                       "location_no"] == location, f'''wh_order_info表订单{reference_id}的补单预占的location预期等于{location},实际等于{result["location_no"]}'''
        else:
            return result["location_no"]

    def check_inventory(self, location_no, item_number, quantity=None):
        """
        校验库存
        """
        sql = f"select quantity from wms.wh_inventory_transaction where item_number='{item_number}' and location_no='{location_no}'"
        result = self.select_data_deal(sql)
        log.info(f'quantity:{item_number}的quantity:{result}')
        if quantity:
            assert result["quantity"] == quantity, f'''{location_no}上商品{item_number}的库存预期为{quantity}实际为{result["quantity"]}'''
        else:
            return result["quantity"]

    def check_orders_shipping_status(self, order_ids, shipping_status=20):
        """
        获取订单状态列表
        :param shipping_status:
        :param order_ids: wms订单ID
        :return:
        """
        if len(order_ids) == 1:
            order_ids = order_ids[0]
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        sql = f"select order_id,shipping_status from wms.wh_order_info where order_status = 0 AND order_id in {order_ids}"
        result = self.select_data_deal(sql)
        log.info(f'orders_shipping_status:{result}')
        if isinstance(result, list):
            shipping_list = tuple(info['shipping_status'] for info in result)
        else:
            shipping_list = result['shipping_status'],
        assert all(x >= shipping_status for x in shipping_list)

    def select_hook_task(self, reference_id, event_key, in_dtm):
        """
        查询业务数据触发的Hook任务记录

        Args:
            event_key (_type_): _description_
            in_dtm (_type_): _description_
        """
        sql = f"select * from wms.wh_hook_task where reference_id='{reference_id}' and event_key ='{event_key}' and in_dtm>={in_dtm}  order by task_id DESC"
        return json.loads(self.wmsdb.query_data(sql))

    def select_mq_task(self, warehouse_number):
        """
        查询上报MQ的记录(该SQL不能使用in_dtm筛选，太慢，默认获取最新20条数据)

        Args:
            warehouse_number (_type_): warehouse_number
        """
        sql = f'select * from wms.wh_msg_history where warehouse_number="{warehouse_number}" order by 1 desc limit 20'
        return json.loads(self.wmsdb.query_data(sql))

    def check_tote_status(self, tote_no, warehouse, status=None):
        """
        校验对应仓库下的tote状态
        :param warehouse: 仓库ID
        :param status: tote状态
        :param tote_no: tote_no
        :return:
        """
        sql = f'select * from wms.wh_storage_location where warehouse_number="{warehouse}" and location_no="{tote_no}"'
        result = self.select_data_deal(sql)
        log.info(f'check_tote_status:{result}')
        if status is not None:
            assert result["flag"] == status, f'''{warehouse}仓库{tote_no}的占用状态预期等于{status},实际等于{result["flag"]}'''
        else:
            return result

    def get_wh_storage_location_info(self, warehouse, location_type, flag=0, info='*'):
        """
        校验对应仓库下的tote状态
        :param warehouse: 仓库ID
        :param location_type: 50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
        :param flag:0:空闲, 1:OOS, 2:question, 3:已占用
        :param info:
        :return:
        """
        sql = f'select {info} from wms.wh_storage_location where warehouse_number="{warehouse}" and location_type={location_type} and flag={flag} limit 1'
        result = self.select_data_deal(sql)
        log.info(f'get_wh_storage_location_info:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def get_location_inventory(self, warehouse, location_no, item_number=None):
        """
        校验库存
        :param warehouse: 仓库ID
        :param cart_no: cartID
        :return:
        """
        if item_number:
            sql = f'select * from wms.wh_inventory_transaction where warehouse_number="{warehouse}" and location_no="{location_no}" and item_number="{item_number}"'
        else:
            sql = f'select * from wms.wh_inventory_transaction where warehouse_number="{warehouse}" and location_no="{location_no}"'
        return json.loads(self.wmsdb.query_data(sql))

    def check_all_qc(self, order_id):
        """
        检查订单下商品是否全部QC
        :param order_id:
        :return:
        """
        sql = f'''SELECT * FROM ( SELECT item_number,( item_quantity - reject_quantity ) AS pack_qty,qc_quantity
        FROM wms.wh_packing_order_item
        WHERE order_id = "{order_id}" ) AS result
        WHERE  pack_qty != qc_quantity'''
        result = self.select_data_deal(sql)
        log.info(f'check_all_qc:{result}')
        assert len(result) == 0, f"{order_id}订单下商品没有全部QC"

    def select_replenish_order(self, order_id):
        """
        查询有无上次执行中断的补单数据
        :param order_id:
        :return:
        """
        sql = f'select * from wms.wh_replenish_order where order_id="{order_id}" and status=0 limit 1'
        result = self.select_data_deal(sql)
        return result

    def select_cart_orders(self, cart_no, warehouse):
        """
        查询cart下待打包的订单
        :param cart_no:
        :param warehouse:
        :return:
        """
        sql = f'''SELECT
        pick.order_id,
        oitem.item_number,
        (oitem.quantity - IFNULL(oitem.reject_quantity, 0)) as item_quantity,
        wiu.upc
        FROM
        wh_picking_order pick
        INNER JOIN wh_order_info info FORCE INDEX(key_wh_ship_status) ON
        info.order_id = pick.order_id
        INNER JOIN wh_order_item oitem ON
        oitem.order_id = pick.order_id
        INNER JOIN wh_item_upc wiu ON
        oitem.item_number = wiu.item_number
        WHERE
        (pick.replenish_tote_no = "{cart_no}"
        or pick.tote_no = "{cart_no}"
        or pick.slot_no = "{cart_no}")
        AND info.warehouse_number = "{warehouse}"
        AND info.shipping_status in (50, 51, 60, 61)
        GROUP BY
        pick.order_id ,
        oitem.item_number;'''
        cart_data = self.select_data_deal(sql)
        log.info(f'系统查出{cart_no}中待打包订单数据:{cart_data}')
        return cart_data

    def select_available_tote(self, location_type, box_size, warehouse, flag):
        """
        查询可用的tote
        :param location_type: 50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
        :param box_size: S,M,L
        :param warehouse: 仓库号
        :param flag: 0:空闲, 1:OOS, 2:question, 3:已占用
        :return:
        """
        box_size_dict = {"S": 5, "M": 10, "L": 15}
        sql = f'''SELECT location_no FROM wms.wh_storage_location  WHERE
        location_type = {location_type} AND flag = {flag} and warehouse_number = "{warehouse}"
        and tote_size = {str(box_size_dict[box_size])}'''
        cart_data = self.select_data_deal(sql)
        log.info(f'select_available_tote:{cart_data}')
        return cart_data

    def select_available_cart_no(self, warehouse, user_id):
        """
        查询未做完的任务
        :param warehouse:
        :param user_id:
        :return:
        """
        sql = f"""
        SELECT * FROM  wms.wh_picking_task WHERE status < 4 AND warehouse_number = "{warehouse}" AND in_user = {user_id}"""
        result = self.select_data_deal(sql)
        log.info(f"未完成的任务,cart_no:{result['cart_no']}")
        return result['cart_no']

    def get_wh_restock_task_info(self):
        """
        获取有补货的任务的仓库
        :return:
        """
        sql = f'''select warehouse_number, storage_type, COUNT(*) as task_qty
        from wms.wh_restock_task
        where status = 0
        group by warehouse_number, storage_type
        order by task_qty desc limit 1'''
        result = self.select_data_deal(sql)
        log.info(f'get_wh_restock_task_info:{result}')
        return result

    def get_restock_task_info(self, task_id):
        """
        check task status
        :param task_id:
        :return:
        """
        sql = f'select * from wms.wh_restock_task where rec_id={task_id}'
        result = self.select_data_deal(sql)
        log.info(f'get_restock_task_info:{result}')
        return result

    def get_restock_task_infoassert(self, item_number,warehouse_number,status):
        """
        check task status
        :param task_id:
        :return:
        """
        sql = f'select * from wms.wh_restock_task where item_number={item_number} and warehouse_number={warehouse_number} and status={status}'
        result = self.select_data_deal(sql)
        log.info(f'get_restock_task_info:{result}')
        return result

    def get_restock_pallet_item(self, task_id):
        '''
        check restock task pallet bind item
        :param task_id:
        :return:
        '''
        sql = f'select pallet_no,item_number,status from wms.wh_pallet_item where task_id={task_id}'
        result = self.select_data_deal(sql)
        log.info(f'get_restock_pallet_item:{result}')
        return result

    def get_route_check_data(self, warehouse, shipping_status, order_type, route_id=None):
        """
        获取Route Check信息
        :param warehouse:
        :param shipping_status:
        :param order_type:
        :param route_id:
        :return:
        """
        sql = f"""select
                o.delivery_dtm,
                o.route_id
            from
                wms.wh_packing_order p
            inner join wms.wh_order_info o on
                p.order_id = o.order_id
            where
                o.warehouse_number = "{warehouse}"
                and o.shipping_status = {shipping_status}
                and o.order_type = {order_type}
                and o.order_status = 0
                and o.route_id is not NULL
                and p.confirm_package_number is NULL"""
        if route_id is not None:
            route_id = tuple(route_id)
            sql = sql + f" and o.route_id in {route_id}"
        sql = sql + " limit 1"
        result = self.select_data_deal(sql)
        log.info(f'get_route_check_data:{result}')
        return result

    def get_track_num(self, order_id):
        """
        获取Tracking Number
        :param order_id:
        :return:
        """
        sql = f'''select
                tracking_num,
                seq
            from
                wms.wh_packing_package_detail pd
            inner join wms.wh_boxes wb on
                pd.box_barcode = wb.barcode
            where
                pd.order_id = "{order_id}"
                and wb.`type` != 3;'''
        result = self.wmsdb.query_data(sql)
        log.info(f'get_track_num:{result}')
        return json.loads(result)

    def get_wh_packing_order_info(self, order_id):
        """
        获取Tracking Number
        :param order_id:
        :return:
        """
        sql = f'''select * from  wms.wh_packing_order where  order_id = "{order_id}"'''
        result = self.select_data_deal(sql)
        log.info(f'get_wh_packing_order_info:{result}')
        return result

    # 盘点SQL...............................................................................................................
    def select_data_count(self, sql: str):
        """
        查询结果二次处理,query_data结果是JSON字符串,转换成list
        :param sql: 查询语句
        """
        log.info(f'sql-select_data_count:{sql}')
        ret = json.loads(self.wmsdb.query_data(sql))
        log.info(f'retffff:{ret}')
        return ret

    def update_count_data_qty(self, warehouse_number, count_id):
        """
        初始化盘点数据
        :param warehouse_number:
        :param count_id:
        :return:
        """
        sql1 = f'update wms.wh_count_info set status =0,is_dealing=0 where warehouse_number={warehouse_number} and rec_id={count_id}'
        sql2 = (f'UPDATE wms.wh_count_details SET diffvalue1 =NULL, edit_user1 = NULL, edit_dtm1 = NULL, count_qty1 = NULL, expired_dtm1 = NULL, '
                f'diffvalue2 = NULL, count_qty2 = NULL, expired_dtm2 = NULL, edit_user2 = NULL, edit_dtm2 = NULL, diffvalue3 = NULL, count_qty3 = NULL, '
                f'expired_dtm3 = NULL, edit_user3 = NULL, final_edit_user = NULL, final_edit_dtm = NULL, final_inv_qty = NULL, final_count_qty = NULL, '
                f'final_diff = NULL, final_expired_dtm = NULL, final_expired_edit_dtm = NULL, final_expired_edit_user = NULL, status = NULL, complete_user = '
                f'NULL, complete_dtm = NULL, edit_dtm3 = NULL, compulsory1 = 1, issue_type = NULL, adjustment_analysis = NULL, display_type = NULL, '
                f'count_type = NULL, deal_type = NULL, detail_status = 10 WHERE info_id= {count_id}')
        sql3 = f'DELETE from wms.wh_count_lpn_details WHERE detail_id in (select rec_id from wms.wh_count_details  where info_id ={count_id})'
        sql4 = f'DELETE from wms.wh_inventory_approval where reference_no IN (SELECT rec_id FROM wms.wh_count_details WHERE info_id={count_id});'
        result1 = self.wmsdb.update_data(sql1)
        result2 = self.wmsdb.update_data(sql2)
        result3 = self.wmsdb.update_data(sql3)
        result4 = self.wmsdb.update_data(sql4)
        log.info(f'update_count_data:{result1},{result2},{result3}')
        return result1, result2, result3

    def delete_count_info(self, warehouse_number, location_no, count_id):
        """
        作废其他的未完成任务
        :param warehouse_number:
        :param location_no
        :param count_id:
        :return:
        """
        sql = f'update wms.wh_count_info set status =5 where status<4 and warehouse_number={warehouse_number} and location_no ="{location_no}" and rec_id!={count_id};'
        result = self.wmsdb.update_data(sql)
        log.info(f'update_count_data:{result}')
        return result

    # FPO相关SQL.....................................................................................................................
    def get_sku_by_order_ids(self, order_ids):
        """
        根据so_order_id 查询wms_order_id ，获取需要添加库存的sku
        :param order_ids:
        :return:
        """
        if isinstance(order_ids, tuple) or isinstance(order_ids, list):
            if len(order_ids) > 1:
                sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type 
                            FROM wms.wh_order_item woi 
                            JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
                            JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                            JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
                            WHERE wsom.so_order_id  IN {order_ids} and woi2.shipping_status <20"""
            else:
                sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type 
                            FROM wms.wh_order_item woi 
                            JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
                            JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                            JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
                            WHERE wsom.so_order_id  = {order_ids[0]}"""
        else:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type 
                        FROM wms.wh_order_item woi 
                        JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
                        JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                        JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
                        WHERE wsom.so_order_id  = {order_ids}"""
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'sku_info={result}')
        return result

    def get_info_by_order_ids(self, order_ids):
        """
        根据so_order_id 查询wms_order_id ，获取需要添加库存的sku
        :param order_ids:
        :return:
        """
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        if len(order_ids) > 1:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wpo.tote_no 
                    FROM wms.wh_order_item woi 
                    JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
                    JOIN wms.wh_picking_order wpo ON wpo.order_id = woi.order_id 
                    JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                    JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
                    WHERE wsom.so_order_id  IN {order_ids} AND woi2.shipping_status=50 AND woi2.order_packing_num is NOT NULL"""
        else:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wpo.tote_no 
                    FROM wms.wh_order_item woi 
                    JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
                    JOIN wms.wh_picking_order wpo ON wpo.order_id = woi.order_id 
                    JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                    JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
                    WHERE wsom.so_order_id = {order_ids[0]} AND woi2.shipping_status=50 AND woi2.order_packing_num is NOT NULL"""

        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'get_sku_by_order_ids = {result}')
        return result

    def finish_picking_task(self, user_id=global_data.wms_user_id):
        """
        完成当前用户未完成的拣货任务
        :param user_id:
        :return:
        """
        sql = f'SELECT picking_task_id FROM wms.wh_picking_task WHERE status !=4 AND in_user = {user_id}'
        result = json.loads(self.wmsdb.query_data(sql))
        if len(result) > 0:
            sql1 = f'UPDATE wms.wh_picking_task SET status = 4 WHERE in_user ={user_id} AND status != 4'
            self.wmsdb.update_data(sql1)
            log.info(f'{user_id}下有未完成拣货任务, picking_task_id为:{result},设置任务已完成.')

    def get_so_order_ids(self, wms_order_ids):
        """
        根据wms_order_ids 查询so_order_id
        :param wms_order_ids:
        :return:
        """
        if '(' not in str(wms_order_ids):
            wms_order_ids = f"""({str(wms_order_ids)})"""
        sql = f"""SELECT wsom.so_order_id FROM wms.wh_so_order_mapping wsom    
                JOIN wms.wh_order_info woi2 ON woi2.source_order_id = wsom.source_order_id 
                WHERE woi2.order_id  IN {wms_order_ids};"""
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'get_so_order_ids = {result}')
        return result

    def get_mof_info_by_order_ids(self, order_ids):
        """
        根据so_order_id 查询wms_order_id ，获取需要添加库存的sku
        :param order_ids:
        :return:
        """
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        if len(order_ids) > 1:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wsom.so_order_id
                        FROM wms.wh_so_order_mapping wsom  
                        JOIN  wms.wh_order_info woi2  ON woi2.source_order_id = wsom.source_order_id 
                        JOIN wms.wh_order_item woi ON woi2.order_id = woi.order_id
                        JOIN wms.wh_item wi ON wi.item_number = woi.item_number
                        WHERE wsom.so_order_id  IN {order_ids} and woi2.order_status =0 and woi2.shipping_status=20"""
        else:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wsom.so_order_id
                        FROM wms.wh_so_order_mapping wsom  
                        JOIN  wms.wh_order_info woi2  ON woi2.source_order_id = wsom.source_order_id 
                        JOIN wms.wh_order_item woi ON woi2.order_id = woi.order_id
                        JOIN wms.wh_item wi ON wi.item_number = woi.item_number
                        WHERE wsom.so_order_id  = {order_ids[0]} and woi2.order_status =0 and woi2.shipping_status=20"""
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'sku_info = {result}')
        return result

    def get_wms_order_by_order_ids(self, order_ids):
        """
        根据so_order_id 查询wms_order_id ，获取需要添加库存的sku
        :param order_ids:
        :return:
        """
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        if len(order_ids) > 1:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wsom.so_order_id,woi2.shipping_status,wot.tag_id 
                        FROM wms.wh_so_order_mapping wsom  
                        JOIN  wms.wh_order_info woi2  ON woi2.source_order_id = wsom.source_order_id 
                        JOIN wms.wh_order_item woi ON woi2.order_id = woi.order_id
                        JOIN wms.wh_item wi ON wi.item_number = woi.item_number
                        LEFT JOIN wms.wh_order_tag wot ON wot.order_id = woi2.order_id
                        WHERE wsom.so_order_id  IN {order_ids} and woi2.order_status =0"""
        else:
            sql = f"""SELECT woi.item_number, woi2.warehouse_number,wi.storage_type, woi2.order_id, woi2.order_type,wsom.so_order_id,woi2.shipping_status,wot.tag_id
                        FROM wms.wh_so_order_mapping wsom  
                        JOIN  wms.wh_order_info woi2  ON woi2.source_order_id = wsom.source_order_id 
                        JOIN wms.wh_order_item woi ON woi2.order_id = woi.order_id
                        JOIN wms.wh_item wi ON wi.item_number = woi.item_number
                        LEFT JOIN wms.wh_order_tag wot ON wot.order_id = woi2.order_id
                        WHERE wsom.so_order_id  = {order_ids[0]} and woi2.order_status =0"""
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'sku_info = {result}')
        return result

    def get_wait_pack_order_info(self, order_ids):
        """
        根据so_order_id 查询wms_order_id ，获取需要添加库存的sku
        :param order_ids:
        :return:
        """
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        sql = f"""SELECT woi2.warehouse_number,wi.storage_type, woi2.order_id, wpo.tote_no 
        FROM wms.wh_order_item woi 
        JOIN wms.wh_order_info woi2 ON woi2.order_id = woi.order_id
        JOIN wms.wh_picking_order wpo ON wpo.order_id = woi.order_id 
        JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
        JOIN wms.wh_item wi ON wi.item_number = woi.item_number 
        WHERE wsom.so_order_id  IN {order_ids}"""
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'get_sku_by_order_ids:{result}')
        unique_data = {d['order_id']: d for d in result}.values()
        return unique_data

    def update_wh_config_date(self, warehouse_number, delivery_date):
        """
        修改对应仓库的系统发货日期(修改后需要更新redis数值)
        :param warehouse_number:
        :param delivery_date:
        :return:
        """
        sql = f"""UPDATE wms.wh_config x SET x.config_value = "{delivery_date}" 
        WHERE x.config_key in ("wms:warehouse:sys_date","wms:order:delivery_date") 
        and x.warehouse_number ={warehouse_number}"""
        self.wmsdb.update_data(sql)
        log.info(f'修改对应仓库warehouse_number:{warehouse_number}的系统发货日期为{delivery_date}')

    def get_wh_config_date(self, warehouse_number, key, info='rec_id'):
        """
        获取对应key的信息
        :param warehouse_number:
        :param key:
        :param info:
        :return:
        """
        sql = f"""SELECT {info} FROM wms.wh_config x WHERE x.config_key = '{key}' and x.warehouse_number ={warehouse_number}"""
        result = json.loads(self.wmsdb.query_data(sql))[0]
        if info != '*':
            log.info(f'获取仓库:{warehouse_number}下,{key}的{info}为{result}')
            return result[info]
        else:
            return result

    def select_wh_config_by_key(self, warehouse_number, config_key):
        """
        查询对应仓库的配置
        :param warehouse_number:
        :param config_key:
        :return:
        """
        sql = f"""select x.config_value from wms.wh_config x WHERE x.config_key = "{config_key}" and x.warehouse_number ={warehouse_number}"""
        result = self.select_data_deal(sql)
        log.info(f'获取对应仓库warehouse_number:{warehouse_number}的{config_key}配置为{result}')
        result = json.loads(result.get("config_value", {}))
        return result

    def get_wms_order_ids(self, order_ids, shipping_status=None):
        """
        获取wms订单ID
        :param shipping_status:
        :param order_ids:
        :return:
        """
        if '(' not in str(order_ids):
            order_ids = f"""({str(order_ids)})"""
        if len(order_ids) > 1:
            if shipping_status:
                sql = f"""SELECT woi2.order_id  FROM wms.wh_order_info woi2   
                        JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                        WHERE wsom.so_order_id  IN {order_ids} and woi2.shipping_status={shipping_status}"""
            else:
                sql = f"""SELECT woi2.order_id  FROM wms.wh_order_info woi2   
                                    JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                                    WHERE wsom.so_order_id  IN {order_ids}"""
        else:
            if shipping_status:
                sql = f"""SELECT woi2.order_id  FROM wms.wh_order_info woi2   
                        JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                        WHERE wsom.so_order_id = {order_ids[0]} and woi2.shipping_status={shipping_status}"""
            else:
                sql = f"""SELECT woi2.order_id  FROM wms.wh_order_info woi2   
                                    JOIN wms.wh_so_order_mapping wsom ON woi2.source_order_id = wsom.source_order_id 
                                    WHERE wsom.so_order_id  = {order_ids[0]}"""
        log.info(f'get_wms_order_ids:{sql}')
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'get_wms_order_ids:{result}')
        return result

    def update_wh_config(self, warehouse, config_key, config_value):
        """
        按key更新仓库配置
        """
        sql = f"""UPDATE wms.wh_config x SET x.config_value = "{config_value}" WHERE x.config_key = "{config_key}" and x.warehouse_number ={warehouse}"""
        self.wmsdb.update_data(sql)
        log.info(f'修改对应仓库warehouse_number:{warehouse}的{config_key}为{config_value}')

    def get_item_info(self, item_number):
        """
        获取item信息
        :param item_number:
        :return:
        """
        sql = f"""select * from wms.wh_item where item_number="{item_number}" limit 1"""
        result = self.select_data_deal(sql)
        log.info(f'get_item_info:{result}')
        return result

    def select_wms_order_info(self, order_id):
        """
        校验订单状态
        :param order_id: wms订单ID
        :return:
        """
        sql = f"select * from wms.wh_order_info where order_id='{order_id}'"
        return self.select_data_deal(sql)

    def get_batch_ids(self, wms_orders):
        """
        获取batch ID
        :param wms_orders:
        :return:
        """
        if len(wms_orders) == 1:
            wms_orders = wms_orders[0]
        if '(' not in str(wms_orders):
            wms_orders = f"""({str(wms_orders)})"""
        sql = f'''SELECT batch_id FROM wms.wh_batch_order WHERE wh_batch_order.order_id in {wms_orders}'''
        result = self.select_data_deal(sql)
        if isinstance(result, list):
            batch_ids = tuple(set(item['batch_id'] for item in result))
        else:
            batch_ids = result['batch_id'],
        log.info(f"订单:{wms_orders}\n对应的batch_id为{batch_ids}")
        return batch_ids

    def get_order_types(self, wms_orders):
        """
        获取order_types
        :param wms_orders:
        :return:
        """
        if '(' not in str(wms_orders):
            wms_orders = f"""({str(wms_orders)})"""
        sql = f'''SELECT order_type FROM wms.wh_batch_order WHERE wh_batch_order.order_id in {wms_orders}'''
        result = self.select_data_deal(sql)
        if isinstance(result, list):
            order_types = tuple(set(item['order_type'] for item in result))
        else:
            order_types = result['order_type'],
        log.info(f"订单:{wms_orders}对应的order_type为{order_types}")
        return order_types

    def get_wms_order_info(self, wms_order):
        """
        获取wms_order_info
        :param wms_order:
        :return:
        """
        sql = f'''SELECT woi2.warehouse_number,woi2.order_id, woi2.order_type 
        FROM wms.wh_order_info woi2  WHERE woi2.order_id = {wms_order}'''
        result = json.loads(self.wmsdb.query_data(sql))[0]
        log.info(f"订单:{wms_order}对应的信息为{result}")
        return result

    def get_wms_order_item(self, order_id):
        """
        获取Order下的Pick Item信息

        Args:
            order_id (_type_): wms order id
        """
        sql = f'''select * from wms.wh_order_item where order_id = {order_id}'''
        return json.loads(self.wmsdb.query_data(sql))

    def get_invoice_order(self, invoice_no):
        """
        _summary_

        Args:
            invoice_no (_type_): _description_
        """
        sql = f'select * from wms.wh_order_info  where invoice_no ="{invoice_no}"'
        return json.loads(self.wmsdb.query_data(sql))

    def update_batch_priority(self, batch_ids):
        """
        更新batch ID优先级
        :param batch_ids:
        :return:
        """
        sql1 = '''UPDATE wms.wh_batch SET batch_priority='2147483646' WHERE batch_priority ='2147483647' AND status =1'''
        self.wmsdb.update_data(sql1)
        log.info("调整当前系统的batch优先级...")
        if '(' not in str(batch_ids):
            batch_ids = f"""({str(batch_ids)})"""
        if len(batch_ids) > 1:
            sql2 = f'''UPDATE wms.wh_batch SET batch_priority='2147483647' WHERE batch_id in {batch_ids} '''
        else:
            sql2 = f'''UPDATE wms.wh_batch SET batch_priority='2147483647' WHERE batch_id = {batch_ids[0]} '''
        print(sql2)
        self.wmsdb.update_data(sql2)
        log.info(f"将Batch:{batch_ids}的优先级调整为:2147483647")

    def set_route_check_status(self, wms_orders):
        """
        根据wms order id, 更新Route Check状态
        :param wms_orders:
        :return:
        """
        if len(wms_orders) == 1:
            wms_orders = wms_orders[0]
        if '(' not in str(wms_orders):
            wms_orders = f"""({str(wms_orders)})"""
        sql1 = f'''UPDATE wh_packing_order SET confirm_package_number =1 WHERE order_id IN {wms_orders}'''
        sql2 = f'''UPDATE wh_packing_order SET confirm_package  =1 WHERE order_id IN {wms_orders}'''
        self.wmsdb.update_data(sql1)
        self.wmsdb.update_data(sql2)

        log.info(f"Route Check状态已更新, 订单为:{wms_orders}")

    def clean_wms_data(self, delivery_id):
        sql1 = f"""DELETE FROM wh_hook_task WHERE event_key ='OUTBOUND_ORDERS_DOWN_ORDER' and reference_id IN ({delivery_id});"""
        sql2 = f"""DELETE FROM wh_hook_log WHERE event_key ='OUTBOUND_ORDERS_DOWN_ORDER' AND reference_id IN ({delivery_id});"""

        sql3 = f"""DELETE soi.* FROM wms.wh_sale_order_item soi JOIN wms.wh_sale_order woi ON woi.order_id = soi.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql4 = f"""DELETE a.* FROM wms.wh_order_item a JOIN wms.wh_order_info b ON a.order_id = b.order_id WHERE b.delivery_id IN ({delivery_id});"""

        sql5 = f"""DELETE wpi.* FROM wms.wh_preoccupancy_item wpi JOIN wms.wh_order_info woi ON woi.order_id = wpi.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql6 = f"""DELETE wpl.* FROM wms.wh_preoccupancy_log wpl JOIN wms.wh_order_info woi ON woi.order_id = wpl.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        sql7 = f"""DELETE wbo.* FROM wms.wh_batch_order wbo JOIN wms.wh_order_info woi ON woi.order_id = wbo.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql8 = f"""DELETE wboi.* FROM wms.wh_batch_order_item wboi JOIN wms.wh_order_info woi ON woi.order_id = wboi.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        sql9 = f"""DELETE wpo.* FROM wms.wh_picking_order wpo JOIN wms.wh_order_info woi ON woi.order_id = wpo.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql10 = f"""DELETE wpoi.*FROM wms.wh_picking_order_item wpoi JOIN wms.wh_order_info woi ON woi.order_id = wpoi.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        sql11 = f"""DELETE wpo.* FROM wms.wh_packing_order wpo JOIN wms.wh_order_info woi ON woi.order_id = wpo.order_id  WHERE woi.delivery_id IN ({delivery_id});"""
        sql12 = f"""DELETE wpoi.*FROM wms.wh_packing_order_item wpoi JOIN wms.wh_order_info woi ON woi.order_id = wpoi.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql13 = f"""DELETE wppd.* FROM wms.wh_packing_package_detail wppd JOIN wms.wh_order_info woi ON woi.order_id = wppd.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        sql14 = f"""DELETE wso.* FROM wms.wh_sorting_order wso JOIN wms.wh_order_info woi ON woi.order_id = wso.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql15 = f"""DELETE wsoi.* FROM wms.wh_sorting_order_item wsoi JOIN wms.wh_order_info woi ON woi.order_id = wsoi.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        sql16 = f"""DELETE wsom.* FROM wms.wh_so_order_mapping wsom JOIN wms.wh_order_info woi ON woi.source_order_id = wsom.source_order_id WHERE woi.delivery_id  IN ({delivery_id});"""

        sql19 = f"""DELETE wro.* FROM wms.wh_replenish_order wro JOIN wms.wh_order_info woi ON woi.order_id = wro.order_id WHERE woi.delivery_id IN ({delivery_id});"""
        sql20 = f"""DELETE wroi.* FROM wms.wh_replenish_order_item wroi JOIN wms.wh_order_info woi ON woi.order_id = wroi.order_id WHERE woi.delivery_id IN ({delivery_id});"""

        #sowing
        sql21 = f"""DELETE wswi.* FROM wms.wh_sowing_wave_item wswi 
                    JOIN wms.wh_sowing_wave wsw ON wsw.reference_id =wswi.reference_id 
                    JOIN wms.wh_order_info woi ON woi.delivery_dtm = wsw.delivery_dtm
                    WHERE woi.delivery_id ={delivery_id};"""
        sql22 = f"""DELETE wswpt.* FROM wms.wh_sowing_wave_picking_task wswpt
                    JOIN wms.wh_sowing_wave wsw ON wsw.reference_id =wswpt.reference_id 
                    JOIN wms.wh_order_info woi ON woi.delivery_dtm = wsw.delivery_dtm
                    WHERE woi.delivery_id = {delivery_id};"""
        sql23 = f"""DELETE wsw.* FROM wms.wh_sowing_wave wsw
                    JOIN wms.wh_order_info woi ON woi.delivery_dtm = wsw.delivery_dtm
                    WHERE woi.delivery_id = {delivery_id};"""
        #最后删除批次数据
        sql17 = f"""DELETE FROM wms.wh_order_info WHERE delivery_id IN ({delivery_id});"""
        sql18 = f"""DELETE FROM wms.wh_sale_order WHERE delivery_id IN ({delivery_id});"""

        self.wmsdb.update_data(sql1)
        self.wmsdb.update_data(sql2)
        self.wmsdb.update_data(sql3)
        self.wmsdb.update_data(sql4)
        self.wmsdb.update_data(sql5)
        self.wmsdb.update_data(sql6)
        self.wmsdb.update_data(sql7)
        self.wmsdb.update_data(sql8)
        self.wmsdb.update_data(sql9)
        self.wmsdb.update_data(sql10)
        self.wmsdb.update_data(sql11)
        self.wmsdb.update_data(sql12)
        self.wmsdb.update_data(sql13)
        self.wmsdb.update_data(sql14)
        self.wmsdb.update_data(sql15)
        self.wmsdb.update_data(sql16)
        self.wmsdb.update_data(sql19)
        self.wmsdb.update_data(sql20)
        self.wmsdb.update_data(sql21)
        self.wmsdb.update_data(sql22)
        self.wmsdb.update_data(sql23)
        self.wmsdb.update_data(sql17)
        self.wmsdb.update_data(sql18)
        log.info("wms数据清理完成")

        return

    def get_receive_task_no(self, warehouse_number, reference_no):
        """
        获取receive_task_no信息
        """
        sql = f"SELECT receive_task_no, status from wms.wh_inbound_receive_task  where warehouse_number ='{warehouse_number}' AND reference_no='{reference_no}' order  by  rec_id DESC ;"
        result = self.select_data_deal(sql)
        log.info(f'get_receive_task_no:{result}')
        return result


    def get_receive_task_status(self, warehouse_number, receive_task_no):
        """
        获取receive_task的status信息
        """
        sql = f"SELECT receive_task_no, status from wms.wh_inbound_receive_task  where warehouse_number ='{warehouse_number}' AND receive_task_no='{receive_task_no}' order  by  rec_id DESC ;"
        result = self.select_data_deal(sql)
        log.info(f'get_receive_task_status:{result}')
        return result
    def get_receive_quantity(self, reference_no, item_number):
        """
        获取receive_quantity信息
        """
        sql = f"select receive_quantity  from wms.wh_inbound_order_item   where reference_no ='{reference_no}' and item_number='{item_number}';"
        result = self.select_data_deal(sql)
        log.info(f'get_receive_quantity:{result}')
        return result

    def get_receive_batch(self, receive_task_no, item_number):
        """
        获取收货 batch 信息
        """
        sql = f"SELECT receive_task_no from wms.wh_inbound_batch  where receive_task_no='{receive_task_no}' and item_number='{item_number}';"
        result = self.select_data_deal(sql)
        print(sql)
        log.info(f'get_receive_batch:{result}')
        return result

    def get_inbound_order_info(self, reference_no):
        """
        获取inbound order 
        """
        sql = f"SELECT * from wms.wh_inbound_order where reference_no='{reference_no}'"
        result = self.select_data_deal(sql)
        log.info(f'get_inbound_order:{result}')
        return result

    def get_putaway_task(self, warehouse_number, reference_no, reference_type, status, inbound_batch_id):
        """
        获取收货完成后生成的上架任务信息
        """
        sql = f"""select
                *
            from
                wms.wh_inbound_putaway_task wipt
            where
                warehouse_number = "{warehouse_number}"
                and reference_no = "{reference_no}"
                and reference_type = {reference_type}
                and status = {status}
                and label_no in (
                select
                    master_lpn_no
                from
                    wms.wh_inbound_batch wib
                where
                    rec_id = {inbound_batch_id})"""
        result = self.select_data_deal(sql)
        log.info(f'get_wh_inbound_putaway_task:{result}')
        return result

    def get_location_item(self, location_no, warehouse_number, item_number):
        """
        获取库位和商品绑定信息
        """
        sql = f'''select rec_id, warehouse_number,location_no, item_number, expire_dtm from wh_storage_location_item where location_no="{location_no}" and warehouse_number="{warehouse_number}" and item_number="{item_number}";'''
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'get_location_item:{result}')
        return result

    def get_pallet_no(self, warehouse_number):
        """
        获取pallet_no
        """
        sql = f"select location_no from wms.wh_storage_location   where   location_type = 38  AND warehouse_number='{warehouse_number}'  and flag  ='0'  and flag_extend is Null limit 1;"
        result = self.select_data_deal(sql)
        print(sql)
        log.info(f'get_pallet_no:{result}')
        return result

    def get_mlpn_no(self, warehouse_number, reference_no, item_number):
        """
        获取待上架的mlpn
        """
        sql = f"select label_no  from wms.wh_inbound_putaway_task  where warehouse_number ={warehouse_number}  and status='10' and reference_no={reference_no}  and item_number={item_number} order by  rec_id  desc  limit 1;"
        result = self.select_data_deal(sql)
        print(sql)
        log.info(f'get_mlpn_no:{result}')
        return result

    def get_as_attribute(self, item_number):
        """
        获取商品AS的属性
        """
        sql = f"select * from wms.wh_item_attribute  where item_number={item_number} and warehouse_number ='25' and attribute ='200';"
        result = self.select_data_deal(sql)
        print(sql)
        log.info(f'get_as_attribute:{result}')
        return result

    def get_geek_attribute(self, item_number):
        """
        获取商品AS的属性
        """
        sql = f"select * from wms.wh_item_attribute  where item_number={item_number} and warehouse_number ='41' and attribute ='300';"
        result = self.select_data_deal(sql)
        print(sql)
        log.info(f'get_geek_attribute:{result}')
        return result




    def get_warehouse_support_lpn_config(self, warehouse_number):
        sql = f"select config_value from wms.wh_config  where config_key ='support_lpn' and warehouse_number ={warehouse_number};"
        return self.select_data_deal(sql)

    def get_automation_order_no(self, warehouse_number, mlpn_no):
        sql = f"select automation_order_no from wms.wh_inbound_putaway_task where warehouse_number ={warehouse_number}  and  label_no ='{mlpn_no}';"
        return self.select_data_deal(sql)

    def get_replenlist_id(self, mlpn_no, warehouse_number):
        sql = f"select  ipt.putaway_no,iptd.rec_id from wh_inbound_putaway_task ipt join wh_inbound_putaway_task_detail iptd on ipt.rec_id=iptd.task_id  where ipt.label_no ='{mlpn_no}' and ipt.warehouse_number ={warehouse_number};"
        return self.select_data_deal(sql)

    def get_inventory_batch_no(self, warehouse_number, lpn_no):
        sql = f"select  batch_no from  wh_lpn_inventory_transaction  where  warehouse_number={warehouse_number}  and  lpn_no='{lpn_no}'; "
        return self.select_data_deal(sql)



    def get_plan_qty(self,warehouse_number,lpn_no):
        sql=f"select original_quantity from wh_lpn_info where warehouse_number={warehouse_number}  and  lpn_no ='{lpn_no}'; "
        return  self.select_data_deal(sql)



    # 库存相关查询
    def get_inventory_transaction(self, warehouse_number, location_no, item_number):
        sql = f"select * from wh_inventory_transaction where warehouse_number='{warehouse_number}' and location_no='{location_no}' and item_number ='{item_number}';"
        return self.select_data_deal(sql)

    def get_inventory_batch(self, item_number, reference_no):
        sql = f"select * from wh_inventory_batch where item_number='{item_number}' and reference_no={reference_no} order by 1 desc limit 1;"
        return self.select_data_deal(sql)

    def get_batch_inventory_transaction(self, warehouse_number, location_no, item_number, batch_no=None):
        if batch_no is not None:
            sql = f"select * from wh_batch_inventory_transaction where warehouse_number='{warehouse_number}' and batch_no='{batch_no}' and location_no='{location_no}' and item_number ='{item_number}';"
        else:
            sql = f"select * from wh_batch_inventory_transaction where warehouse_number='{warehouse_number}' and location_no='{location_no}' and item_number ='{item_number}';"
        return self.select_data_deal(sql)

    def get_lpn_inventory_transaction(self, warehouse_number, location_no, item_number):
        sql = f"select * from wh_lpn_inventory_transaction where warehouse_number='{warehouse_number}' and location_no='{location_no}' and item_number ='{item_number}';"
        return self.select_data_deal(sql)

    def get_batch_inventory_transaction_log(self, warehouse_number, location_no, item_number, reference_id, batch_no, inventory_type=[], in_dtm=None):
        sql = f"select * from wh_batch_inventory_transaction_log where warehouse_number='{warehouse_number}' and reference_id={reference_id} and location_no='{location_no}' and batch_no='{batch_no}' and item_number='{item_number}'"
        if inventory_type:
            sql += f" and inventory_type in ({', '.join(map(str, inventory_type))})"
        if in_dtm:
            sql += f" and in_dtm >= {in_dtm}"
        sql += " order by 1 desc"
        return self.select_data_deal(sql)

    def get_inventory_transaction_log(self, warehouse_number, location_no, item_number, reference_id, inventory_type=[], in_dtm=None):
        sql = f"select * from wh_inventory_transaction_log where warehouse_number='{warehouse_number}' and reference_id={reference_id} and (from_location='{location_no}' or to_location='{location_no}') and item_number='{item_number}'"
        if inventory_type:
            sql += f" and type_code in ({', '.join(map(str, inventory_type))})"
        if in_dtm:
            sql += f" and in_dtm >= {in_dtm}"
        sql += " order by 1 desc"
        return self.select_data_deal(sql)

    def update_tote_status(self, warehouse_number, toteNo):
        sql1 = f"""UPDATE wms.wh_storage_location SET flag =0 WHERE warehouse_number={warehouse_number} AND location_no ={toteNo}"""
        self.wmsdb.update_data(sql1)
        log.info("tote重置为可用")
        return

    def check_downorder_wms(self, delivery_id):
        sql = f"""select info.invoice_no,item.item_number,sum(item.quantity) as itemQuantity
                from wh_order_info info
                join wh_order_item item on item.order_id = info.order_id
                where delivery_id={delivery_id}
                and info.order_status = 0
                and ( item.reject_quantity is null or item.reject_quantity = 0 )  and if(info.order_type = 0,info.source_order_id = info.order_id, 1 = 1)
                group by info.order_id,info.invoice_no,item.item_number
                ORDER BY CAST(item.item_number AS UNSIGNED),info.invoice_no"""

        result = self.select_data_deal(sql)
        log.info(f'wms_order_qty:{result}')
        return result

    def get_vendor_order(self, order_no):
        """
        获取供应商订单信息
        """
        sql = f"select * from wh_vendor_return_order where return_no={order_no}"
        result = self.select_data_deal(sql)
        log.info(f'get_vendor_order:{result}')
        return result

    def get_vendor_orderitem(self, order_no):
        """
        获取供应商订单明细信息
        """
        sql = f"select * from wh_vendor_return_order_item where return_no={order_no}"
        result = self.select_data_deal(sql)
        log.info(f'get_vendor_orderitem:{result}')
        return result

    def get_vendor_task(self, order_no):
        """
        获取预占后任务信息
        """
        sql = f"select rec_id, work_type, item_level, location_no, picking_way, picking_pallet_no from wh_simple_pick_task where reference_no='{order_no}'"
        log.info(f'sqlff:{sql}')
        result = self.select_data_count(sql)
        log.info(f'get_vendor_task:{result}')
        return result

    def get_commonpick_task(self, order_no):
        """
        获取预占后任务信息
        """
        sql = f"select rec_id, work_type, location_no, pallet_no from wh_common_pick_task_dispatch where reference_no='{order_no}'"
        log.info(f'sqlff:{sql}')
        result = self.select_data_count(sql)
        log.info(f'get_vendor_task:{result}')
        return result

    def get_batch_preoccupancy_inventory(self, reference_no):
        """
        获取预占后batch预占库存信息
        return list
        """
        sql = f"select * from wh_batch_preoccupancy_inventory where reference_no='2_{reference_no}'"
        result = self.select_data_count(sql)
        log.info(f'wh_batch_preoccupancy_inventory:{result}')
        return result

    def update_vendor_taskpriority(self, order_no):
        '''
        调整任务优先级
        '''
        sql = f'''update wms.wh_vendor_return_task set task_priority=758790000 where task_priority >= 999999777'''
        self.wmsdb.update_data(sql)
        sql = self.wmsdb.update_data(f"update wh_vendor_return_task set task_priority = '999999777' where return_no='{order_no}'")
        log.info(f'update_vendor_taskpriority:{sql}')

    def get_wh_vendor_storage_location_info(self, warehouse, location_type, flag=0, info='*'):
        """
        校验对应仓库下的tote状态
        :param warehouse: 仓库ID
        :param location_type: 50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
        :param flag:0:空闲, 1:OOS, 2:question, 3:已占用
        :param info:
        :return:
        """
        sql = f'select {info} from wms.wh_storage_location where warehouse_number="{warehouse}" and location_type={location_type} and flag={flag} and flag_extend is Null limit 1'
        result = self.select_data_deal(sql)
        log.info(f'get_wh_storage_location_info:{result}')
        if info == '*':
            return result
        else:
            return result[info]
    def pick_up_info(self, order_no, info='*'):
        sql = f"select {info} from wh_pick_up_info where return_no={order_no}"
        result = self.select_data_deal(sql)
        log.info(f'get_wh_storage_location_info:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def pick_up_pallet(self, order_no, info='*'):
        sql = f"select {info} from wh_pick_up_pallet where return_no={order_no}"
        result = self.select_data_deal(sql)
        log.info(f'get_wh_storage_location_info:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def get_repack_data(self, warehouse, delivery_date):
        """
        获取repack任务信息
        """
        sql = f"SELECT task_id FROM wms.mes_repack_task mrt WHERE warehouse_number ='{warehouse}' AND delivery_date= UNIX_TIMESTAMP('{delivery_date}')"
        result = self.select_data_deal(sql)
        log.info(f'get_repack_data:{result}')
        return result


    def get_transship_task(self, warehouse_number, ts_number):
        """
        获取未做的transship_task信息
        """
        sql = f"select * from wms.wh_transship_task wtt  where warehouse_number ='{warehouse_number}' and  ts_number = {ts_number} and status=0"
        result = json.loads(self.wmsdb.query_data(sql))
        log.info(f'transship_task_data:{result}')
        return result

    def get_transship_order(self, ts_number):
        """
        获取调拨单信息
        """
        sql = f"select * from wms.wh_transship_order where ts_number={ts_number}"
        result = self.select_data_deal(sql)
        log.info(f'transship_order:{result}')
        return result

    def update_transship_order_item(self, ts_number):
        """
        更新transship_order_item预占规则
        """
        sql = f"update wms.wh_transship_order_item set outbound_policy = 1 where ts_number = {ts_number}"
        self.wmsdb.update_data(sql)

    def update_transship_order_status(self, status, ts_number):
        """
        更新transship_order_item预占规则
        """
        sql = f"update wms.wh_transship_order set status = {status} where ts_number = {ts_number}"
        self.wmsdb.update_data(sql)

    def update_transship_task_priority(self, warehouse_number):
        """
        更新transship_task优先级
        """
        sql = f'''update wms.wh_transship_task set task_priority=9999939999 where warehouse_number={warehouse_number} and type=3 and task_priority >= 9999940000 and status =0'''
        self.wmsdb.update_data(sql)

    def get_transship_ob_pallet(self, ts_number):
        """
        获取调拨单的出库PALLET信息
        """
        sql = f'''select * from wms.wh_transship_batch_receive  where ts_number ="{ts_number}" limit 1'''
        result = self.select_data_deal(sql)
        log.info(f'transship_order_ob_pallet:{result}')
        return result

    def get_repack_status(self, task_id, item_number, status=None, info='*'):
        """

        :param task_id:
        :param Item_number:
        :return:
        """
        sql = f"SELECT {info} FROM wms.mes_repack_item WHERE task_id = {task_id} AND item_number ={item_number} ORDER BY 1 DESC LIMIT 1;"
        result = self.select_data_deal(sql)
        log.info(f'repack任务:{task_id}的Item:{item_number}status:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def get_putaway_info(self, task_id, reference_no, item_number, info='*'):
        """

        :param warehouse_number:
        :param item_number:
        :param po_number:
        :param info:
        :return:
        """
        sql = f"SELECT {info} FROM wms.wh_inbound_putaway_task WHERE rec_id = {task_id} AND reference_no = '{reference_no}' AND  item_number ='{item_number}' ORDER BY 1 DESC LIMIT 1;"
        result = self.select_data_deal(sql)
        log.info(f'repack上架任务:{reference_no}的Item:{item_number}status:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def update_putaway_task(self,status,user_id):
        sql = f"UPDATE wh_inbound_putaway_task SET status = {status} WHERE status = '20' AND deal_user_id = {user_id};"
        self.wmsdb.update_data(sql)

    def get_putawaydetail_info(self, task_id, info='*'):
        """
        :param reference_no:
        :param item_number:
        :param info:
        :return:
        """
        sql = f"SELECT {info} FROM wms.wh_inbound_putaway_task_detail WHERE task_id={task_id} ORDER BY 1 DESC  LIMIT 1;"
        result = self.select_data_deal(sql)
        log.info(f'repack上架任务明细:{task_id}status:{result}')
        if info == '*':
            return result
        else:
            return result[info]

    def get_order_info(self, order_id, info=None):
        """
        :param order_id:
        :param info:
        :return:
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"SELECT {info_str} FROM wms.wh_order_info WHERE order_id={order_id};"
        result = self.select_data_deal(sql)
        log.info(f'订单信息:{order_id}:{result}')
        return result

    def get_so_order_mapping(self, order_id, info=None):
        """
        根据so订单获取wms订单信息
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = (f"""
                SELECT DISTINCT {info_str} FROM wms.wh_order_info woi 
                JOIN wms.wh_so_order_mapping wsom ON wsom.source_order_id =woi.source_order_id
                JOIN wms.wh_order_item woi2 ON woi2.order_id = woi.order_id
                JOIN wms.wh_order_tag wot ON wot.order_id = woi.order_id
                WHERE wsom.so_order_id ={order_id};""")
        result = self.select_data_deal(sql)

        if isinstance(result, list) and result:
            # 处理非空列表
            log.info(f'wms订单信息:{order_id}: {result}')
            first_result = result[0]
            return first_result
        elif isinstance(result, dict) and result:
            # 处理非空字典
            log.info(f'wms订单信息:{order_id}: {result}')
            return result
        else:
            log.info(f'wms订单信息:{order_id} 无结果')
            return None

    def get_so_order_region(self, order_id, info=None):
        """
        根据so订单获取wms订单信息
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = (f"""
            SELECT {info_str} FROM wms.wh_order_extend woe 
            JOIN wms.wh_order_info woi ON woi.order_id =woe.order_id
            JOIN wms.wh_so_order_mapping wsom ON wsom.source_order_id =woi.source_order_id
            WHERE wsom.so_order_id = {order_id};""")
        result = self.select_data_deal(sql)

        if isinstance(result, list) and result:
            # 处理非空列表
            log.info(f'wms订单region:{order_id}: {result}')
            first_result = result[0]
            return first_result
        elif isinstance(result, dict) and result:
            # 处理非空字典
            log.info(f'wms订单region:{order_id}: {result}')
            return result
        else:
            log.info(f'wms订单region:{order_id} 无结果')
            return None

    def get_order_as_task(self, order_id, info=None):
        """
        获取订单automation表相关信息
        :return:
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"SELECT {info_str} FROM wms.wh_automation_pick_task WHERE order_id={order_id} order by rec_id desc;"
        result = self.select_data_deal(sql)
        log.info(f'AS订单Task:{order_id}:{result}')
        if isinstance(result, dict):
            result = [result]
        elif not isinstance(result, list):
            raise ValueError("返回的数据类型不正确")
        return result

    def get_order_as_list(self, pick_list_id, info=None):
        """
        获取订单automation表相关信息
        :return:
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"SELECT {info_str} FROM wms.wh_automation_pick_list WHERE rec_id={pick_list_id};"
        result = self.select_data_deal(sql)
        log.info(f'AS拣货任务:{pick_list_id}:{result}')
        if isinstance(result, dict):
            result = [result]
        elif not isinstance(result, list):
            raise ValueError("返回的数据类型不正确")
        return result

    def get_order_as_task_detail(self, pick_list_id, info=None):
        """
        获取wh_automation_pick_task_detail id
        :return:
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"SELECT {info_str} FROM wms.wh_automation_pick_task_detail WHERE pick_list_rec_id={pick_list_id};"
        result = self.select_data_deal(sql)
        log.info(f'AS拣货任务明细:{pick_list_id}:{result}')
        if isinstance(result, dict):
            result = [result]
        elif not isinstance(result, list):
            raise ValueError("返回的数据类型不正确")

        return result

    def update_automation_pick_list(self, status, pick_list_id):
        """
        批量更新订单状态
        :param order_status:
        :param delivery_dtm:
        :return:
        """
        sql = f"UPDATE wms.wh_automation_pick_list SET status ={status},container_id = Null,cart_no = Null WHERE rec_id = {pick_list_id}"
        self.wmsdb.update_data(sql)


    def query_chute(self,warehouse_number):
        sql = f"select chute_id  from wms.wh_automation_chute where warehouse_number ={warehouse_number} ORDER BY RAND() LIMIT 1"
        result = self.select_data_deal(sql)
        return result

    def update_pick_list_id(self, pick_list_id):
        """
        重置数据Pick List id
        :param pick_list_id:
        :return:
        """
        sql1 = f"SELECT rec_id FROM wms.wh_automation_pick_list wapl ORDER BY rec_id DESC LIMIT 1;"
        result = self.select_data_deal(sql1)['rec_id']
        new_pick_list = result + 1
        sql2 = f"UPDATE wh_automation_pick_list SET rec_id = {new_pick_list} WHERE rec_id ={pick_list_id};"
        sql3 = f"UPDATE wh_automation_pick_task SET pick_list_rec_id = {new_pick_list} WHERE pick_list_rec_id = {pick_list_id};"
        sql4 = f"UPDATE wh_automation_pick_task_detail SET pick_list_rec_id = {new_pick_list} WHERE pick_list_rec_id={pick_list_id};"
        self.wmsdb.update_data(sql2)
        self.wmsdb.update_data(sql3)
        self.wmsdb.update_data(sql4)

    def delete_wh_automation_pick_result(self, pick_list_id):
        """
        批量更新订单状态
        :param order_status:
        :param delivery_dtm:
        :return:
        """
        sql = f"DELETE FROM wms.wh_automation_pick_result where pick_list_rec_id ={pick_list_id};"
        self.wmsdb.update_data(sql)

    def delete_wh_picking_order(self,order_id):
        sql = f"DELETE FROM wms.delete_wh_picking_order where order_id ={order_id};"
        self.wmsdb.update_data(sql)

    def update_order(self, order_status, delivery_dtm, warehouse_number):
        """
        批量更新订单状态
        :param order_status:
        :param delivery_dtm:
        :return:
        """
        sql = f"UPDATE wms.wh_order_info SET order_status = '{order_status}' WHERE delivery_dtm = '{delivery_dtm}' AND warehouse_number = '{warehouse_number}' AND order_type IN(0,4);"
        self.wmsdb.update_data(sql)

    def update_autostore_config(self, start_dtm, warehouse_number):
        """
        更新automation配置，发送Pick List
        :return:
        """
        json_data = {
            "active": True,
            "agingOrderMaxTime": 600,
            "oneItemOneOrderVolumeLimit": 1000,
            "oneItemVolumeMaxLimit": 2500,
            "oneItemVolumeMinLimit": 1,
            "pickCartConfigList": [{
                "count": 4,
                "size": 10,
                "weight": 10
            }, {
                "count": 2,
                "size": 15,
                "weight": 15
            }, {
                "size": 25,
                "count": 0,
                "weight": 25
            },
                {
                    "size": 30,
                    "count": 0,
                    "weight": 30
                }
            ],
            "startDeliveryDate": start_dtm,
            "supplier": "AutoStore",
            "sendDetailIdDeliveryDate": "2000-11-29",
            "supportOneItem": False,
            "oosLockInventory": True,
            "sendSkipWaveIds": [],
            "cancelOrderSetKpiComplete": False,
            "restockCallActive": True,
            "mailOrderSupportOneItem": True,
            "mailOrderOneItemOneOrderVolumeLimit": 1000,
            "asItemCanMarkCondition": {
                "max_sku_weight_lb": 10,
                "max_skuBox_weight_lb": 54,
                "max_skuBox_volume_in3": 1000
            }
        }
        json_str = json.dumps(json_data)
        sql = f"UPDATE wms.wh_config SET config_value = '{json_str}' WHERE config_key = 'support_automation' AND warehouse_number={warehouse_number};"
        self.wmsdb.update_data(sql)

    def get_lpn_info(self, reference_no):
        """
        获取lpn信息
        """
        sql = f"SELECT * from wms.wh_lpn_info where reference_no='{reference_no}'"
        result = self.select_data_deal(sql)
        log.info(f'get_inbound_order:{result}')
        return result

    def get_order_tag(self,so_order_id,info=None):
        """
        根据so order获取wms wh_order_tag
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"""
               SELECT {info_str} from wms.wh_order_tag wot
                JOIN wms.wh_order_info woi ON woi.order_id =wot.order_id
                JOIN wms.wh_so_order_mapping wsom ON wsom.source_order_id = woi.source_order_id
                JOIN wms.wh_order_item wom ON wom.order_id = woi.order_id
                WHERE wsom.so_order_id = {so_order_id}"""
        result = self.select_data_deal(sql)
        log.info(f'so_order:{so_order_id}对应wms_order_tag:{result}')
        return result

    def get_order_type(self,so_order_id,item_number,info=None):
        """
        根据so order获取wms wh_order_tag
        """
        if info is None:
            info_str = '*'
        else:
            info_str = ', '.join(info)

        sql = f"""
               SELECT {info_str} FROM wms.wh_order_info woi 
                JOIN wms.wh_so_order_mapping wsom ON wsom.source_order_id = woi.source_order_id
                JOIN wms.wh_order_item woi2 ON woi2.order_id = woi.order_id
                WHERE wsom.so_order_id = {so_order_id} AND woi2.item_number ={item_number} limit 1;"""
        result = self.select_data_deal(sql)
        log.info(f'so_order:{so_order_id}对应wms_order_type:{result}')
        return result



if __name__ == '__main__':
    wms = WmsDB()
    # print(wms.get_inventory_transaction_log("33", "TM1051", "88609", "17866123", inventory_type=[261,263], in_dtm=1730446037))
    #print(wms.select_hook_task("17867805", "TMS_PACKAGE", 1731316699))
    # ids = (24250588102, 24250588202, 24250588203, 24250588403, 242505883, 242505885)
    # wms.check_orders_shipping_status(order_ids=ids, shipping_status=60)
    #wms.finish_picking_task(user_id=7642085)
    # task_id = wms.get_repack_data( warehouse =25,delivery_date ="2024-11-13")
    #print(wms.get_vendor_task(492929))
    # 获取task_detail表id，item
    #resp3 = wms.get_order_as_task_detail(pick_list_id=4146,
                                                # info=['rec_id', 'item_number', 'batch_no', 'receive_dtm',
                                                #       'expire_dtm', 'quantity'])

    result = wms.get_so_order_mapping(order_id=42658652, info=['wot.tag_id'])
    print(result)
