# !/usr/bin/python3
# -*- coding: utf-8 -*-

import math
import weeeTest
from jsonpath import jsonpath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.wms_assert_utils import WmsAssertUtils
from wms.test_dir.business.adjust_approve_process import AdjustApproveProcess



class TestRepack(weeeTest.TestCase):

    def setup_class(self):
        self.warehouse_number = global_data.repack_picking["warehouse_number"]
        self.storage_type = global_data.repack_picking["storage_type"]
        self.item_number = global_data.repack_picking["item_number"]
        self.item_per_pack = global_data.repack_picking["item_per_pack"]
        self.target_item = global_data.repack_picking["target_item"]
        self.target_item_per_pack = global_data.repack_picking["target_item_per_pack"]
        self.target_quantity = global_data.repack_picking["target_quantity"]
        self.conversion_rate = global_data.repack_picking["conversion_rate"]
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.current_date = wms.util.get_special_date()
        self.is_prod = global_data.is_prod

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_repack_task_finish(self):
        """
        【114630】Repack finish 流程
        """
        last_date = wms.util.get_special_date(days=-1)
        if not self.is_prod:
            wms.update_central_config(self.warehouse_number, "wms:warehouse:sys_date", last_date)
        task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, last_date)
        if task_res["body"]["data"] == []:
            # 周末不跑脚本，周一需要结束掉上周五生成的任务，释放repack pick pallet
            last_date = wms.util.get_special_date(days=-2)
            task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, last_date)
        if task_res["body"]["data"]:
            if jsonpath(task_res, "$.body.data[0].status")[0] != 30:
                # 获取task_id
                task_id = jsonpath(task_res, "$.body.data[0].task_id")[0]
                # #获取待收货任务列表
                po_number = "R"+str(task_id)
                task_list = wms.put_away.query_central_putaway_task_list(self.warehouse_number, po_number, wms.util.get_special_date(days=-7), wms.util.get_special_date(days=1), status_list=[10, 20, 40])
                if task_list == []:
                    wms.repack_api.FinishRepackTask(self.warehouse_number, jsonpath(task_res, "$.body.data[0].task_id")[0])
                    # finsih接口中需要处理库存，释放当天任务下所有的Pick Pallet，添加等待
                    wms.util.wait(sleep_time=5)
                    task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, last_date)
                    assert jsonpath(task_res, "$.body.data[0].status")[0] == 30, f"仓库：{self.warehouse_number},date:{last_date}下的repack任务结束失败"


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_repack_task_create(self):
        """
        【114629】Repack Task创建流程
        """
        # 创建拣货任务
        wms.repack_api.RepackCreateTask(delivery_date= self.current_date,warehouse=self.warehouse_number)
        wms.util.wait(sleep_time=5)
    
        # 查询任务是否创建成功
        task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, self.current_date)
        assert jsonpath(task_res, "$.body.data[0]"), f"{self.current_date} repack任务创建失败"

        # 获取task_id
        task_id = jsonpath(task_res, "$.body.data[0].task_id")[0]

        # 加库存 适配动态数据
        location_inv_list = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=self.item_number, location_types=[3])
        inventory_qty = 0
        for inv_detail in location_inv_list:
            inventory_qty += inv_detail["quantity"]

        # 若item location avaiable qty 太少，则加库存
        if inventory_qty < self.target_quantity*self.conversion_rate:
            avaiable_stock = wms.common_api.get_avaiable_stock(self.warehouse_number, storage_type=2)[0]["stock_sn"]
            wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, self.user_name + '(' + self.user_id + ')',
                                            avaiable_stock, adjust_number=50, is_lpn=True, pieces_per_pack=self.item_per_pack, receive_date="2025-07-01", lpn_qty=math.ceil(50/self.item_per_pack))
            # 库存审核
            AdjustApproveProcess().inv_approve_operate(self.user_name + '(' + self.user_id + ')',  self.item_number, self.warehouse_number, avaiable_stock, 2)
            inv_res = wms.adjust_api.query_location_inv(
                    self.item_number,
                    avaiable_stock,
                    self.warehouse_number,
            )["body"]["invAdjustList"]
            lpn_list = list(map(lambda i: i["lpn_no"], inv_res[0]["lpnInfos"]))
            # 解锁库位
            wms.adjust_api.lock_inv(0, self.item_number, avaiable_stock, self.warehouse_number, True, lpn_list, None)

        #查看任务详情，删除指定Item，再添加
        details_res = wms.repack_api.getRepackTaskDetail(warehouse=self.warehouse_number, task_id=task_id)
        ItemRecid = [item for item in details_res['body'] if item['item_number'] == self.item_number]
        if ItemRecid != []:
            ItemRecid = ItemRecid[0]["rec_id"]
            resp6 = wms.repack_api.deleteRepackItem(ItemRecId=ItemRecid)
            if resp6['success'] == True:
                resp7 = wms.repack_api.addRepackItem(itemNumber=self.item_number, piecesPerPack=self.item_per_pack, targetItemNumber=self.target_item,targetPiecesPerPack=self.target_item_per_pack, targetQuantity=self.target_quantity, taskId=task_id, warehouse_number=self.warehouse_number)
                assert resp7['success'] == True,"Item存在且未拣货,删除后添加失败"
                wms.util.wait(sleep_time=10)
            else:
                #更新Item
                resp9 = wms.repack_api.updateRepackItem(warehouse_number=self.warehouse_number, task_id=task_id, rec_id=ItemRecid, quantity=self.target_quantity)
                assert resp9['success'] == True,"Item存在且已开始拣货,更新Item失败"
                wms.util.wait(sleep_time=10)
        else:
            resp8 = wms.repack_api.addRepackItem(itemNumber=self.item_number, piecesPerPack=self.item_per_pack, targetItemNumber=self.target_item,targetPiecesPerPack=self.target_item_per_pack, targetQuantity=self.target_quantity, taskId=task_id,warehouse_number=self.warehouse_number)
            assert resp8['success'] == True,"Item不存在,直接添加失败"
            wms.util.wait(sleep_time=10)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_repack_pick(self):
        """
        【112977】Repack Pick 流程
        """
        # 偶现用例重复执行，添加日志
        weeeTest.log.debug("test_repack_pick开始执行")
        # 记录进入模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_pick")
        # repack pick获取任务列表
        task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, self.current_date)
        assert jsonpath(task_res, "$.body.data[0]"), f"{self.current_date}下无repack任务"

        # 获取task_id
        task_id = jsonpath(task_res, "$.body.data[0].task_id")[0]
        pick_info = wms.repack_api.pickListWitchLpn(action=0, warehouse=self.warehouse_number, palletno="", taskId=task_id, type=None)["body"]
        task_list = {"full_num": pick_info['full_num'],"ground_num": pick_info['ground_num'],"air_num": pick_info['air_num']}
        non_zero_values = {key:value for key, value in task_list.items() if value != 0}
        task_type = list(non_zero_values.keys())
        assert task_type != [], "无可领取的repack pick任务"

        if task_type[0] == "full_num":
            type =1
        elif task_type[0] == "ground_num":
            type =2
        else:
            type =3

        # 查询可用的pallet
        palletno = wms.common_api.get_avaiable_location(self.warehouse_number, 38, flag=0)[0]["location_no"]

        #领取任务 type 1-Full; 2-ground; 3-air;
        picking_list = wms.repack_api.pickListWitchLpn(action=1,warehouse=self.warehouse_number,palletno =palletno ,taskId = task_id,type= type)['body']['picking_list']
        task_location_no = picking_list[0]['source_location']
        type_upc_lpn = picking_list[0]['picking_way']
        picking_task_id = picking_list[0]['picking_task_id']
        pieces_per_pack = picking_list[0]['pieces_per_pack']
        assert type_upc_lpn==2, "拣货任务不是LPN模式"

        picking_detail = wms.repack_api.pickingDetailWitchLpn(locationno=task_location_no,picking_task_id=picking_task_id,
                                                    warehouse=self.warehouse_number,new_lpn_no='')["body"]

        pickedLpnCount = picking_detail ['pickedLpnCount']
        taskLpnCount = picking_detail ['taskLpnCount']
        allLpnList = picking_detail ['allLpnList']
        NopickedLpnlist = [lpn_no['lpn_no'] for lpn_no in allLpnList if lpn_no['picked'] == False]
        pickedLpnlist = [lpn_no['lpn_no'] for lpn_no in allLpnList if lpn_no['picked'] == True]

        # 循环遍历NopickedLpnlist
        while pickedLpnCount < taskLpnCount:
            # 从列表中获取当前的LPN
            current_lpn = NopickedLpnlist[pickedLpnCount]
            # 调用函数并传递当前的LPN
            wms.repack_api.pickingDetailWitchLpn(locationno=task_location_no,picking_task_id=picking_task_id,warehouse=self.warehouse_number,
                                                new_lpn_no=current_lpn,pickedLpnList=pickedLpnlist)
            # 将当前使用的LPN添加到pickedLpnlist中
            pickedLpnlist.append(current_lpn)
            # 打印当前的计数
            pickedLpnCount += 1
            print(f"Picked LPN Count: {pickedLpnCount}, Current LPN: {current_lpn}")

        #confirm
        wms.repack_api.pickingConfirmWitchLpn(warehouse=self.warehouse_number,locationno=task_location_no,PalletNo=palletno,
                                            skuQuantity=taskLpnCount*pieces_per_pack,picking_task_id=picking_task_id,pickedlpnlist=pickedLpnlist)
        wms.util.wait(sleep_time=3)
        # 记录退出模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_pick", action=0)
        #check item status
        task_detail = wms.repack_api.getRepackTaskDetail(warehouse=self.warehouse_number, task_id=task_id)
        for item in task_detail['body']:
            if item['item_number'] == self.item_number:
                assert item["status"] == 20, "已拣货repack Item任务状态不是20"


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_repack_receive(self):
        """
        【112979】Repack Receive流程
        """
        # 记录进入模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_receive")
        task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, self.current_date)
        assert jsonpath(task_res, "$.body.data[0]"), f"{self.current_date}下无repack任务"

        # 获取task_id
        task_id = jsonpath(task_res, "$.body.data[0].task_id")[0]

        # #获取待收货任务列表
        po_number = "R"+str(task_id)
        receive_list = wms.repack_api.queryReceiveList(po_number=po_number,status=0,pageSize=5,startColumn = 0,
                                            warehouse_number=self.warehouse_number,reference_type=4)["body"]["poItemList"]
        assert receive_list, f"{self.current_date}下无可收货的数据"

        #进入receive details页面
        receive_detail = wms.repack_api.queryReceiveList(po_number=po_number, status=0, pageSize=5, startColumn=0,
                                            warehouse_number=self.warehouse_number, reference_type=4,
                                            is_search_all=True, upc_code=receive_list[0]["upc_code"])["body"]["poItemList"]
        assert receive_detail, f'{self.current_date}下upc={receive_list[0]["upc_code"]} 无可收货的数据'
        wms.repack_api.queryRepackItemDetail(reference_no=po_number,item_number=receive_detail[0]["item_number"],
                                                    warehouse_number=self.warehouse_number,user_id=self.user_id)

        #获取Item Batch
        batch_no = wms.repack_api.queryRepackPickBatchList(reference_no=po_number,item_number=receive_detail[0]["item_number"],
                                                    warehouse_number=self.warehouse_number,user_id=self.user_id)['body'][0]["batch_no"]

        #Confirm
        wms.repack_api.receiveConfirm(lp="",
                                    po_number=po_number,
                                    item_number=receive_detail[0]["item_number"],
                                    rec_box=0,
                                    rec_qty=self.target_quantity,
                                    pieces_per_pack=receive_detail[0]["pieces_per_pack"],
                                    in_user=self.user_id,
                                    warehouse_number =self.warehouse_number,
                                    location_no ="",
                                    module_name="repack_receive",
                                    qc_ExpiredDate="",
                                    batch_no =batch_no)
        wms.util.wait(sleep_time=3)
        # 记录退出模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_receive", action=0)
        # 检查上架任务是否生成
        task_list = wms.put_away.query_central_putaway_task_list(self.warehouse_number, po_number, wms.util.get_special_date(days=-1), wms.util.get_special_date(days=1), status_list=[10])
        assert task_list, "收货完成后没有状态是已创建的上架任务"


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_repack_putaway(self):
        """
        【112978】Repack Putaway 流程
        """
        # 记录进入模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_putaway")
        task_res = wms.repack_api.queryCentralRepackTask(self.warehouse_number, self.current_date)
        assert jsonpath(task_res, "$.body.data[0]"), f"{self.current_date}下无repack任务"

        # 获取task_id
        task_id = jsonpath(task_res, "$.body.data[0].task_id")[0]

        # 获取当前用户有无已领取未上架的任务,# 若有未完成的任务先释放掉
        undo_info = wms.repack_api.queryUndoTaskList(warehouse_number=self.warehouse_number,user_id=self.user_id,po_number="")["body"]
        if undo_info.get("task_id"):
            wms.repack_api.partialComplete(self.warehouse_number, undo_info.get("task_id"), self.user_name + '(' + self.user_id + ')')

        po_number = "R" + str(task_id)
        wms.repack_api.queryUndoTaskList(warehouse_number=self.warehouse_number,user_id=self.user_id,po_number=po_number)["body"]

        # 获取待上架任务列表
        todo_list = wms.repack_api.todoList(warehouse_number=self.warehouse_number,po_number=po_number,storage_type=self.storage_type,user_id=self.user_id)['body']
        assert todo_list, f"{self.current_date}下无repack putaway任务"    

        # 查询可用的pallet
        palletno = wms.common_api.get_avaiable_location(self.warehouse_number, 38, flag=0)[0]["location_no"]

        # 扫描mLPN
        resp3 = wms.repack_api.scanAndCheckTask(warehouse_number=self.warehouse_number,po_number=po_number,user_id=self.user_id,lpn_no=todo_list[0]['label_no'])
        putaway_task_id = resp3['body'][0]['task_id']

        # Confirm
        wms.repack_api.confirmAndScheduleTasks(warehouse_number=self.warehouse_number,user_id=self.user_id,
                                                   putaway_task_id=putaway_task_id,pallet_no=palletno)

        #获取推荐库位
        putaway_info = wms.repack_api.queryPutawayInfo(putaway_task_id=putaway_task_id,warehouse_number=self.warehouse_number,user_id=self.user_id)["body"]
        exist_location_list = putaway_info["locationList"]

        recommend_location = putaway_info['recommend_location']
        location_no = None
        if exist_location_list:
            location_no = exist_location_list[0]['location_no']
            weeeTest.log.debug("获取到的Location no")
            weeeTest.log.debug(location_no)
        else:
            if recommend_location:
                location_no = recommend_location
            else:
                location_no = wms.common_api.get_avaiable_bin(self.warehouse_number, storage_type=int(self.storage_type))[0]["bin_sn"]

        location_list = wms.common_api.get_avaiable_location(self.warehouse_number, 4, location_no=location_no)
        if location_list:
            location_info = location_list[0]
            location_attr = location_info["locationAttributes"]
            attr_list = []
            if location_attr:
                attr_list = jsonpath(location_attr, "$[*].code")
            #给bin库位添加允许放同规格多Batch库存属性
            if 34 not in attr_list:
                attr_list.append(34)
                wms.common_api.update_location(location_info["rec_id"], self.warehouse_number, location_no, 4, self.user_name + '(' + self.user_id + ')', size_id=location_info["size_id"], storage_temperate_zone=location_info["storage_temperate_zone"],
                                            locationAttributeCodes=attr_list, item_count=location_info["item_count"])

        #获取任务详情及LPN
        item_datail = wms.repack_api.queryTaskConfirmDetail(putaway_task_id=putaway_task_id,location_no=location_no,
                                                  warehouse_number=self.warehouse_number,user_id=self.user_id)["body"]
        item_number = item_datail['item_number']
        recommend_lpn_quantity = item_datail['recommend_lpn_quantity']
        lpnInfo = item_datail['lpnInfoList']

        # 组装putaway confirm 需要的参数
        new_list_of_dicts = []
        for d in lpnInfo:
            new_dict = {'rec_id': d['rec_id'], 'warehouse_number': None,"item_number":None,
                        "lpn_no":d['lpn_no'],"original_quantity":d['original_quantity'],"receive_dtm":d['receive_dtm']}
            new_list_of_dicts.append(new_dict)

        #Confirm
        wms.repack_api.confirmPutaway(item_number=item_number,location_no=location_no,task_id=putaway_task_id,
                                          warehouse_number=self.warehouse_number,in_user=self.user_id,module_name="repack_putaway",
                                          lpnInfoList=new_list_of_dicts,actual_location_recommend_qty=recommend_lpn_quantity)
        # 记录退出模块日志
        wms.common_api.record_moudle_log(self.warehouse_number, "repack_putaway", action=0)
        # 检查上架任务状态
        task_list = wms.put_away.query_central_putaway_task_list(self.warehouse_number, po_number, wms.util.get_special_date(days=-1), wms.util.get_special_date(days=1), pallet_no=palletno)
        assert jsonpath(task_list, "$[*].status")[0] == "50", "已上架的任务状态不是50"
        WmsAssertUtils().check_location_status(self.warehouse_number, 38, palletno, 0)




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
