# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.utils import DataUtils


class TestOOSOrder(weeeTest.TestCase):

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_partial_oos(self):
        """
        【111944】订单下商品部分OOS
        """
        warehouse_number = global_data.partial_oos["warehouse_number"]
        order_id = global_data.partial_oos["order_id"]
        delivery_dtm = global_data.partial_oos["delivery_dtm"]
        item_number = global_data.partial_oos["item_number"]
        order_type = global_data.partial_oos["order_type"]

        # 恢复订单到未做OOS状态
        wms.wms_db.update_order_item(order_id)

        # 登录
        account, username = wms.wms_login.common_login()

        oos_list = wms.oos_api.get_order_oos_list_detail(delivery_dtm, item_number, warehouse_number, order_type)["body"]
        wms.oos_api.order_oos_items(warehouse_number, account, delivery_dtm, oos_list, order_type)

        # check reject quantity
        order_item = wms.wms_db.get_wms_order_item(order_id)
        item = jmespath(order_item, f"[?item_number=='{item_number}'] | [0]")
        assert item["reject_quantity"] == oos_list[0]["oos_quantity"], f"""Item的reject_quantity与实际做OOS的数量不一致，预期={oos_list[0]["oos_quantity"]}, 实际={item["reject_quantity"]}"""

        # check order status
        order_info = wms.wms_db.select_wms_order_info(order_id)
        assert order_info["order_status"] ==0, f"""订单下部分商品OOS， 订单状态预期=0，实际={order_info["order_status"]}"""

        # 退出登录
        wms.logout(warehouse_number=warehouse_number, user_id=account, user_name=username)


    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_all_oos(self):
        """
        【111943】订单下商品全部OOS
        """
        warehouse_number = global_data.all_oos["warehouse_number"]
        order_id = global_data.all_oos["order_id"]
        delivery_dtm = global_data.all_oos["delivery_dtm"]
        item_number = global_data.all_oos["item_number"]
        order_type = global_data.all_oos["order_type"]

        # 恢复订单到未做OOS状态
        wms.wms_db.update_order_item(order_id)
        wms.wms_db.update_order_status(order_id, 0)

        # 登录
        account, username = wms.wms_login.common_login()

        oos_list = wms.oos_api.get_order_oos_list_detail(delivery_dtm, item_number, warehouse_number, order_type)["body"]
        wms.oos_api.order_oos_items(warehouse_number, account, delivery_dtm, oos_list, order_type)

        # check reject quantity
        order_item = wms.wms_db.get_wms_order_item(order_id)
        assert order_item[0]["reject_quantity"] == oos_list[0]["oos_quantity"], f"""Item的reject_quantity与实际做OOS的数量不一致，预期={oos_list[0]["oos_quantity"]}, 实际={order_item[0]["reject_quantity"]}"""

        # check order status
        order_info = wms.wms_db.select_wms_order_info(order_id)
        assert order_info["order_status"] ==4, f"""订单下全部商品OOS， 订单状态预期=4，实际={order_info["order_status"]}"""

        # 退出登录
        wms.logout(warehouse_number=warehouse_number, user_id=account, user_name=username)


    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_cs_cancel_order(self):
        """
        【111019】CS取消订单(订单已拣货)
        """        
        warehouse_number = global_data.cancel_order["warehouse_number"]
        order_id = global_data.cancel_order["order_id"]
        order_type = global_data.cancel_order["order_type"]

        # 恢复订单到未取消状态
        wms.wms_db.update_order_item(order_id)
        wms.wms_db.update_order_status(order_id, 0)

        # 登录
        account, username = wms.wms_login.common_login()

        # 执行取消操作
        wms.oos_api.cs_cancel_order(account, warehouse_number, order_id, order_type)

        # check reject quantity（订单下商品全取消时，reject_quantity不更新， 部分取消时，此断言打开）
        # order_item = wms.wms_db.get_wms_order_item(order_id)
        # for item in order_item:
        #     assert item["reject_quantity"] == item["quantity"]

        # check order status
        order_info = wms.wms_db.select_wms_order_info(order_id)
        assert order_info["order_status"] == 2

        # 退出登录
        wms.logout(warehouse_number=warehouse_number, user_id=account, user_name=username)


    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_route_check_move_to_cot(self):
        """
        【111012】Route Check Move To Cot
        """
        warehouse_number = global_data.route_check_cancel["warehouse_number"]
        route_id = global_data.route_check_cancel["route_id"]
        delivery_date = global_data.route_check_cancel["delivery_date"]
        storage_type = global_data.route_check_cancel["storage_type"]
        tracking_num = global_data.route_check_cancel["tracking_num"]
        order_id = global_data.route_check_cancel["order_id"]

        # 恢复订单到未做route check cancel状态
        wms.wms_db.update_order_ship_status(order_id, 70)
        wms.wms_db.rollback_wait_oos(order_id)

        # 登录
        account, username = wms.wms_login.common_login()

        # route check 扫描已取消订单的trackign number
        rep = wms.route_check.package_confirm(warehouse_number, delivery_date, route_id, tracking_num, storage_type)
        assert rep == "The order was canceled by user!", "route check扫描的订单未取消"

        # 管理员取消
        wms.route_check.supervisor_cancel(warehouse_number, delivery_date, route_id, tracking_num, storage_type, username + '(' + account + ")")

        #检查wh_packing_ordre中的deal_status
        packing_info = wms.wms_db.get_wh_packing_order_info(order_id)
        assert packing_info["deal_status"] == 20, "未更新wh_packing_order表的deal_status为已扫主管码状态"

        # Central端 move to cot
        wms.oos_api.central_move_to_cot(warehouse_number, order_id)

        #检查wh_packing_ordre中的deal_status
        packing_info = wms.wms_db.get_wh_packing_order_info(order_id)
        assert packing_info["deal_status"] == 30, "未更新wh_packing_order表的deal_status为已move to cot状态"
        assert packing_info["deal_mode"] == 2, "未更新wh_packing_order表的deal_mode为Central-->Cot"

        # check order status
        wms.wms_assert.check_order_ship_status(order_id, 70)

        # 退出登录
        wms.logout(warehouse_number=warehouse_number, user_id=account, user_name=username)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)