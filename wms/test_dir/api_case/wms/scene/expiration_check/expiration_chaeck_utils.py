# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  yu.yang
@Version        :  V1.0.0
------------------------------------
@File           :  expiration_chaeck_utils.py
@Description    :  
@CreateTime     :  2025/8/29 10:36
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/8/29 10:36
"""
import weeeTest
from weeeTest import jmespath
from datetime import datetime
from wms.test_dir.api.wms.wms import wms


class ExpirationCheck(weeeTest.TestCase):
    def expired_common(self, warehouse_number, location_type, user_id, edit_user, storage_type):
        res = wms.expired_api.expired_query_task(warehouse_number, location_type, storage_type, -1)
        rec_id = jmespath(res, "body.data[0].rec_id")
        location_no = jmespath(res, "body.data[0].location_no")
        item_number = jmespath(res, "body.data[0].item_number")
        status = int(jmespath(res, "body.data[0].status"))
        wms.expired_api.expired_assign_task(rec_id, user_id)
        checked_status = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while checked_status < 3:
            res1 = wms.expired_api.expired_scan_location(warehouse_number, location_no, status, edit_user)
            upc = jmespath(res1, "body[0].upc_code")
            res2 = wms.expired_api.expired_goods(warehouse_number, upc, location_no)
            lpn_lists = jmespath(res2, "body.lpnList")
            if lpn_lists:
                # 走LPN流程
                # 获取当前时间expired_date加一年
                date = datetime.now()
                expired_dtm_input = date.replace(year=date.year + 1).strftime('%Y-%m-%d')
                expired_dtm_str = date.replace(year=date.year + 1, hour=0, minute=0, second=0, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')
                wms.expired_api.expired_lpn_confirm(warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input,
                                                    lpn_lists)
                # 查询任务状态
                checked_status, status = self.expired_checks(status, rec_id, item_number, location_no, warehouse_number, expired_dtm_input, location_type,
                                                             storage_type)
            else:
                # 走UPC流程
                # 获取当前时间expired_date加一年
                date = datetime.now()
                expired_dtm_input = date.replace(year=date.year + 1).strftime('%Y-%m-%d')
                expired_dtm_str = date.replace(year=date.year + 1, hour=0, minute=0, second=0, microsecond=0).strftime('%Y/%m/%d %H:%M:%S')
                wms.expired_api.expired_confirm(warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input)
                # 查询任务状态
                checked_status, status = self.expired_checks(status, rec_id, item_number, location_no, warehouse_number, expired_dtm_input, location_type,
                                                             storage_type)

    def expired_checks(self, status, rec_id, item_number, location_no, warehouse_number, expired_dtm_input, location_type, storage_type):
        """
        结果检查
        """
        res = wms.expired_api.expired_query_task(warehouse_number, location_type, storage_type, None, location_no, item_number)
        checked_status = int(jmespath(res, "body.data[0].status"))
        if checked_status == 1:
            weeeTest.log.info('一盘完成,待二盘......')
            # 更新盘点数据,继续二盘
            status = status + 1
            # 分配任务
            wms.expired_api.expired_assign_task(rec_id, 7644738)
            weeeTest.log.info(f'进行{status + 1}盘, count_id: {rec_id}')
        if checked_status == 2:
            weeeTest.log.info('二盘完成,待三盘......')
            # 更新盘点数据,继续三盘
            status = status + 1
            # 分配任务
            wms.expired_api.expired_assign_task(rec_id, 7396768)
            weeeTest.log.info(f'进行{status + 1}盘, count_id: {rec_id}')
        if checked_status == 4:
            weeeTest.log.info(f'count_id:{rec_id},盘点完成！！！')
            res_expire = wms.adjust_api.query_location_inv(item_number, location_no, warehouse_number)
            expire_dtm = jmespath(res_expire, "body.invAdjustList[0].expire_dtm")
            assert expired_dtm_input == expire_dtm, "库位保质期和盘点保质期不一致"
        return checked_status, status
