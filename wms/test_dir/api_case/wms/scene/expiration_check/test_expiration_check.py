import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.scene.expiration_check.expiration_chaeck_utils import ExpirationCheck


class TestExpired(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.edit_user = self.user_name + '(' + self.user_id + ")"  # 拼接in_user
        self.warehouse_number = global_data.expiration_check['warehouse_number']
        self.storage_type = global_data.expiration_check['storage_type']

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_get_task(self):
        """
        【111839】保质期检查：领取保质期任务
        """
        # 保质期任务领取
        wms.expired_api.expired_get_task(self.warehouse_number, self.storage_type, self.edit_user)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_release_task(self):
        """
        【111840】保质期检查：释放保质期任务
        """
        # 释放保质期任务
        wms.expired_api.expired_release_task(self.edit_user)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_bin_upc(self):
        """
        【111850】保质期检查：Bin库位UPC
        """
        # 调用UPC保质期检查
        ExpirationCheck().expired_common(self.warehouse_number, 4, self.user_id, self.edit_user, self.storage_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expiration_check_stock_lpn(self):
        """
        【111851】保质期检查：Stock库位LPN
        """
        # 调用LPN的保质期检查
        ExpirationCheck().expired_common(self.warehouse_number, 3, self.user_id, self.edit_user, self.storage_type)
