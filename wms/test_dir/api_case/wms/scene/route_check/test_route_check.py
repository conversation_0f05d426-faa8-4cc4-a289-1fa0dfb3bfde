# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.test_dir.api.wms.wms import wms
from wms.qa_config import global_data


class TestRouteCheck(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mail_order_route_check_process(self):
        """
        【113095】直邮Route check 流程
        """
        warehouse = 33
        order_type = 4
        # 登录
        account, username = wms.wms_login.common_login()

        # 获取直邮订单的线路
        route_list = []
        mo_prefix_config = wms.wms_db.select_wh_config_by_key(warehouse, "wms:downorder:mail_order_wave")
        for config in mo_prefix_config:
            route_list.append(config["routeId"])
        # 获取待route check 的数据
        ret = wms.wms_db.get_route_check_data(warehouse=warehouse, shipping_status=70, order_type=order_type,
                                              route_id=route_list)
        if not ret:
            return
        delivery_dtm = wms.util.utc_to_day(ret['delivery_dtm'])
        route_id = ret["route_id"]

        # 开始route check操作
        wms.route_check_operation(warehouse, delivery_dtm, route_id, order_type, account, username)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
