#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :  Geekplus Picking 流程测试脚本
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""


import weeeTest
from wms.test_dir.api.wms.wms_autopicking import wms_auto


class TestPicking(weeeTest.TestCase):

    def test_geekplus_picking(self,order_id=None):
        """
        【112957】Geekplus Picking 流程
        """

        if order_id is None:
            order_id = 242523270

        wms_auto.geek_picking(order_id=order_id)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

