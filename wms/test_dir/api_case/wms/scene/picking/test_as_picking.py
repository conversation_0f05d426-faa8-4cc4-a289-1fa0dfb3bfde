# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""

import weeeTest
from wms.test_dir.api.wms.wms_autopicking import wms_auto

class TestPicking(weeeTest.TestCase):

    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_autostore_picking(self,order_id=None):
        """
        【112956】AS Picking 流程
        """
        if order_id is None:
            order_id = 242548069

        wms_auto.as_picking(order_id=order_id)



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
