# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""

"""
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestAlcoholPicking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_alcohol_picking(self):
        """
        【112971】Alcohol picking 流程
        """

        warehouse_number = global_data.alcohol_picking['warehouse_number']
        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 查询可用的alcohol cart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=51, flag=0,
                                                              info='location_no')

        # 创建拣货任务
        resp = wms.alcohol_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        message_id = resp['messageId']
        # 判断是否无任务
        if message_id == '32003':
            return
        # 判断是否有未做完的任务
        elif message_id == '99998':
            print("用户有未完成的任务，重新scan cart再进行作业")
            # 可补充再查询未做完的任务，继续完成任务
            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

        else:
            # 拣货
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = wms.alcohol_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束PICKING拣货任务
            wms.alcohol_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                 picking_type=jmespath(resp, "body.picking_type"),
                                                 packing_line=jmespath(resp, "body.packing_line"),
                                                 picking_task_status=jmespath(resp, "body.status"),
                                                 warehouse_number=warehouse_number)


            # scan cart putwall
            resp = wms.alcohol_picking.putwall_scan_cart(cart_no=location_no, warehouse_number=warehouse_number)

            box_size = jmespath(resp, "body.box_size")
            box_size_origin = int(jmespath(resp, "body.box_size_origin"))
            print('需要绑定的tote size:', box_size)
            order_ids = jmespath(resp, "body.orderList[*].order_id")
            # 绑定tote前是31
            picking_task_status = jmespath(resp, "body.picking_task_status")
            picking_task_id = jmespath(resp, "body.picking_task_id")

            # 查询可用的tote
            tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                        box_size=box_size)

            # 判断订单与数据库中查询的tote数量
            total_order_num = len(order_ids)
            # total_shipping_num = len(shipping_types)
            # assert total_order_num == total_shipping_num
            assert len(tote_nos) >= total_order_num

            # 订单绑定拣货框
            print("共" + str(total_order_num) + "个订单需要绑定拣货框")
            for i in range(total_order_num):
                # 绑定tote时候的参数是size数
                resp = wms.alcohol_picking.bind_tote_number(tote_no=tote_nos[i]["location_no"],
                                                           order_id=order_ids[i],
                                                           box_size=box_size_origin,
                                                           warehouse_number=warehouse_number)

            # pick items /wms/putwall/item
            resp = wms.alcohol_picking.pick_items(picking_task_id=picking_task_id, warehouse_number=warehouse_number)
            tote_no = jmespath(resp, "body.tote_no")

            # sorting items
            while True:
                item_number = jmespath(resp, "body.item_number")
                if item_number is None:
                    break

                #pick出一个items，做一次soring，从wms/putwall/item的返回值里拿到sorting需要的信息

                resp=wms.alcohol_picking.pick_items(picking_task_id=picking_task_id,warehouse_number=warehouse_number)
                resp2=wms.alcohol_picking.sorting(action=1,
                                                item_number = jmespath(resp, "body.item_number"),
                                                order_id = jmespath(resp, "body.order_id"),
                                                picking_task_id = picking_task_id,
                                                sorting_quantity=jmespath(resp, "body.pick_quantity"))

                status = jmespath(resp2, "body.status")
                print("订单的状态：",status)
                if status==4:
                    #调用scan cart获取打包线，结束任务
                    resp = wms.alcohol_picking.putwall_scan_cart(cart_no=location_no, warehouse_number=warehouse_number)
                    picking_line = jmespath(resp, "body.picking_line")
                    cart_no = jmespath(resp, "body.cart_no")
                    picking_task_id = jmespath(resp, "body.picking_task_id")

                    wms.alcohol_picking.task_finish(cart_no = cart_no,
                                                    packing_line = picking_line,
                                                    picking_task_id = picking_task_id,
                                                    warehouse_number=warehouse_number)

            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

    if __name__ == '__main__':
        weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
