# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms

global expire_date


class TestRoReceiveAPI(weeeTest.TestCase):
    def setup_class(self):
        # 相关接口需要的信息
        self.rep_info = globals()  # globals()返回包含当前作用域全局变量的字典。

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_ro_receive(self):
        """
         [110135]RO收货流程
        """
        # 登录
        global expire_date
        account, username = wms.wms_login.common_login()
        self.rep_info["userid"] = account  # rep_info={"userid"：account，"username"：username}
        self.rep_info["username"] = username

        # 获取仓库和po号
        self.rep_info['warehouse_number'] = global_data.ro_receive_info['warehouse_number']
        self.rep_info['ro_number'] = global_data.ro_receive_info['reference_no']
        self.rep_info['item_number'] = global_data.ro_receive_info['item_number']

        # 获取商品的storage_type
        storage_type = wms.get_storage_type(self.rep_info['item_number'])

        # 获取仓库support_lpn的config_value
        config_value = wms.wms_db.select_wh_config_by_key(self.rep_info['warehouse_number'], "support_lpn")
        print(f"--------------------------------{config_value}")

        # 检查指定存储类型的值是否为 false
        is_false = config_value.get(storage_type, None)
        print(f"--------------------------------{is_false}")
        # 根据support_lpn 配置判断是否需要调用接口打开仓库lpn配置并打印结果
        if not is_false:
            print(f"{storage_type} 的support_lpn 配置 是 false，调用接口打开仓库lpn配置")
            # 打开仓库LPN配置，
            wms.update_central_config(self.rep_info['warehouse_number'], "support_lpn",
                                      "{ \"dry\": true,\"fresh\": true,\"frozen\": true}")
        else:
            print(f"{storage_type} 的support_lpn 配置 是 true.")

        # user赋值
        wms.central_receive.user = username + '(' + account + ')'
        wms.put_away.account = account
        wms.put_away.user = username + '(' + account + ')'

        # 模拟用户进入Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'ro_receive')

        # 执行ro收货操作
        # 通过sql查询未被占用的pallet_no
        pallet_no = wms.wms_db.get_pallet_no(self.rep_info['warehouse_number'])["location_no"]
        # 扫描pallet
        wms.Ro_Receive.ro_scan_pallet(self.rep_info['warehouse_number'], pallet_no)
        # 扫描RO单
        wms.Ro_Receive.query_ro(self.rep_info['warehouse_number'], self.rep_info['ro_number'])
        # 获取RO单下的商品信息
        result = wms.Ro_Receive.query_ro_itemlist(self.rep_info['ro_number'], self.rep_info['warehouse_number'])
        #————————————————————————加断言
        if not result:
            return
        upc_list = [data['upc'] for data in result]
        print(f"========{upc_list}")
        self.rep_info['upc_list'] = upc_list
        print(f"========{self.rep_info['upc_list']}")

        # 扫描upc
        for upc in self.rep_info['upc_list']:
            wms.Ro_Receive.query_ro_item_list(self.rep_info['ro_number'], self.rep_info['warehouse_number'], upc)
        # 进入收货详情页面获取收货详情
        result = wms.Ro_Receive.query_ro_item_detail(self.rep_info['ro_number'], self.rep_info['item_number'],
                                                     self.rep_info['warehouse_number'])
        # if not result:
        #     return
        print(f"Result type: {type(result)}")
        print(f"Result content: {result}")

        pieces_per_pack = result.get('pieces_per_pack')
        print(f"========{pieces_per_pack}")

        check_expire_date = result.get('check_expire_date')
        print(f"========{check_expire_date}")

        # 收货前检查receive_quantity
        pre_receive_quantity = wms.wms_db.get_receive_quantity(self.rep_info['ro_number'], self.rep_info['item_number'])
        # 收货前检查RECG0001好货区的库存——添加库存收货数据校验
        pre_item_recg_inv = wms.wms_db.get_inventory_transaction(self.rep_info['warehouse_number'], 'RECG0001',
                                                                 self.rep_info['item_number'])

        # 输入收货数据点击confirm提交收货
        if check_expire_date is True:
            expire_date = wms.util.get_special_date(days=730)
        else:
            expire_date = ""
        wms.Ro_Receive.ro_receive_confirm(pallet_no, self.rep_info['ro_number'], self.rep_info['item_number'],
                                          expire_date, self.rep_info['warehouse_number'], pieces_per_pack)

        # 收货后校验 receive_quantity是否增加正确
        post_receive_quantity = wms.wms_db.get_receive_quantity(self.rep_info['ro_number'],
                                                                self.rep_info['item_number'])
        assert post_receive_quantity['receive_quantity'] == pre_receive_quantity[
            'receive_quantity'] + 1, "receive_quantity记录错误"

        # 收货后校验 RECG0001好货区库存是否正确增加——添加库存收货数据校验(用例评审后添加校验)
        post_item_recg_inv = wms.wms_db.get_inventory_transaction(self.rep_info['warehouse_number'], 'RECG0001',
                                                                  self.rep_info['item_number'])
        assert post_item_recg_inv['quantity'] == pre_item_recg_inv['quantity'] + 1, "库存增加错误"
        print("收货完成")

        # 点击switchPallet生成上架单
        wms.Ro_Receive.switch_pallet(self.rep_info['warehouse_number'], pallet_no)
        self.rep_info['pallet_no'] = pallet_no


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_ro_putaway(self):
        """
        [110053] ro上架流程
        """
        # 获取上架warehouse_number、sku
        self.rep_info['putaway_warehouse_no'] = global_data.ro_putaway_info['warehouse_number']
        self.rep_info['item_number'] = global_data.ro_putaway_info['item_number']

        # 执行ro上架操作
        # 扫描pallet
        wms.Ro_PutAway.ro_check_pallet(self.rep_info['putaway_warehouse_no'], self.rep_info['pallet_no'])

        # 获取pallet下的待上架单
        result = wms.Ro_PutAway.ro_list(self.rep_info['pallet_no'], self.rep_info['putaway_warehouse_no'])
        # ————————添加断言
        if not result:
            return
        task_id_list = [data['task_id'] for data in result]
        print(f"========{task_id_list}")
        self.rep_info['task_id_list'] = task_id_list
        print(f"========{self.rep_info['task_id_list']}")

        # 点击confirm 跳转到上架详情页面
        wms.Ro_PutAway.ro_schedule_tasks(self.rep_info['putaway_warehouse_no'], self.rep_info['task_id_list'],  self.rep_info['pallet_no'])

        # 获取待上架单的详细信息
        task_id_location_no = {}
        for task_id in self.rep_info['task_id_list']:
            result = wms.Ro_PutAway.ro_query_putaway_info(task_id, self.rep_info['putaway_warehouse_no'])
            # process对应解释：1：OnlyBin、2： OnlyStock、3: random、5：D_RTV   "location_type": 4-bin,3-stock
            print("**************")
            print(result)
            print("**************")
            if result['recommend_location']:
                task_id_location_no[task_id] = result['recommend_location']
            else:
                if result['locationList']:
                    if result['process'] == 1:
                        for location in result['locationList']:
                            if location['location_type'] == 4:
                                task_id_location_no[task_id] = location['location_no']
                    elif result['process'] == 2:
                        for location in result['locationList']:
                            if location['location_type'] == 3:
                                task_id_location_no[task_id] = location['location_no']
                    elif result['process'] == 3:
                        task_id_location_no[task_id] = result['locationList'][0]['location_no']
                    elif result['process'] == 5:
                        task_id_location_no[task_id] = 'D_RTV'
                    else:
                        pass

        # —————————————————————— 添加task_id_location_no 如果没获取到，添加断言

        # 扫描要上架的库位（获取上架单的详情）
        for task_id, location_no in task_id_location_no.items():
            wms.Ro_PutAway.query_ro_task_confirm_detail(task_id, location_no,self.rep_info['putaway_warehouse_no'])

            # 扫描UPC——前端校验，不调用接口

            # 点击confirm,提交上架数据
            wms.Ro_PutAway.ro_confirm_putaway(1, self.rep_info['putaway_warehouse_no'], task_id, location_no, 1)

        # ——————————————————————添加上架校验

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net",
                  case_list=["test_ro_receive_putaway.py::TestRoReceiveAPI"], debug=True, open=False,
                  ext=['-m', 'WMS', "--junitxml=tests/results.xml"])
