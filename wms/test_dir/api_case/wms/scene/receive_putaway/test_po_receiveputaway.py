# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_po_receiveputaway.py
@Description    :  
@CreateTime     :  2025/6/21 11:24
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/21 11:24
"""
# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from jsonpath import jsonpath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business import poreceive_process
from wms.test_dir.api_case.wms import utils


class TestCentralReceiveAPI(weeeTest.TestCase):

    def setup_class(self):
        # 登录系统
        self.account, self.user_name = wms.wms_login.common_login()
        # 相关接口需要的信息
        self.rep_info = globals()  # globals()返回包含当前作用域全局变量的字典。
        self.rep_info["userid"] = self.account  # rep_info={"userid"：account，"username"：username}
        self.rep_info["username"] = self.user_name
        self.rep_info["erp_header"] = global_data.erp_header

        # 获取仓库和po号
        self.rep_info['warehouse_number'] = global_data.receive_info['warehouse_number']
        self.rep_info['item_number'] = global_data.receive_info['item_number']
        self.rep_info['vendor_id'] = global_data.receive_info['vendor_id']
        self.rep_info['storage_type'] = global_data.receive_info['storage_type']
        self.rep_info['est_delivery_date']=utils.DataUtils.get_special_date(days=1)


    def teardown_class(self):
        """测试后清理工作"""
        # 退出登录
        wms.wms_login.logout(warehouse_number=self.rep_info['warehouse_number'], user_id=self.account, user_name=self.user_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_create_po(self):
        """
        [114886]创建po
        """
        # 创建po
        resp = wms.central_receive.create_purchase_order(vendor_id=self.rep_info['vendor_id'],warehouse_number=self.rep_info['warehouse_number'],
                                                  est_delivery_date=self.rep_info['est_delivery_date'],
                                                  item=self.rep_info['item_number'])
        po = jsonpath(resp,'$.id')[0]

        #下发wms
        data = {
            "id": int(po)
        }
        wms.central_receive.confirm_po_order(data)
        self.rep_info['po_number'] = po

        #校验po
        po_number = wms.central_receive.central_query_po(self.rep_info['warehouse_number'], po)
        assert po_number != 'This PO not exists, please check!',  "下发po单失败"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_centralreceive(self):
        """
        [112679]普通商品Central收货流程
        """
        # 执行central收货操作
        poreceive_process.central_receive(warehouse_number=self.rep_info['warehouse_number'],
                                          po_number=self.rep_info['po_number'],
                                          user=self.rep_info['userid'])

        # 校验po单已生成的receive_task
        result = wms.central_receive.receive_list(self.rep_info['warehouse_number'], self.rep_info['po_number'])
        assert result[0]["receiveInfoList"] != [], "生成receive_task失败"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_appreceive(self):
        """
        [112679]普通商品APP收货流程
        """
        # 执行收货操作
        poreceive_process.app_receive(warehouse_number=self.rep_info['warehouse_number'],
                                      po_number=self.rep_info['po_number'])


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_putaway(self):
        """
        [112982]普通商品上架流程
        """
        # 执行上架操作
        poreceive_process.putaway_operation(putaway_warehouse_no=self.rep_info['warehouse_number'],
                                            po_number=self.rep_info['po_number'],
                                            in_user=self.rep_info['userid'],
                                            item_number=self.rep_info['item_number'],
                                            storage_type=self.rep_info['storage_type'])

        # 校验上架前后的库存，查询总库存
        putaway=wms.put_away.query_central_putaway_task_list(self.rep_info['warehouse_number'],self.rep_info['po_number'])
        assert jsonpath(putaway,'$[0].actual_quantity')[0] == jsonpath(putaway,'$[0].plan_quantity')[0] ,"上架数量与计划数量不一致"



if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
