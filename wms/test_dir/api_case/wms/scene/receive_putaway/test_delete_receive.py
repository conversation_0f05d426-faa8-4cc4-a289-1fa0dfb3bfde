# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestCentralReceiveAPI(weeeTest.TestCase):

    def setup_class(self):
        # 相关接口需要的信息
        self.rep_info = globals()  # globals()返回包含当前作用域全局变量的字典。

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_receive(self):
        """
        [110803]普通商品收货错收冲减流程
        """
        # 登录
        account, username = wms.wms_login.common_login()
        self.rep_info["userid"] = account  # rep_info={"userid"：account，"username"：username}
        self.rep_info["username"] = username

        # 获取仓库和po号
        self.rep_info['warehouse_number'] = global_data.receive_delete_info['warehouse_number']
        self.rep_info['po_number'] = global_data.receive_delete_info['reference_no']
        self.rep_info['item_number'] = global_data.receive_delete_info['item_number']

        # 获取商品的storage_type
        storage_type = wms.get_storage_type(self.rep_info['item_number'])

        # 获取仓库support_lpn的config_value
        config_value = wms.wms_db.select_wh_config_by_key(self.rep_info['warehouse_number'], "support_lpn")
        print(f"--------------------------------{config_value}")

        # 检查指定存储类型的值是否为 false
        is_false = config_value.get(storage_type, None)
        print(f"--------------------------------{is_false}")
        # 根据support_lpn 配置判断是否需要调用接口打开仓库lpn配置并打印结果
        if not is_false:
            print(f"{storage_type} 的support_lpn 配置 是 false，调用接口打开仓库lpn配置")
            # 打开仓库LPN配置，
            wms.update_central_config(self.rep_info['warehouse_number'], "support_lpn",
                                      "{ \"dry\": true,\"fresh\": true,\"frozen\": true}")
        else:
            pass
            print(f"{storage_type} 的support_lpn 配置 是 true.")

        # user赋值
        wms.central_receive.user = username + '(' + account + ')'

        # 模拟用户进入Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'receive')

        # 收货
        # 执行收货操作
        receive_task_no_list = wms.receive_operation(self.rep_info['warehouse_number'], self.rep_info['po_number'],
                                                     is_check=False)

        # 添加错收冲减前后的库存和数据校验

        # 错收冲减前检查receive_quantity
        pre_receive_quantity = wms.wms_db.get_receive_quantity(self.rep_info['po_number'], self.rep_info['item_number'])
        # 错收冲减前检查RECG0001好货区的库存
        pre_item_recg_inv = wms.wms_db.get_inventory_transaction(self.rep_info['warehouse_number'], 'RECG0001',
                                                                 self.rep_info['item_number'])

        # 执行错收冲减
        if receive_task_no_list:
            wms.central_receive.delete_receive(self.rep_info['warehouse_number'], receive_task_no_list[-1])

            # 添加错收冲减的status校验
            # 获取调用错收冲减接口之后该receive_task的status
            receive_task_status = wms.wms_db.get_receive_task_status(self.rep_info['warehouse_number'],
                                                                     receive_task_no_list[-1])
            assert receive_task_status['status'] == 10, "错收冲减完成 receive_task_no的status错误"

            # 收货后校验 receive_quantity是否扣减正确
            post_receive_quantity = wms.wms_db.get_receive_quantity(self.rep_info['po_number'],
                                                                    self.rep_info['item_number'])
            assert post_receive_quantity['receive_quantity'] == pre_receive_quantity[
                'receive_quantity'] - 1, "receive_quantity扣减错误"
            # 收货后校验 RECG0001好货区库存是否正确扣减
            post_item_recg_inv = wms.wms_db.get_inventory_transaction(self.rep_info['warehouse_number'], 'RECG0001',
                                                                      self.rep_info['item_number'])
            assert post_item_recg_inv['quantity'] == pre_item_recg_inv['quantity'] - 1, "库存增加错误"
            print("错收冲减完成")


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net",
                  case_list=["test_delete_receive.py::TestCentralReceiveAPI"], debug=True, open=False,
                  ext=['-m', 'WMS', "--junitxml=tests/results.xml"])
