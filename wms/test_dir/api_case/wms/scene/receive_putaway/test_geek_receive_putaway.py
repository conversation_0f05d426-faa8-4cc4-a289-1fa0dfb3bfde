# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business import poreceive_process


class TestCentralReceiveAPIGeek(weeeTest.TestCase):

    def setup_class(self):
        # 相关接口需要的信息
        self.rep_info = globals()  # globals()返回包含当前作用域全局变量的字典。
        # 登录
        account, username = wms.wms_login.common_login()
        self.rep_info["userid"] = account  # rep_info={"userid"：account，"username"：username}
        self.rep_info["username"] = username

        # 获取仓库和po号
        self.rep_info['warehouse_number'] = global_data.geek_receive_info['warehouse_number']
        self.rep_info['po_number'] = global_data.geek_receive_info['reference_no']
        self.rep_info['item_number'] = global_data.geek_receive_info['item_number']

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_as_centralreceive(self):
        """
        [112679]AS商品收货流程
        """
        # 模拟用户进入Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'receive')

        poreceive_process.central_receive(warehouse_number=self.rep_info['warehouse_number'],
                                          po_number=self.rep_info['po_number'],
                                          user=self.rep_info['userid'])

        # 校验po单已生成的receive_task
        result = wms.central_receive.receive_list(self.rep_info['warehouse_number'], self.rep_info['po_number'])
        assert result[0]["receiveInfoList"] != [], "生成receive_task失败"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_as_appreceive(self):
        """
        [112679]as商品APP收货流程
        """
        # 执行收货操作
        poreceive_process.app_receive(warehouse_number=self.rep_info['warehouse_number'],
                                      po_number=self.rep_info['po_number'])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_geek_putaway(self):
        """
        [109420]Geek商品上架上报geek流程
        """
        # 获取上架warehouse_number、location_no、sku
        self.rep_info['putaway_warehouse_no'] = global_data.geek_putaway_info['warehouse_number']
        self.rep_info['location_no'] = global_data.geek_putaway_info['location_no']
        self.rep_info['item_number'] = global_data.geek_putaway_info['item_number']
        self.rep_info['po_number'] = global_data.geek_receive_info['reference_no']

        # task_id_rec_id_lpn_no, pallet_no = wms.putaway_operation(self.rep_info['putaway_warehouse_no'], self.rep_info['po_number'] , self.rep_info['item_number'], type=2)
        task_id_rec_id_lpn_no, pallet_no=poreceive_process.putaway_operation(putaway_warehouse_no=self.rep_info['warehouse_number'],
                                            po_number=self.rep_info['po_number'],
                                            in_user=self.rep_info['userid'],
                                            item_number=self.rep_info['item_number'],
                                            type=2)
        self.rep_info['task_id_rec_id_lpn_no'] = task_id_rec_id_lpn_no
        self.rep_info['pallet_no'] = pallet_no


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_geek_putaway_post(self):
        '''
        [112980]geek系统上架完成回调流程
        '''
        #获取上架的rec_id、lpn
        for rec_id_lpn_no_list in self.rep_info['task_id_rec_id_lpn_no'].values():
            for rec_id_lpn_no in rec_id_lpn_no_list:
                rec_id, lpn_no = rec_id_lpn_no[0], rec_id_lpn_no[1]
                mlpn_no = '-'.join(lpn_no.split('-')[0:3])
                plan_qty = wms.wms_db.get_plan_qty(self.rep_info['putaway_warehouse_no'], lpn_no )['original_quantity']
                result = wms.wms_db.get_replenlist_id(mlpn_no, self.rep_info['putaway_warehouse_no'])
                putaway_no = result['putaway_no']
                batch_no = wms.wms_db.get_inventory_batch_no(self.rep_info['putaway_warehouse_no'], lpn_no)['batch_no']
                wms.put_away.autostore_geek_host_receive(self.rep_info['pallet_no'], lpn_no,self.rep_info['item_number'],
                                                         plan_qty, batch_no, putaway_no)







if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net",
                  case_list=["test_geek_receive_putaway.py::TestCentralReceiveAPIGeek"], debug=True, open=False,
                  ext=['-m', 'WMS', "--junitxml=tests/results.xml"])