# !/usr/bin/python3
# -*- coding: utf-8 -*-
import weeeTest
import math
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.adjust_approve_process import AdjustApproveProcess


class TestMoveTools(weeeTest.TestCase):
    def setup_class(self):
        """Set up common data and login for all tests."""
        self.warehouse_number = global_data.moving_tool['warehouse_number']
        self.item_number = global_data.moving_tool['item_number']
        self.upc_code = global_data.moving_tool['upc_code']
        self.upc_location = global_data.moving_tool['upc_location']
        self.lpn_location = global_data.moving_tool['lpn_location']
        self.e_tote = global_data.moving_tool['e_tote']
        self.lpn_e_tote = global_data.moving_tool['lpn_e_tote']
        self.pieces_per_pack = global_data.moving_tool['pieces_per_pack']
        self.receive_date = global_data.moving_tool['receive_date']
        self.expire_date = global_data.moving_tool['expire_date']
        user_id, user_name = wms.wms_login.common_login()
        self.in_user = f"{user_name}({user_id})"

    def _perform_upc_move_and_verify(self, location_from, location_to, upc_code, qty, move_in):
        """Helper function to perform and verify a UPC move."""
        # Get from location inv
        inv_list = wms.moving_api.app_query_location_inv(self.warehouse_number, location_from)
        item_inv = jmespath(inv_list, f"[?item_number=='{self.item_number}']")

        # 若from location avaiable qty 太少，则加库存(Bin才加库存)
        if not item_inv and len(location_from) > 8:
            wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, self.in_user, location_from, 100, is_lpn=False, pieces_per_pack=self.pieces_per_pack,
                                            expire_dtm=self.expire_date, receive_date=self.receive_date)
            # 审核库存
            AdjustApproveProcess().inv_approve_operate(self.in_user, self.item_number, self.warehouse_number, location_from, action_type=3)

        # Call item check API
        resp = wms.moving_api.check_item(self.warehouse_number, location_from, upc_code, move_in)["body"]
        item_info = jmespath(resp, f"[?item_number=='{self.item_number}']")[0]
        batch_list = item_info["batchInvList"]
        batch_info = {}
        for b in batch_list:
            if wms.util.utc_to_day(b["receive_dtm"]) == self.receive_date and wms.util.utc_to_day(b["expire_dtm"]) == self.expire_date and int(float(b["pieces_per_pack"])) == self.pieces_per_pack:
                batch_info = b
                break
        assert batch_info, f"from location:{location_from} 中无相同规格的Batch No"
        expire_dtm = batch_info["expire_dtm"]
        receive_dtm = batch_info["receive_dtm"]

        # Get quantity before move
        pre_inv = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=self.item_number, location_no=location_to)
        qty_old = 0
        if pre_inv:
            qty_old = jmespath(pre_inv, "[0].quantity")

        # Perform move
        wms.moving_api.move_upc(batch_info["batch_no"], location_from, location_to, qty, self.item_number, self.warehouse_number, expire_dtm,
                                 move_in, receive_dtm, self.in_user)

        # Get quantity after move
        post_inv = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=self.item_number, location_no=location_to)
        assert post_inv, f"使用通用工具转以后在Central中查不到库存记录,仓库{self.warehouse_number},item_number:{self.item_number}"
        qty_new = jmespath(post_inv, "[0].quantity")
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移后库存错误"
        weeeTest.log.info(f'Successfully moved UPC {upc_code} from {location_from} to {location_to}')

    def _perform_lpn_move_and_verify(self, location_from, location_to, move_in):
        """Helper function to perform and verify an LPN move."""
        # Get LPN info
        resp1 = wms.moving_api.query_item_qty(item_number=self.item_number, location_to=location_from,
                                               warehouse_number=self.warehouse_number)
        # 若from location avaiable qty 太少，则加库存(Stock才加库存)
        if len(resp1) == 0 and len(location_from) > 8:
            wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, self.in_user, location_from, 100, is_lpn=True, pieces_per_pack=self.pieces_per_pack,
                                            expire_dtm=self.expire_date, receive_date=self.receive_date, lpn_qty=math.ceil(100/self.pieces_per_pack))
            # 审核库存
            AdjustApproveProcess().inv_approve_operate(self.in_user, self.item_number, self.warehouse_number, location_from, action_type=3)
            resp1 = wms.moving_api.query_item_qty(item_number=self.item_number, location_to=location_from, warehouse_number=self.warehouse_number)

        lpn_list = jmespath(self.response, "object[0].lpnDetailDTOList")
        lpn_no = lpn_list[0]["lpnNo"]
        upc_code = lpn_no  # For LPN moves, upc_code is the LPN number

        qty = next((entry["quantity"] for entry in lpn_list if entry["lpnNo"] == lpn_no), None)
        assert qty, f"Could not find quantity for LPN {lpn_no}"

        # Call item check API
        resp = wms.moving_api.check_item(self.warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body[0].expire_dtm")
        receive_dtm = jmespath(resp, "body[0].receive_dtm")
        lpn_info_lists = jmespath(resp, "body[0].lpn_info_list")
        lpn_info_list = next((item for item in lpn_info_lists if any(lpn["lpn_no"] == lpn_no for lpn in item["lpn_no_list"])))

        # Get quantity before move
        pre_inv = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=self.item_number, location_no=location_to)
        qty_old = 0
        if pre_inv:
            qty_old = jmespath(pre_inv, "[0].quantity")

        # Perform move
        wms.moving_api.move_lpn(location_from, location_to, qty, self.item_number, self.warehouse_number, expire_dtm,
                                 receive_dtm, lpn_info_list, self.in_user)

        # Get quantity after move
        post_inv = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=self.item_number, location_no=location_to)
        assert post_inv, f"使用通用工具转以后在Central中查不到库存记录,仓库{self.warehouse_number},item_number:{self.item_number}"
        qty_new = jmespath(post_inv, "[0].quantity")
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移后库存错误"
        weeeTest.log.info(f'Successfully moved UPC {upc_code} from {location_from} to {location_to}')

    # Move Out Tests
    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc1(self):
        """
        【111582】MoveOut：【UPC】Bin转移到E0001
        """
        self._perform_upc_move_and_verify(
            location_from=self.upc_location,
            location_to=self.e_tote,
            upc_code=self.upc_code,
            qty=2,
            move_in=False
        )

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc2(self):
        """
        【111583】MoveOut：【UPC】Bin转移到D-Spoilage
        """
        self._perform_upc_move_and_verify(
            location_from=self.upc_location,
            location_to="D-Spoilage",
            upc_code=self.upc_code,
            qty=1,
            move_in=False
        )

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc3(self):
        """
        【111584】MoveOut：【UPC】D-Spoilage转移到E0001
        """
        self._perform_upc_move_and_verify(
            location_from="D-Spoilage",
            location_to=self.e_tote,
            upc_code=self.upc_code,
            qty=1,
            move_in=False
        )

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc4(self):
        """
        【111585】MoveOut：【UPC】bin转移到E0001,E0001再转移到新的Bin
        """
        # Clear inventory
        new_bin = global_data.moving_tool['new_upc_location']
        locationNos = [new_bin]
        wms.moving_api.clear_inv(self.warehouse_number, self.in_user, locationNos)
        weeeTest.log.info(f"{new_bin}库存已清除......")

        # Move from bin to E0008
        self._perform_upc_move_and_verify(
            location_from=self.upc_location,
            location_to=self.e_tote,
            upc_code=self.upc_code,
            qty=1,
            move_in=False
        )

        # Move from E0008 to new bin
        self._perform_upc_move_and_verify(
            location_from=self.e_tote,
            location_to=new_bin,
            upc_code=self.upc_code,
            qty=1,
            move_in=False
        )

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_lpn1(self):
        """
        【112894】MoveOut：【LPN】Stock转移到E0008
        """
        self._perform_lpn_move_and_verify(
            location_from=self.lpn_location,
            location_to=self.lpn_e_tote,
            move_in=False
        )

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_lpn2(self):
        """
        【112895】MoveOut：【LPN】Stock转移到D-Spoilage
        """
        self._perform_lpn_move_and_verify(
            location_from=self.lpn_location,
            location_to="D-Spoilage",
            move_in=False
        )

    # Move In Tests
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_upc_1(self):
        """
        【111586】MoveIn：【UPC】E0001转移到Bin
        """
        self._perform_upc_move_and_verify(
            location_from=self.e_tote,
            location_to=self.upc_location,
            upc_code=self.upc_code,
            qty=1,
            move_in=True
        )

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_upc_2(self):
        """
        【111587】MoveIn：【UPC】E0001转移到D-Stock
        """
        self._perform_upc_move_and_verify(
            location_from=self.e_tote,
            location_to="D-Stock",
            upc_code=self.upc_code,
            qty=1,
            move_in=True
        )

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_move_in_lpn_1(self):
        """
        【112899】MoveIn：【LPN】E0008转移到Stock
        """
        self._perform_lpn_move_and_verify(
            location_from=self.lpn_e_tote,
            location_to=self.lpn_location,
            move_in=True
        )

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
