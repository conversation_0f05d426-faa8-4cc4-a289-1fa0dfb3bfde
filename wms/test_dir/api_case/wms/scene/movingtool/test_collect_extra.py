# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestCollectExtra(weeeTest.TestCase):
    """
    CollectExtra流程
    """

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "Debug", "WMS-PRD")
    def test_transship_receive(self):
        """
        【112238】CollectExtra 流程
        """
        warehouse_number = global_data.collect_extra_data["warehouse_number"]
        location_no = global_data.collect_extra_data["location_no"]
        upc = global_data.collect_extra_data["upc"]
        wms.wms_login.common_login()

        # 进入collect extra搜索商品
        item_list = wms.collect_extra_api.collect_extra_search_item(warehouse_number, upc)["body"]["items"]
        assert item_list !=[], f"使用UPC:{upc}未搜索到商品"

        item_info = item_list[0]
        item_number = item_info["item_number"]
        pre_item_inv = wms.adjust_api.query_location_inv_list(warehouse_number, location_no=location_no, item_number=item_number)
        quantity = 0
        adjust_quantity = 1
        if pre_item_inv:
            quantity = pre_item_inv[0]["quantity"]
        expire_dtm_str = wms.util.get_special_date(days=365)
        receive_dtm_str = wms.util.get_special_date()
        wms.collect_extra_api.collect_extra_complete(warehouse_number, location_no, item_number, adjust_quantity, item_info["pieces_per_pack"], expire_dtm_str, receive_dtm_str)
        # 检查库存
        post_item_inv = wms.adjust_api.query_location_inv_list(warehouse_number, location_no=location_no, item_number=item_number)
        assert post_item_inv, "collect extra 流程完成后Central inventory summary中查询不到数据"
        assert post_item_inv[0]["quantity"] == quantity+adjust_quantity, f'collect extra 流程完成后,{location_no}中{item_number}的库存应等于{quantity+adjust_quantity},实际等于{post_item_inv[0]["quantity"]}'


if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)