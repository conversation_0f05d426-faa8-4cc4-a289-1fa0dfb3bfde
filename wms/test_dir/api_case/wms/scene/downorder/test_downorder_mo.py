import time
import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms

class TestDownOrderMo(weeeTest.TestCase):
    """
    订单下发仓库测试
    """

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mo_down_order(self,order_id = None):
        """
        【107307】MO订单下发测试
        """
        # 登录
        wms.wms_login.common_login()

        if order_id is None:
            order_id = 42654667  # 默认订单ID

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result is not None:
            delivery_id = result['delivery_id']

            # 重置数据并下发
            tms.login()
            wms.reset_data(delivery_id)
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            wms.check_data(delivery_id_new)
        else:
            # 直接下发
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            wms.check_data(delivery_id_new)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
