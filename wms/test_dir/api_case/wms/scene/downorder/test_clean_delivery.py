# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_clean_delivery.py
@Description    :  
@CreateTime     :  2024/12/27 11:39
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/27 11:39
"""

import weeeTest

from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms
import json

class TestClean(weeeTest.TestCase):
    """
    订单下发仓库测试
    """

    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_clean_delivery_data(self):
        """
        发货批次数据清理
        """
        tms.login()

        delivery = 43057
        #重置WMS Data
        wms.wms_db.clean_wms_data(delivery_id=delivery)

        #重置FPO数据
        tms.tms_db.clean_fpo_data(delivery_id=delivery)

        #重置TMS数据
        sub_region_id = tms.tms_db.get_sub_region_id_by_delivery(delivery_id=delivery)
        if sub_region_id != []:
            delivery_date = tms.tms_db.get_delivery_date(delivery_id=delivery)
            print(sub_region_id,delivery_date)
            tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids= sub_region_id)

        #重置ERP数据
        tms.tms_db.delete_delivery(delivery_id=delivery)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
