# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  testdeom.py
@Description    :
@CreateTime     :  2023/11/26 20:43
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/26 20:43
"""

import weeeTest
from tms.test_dir.api_case.tms import tms_sql
from wms.test_dir.api_case.wms import wms_sql
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms


class TestDownOrderJob(weeeTest.TestCase):
    """
    订单下发仓库测试
    """
    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_sent_to_wms(self,order_id = None):
        """
        下发指定订单到WMS
        """
        if order_id is None:
            order_id = 42624315  # 默认订单ID

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result is not None:
            delivery_id = result['delivery_id']

            # 重置数据并下发
            tms.login()
            wms.reset_data(delivery_id)
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            wms.check_data(delivery_id_new)
        else:
            # 直接下发
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            wms.check_data(delivery_id_new)



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
