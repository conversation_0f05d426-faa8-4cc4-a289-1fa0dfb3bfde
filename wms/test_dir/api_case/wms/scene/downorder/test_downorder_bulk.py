# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_downorder_bulk.py
@Description    :
@CreateTime     :  2025/2/17 18:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/2/17 18:38
"""

import time
import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms

class TestDownOrderBulk(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_down_order(self,order_id = None):
        """
        【107305】Bulk订单下发测试
        """
        warehouse_number = global_data.normal_picking['warehouse_number']
        # 登录
        user_id, user_name = wms.wms_login.common_login()

        # 指定订单
        if order_id is None:
            order_id = 42666234

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result != None:
            delivery_id = result['delivery_id']

            # 重置数据并下发
            tms.login()
            wms.reset_data(delivery_id)
            # 查询订单干货SKU
            result1= tms.tms_db.query_order_sku(order_id=order_id,storage_type="N")
            item_number = result1[0][0]
            # 查询down单模型，确认支持bulk
            wms.common_api.update_sys_config(config_id=1403, config_key="wmsso:downorder:model",
                                             warehouse_number=warehouse_number,
                                             config_value='[{ "model": "all", "freshFrozenMerge": false, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }]')

            # 标记Dry SKU为Bulk sku
            wms.down_order.bulk_sku_tag(warehouse_number=warehouse_number, item_number=item_number, user_id=user_id,
                                        attributeCode="11", attributeView="outbound_by_case", updateValue=True)
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            # check bulk tag
            order_tag = wms.wms_db.get_order_tag(so_order_id=order_id,
                                                 info=['wot.order_id', 'wot.tag_id', 'wot.tag_desc', 'wom.item_number'])
            print(order_tag)
            if order_tag['item_number'] == item_number and (
                    order_tag['tag_id'] == 3 or order_tag['tag_desc'] == 'bulk order'):
                print("bulk拆分成功")
            wms.check_data(delivery_id_new)


        else:
            # 直接下发 标记干货SKU
            result1 = tms.tms_db.query_order_sku(order_id=order_id, storage_type="N")
            item_number = result1[0][0]
            # 查询down单模型，确认支持bulk
            wms.common_api.update_sys_config(config_id=1403, config_key="wmsso:downorder:model",
                                             warehouse_number=warehouse_number,
                                             config_value='[{ "model": "all", "freshFrozenMerge": false, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }]')

            # 标记Dry SKU为Bulk sku
            wms.down_order.bulk_sku_tag(warehouse_number=warehouse_number, item_number=item_number, user_id=user_id,
                                        attributeCode="11", attributeView="outbound_by_case", updateValue=True)
            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            # check bulk tag
            order_tag = wms.wms_db.get_order_tag(so_order_id=order_id,
                                                 info=['wot.order_id', 'wot.tag_id', 'wot.tag_desc', 'wom.item_number'])
            print(order_tag)
            if order_tag['item_number'] == item_number and (
                    order_tag['tag_id'] == 3 or order_tag['tag_desc'] == 'bulk order'):
                print("bulk拆分成功")

            wms.check_data(delivery_id_new)



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
