# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_cutoff_manage.py
@Description    :
@CreateTime     :  2025/6/27 13:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/27 13:30
"""

import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import wms


class TestCutoffManage(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'FPO')
    def test_cutoff_manage(self):
        """
        【107305】FPO Region截单
        """
        # 登录
        wms.wms_login.common_login()

        # 截单时间 当前时间+2days
        cutoff_date = wms.util.get_special_date(days=2)
        current_time = wms.util.get_current_time()
        response = wms.down_order_job.cutoff_config(delivery_date=cutoff_date, region_id=20, warehouse_no=20)

        if not response['success']:
            res = wms.down_order_job.get_date_config(
                params=f'start_date_str={cutoff_date}&end_date_str={cutoff_date}&region_type=0&region_id=20&pageSize=15&startColumn=0')
            config_id = res['body']['data'][0]['id']
            last_order_time = res['body']['data'][0]['last_order_time']

            if last_order_time <= current_time:
                wms.down_order_job.editDeliveryConfig(config_id=config_id, cutoff_time=f"{cutoff_date} 00:00:00")
            wms.down_order_job.cutoff_config(delivery_date=cutoff_date, region_id=20, warehouse_no=20)

        # 查询履约日期，截单发货日不再返回
        wms.util.wait()
        date_result = wms.down_order_job.get_performance_date(zipcode=77070)

        # Grocery履约
        delivery_dates = jmespath(date_result, "body.delivery_dates[*].delivery_date")
        assert cutoff_date not in delivery_dates, f"region_id=20, delivery_date={cutoff_date}已cutoff,但履约接口仍然可以查到"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
