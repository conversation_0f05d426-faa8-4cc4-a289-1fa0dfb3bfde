import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.scene.count.count_utils import CountApi


class TestCountStock(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.edit_user = self.user_name + '(' + self.user_id + ")"  # 拼接in_user
        self.warehouse_number = global_data.get_count['warehouse_number']
        self.storage_type = global_data.get_count['storage_type']
        self.stock_location_type = global_data.get_count['stock_location_type']
        self.status = global_data.get_count['status']
        self.stock_module_name = global_data.get_count['stock_module_name']
        self.restock_type = global_data.get_count['restock_type']
        self.diff_qty = 1  # 差异值
        self.in_user = self.edit_user

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_count(self):
        """
        【112888】Stock盘点:领取任务+盘点全流程
        """
        weeeTest.log.info('盘点开始............................')
        # 调用getList4Count领取任务
        CountApi().get_stock_task(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status,
                                  self.stock_module_name, self.restock_type)
        # 调用Stock盘点通用方法
        CountApi().count_stock_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.diff_qty,
                                      self.in_user, self.user_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_qty(self, count_id=12558288, location_no="D2106-2-1"):
        """
		【111600】Stock_LPN盘点:数量盘点
		"""
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        # 调用Stock盘点通用方法
        CountApi().count_stock_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.diff_qty, self.in_user, self.user_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_empty(self, count_id=12558289, location_no="D0101-1-3"):
        """
		【111602】Stock_LPN盘点:empty流程
		"""
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        weeeTest.log.info('盘点开始............................')
        # 调用empty通用方法
        CountApi().count_empty_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.in_user, self.user_id, count_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_lpn_count_lpn_missing(self, count_id=12558290, location_no="D0102-1-2"):
        """
		【111596】Stock_LPN盘点:LPN Missing流程
		"""
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        weeeTest.log.info('盘点开始............................')
        # 调用Stock盘点通用方法
        CountApi().count_stock_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.diff_qty, self.in_user, self.user_id, count_id, missing_lpn=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_upc_count_qty(self, count_id=12558291, location_no="D2106-1-1"):
        """
        【111594】Stock_UPC盘点:数量盘点
        """
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        # 调用Stock盘点通用方法
        CountApi().count_stock_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.diff_qty, self.in_user, self.user_id, count_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_upc_count_empty(self, count_id=12558292, location_no="D0102-2-2"):
        """
        【111602】Stock_UPC盘点:empty流程
        """
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        weeeTest.log.info('盘点开始............................')
        # 调用empty通用方法
        CountApi().count_empty_common(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                      self.in_user, self.user_id, count_id)

        # 退出登录
        wms.wms_login.logout(self.warehouse_number, self.user_id, self.user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
