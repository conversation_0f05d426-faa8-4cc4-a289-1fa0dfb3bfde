import weeeTest
from weeeTest import jmespath
from weeeTest.utils import jsonpath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.adjust_approve_process import AdjustApproveProcess


class CountApi(weeeTest.TestCase):

    def get_bin_task(self, warehouse_number, edit_user, storage_type, location_type, status, module_name):
        """
		领取bin库位的任务
		"""
        # 领取任务
        weeeTest.log.info('领取任务开始............................')
        resp = wms.count.count_get_bin_task(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务领取成功！！！')

    def get_stock_task(self, warehouse_number, edit_user, storage_type, location_type, status,
                       module_name, restock_type):
        """
		领取Stock库位的任务
		"""
        # 领取任务
        weeeTest.log.info('领取任务开始............................')
        resp = wms.count.count_get_stock_task(warehouse_number, edit_user, storage_type, location_type, status,
                                              module_name, restock_type)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务领取成功！！！')

    def release_task(self, edit_user):
        """
		盘点任务释放
		"""
        # 调用释放接口
        resp = wms.count.count_release_task(edit_user)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务释放成功！！！')

    def get_count_data(self, warehouse_number, edit_user, storage_type, location_type, status, module_name):
        """
		bin库位盘点参数获取
		"""
        # 进行盘点
        resp = wms.count.count_task_list(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert resp['success'] is True
        count_id = jmespath(resp, "body[0].rec_id")  # 获取info_id
        location_no = jmespath(resp, "body[0].location_no")  # 获取location_no
        status = jmespath(resp, "body[0].status")  # 获取status
        weeeTest.log.info(f'盘点参数：count_id: {count_id}, location_no:{location_no},status: {status}')
        return count_id, location_no, status

    def get_location_detail(self, count_id, status, module_name):
        """
		获取库位盘点明细
		"""
        resp1 = wms.count.scan_location(count_id, status, module_name)
        sku_list = len(resp1["body"])
        return sku_list, resp1

    def get_sku_detail(self, resp1, location_no, warehouse_number):
        """
		获取库位商品明细
		"""
        item_list = []
        is_lpn_list = []
        for item in resp1["body"]:
            item_number = item["item_number"]
            rec_id = item["rec_id"]
            resp2 = wms.count.goods(location_no, warehouse_number, item_number, rec_id)
            is_lpn = jmespath(resp2, "body.lpnList")
            item_list.append(item_number)
            is_lpn_list.append(is_lpn)
        return item_list, is_lpn_list

    def count_bin_upc(self, item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name):
        """
		bin位confirm
		"""
        # UPC盘点
        if status == 1:
            in_user = "yu.yang.20(7644738)"  # 指定二盘in_user
        if status == 2:
            in_user = "jing.wang55(7396768)"  # 指定三盘in_user
        inv_res = wms.adjust_api.query_location_inv(item_number, location_no, warehouse_number)["body"]["invAdjustList"]
        sys_qty = inv_res[0]["quantity"] or 0  # 判断sys_qty是否为空，为空则赋值为0
        input_qty = int(sys_qty) + int(diff_qty)  # 实际盘点数量
        wms.count.confirm_num(location_no, warehouse_number, item_number, input_qty, in_user, status, module_name)

    def count_stock_lpn(self, is_lpn, warehouse_number, location_no, item_number, in_user, status, module_name, missing_lpn=False):
        """
		Stock位confirm
		"""
        # LPN盘点
        if status == 1:
            in_user = "yu.yang.20(7644738)"  # 指定二盘in_user
        if status == 2:
            in_user = "jing.wang55(7396768)"  # 指定三盘in_user
        lpn_list = []  # 拼接confirm的LPN list
        for lpn in is_lpn:
            new_lpn_list = {
                "lpn_no": lpn["lpn_no"],
                "expired_dtm_str": None,
                "pieces_per_pack": lpn["pieces_per_pack"],
                "inputQty": lpn["lpn_quantity"],
                "warehouse_number": lpn["warehouse_number"],
                "item_number": lpn["item_number"],
                "location_no": lpn["location_no"],
                "lot_code": lpn["lot_code"],
                "date_type": lpn["date_type"]
            }
            lpn_list.append(new_lpn_list)
        if missing_lpn:
            missing_lpn = {
                "warehouse_number": "29",
                "item_number": "35256",
                "location_no": "B1005-1-2",
                "lpn_no": "",
                "expired_dtm_str": "2026-01-01",
                "inputQty": 25,
                "pieces_per_pack": "25",
                "lot_code": None,
                "date_type": 0
            }
            lpn_list.append(missing_lpn)
        # 调用confirm接口
        wms.count.lpn_confirm(warehouse_number, location_no, item_number, in_user, status, module_name, lpn_list)

    def count_bin_common(self, warehouse_number, edit_user, storage_type, location_type, status, module_name, diff_qty, in_user, user_id, count_id=None):
        """
        UPC盘点通用方法
        """
        # 查看领取的任务
        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        if count_id is not None:
            assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
        count_id = count_data[0]
        location_no = count_data[1]
        status = count_data[2]
        counted_status = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while counted_status < 3:
            detail = CountApi().get_location_detail(count_id, status, module_name)
            sku_list = detail[0]
            resp1 = detail[1]
            # SKU个数获取
            if sku_list > 0:
                sku_data = CountApi().get_sku_detail(resp1, location_no, warehouse_number)
                item_list = sku_data[0]
                is_lpn_list = sku_data[1]
                for i in range(len(item_list)):
                    item_number = item_list[i]
                    is_lpn = is_lpn_list[i]
                    # 判断是LPN盘点还是UPC盘点
                    if not is_lpn:
                        CountApi().count_bin_upc(item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name)
                    else:
                        pass  # 走LPN盘点,bin库位不走LPN流程
                # 盘点finish
                CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
                # 盘点结果检查
                counted_status, status = CountApi().count_checks(count_id, status, user_id, warehouse_number, location_no)
            else:
                # 没有SKU,走empty流程
                CountApi().count_empty_common(warehouse_number, edit_user, storage_type, location_type, status, module_name, in_user, user_id, count_id)
                # 盘点结果检查
                counted_status, status = CountApi().count_checks(count_id, status, user_id, warehouse_number, location_no)

    def count_finish(self, count_id, in_user, sku_list, module_name, status):
        """
		盘点finish
		"""
        # finish接口
        if status == 1:
            in_user = "yu.yang.20(7644738)"  # 指定二盘in_user
        if status == 2:
            in_user = "jing.wang55(7396768)"  # 指定三盘in_user
        wms.count.count_finish(count_id, in_user, sku_list, module_name, status)

    def count_checks(self, count_id, status, user_id, warehouse_number, location_no):
        """
		查询盘点状态是否完成
		"""
        count_list = wms.count.query_countinfo_list(warehouse_number, location_no)
        counted_status = int(jsonpath(count_list, f'$.[?(@.rec_id == {count_id})].status')[0])  # 获取当前盘点状态
        if counted_status == 1:
            weeeTest.log.info('一盘完成,待二盘......')
            # 更新盘点数据,继续二盘
            status = status + 1
            # 分配任务
            wms.count.assign_count_task(warehouse_number, count_id, 7644738)
            weeeTest.log.info(f'进行{status + 1}盘,count_id: {count_id}')
        if counted_status == 2:
            weeeTest.log.info('二盘完成,待三盘......')
            # 更新盘点数据,继续三盘
            status = status + 1
            # 分配任务wms_login
            wms.count.assign_count_task(warehouse_number, count_id, 7396768)
            weeeTest.log.info(f'进行{status + 1}盘,count_id: {count_id}')
        if counted_status == 4:
            weeeTest.log.info(f'count_id:{count_id},盘点完成！！！')
            # 审核表查询盘点审核任务，并进行盘点
            AdjustApproveProcess().inv_approve_operate(user_id, None, warehouse_number, location_no, 1, ["1"], approve_type=1)
        return counted_status, status

    def count_empty_common(self, warehouse_number, edit_user, storage_type, location_type, status, module_name, in_user, user_id, count_id=None):
        """
		empty盘点
		"""
        # 查看领取的任务
        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        if count_id is not None:
            assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
        count_id = count_data[0]
        location_no = count_data[1]
        status = count_data[2]
        counted_status = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while counted_status < 3:
            CountApi().get_location_detail(count_id, status, module_name)
            # 没有SKU,走empty流程
            wms.count.empty_scan_location(count_id, in_user, status, module_name)
            # 盘点结果检查
            counted_status, status = CountApi().count_checks(count_id, status, user_id, warehouse_number, location_no)

    def count_stock_common(self, warehouse_number, edit_user, storage_type, location_type, status, module_name, diff_qty, in_user, user_id, count_id=None,
                           missing_lpn=False):
        """
    	Stock_upc盘点通用方法
    	"""
        weeeTest.log.info('盘点开始............................')
        # 查看领取的任务
        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        if count_id is not None:
            assert count_id == count_data[0], "领取的任务ID和盘点ID不一致"  # 校验任务ID是不是和参数一致
        count_id = count_data[0]
        location_no = count_data[1]
        status = count_data[2]
        counted_status = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while counted_status < 3:
            detail = CountApi().get_location_detail(count_id, status, module_name)
            sku_list = detail[0]
            resp1 = detail[1]
            # SKU个数获取
            if sku_list > 0:
                sku_data = CountApi().get_sku_detail(resp1, location_no, warehouse_number)
                item_list = sku_data[0]
                is_lpn_list = sku_data[1]
                for i in range(len(item_list)):
                    item_number = item_list[i]
                    is_lpn = is_lpn_list[i]
                    # 判断是LPN盘点还是UPC盘点
                    if not is_lpn:
                        # UPC 盘点
                        CountApi().count_bin_upc(item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name)
                    else:
                        # LPN 盘点
                        CountApi().count_stock_lpn(is_lpn, warehouse_number, location_no, item_number, in_user, status, module_name, missing_lpn)
                # 盘点finish
                CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
                # 盘点结果检查
                counted_status, status = CountApi().count_checks(count_id, status, user_id, warehouse_number, location_no)
            else:
                # 没有SKU,走empty流程
                CountApi().count_empty_common(warehouse_number, edit_user, storage_type, location_type, status, module_name, in_user, user_id, count_id)
                # 盘点结果检查
                counted_status, status = CountApi().count_checks(count_id, status, user_id, warehouse_number, location_no)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
