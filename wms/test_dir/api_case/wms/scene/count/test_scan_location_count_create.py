import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.scene.count.count_utils import CountApi
from wms.qa_config import global_data


class TestCountScanLocation(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.edit_user = self.user_name + '(' + self.user_id + ")"  # 拼接in_user
        self.warehouse_number = global_data.get_count['warehouse_number']
        self.storage_type = global_data.get_count['storage_type']
        self.location_type = global_data.get_count['bin_location_type']
        self.module_name = global_data.get_count['scan_location_module_name']
        self.location_no = global_data.get_count['scan_location_location_no']
        self.diff_qty = 1  # 差异值
        self.in_user = self.edit_user

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_scan_location_count_create(self):
        """
        【111603】ScanLocation盘点
        """
        # 开始盘点
        weeeTest.log.info('盘点开始............................')
        # 查询库位盘点任务状态
        resp = wms.count.query_location_count_status(self.location_no, self.in_user, self.warehouse_number)
        count_id = jmespath(resp, "body.count_id")
        status = jmespath(resp, "body.status")
        # 判断任务是否被其他人领取
        if status != 6:
            # 判断是否有任务
            if count_id is None:
                # 没任务,先生成新任务
                resp_new = wms.count.create_count_task_by_location(self.location_no, self.warehouse_number, self.in_user)
                count_id = jmespath(resp_new, "body.count_id")
                status = jmespath(resp_new, "body.status")
            # 开始盘点
            weeeTest.log.info(f'扫描库位开始盘点......')
            wms.count.scan_location(count_id, status, self.module_name)
            # 查询库位SKU_number
            sku_length = wms.adjust_api.query_location_inv_list(self.warehouse_number, self.location_no)
            # sku_length输出：[{'item_number': '15597'}, {'item_number': '98817'}]
            sku_list = len(sku_length)  # 库位SKU的个数,循环个数finish的时候参数需要
            weeeTest.log.info(f'库位SKU的个数：{sku_list}个')
            counted_status = status  # 根据盘点状态进行盘点
            # 盘点次数循环：1/2/3
            while counted_status < 3:
                # for循环盘点SKU
                if sku_list > 0:
                    # 根据库位SKU个数拼接item_numbers
                    data_list = sku_length
                    item_numbers = []
                    for data in data_list:
                        item_number = data["item_number"]
                        item_numbers.append(item_number)
                    weeeTest.log.info(f'库位SKU:{item_numbers}')
                    for item in item_numbers:
                        item_number = item
                        rec_id = count_id
                        resp2 = wms.count.goods(self.location_no, self.warehouse_number, item_number, rec_id)
                        is_lpn = jmespath(resp2, "body.lpnList")
                        if not is_lpn:
                            # UPC盘点
                            CountApi().count_bin_upc(item_number, self.warehouse_number, self.location_no, self.diff_qty, self.in_user,
                                                     status,
                                                     self.module_name)
                        else:
                            # LPN盘点
                            CountApi().count_stock_lpn(is_lpn, self.warehouse_number, self.location_no, item_number, self.in_user, status,
                                                       self.module_name)
                    # 盘点finish
                    CountApi().count_finish(count_id, self.in_user, sku_list, self.module_name, status)
                    # 盘点结果检查
                    counted_status, status = CountApi().count_checks(count_id, status, self.user_id, self.warehouse_number, self.location_no)

                else:
                    # 没有SKU,走empty流程
                    CountApi().count_empty_common(self.warehouse_number, self.edit_user, self.storage_type, self.location_type, status,
                                                  self.module_name, self.in_user, self.user_id, count_id)
                    # 盘点结果检查
                    counted_status, status = CountApi().count_checks(count_id, status, self.user_id, self.warehouse_number, self.location_no)
            weeeTest.log.info(f'新生成的盘点任务已盘点完成......')
        else:
            # 盘点任务审核中,status=6
            weeeTest.log.info(f'盘点任务待审核......')
        # 退出登录
        wms.logout(self.user_id, self.user_name, self.warehouse_number)
        weeeTest.log.info(f'已退出登录......')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
