import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.scene.count.count_utils import CountApi


class TestCountBin(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.edit_user = self.user_name + '(' + self.user_id + ")"  # 拼接in_user
        self.warehouse_number = global_data.get_count['warehouse_number']
        self.storage_type = global_data.get_count['storage_type']
        self.bin_location_type = global_data.get_count['bin_location_type']
        self.status = global_data.get_count['status']
        self.bin_module_name = global_data.get_count['bin_module_name']
        self.restock_type = global_data.get_count['restock_type']
        self.diff_qty = 1  # 差异值
        self.in_user = self.edit_user

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bin_count(self):
        """
		【112889】bin盘点:领取任务+盘点全流程
		"""
        weeeTest.log.info('盘点开始............................')
        # 调用getList4Count领取任务
        CountApi().get_bin_task(self.warehouse_number, self.edit_user, self.storage_type, self.bin_location_type, self.status, self.bin_module_name)
        # 调用UPC盘点通用方法
        CountApi().count_bin_common(self.warehouse_number, self.edit_user, self.storage_type, self.bin_location_type, self.status, self.bin_module_name,
                                    self.diff_qty,
                                    self.in_user, self.user_id, count_id=None)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bin_count_qty(self, count_id=12546522, location_no="A1218-1-4"):
        """
		【111593】bin盘点:数量盘点
		"""
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        CountApi().count_bin_common(self.warehouse_number, self.edit_user, self.storage_type, self.bin_location_type, self.status, self.bin_module_name,
                                    self.diff_qty, self.in_user, self.user_id, count_id)  # 调用盘点通用方法

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bin_count_empty(self, count_id=12558346, location_no="C0216-5-3"):
        """
		【111601】bin盘点:empty流程
		"""
        # 初始化盘点数据
        CountApi().release_task(self.edit_user)  # 释放任务
        wms.wms_db.update_count_data_qty(self.warehouse_number, count_id)  # 初始化盘点明细
        wms.wms_db.delete_count_info(self.warehouse_number, location_no, count_id)  # 作废库位多余的任务
        wms.count.assign_count_task(self.warehouse_number, count_id, self.user_id)  # 分配任务
        weeeTest.log.info('盘点开始............................')
        # 调用empty通用方法
        CountApi().count_empty_common(self.warehouse_number, self.edit_user, self.storage_type, self.bin_location_type, self.status, self.bin_module_name, self.in_user,
                                      self.user_id, count_id)

        # 退出登录
        wms.wms_login.logout(self.warehouse_number, self.user_id, self.user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
