import weeeTest
from wms.test_dir.api_case.wms.scene.count.count_utils import CountApi
from wms.test_dir.api.wms.wms import wms
from wms.qa_config import global_data


class TestGetTask(weeeTest.TestCase):
    def setup_class(self):
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        self.edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        self.warehouse_number = global_data.get_count['warehouse_number']
        self.storage_type = global_data.get_count['storage_type']
        self.bin_location_type = global_data.get_count['bin_location_type']
        self.stock_location_type = global_data.get_count['stock_location_type']
        self.status = global_data.get_count['status']
        self.bin_module_name = global_data.get_count['bin_module_name']
        self.stock_module_name = global_data.get_count['stock_module_name']
        self.restock_type = global_data.get_count['restock_type']

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_get_bin_task(self):
        """
		【111589】领取bin库位的任务
		"""
        # 领取bin库位任务
        CountApi().get_bin_task(self.warehouse_number, self.edit_user, self.storage_type, self.bin_location_type, self.status, self.bin_module_name)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_get_stock_task(self):
        """
		【111590】领取Stock库位的高层任务
		"""
        # 调用领取stock库位任务方法
        CountApi().get_stock_task(self.warehouse_number, self.edit_user, self.storage_type, self.stock_location_type, self.status, self.stock_module_name,
                                  self.restock_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_release_task(self):
        """
		【111592】盘点任务释放
		"""
        # 调用释放任务方法
        CountApi().release_task(self.edit_user)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
