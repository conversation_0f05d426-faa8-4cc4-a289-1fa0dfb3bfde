# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms
from wms.test_dir.api_case.wms.wms_assert_utils import WmsAssertUtils
from wms.test_dir.business.picking_process import general_picking_operate
from wms.test_dir.business.packing_process import packing_process
from wms.test_dir.business.route_check_process import route_check_operation
from wms.test_dir.business.adjust_inv_process import bin_adjust
from wms.test_dir.business.down_order_process import DownOrderProcess
from common.api.order_utils import OrderTestUtils


class TestOrderOutboundProcess(weeeTest.TestCase):
    """
    订单下单-down单-预占-Picking-Packing-Route Check全流程Case
    """

    def setup_class(self):
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.in_user = f"{self.user_name}({self.user_id})"
        self.warehouse_number = global_data.outbound_info["warehouse_number"]
        self.region_id = global_data.outbound_info["region_id"]
        self.region_name = global_data.outbound_info["region_name"]
        self.order_type_list = global_data.outbound_info["order_type"]
        self.zipcode = global_data.outbound_info["zipcode"]
        self.order_id = []
        self.delivery_id = ""
        self.down_order = DownOrderProcess()

        if not global_data.is_prod:
            # 清除历史数据
            self.delivery_date = global_data.outbound_info["delivery_date"]
            wms.update_central_config(self.warehouse_number, "wms:order:delivery_date", self.delivery_date)
            delivery_list = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            if delivery_list:
                tms.login()
                wms.reset_data(delivery_list[0]["id"])
        else:
            delivery_dates = wms.down_order.query_fulfill_date(self.zipcode)
            self.delivery_date = jmespath(delivery_dates, f"[?all_oos == `false`].delivery_date")[0]

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'order', 'Order-PRD')
    def test_purchase_order(self):
        """
        下单
        """
        # 生产环境实时下单，测试环境使用已有数据
        if global_data.is_prod:
            products = global_data.create_order_ptoduct
            order_id = OrderTestUtils().create_test_order(products, delivery_date=self.delivery_date)
            self.order_id.append(order_id)
            weeeTest.log.info(f"下单成功,Order_id={order_id}")
        else:
            pass

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_manual_down_order(self):
        """
        normal down order流程
        """
        self.down_order.manual_pull_order()
        for order_id in self.order_id:
            order_info = wms.down_order.fpo_order_page(order_id)
            assert order_info, f"Customer order:{order_id} does not exist in FPO"
            self.delivery_date = order_info[0]["delivery_date"]
            weeeTest.log.info(f"发货日变为{self.delivery_date}")
        self.down_order.generate_invoice(self.warehouse_number, self.delivery_date, [self.region_id], self.user_id)
        delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
        assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
        self.delivery_id = delivery_list_new[0]["id"]

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_cutoff_manage(self):
        """
        【107305】FPO Region截单(出库全流程)
        """
        self.down_order.cutoff_order(self.warehouse_number, self.delivery_date, self.region_id)

        # 查询履约日期，截单发货日不再返回
        wms.util.wait()
        date_result = wms.down_order.query_fulfill_date(self.zipcode)
        date_list = jmespath(date_result, "[*].delivery_date")
        assert self.delivery_date not in date_list, f"region_id={self.region_id}, delivery_date={self.delivery_date}已cutoff,但履约接口仍然可以查到"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_update_route_info(self):
        """
        排车下发WMS路线信息
        """
        weeeTest.log.info(f"排车的发货日为{self.delivery_date}")
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        invoice_list = wms.down_order.fpo_invoice_page(delivery_id=self.delivery_id)
        packing_num = 1
        for invoice in invoice_list:
            invoice_id = invoice["group_invoice_id"] if invoice["group_invoice_id"] else invoice["invoice_id"]
            wms_order_list = wms.down_order.query_invoice_order(invoice_id)
            for order in wms_order_list:
                orderId = str(order["orderId"])
                if order["orderType"] in [2,3,7]:
                    if len(orderId) >= 9 and orderId[-2:] in {'02', '03', '07'}:
                     orderId = orderId[:-2]
                wms.down_order.update_order_route_info([self.delivery_id], self.warehouse_number, invoice_id, packing_num, 9999, orderId)
            packing_num += 1

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_ready_for_picking(self):
        """
        ready_for_picking流程
        """
        DownOrderProcess().ready_for_picking(self.warehouse_number, self.delivery_date, self.region_name)
        region_list = wms.down_order.fpo_region_by_warehouse(self.warehouse_number, self.delivery_date)
        region_info =jmespath(region_list, f"[?region_id == `{self.region_id}`]")[0]
        assert region_info["ready_for_picking"] == True, f"{self.region_name}在{self.delivery_date} ready for picking失败"


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_generate_batch(self):
        """
        generate_batch流程
        """
        for order_type in self.order_type_list:
            # 查询无库存可预占的订单
            oos_info = wms.oos_api.get_order_oos_list_page(self.warehouse_number, self.delivery_date, order_type=order_type)
            if oos_info["recordsTotal"] !=0:
                oos_info = wms.oos_api.get_order_oos_list_page(self.warehouse_number, self.delivery_date, order_type=order_type, pageSize=oos_info["recordsTotal"])["data"]
                for item in oos_info:
                    location_no = None
                    if item["binList"]:
                        location_no = item["binList"][0]
                    bin_adjust(item["item_number"], self.warehouse_number,self.in_user, location_no=location_no)
            DownOrderProcess().generate_batch_operate(self.warehouse_number, self.delivery_date, order_type)
            wms.util.wait(sleep_time=1)

        # 查询是否还有未预占成功的订单        
        order_list = wms.batch.query_order_info(self.warehouse_number,self.delivery_date, status="0")
        assert len(order_list) == 0, f"仓库：{self.warehouse_number},delivery_data:{self.delivery_date},order_type:{order_type} 存在未预占成功的订单"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_normal_picking(self):
        """
        【112950】Normal picking 流程
        """
        for storage_type in ["1", "2", "3"]:
            general_picking_operate(self.warehouse_number, storage_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_dryice_picking(self):
        """
        【112950】frozen dry ice picking流程
        """
        general_picking_operate(self.warehouse_number, "7")


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mailorder_picking(self):
        """
        【112953】Mail Order picking 流程
        """
        warehouse_number = global_data.mo_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=50, picking_channel=1, module_name="mail_order")


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_oneitem_picking(self):
        """
        【112952】Oneitem picking 流程
        """
        general_picking_operate(self.warehouse_number, "3", location_type=52, is_oneitem=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_bulk_picking(self):
        """
        【112951】Bulk picking 流程
        """
        general_picking_operate(self.warehouse_number, "1", location_type=73, is_bulk=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mof_picking(self):
        """
        【112954】MOF picking 流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        general_picking_operate(warehouse_number, "6", location_type=50, picking_channel=1, module_name="mail_order", is_mof=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fbw_picking(self):
        """
        【112955】Fbw picking 流程
        """
        warehouse_number = global_data.fbw_picking['warehouse_number']
        storage_type = global_data.fbw_picking['storage_type']
        general_picking_operate(warehouse_number, storage_type, location_type=75)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','OOS')
    def test_normal_picking_oos(self):
        """
        【114910】Normal Picking OOS流程
        """
        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']
        general_picking_operate(warehouse_number, storage_type, is_oos=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'OOS','MO')
    def test_mo_picking_oos(self):
        """
        【115012】MO Picking OOS流程
        """
        warehouse_number = global_data.mo_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=50, picking_channel=1, module_name="mail_order",is_oos = True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'OOS','MOF')
    def test_mof_picking_oos(self):
        """
        【115222】MOF Picking OOS流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        general_picking_operate(warehouse_number, "6", location_type=50, picking_channel=1, module_name="mail_order", is_mof=True,is_oos = True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_oneitem_oos(self):
        """
        【114912】Oneitem oos流程
        """
        warehouse_number = global_data.one_item_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=52, is_oneitem=True,is_oos=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','bulk')
    def test_bulk_oos(self):
        """
        【114911】Bulk oos流程
        """
        warehouse_number = global_data.bulk_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=73, is_bulk=True,is_oos=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_normal_packing(self):
        """
        【112823】普通订单打包出库流程
        """
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        for order_type in self.order_type_list:
            storage_type = wms.select_order_storage_type(order_type)
            order_list = wms.batch.query_order_pick_type(self.warehouse_number, self.region_name, self.delivery_date, "10", self.delivery_id, pick_type=1, order_type=order_type)
            for order in order_list:
                if order["shipping_status"] in (50,51,60,61):
                    packing_process.general_packing_operate(self.warehouse_number, order["tote_no"], storage_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_bulk_packing(self):
        """
        【112835】Bulk打包出库流程
        """
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        for order_type in self.order_type_list:
            storage_type = wms.select_order_storage_type(order_type)
            order_list = wms.batch.query_order_pick_type(self.warehouse_number, self.region_name, self.delivery_date, "10", self.delivery_id, pick_type=4, order_type=order_type)
            cart_list = []
            for order in order_list:
                if order["shipping_status"] in (50,51,60,61) and order["tote_no"] not in cart_list:
                    cart_list.append(order["tote_no"])
            for cart_no in cart_list:
                packing_process.bulk_packing_operate(self.warehouse_number, cart_no, storage_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_oneitem_packing(self):
        """
        【112829】one item打包流程
        """
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        for order_type in self.order_type_list:
            storage_type = wms.select_order_storage_type(order_type)
            order_list = wms.batch.query_order_pick_type(self.warehouse_number, self.region_name, self.delivery_date, "10", self.delivery_id, pick_type=3, order_type=order_type)
            cart_list = []
            for order in order_list:
                if order["shipping_status"] in (50,51,60,61) and order["tote_no"] not in cart_list:
                    cart_list.append(order["tote_no"])
            for cart_no in cart_list:
                orderId_list = jmespath(order_list,  f"[?cart_no=='{cart_no}'].order_id")
                packing_process.oneitem_packing_operate(self.warehouse_number, cart_no, storage_type, orderId_list=orderId_list)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'Order-PRD')
    def test_route_check_process(self):
        """
        【112990】 Route check 流程
        """
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        for order_type in ["1", "2", "3", "7"]:
            route_check_operation(self.warehouse_number, self.delivery_date, 506, order_type, self.delivery_id)

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", 'Order-PRD')
    def test_oos_refund_order(self):
        """
        【111013】Order OOS Refund Move To Cot
        """
        if self.delivery_id == "":
            wms.util.wait(sleep_time=6)
            delivery_list_new = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
            assert delivery_list_new, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
            self.delivery_id = delivery_list_new[0]["id"]
        # 获取可做OOS refund的order id
        order_list = wms.oos_api.oos_refund_order_list(self.warehouse_number,self.delivery_date, self.in_user)
        # 获取可做OOS refund的Item List
        for order in order_list:
            rep = wms.oos_api.order_oos_refund_list(self.warehouse_number, order["order_id"])["body"]
            items_inv = {}
            item_list = []
            for r in rep:
                item_list.append({"item_number": r["item_number"], "quantity": r["quantity"]})
                item_inv = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=r["item_number"], location_no=f"COT0{str(self.warehouse_number)}")
                if item_inv:
                    items_inv[r["item_number"]] = [item_inv[0]["quantity"]]
                else:
                    items_inv[r["item_number"]] = [0]
                items_inv[r["item_number"]].append(r["quantity"])
            # 整单OOS Refund
            wms.oos_api.order_oos_refund(self.warehouse_number, order["order_id"], item_list, self.user_id, str(order["order_type"]))
            WmsAssertUtils().check_order_status(self.warehouse_number, order["order_id"], 73)
            for item_number, qty in items_inv.items():
                post_qty = wms.adjust_api.query_location_inv_list(self.warehouse_number, item_number=item_number, location_no=f"COT0{str(self.warehouse_number)}")[0]["quantity"]
                assert post_qty == qty[0] + qty[1], f"仓库：{self.warehouse_number},Item_number:{item_number}给库位COT0{self.warehouse_number}添加库存后，库存应为{qty[0] + qty[1]}，实际为{post_qty}"
                WmsAssertUtils().check_inventory_log(self.warehouse_number, item_number, order["order_id"], 262)

    def teardown_class(self):
        # 退出登录
        wms.logout(warehouse_number=self.warehouse_number, user_id=self.user_id, user_name=self.user_name)


if __name__ == '__main__':
    # weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
    # 只运行包含mark:api的case
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True, ext=["-m","aa"])

