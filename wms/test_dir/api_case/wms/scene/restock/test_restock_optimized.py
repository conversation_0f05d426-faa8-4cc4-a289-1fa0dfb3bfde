# !/usr/bin/python3
# -*- coding: utf-8 -*-
import time

import weeeTest
from jsonpath import jsonpath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestRestock(weeeTest.TestCase):
    """补货流程测试类"""

    def setup_class(self):
        """测试前准备工作"""
        # 登录系统
        self.account, self.user_name = wms.wms_login.common_login()

        # 初始化补货接口参数
        self.rep_info = globals()

        self.rep_info['warehouse_number'] = global_data.restock['warehouse_number']
        self.rep_info['storage_type'] = global_data.restock['storage_type']
        self.rep_info['itemNumber'] = global_data.restock['item_number']
        self.rep_info['jobitem_number'] = global_data.restock['jobitem_number']
        res = wms.restock.listStockInfos(warehouse_number=self.rep_info['warehouse_number'], item_number=self.rep_info['itemNumber'])
        stock_location = jsonpath(res, "$[0]")[0]
        self.rep_info['stock_location'] = stock_location
        self.rep_info['curdate'] = time.strftime("%Y-%m-%d", time.localtime())
        self.rep_info['delivery_date'] = global_data.restockjob['delivery_date']
        self.rep_info['configid'] = global_data.restockjob['configid']
        self.rep_info['itemlist'] = global_data.restockjob['itemlist']
        self.rep_info['inUser'] = self.user_name + '(' + str(self.account) + ')'

        # 设置仓库和存储类型
        wms.util.update_header(weee_warehouse=self.rep_info['warehouse_number'])
        wms.util.update_header(weee_wms_storage_type=self.rep_info['storage_type'])


    def teardown_class(self):
        """测试后清理工作"""
        # 退出登录
        wms.wms_login.logout(warehouse_number=self.rep_info['warehouse_number'], user_id=self.account, user_name=self.user_name)

    def _get_available_pallet(self):
        """获取可用的托盘"""
        datelist=wms.common_api.get_avaiable_location(warehouse_number=self.rep_info['warehouse_number'],location_type=38,flag=0)
        return jsonpath(datelist,'$[0].location_no')[0]


    def _create_restock_task(self,warehouse,user,item_number, stock_location, bin_location):
        """创建补货任务方法"""
        # 查询是否存在补货任务
        datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                           item_number=item_number, status=0)

        if datalist:
            assert jsonpath(datalist, '$[0].item_number')[0] == item_number
            recid = jsonpath(datalist, '$[0].rec_id')[0]
            wms.restock.delete_restock_task(recid, self.rep_info['inUser'])

        #获取商品库存lpn
        res = wms.adjust_api.query_location_inv(item_number=self.rep_info['itemNumber'],location_no=None,lpn_no=None, warehouse_number=self.rep_info['warehouse_number'])
        lpninfo = jsonpath(res, f"$.body.invAdjustList[?(@.location_no=='{stock_location}')].lpnInfos")[0]
        if lpninfo != []:
            lpn = jsonpath(res, f"$.body.invAdjustList[?(@.location_no=='{stock_location}')].lpnInfos[0].lpn_no")[0]
        else:
            lpn = ''

        # 创建补货任务
        wms.restock.add_restock_task(
            warehouse=warehouse,
            user=user,
            item_number=item_number,
            delivery_dtm_str=self.rep_info['curdate'],
            stock_location=stock_location,
            bin_location=bin_location,
            recommend_box=1,
            recommend_qty=1,
            lpn_no=lpn
        )

    def _create_restock_taskjob(self,itemlist):
        """job创建补货任务方法"""
        # 查询是否存在补货任务
        for item in itemlist:
            datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                           item_number=item, status=0)

            if datalist:
                assert jsonpath(datalist, '$[0].item_number')[0] == item
                recid = jsonpath(datalist, '$[0].rec_id')[0]
                wms.restock.delete_restock_task(recid, self.rep_info['inUser'])

        # 创建job任务
        wms.restock.generate_restock_task_job(
            warehouse_number=self.rep_info['warehouse_number'],
            user=self.rep_info['inUser']
        )

        timeout = 3  # 超时时间3秒
        start_time = time.time()
        task_found = False

        while time.time() - start_time < timeout:
            # 查询新创建的补货任务
            new_datalist = wms.restock.query_restock_task_list(
                self.rep_info['warehouse_number'],
                status=0
            )
            # 断言生成任务是否成功
            # if len(new_datalist) == 4:
            if new_datalist:
                assert 0 not in jsonpath(new_datalist, '$..priority')
                # 验证任务信息
                # item_numbers = jsonpath(new_datalist, '$..item_number')
                # for item in item_numbers:
                #     assert item in itemlist
                task_found = True
                break

            time.sleep(1)

        # 如果超时仍未找到任务，报错
        # if not task_found:
        #     raise AssertionError(f"生成任务失败：在{timeout}秒内未能查询到补货任务")

    def _get_or_create_restock_task(self, warehouse_number):
        """获取或创建补货任务

        Args:
            warehouse_number: 指定仓库编号，如果为None则使用当前仓库
        """
        # 如果指定了仓库，则切换到指定仓库
        if warehouse_number:
            wms.util.update_header(weee_warehouse=warehouse_number)

        #查询当前用户有无未做完的任务
        task = wms.restock.get_tasks(action=0,warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,
                                                    in_user=self.rep_info['inUser'],storage_type=self.rep_info['storage_type'])

        if not task:
            # 尝试创建新的补货任务
            self._create_restock_task(warehouse=self.rep_info['warehouse_number'],user=self.account,
                              item_number=self.rep_info['itemNumber'], stock_location=self.rep_info['stock_location'], bin_location='')

    def _execute_pick_process(self, pallet):
        """执行拣货流程"""
        body = wms.restock.pick_summary(warehouse_number=self.rep_info['warehouse_number'],
                                        storage_type=self.rep_info['storage_type'], inuser=self.rep_info['inUser'])
        pick_qty = {
            "groundPick": body["groundPickCount"],
            "airPick": body["airPickCount"],
            "fullPallet": body["fullPalletPickCount"]
        }

        # 拉取补货任务
        restock_type = {"groundPick": "1", "airPick": "2", "fullPallet": "3"}
        pick_type = restock_type[max(pick_qty, key=lambda key: pick_qty[key])]
        task = wms.restock.get_tasks(pick_type=pick_type, warehouse_number=self.rep_info['warehouse_number'],
                                     user_id=self.account,
                                     in_user=self.rep_info['inUser'], storage_type=self.rep_info['storage_type'])
        # 提取任务信息
        task_id = task[0]["rec_id"]
        stock_location = task[0]["stock_location"]
        recommend_box = task[0]["recommend_box"]
        recommend_qty = task[0]["recommend_qty"]

        # 扫描拣货pallet
        wms.restock.scan_pallet(warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,in_user=self.rep_info['inUser'],pallet=pallet)

        # 根据任务类型执行不同的拣货流程
        if task[0]["lpnNo"] != '':
            # LPN拣货流程
            lpn_list = wms.restock.scan_lpn_location(task_id, stock_location,self.rep_info['storage_type'], self.rep_info['warehouse_number'], self.rep_info['inUser'])["body"]["lpns"]
            pick_lpn = []
            pick_qty = 0
            for lpn in lpn_list:
                if pick_qty + lpn["quantity"] <= recommend_qty:
                    pick_lpn.append(lpn["lpnNo"])
                    pick_qty += lpn["quantity"]
                else:
                    break

            # 确认拣货
            wms.restock.pick_confirm(
                task_id, stock_location, recommend_box,
                recommend_qty, pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'],self.rep_info['storage_type'], lpn_flag=True, lpn_nos=pick_lpn
            )
        else:
            wms.restock.scan_location(task_id, stock_location,warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,in_user=self.rep_info['inUser'])
            # pick confirm
            wms.restock.pick_confirm(task_id, stock_location, recommend_box,
                                     recommend_qty, pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'],self.rep_info['storage_type'])

        # 验证pallet与商品绑定
        datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                       item_number=self.rep_info['itemNumber'], status=1)
        assert jsonpath(datalist, '$[0].pallet_no')[0] == pallet

        # 完成拣货，释放任务
        wms.restock.pick_complete(pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'], self.rep_info['storage_type'])

        # 验证拣货完成状态
        datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                       item_number=self.rep_info['itemNumber'], status=2)
        assert jsonpath(datalist, '$[0].status')[0] == 2
        return task_id

    def _execute_bin_load_process(self, pallet):
        """执行上架流程"""
        # 获取上架任务
        bin_load_task = wms.restock.bin_load_list(pallet,self.rep_info['warehouse_number'],self.account,self.rep_info['inUser'],self.rep_info['storage_type'])["list"]
        assert bin_load_task != [],"未获取到上架任务"

        task = bin_load_task[0]
        task_id = task["rec_id"]
        bin_location = task["bin_sn"]
        upc = task["upc"]
        real_take_box = task["real_take_box"]
        real_take_qty = task["real_take_qty"]
        is_lpn_task = task["isLpnTask"]
        item_number = task["item_number"]
        action = None

        # 扫描商品UPC
        slotting_logic_list = wms.restock.bin_load_list(pallet, self.rep_info['warehouse_number'],
                                                        self.account,self.rep_info['inUser'],self.rep_info['storage_type'], search_code=upc)["slottingLogicList"]

        # 确定上架库位
        if bin_location is '':
            if slotting_logic_list is not None:
                bin_location = slotting_logic_list[0]["locationNO"]
            else:
                stock_list = wms.restock.no_bin_recommendLocationList(warehouse_number=self.rep_info['warehouse_number'],in_user=self.rep_info['inUser'],location_type=3,item_number=item_number,recommend_total_qty=real_take_qty)
                bin_location = stock_list[0]
                action = 4

        # 根据任务类型执行不同的上架流程
        if is_lpn_task:
            # LPN上架流程
            can_scan_lpn_list = wms.restock.lpn_bin_load_detail(
                task_id, pallet, bin_location, action, self.rep_info['warehouse_number']
            )["canScanLpnList"]
            can_scan_lpn_list = list(map(lambda i: i["lpnNo"], can_scan_lpn_list))

            # 确认上架
            if action is None:
                action = 0
            wms.restock.lpn_bin_load_confirm(task_id, pallet, bin_location, can_scan_lpn_list, self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'], action)
        else:
            wms.restock.bin_load_get_detail(task_id, pallet, bin_location, action,warehouse_number=self.rep_info['warehouse_number'])

            # binload confirm
            wms.restock.bin_load_confirm(task_id, pallet, bin_location, real_take_box, real_take_qty, warehouse_number=self.rep_info['warehouse_number'], user_id=self.account, in_user=self.rep_info['inUser'])

        # 完成上架
        wms.restock.bin_load_complete(pallet, self.rep_info['warehouse_number'], self.rep_info['inUser'],self.account)

        # 验证pallet状态
        datelist=wms.common_api.get_avaiable_location(warehouse_number=self.rep_info['warehouse_number'],location_type=38,location_no=pallet)
        assert jsonpath(datelist,'$[0].flag')[0] == 0

        # 验证任务状态
        #查询当前任务的状态
        datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],task_id=task_id,
                                                       item_number=self.rep_info['itemNumber'], status=3)
        assert jsonpath(datalist, '$[0].status')[0] == 3

        #创建前删除任务
        # pick按照实际拉取的pick，拉取排除as任务
        # binload状态断言根据当前任务断言
        # 订单时间参数化，当前6.1日
        # job商品参数化

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_create_restock_task(self):
        """
        [114853]手动创建补货任务
        """
        #创建补货任务
        self._create_restock_task(warehouse=self.rep_info['warehouse_number'],user=self.account,
                              item_number=self.rep_info['itemNumber'], stock_location=self.rep_info['stock_location'], bin_location='')
        datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                       item_number=self.rep_info['itemNumber'], status=0)
        assert jsonpath(datalist, '$[0].item_number')[0] == self.rep_info['itemNumber'],"补货任务创建失败"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_restock_pick(self):
        """
        [114852]restock pick
        """
        # 模拟用户进入restock_pick模块
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'restock_pick')
        # 获取或创建补货任务
        self._get_or_create_restock_task(warehouse_number=self.rep_info['warehouse_number'])
        self.rep_info['pallet'] = self._get_available_pallet()
        # 执行restock pick流程
        self._execute_pick_process(self.rep_info['pallet'])
        # 模拟用户退出restock_pick模块
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'restock_pick', action=0)

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_restock_binload(self):
        """
        [114854]restock binload
        """
        # 模拟用户进入restock_bin_load模块
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'restock_bin_load')
        # 执行restock binload流程
        self._execute_bin_load_process(self.rep_info['pallet'])
        # 模拟用户进入restock_bin_load模块
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'restock_bin_load', action=0)

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','WMS-PRD')
    def test_create_restock_taskjob(self):
        """
        [114855]job创建补货任务
        """
        # wms.common_api.update_sys_config(config_key="wms:order:delivery_date", config_value=self.rep_info['delivery_date'],
        #                                  warehouse_number=self.rep_info['warehouse_number'], config_id=self.rep_info['configid'])

        # 生成job补货任务普通,4,6街属性的补货任务
        itemlist = self.rep_info['itemlist']
        self._create_restock_taskjob(itemlist)

        # 验证46街任务,as任务创建成功
        # datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
        #                                                item_number='1829', status=0)
        # assert jsonpath(datalist, '$[0].task_target_bin_type')[0] == 6
        # datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
        #                                                item_number='3608', status=0)
        # assert jsonpath(datalist, '$[0].task_target_bin_type')[0] == 5
        # datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
        #                                                item_number='14659', status=0)
        # assert jsonpath(datalist, '$[0].task_target_bin_type')[0] == 4

        for item in itemlist:
            datalist = wms.restock.query_restock_task_list(self.rep_info['warehouse_number'],
                                                           item_number=item, status=0)

            if datalist:
                assert jsonpath(datalist, '$[0].item_number')[0] == item
                recid = jsonpath(datalist, '$[0].rec_id')[0]
                wms.restock.delete_restock_task(recid, self.rep_info['inUser'])



if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
