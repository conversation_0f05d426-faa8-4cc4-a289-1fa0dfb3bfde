# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.utils import DataUtils
from wms.test_dir.api.wms.replenish.replenish_api import ReplenishAPI
import time, json, copy


class TestReplenishOos(weeeTest.TestCase):

    util = DataUtils()
    replenish = ReplenishAPI()
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_replenish_oos(self):
        """
        【115778】normal replenish oos流程
        """
        warehouse = global_data.replenish_oos['warehouse']
        tote_no = global_data.replenish_oos['tote_no']
        order_id = global_data.replenish_oos['order_id']
        storage_type = global_data.replenish_oos['storage_type']
        hot_tote = global_data.replenish_oos['hot_tote']
        # 登录
        account, username = wms.wms_login.common_login()
        self.replenish.warehouse = warehouse

        # 如果tote中有库存清空
        location = [tote_no]
        wms.moving_api.clear_inv(warehouse,"7390819",location)

        # 检查当前订单状态
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id)
        if shipping_status == 70:
            wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)
        else:
            # 检查数据库中有无上次执行中断的补单数据,有则强制结束，订单状态变为待打包
            ret = wms.wms_db.select_replenish_order(order_id=order_id)
            if isinstance(ret, dict):
                wms.replenish.checkout_force_complete(ret["tote_no"], ret["slot_no"])

        # 执行打包操作
        oos_item,is_batch, items, in_dtm = wms.normal_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_replenish=True)

        wms.normal_packing.query_replenish_quantity(tote_no, order_id)

        # 转补单
        wms.normal_packing.packing_replenish(tote_no, order_id, oos_item)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=42)

        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        # check inventory
        for item in items:
            if item["item_number"] != oos_item.get("item_number", ""):
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, in_dtm=in_dtm)
            else:
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)

        # 判断库位是否lock，是的话解锁
        target_location = "C0805-1-1"
        inv_data = wms.adjust_api.query_location_inv(oos_item["item_number"],target_location,warehouse)
        inv_adjust_list = inv_data["body"]["invAdjustList"]
        batch_no = None  # 初始化 batch_no

        # 遍历库存调整列表
        for inv_item in inv_adjust_list:
            if inv_item["location_no"] == target_location:
                # 获取 batch_no
                batch_no = inv_item.get("batch_no")

                # 检查该位置是否被锁定
                if inv_item["is_lock"] == 1:
                    assert inv_item["can_lock_unlock"], f"位置 {target_location} 不能被解锁"
                    # 这里调用解锁的方法
                    try:
                        wms.adjust_api.lock_inv(0, oos_item["item_number"], target_location, warehouse, False, None,
                                                batch_no)
                    except Exception as e:
                        assert False, f"解锁位置 {target_location} 时出错: {str(e)}"
                else:
                    assert inv_item["is_lock"] == 0, f"位置 {target_location}未锁定 "

                break  # 找到指定位置后退出循环
        else:
            assert False, f"未找到位置 {target_location}"

        # replenish order check in
        self.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        self.replenish.check_slot(slot_no, order_id)
        batch_no = self.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id, status=45)
        # 获取预占的bin位
        location_no = wms.wms_db.check_inventory_log_location(warehouse=warehouse,reference_id=order_id)

        # replenish item pick
        # hot_tote = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=26, flag=0,
        #                                                    info='location_no')
        hot_tote_status = wms.wms_db.check_tote_status(warehouse=warehouse,tote_no=hot_tote)
        if hot_tote_status == 3:
            wms.wms_db.update_tote_status(warehouse_number=warehouse, toteNo = hot_tote)
        # pick oos
        wait_picking_list = self.replenish.get_pick_list()
        self.replenish.scan_hot_tote(hot_tote)
        self.replenish.pick_oos_confirm(wait_picking_list, hot_tote)
        # 解锁库位
        wms.adjust_api.lock_inv(0, oos_item["item_number"],location_no,warehouse,False,None, batch_no)

        # 二次预占PICK
        wait_picking_list = self.replenish.get_pick_list()
        self.replenish.pick_confirm(wait_picking_list, hot_tote)

        # replenish item check in
        hot_pick_list = self.replenish.query_hot_pick_tote()["body"]
        assert hot_tote in list(map(lambda h: h["tote_no"], hot_pick_list)), f"{hot_tote}不在待check in的pick tote列表中"
        self.replenish.scan_hot_pick_tote(hot_tote)
        checkin_list = self.replenish.get_checkin_list(hot_tote)["body"]
        self.replenish.item_checkin_confirm(hot_tote, slot_no, checkin_list)
        self.replenish.finish_replenish(slot_no, tote_no)

        # check inventory check in tote
        wms.wms_db.check_inventory(location_no=tote_no, item_number=oos_item.get("item_number", ""), quantity=oos_item["item_quantity"])

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=50)

        # check tote status
        time.sleep(3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=hot_tote, status=0)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将order的状态置为结束，避免被其他业务使用
        wms.wms_db.update_order_ship_status(ship_status=70, order_id=order_id)
        wms.wms_db.update_location_status(warehouse=warehouse, location_no=hot_tote, flag=3)



if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
