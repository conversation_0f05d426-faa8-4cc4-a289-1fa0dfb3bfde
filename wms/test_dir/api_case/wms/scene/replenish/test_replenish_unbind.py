# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.utils import DataUtils
from wms.test_dir.api.wms.replenish.replenish_api import ReplenishAPI
import time, json, copy


class TestReplenishUnbind(weeeTest.TestCase):

    util = DataUtils()
    replenish = ReplenishAPI()
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_replenish_unbind(self):
        """
        【115907】normal replenish unbind流程
        """
        warehouse = global_data.replenish_unbind['warehouse']
        tote_no = global_data.replenish_unbind['tote_no']
        order_id = global_data.replenish_unbind['order_id']
        storage_type = global_data.replenish_unbind['storage_type']
        # 登录
        account, username = wms.wms_login.common_login()
        self.replenish.warehouse = warehouse

        # replenish order check in
        self.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        self.replenish.check_slot(slot_no, order_id)
        self.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id, status=45)

        self.replenish.get_user_replenish_list(storage_type)
        self.replenish.checkout_unbind(tote_no,slot_no)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=42)

        # check tote status
        time.sleep(3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
