# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_packing.py
@Description    :  
@CreateTime     :  2023/6/6 18:18
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 18:18
"""

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
import time, json, copy



class TestQueryPackingResult(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_qurey(self):
        """
        【114884】普通订单打包台detail 查询
        """
        warehouse = global_data.normal_packing_query['warehouse']
        day = global_data.normal_packing_query['day']
        order_packing_num = global_data.normal_packing_query['order_packing_num']
        order_type= global_data.normal_packing_query['order_type']
        storage_type= global_data.normal_packing_query['storage_type']

        # 登录
        account, username = wms.wms_login.common_login()
        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 查询订单详情
        res = wms.normal_packing.query_packing_result(day, order_packing_num, order_type)
        # 获取 item_number
        body = res['body']
        packing_order_items = body['packingOrderItems']
        packageDetails = body['packageDetails']

        # 确保 packingOrderItems 不为空
        assert packing_order_items, "packingOrderItems 为空"
        # 获取第一个商品的 item_number、tracking_num
        item_number = packing_order_items[0]['item_number']
        tracking_num = packageDetails[0]['tracking_num']
        if tracking_num:
            return tracking_num
        assert item_number == '25652', f"item_number 不匹配，期望 '25652'，实际为 '{item_number}'"

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_reprint(self):
        """
        【100320】普通订单打包台detail reprint
        """
        warehouse = global_data.normal_packing_query['warehouse']
        day = global_data.normal_packing_query['day']
        order_packing_num = global_data.normal_packing_query['order_packing_num']
        order_type= global_data.normal_packing_query['order_type']
        storage_type= global_data.normal_packing_query['storage_type']

        # 登录
        account, username = wms.wms_login.common_login()
        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))
        wms.normal_packing.user = username + '(' + account + ')'

        # 查询订单详情
        res = wms.normal_packing.query_packing_result(day, order_packing_num, order_type)

        first_package= res['body']['packageDetails'][0]
        # 获取重新打印前的tracking_num
        tracking_num_before = first_package ['tracking_num']
        assert tracking_num_before is not None and tracking_num_before != "", "tracking_num 为空"
        rec_id = first_package.get('rec_id')
        box_desc = first_package.get('box_desc')

        # 输入管理员密码后重新打印label
        wms.normal_packing.get_superPassword()
        label_info = wms.normal_packing.packing_reprint(box_desc, day, order_packing_num, rec_id )
        label = label_info['body']['label']
        assert label , "Label 为空,reprint失败"
        time.sleep(3)
        # 获取重新打印后的 tracking_num
        tracking_num_after = self.test_normal_packing_qurey()
        assert tracking_num_after == tracking_num_before, "reprint前后tracking_num不一致"

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_packing_reset(self):
        """
        【100321】普通订单打包台detail reset
        """
        warehouse = global_data.normal_packing_query['warehouse']
        day = global_data.normal_packing_query['day']
        order_packing_num = global_data.normal_packing_query['order_packing_num']
        order_type= global_data.normal_packing_query['order_type']
        storage_type= global_data.normal_packing_query['storage_type']

        # 登录
        account, username = wms.wms_login.common_login()
        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))
        wms.normal_packing.user = username + '(' + account + ')'

        # 查询订单详情
        res = wms.normal_packing.query_packing_result(day, order_packing_num, order_type)

        first_package= res['body']['packageDetails'][0]
        # 获取重新reset前的tracking_num
        tracking_num_before = first_package ['tracking_num']
        assert tracking_num_before is not None and tracking_num_before != "", "tracking_num 为空"

        # 输入管理员密码后reset
        wms.normal_packing.get_superPassword()
        box_id = wms.normal_packing.packing_queryResetLabel(order_packing_num)['box_id']
        label_info = wms.normal_packing.packing_reset(day, order_packing_num, box_id ,1 )
        assert label_info['body']['label'], "Label is empty, reset failed"

        time.sleep(3)
        # 获取重新打印后的 tracking_num
        tracking_num_after = self.test_normal_packing_qurey()
        assert tracking_num_after != tracking_num_before,  f"Tracking number didn't change. Before: {tracking_num_before}, After: {tracking_num_after}"

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)



if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
