# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestMOPacking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mo_packing(self):
        """
        【112831】mail order dry打包出库流程
        """
        warehouse = global_data.mod_packing['warehouse']
        tote_no = global_data.mod_packing['tote_no']
        order_id = global_data.mod_packing['order_id']
        storage_type = global_data.mod_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)
        # 登录
        account, username = wms.wms_login.common_login()

        # 执行打包操作
        wms.normal_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_mod=True)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
