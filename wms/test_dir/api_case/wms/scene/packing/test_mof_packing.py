# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestMofPacking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mof_packing(self):
        """
        【112837】mail order fresh 打包出库
        """
        warehouse = global_data.mof_packing['warehouse']
        tote_no = global_data.mof_packing['tote_no']
        order_id = global_data.mof_packing['order_id']
        storage_type = global_data.mof_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)

        # 登录
        account, username = wms.wms_login.common_login()

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置MOF storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 开始打包
        wms.mof_packing_operation(warehouse, tote_no, order_id, storage_type, account, username)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)
        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'MOF-OOS')
    def test_mof_forcestock_packing(self):
        """
        【112839】mail order fresh 缺发出库
        """
        warehouse = global_data.mof_forcestock_packing['warehouse']
        tote_no = global_data.mof_forcestock_packing['tote_no']
        order_id = global_data.mof_forcestock_packing['order_id']
        storage_type = global_data.mof_forcestock_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)

        # 登录
        account, username = wms.wms_login.common_login()

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置MOF storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 开始打包
        wms.mof_packing_operation(warehouse, tote_no, order_id, storage_type, account, username,is_oos=True )

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)
        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
