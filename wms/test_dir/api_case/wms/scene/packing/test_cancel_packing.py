# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestCancelPacking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_cancel_packing(self):
        """
        【112842】普通订单CS取消后打包台Move To Cot
        """
        warehouse = global_data.normal_cancel_packing['warehouse']
        tote_no = global_data.normal_cancel_packing['tote_no']
        order_id = global_data.normal_cancel_packing['order_id']
        storage_type = global_data.normal_cancel_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)

        # 登录
        account, username = wms.wms_login.common_login()

        #获取订单下商品在COT中的库存
        items_inv = {}
        order_item = wms.wms_db.get_wms_order_item(order_id)
        for item in order_item:
            item_inv = wms.wms_db.get_inventory_transaction(warehouse, f"COT0{str(warehouse)}", item["item_number"])
            if item_inv:
                items_inv[item["item_number"]] = item_inv["quantity"]
            else:
                items_inv[item["item_number"]] = 0

        # 执行打包操作
        is_batch, items, in_dtm = wms.normal_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_cancel=True)

        # check order shipping_status=72
        wms.wms_db.check_shipping_status(order_id=order_id, status=72)
        # check tote status
        wms.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)
        # check tote/cart inventory empty
        wms.wms_assert.check_location_empty(warehouse, tote_no)
        # check tote inventory move to cot tote

        for item in items:
            wms.wms_assert.check_non_batch_invenotry(warehouse, f"COT0{str(warehouse)}", item["item_number"], item["quantity"], reference_no=order_id, inventory_type=[801], old_qty=items_inv[item["item_number"]], in_dtm=in_dtm)
            if is_batch:
                wms.wms_assert.check_batch_invenotry(warehouse, f"COT0{str(warehouse)}", item["item_number"], item["quantity"], batch_no=item["batch_no"], reference_no=order_id, inventory_type=[801], in_dtm=in_dtm)

        #检查wh_packing_ordre中的deal_status
        packing_info = wms.wms_db.get_wh_packing_order_info(order_id)
        assert packing_info["deal_status"] == 30, "未更新wh_packing_order表的deal_status为已move to cot状态"
        assert packing_info["deal_mode"] == 1, "未更新wh_packing_order表的deal_mode为Packing Station → COT"

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mof_cancel_packing(self):
        """
        【112841】直邮生鲜订单CS取消后打包台Move To Cot
        """
        warehouse = global_data.mof_cancel_packing['warehouse']
        tote_no = global_data.mof_cancel_packing['tote_no']
        order_id = global_data.mof_cancel_packing['order_id']
        storage_type = global_data.mof_cancel_packing['storage_type']

        wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)

        # 登录
        account, username = wms.wms_login.common_login()

        #获取订单下商品在COT中的库存
        items_inv = {}
        order_item = wms.wms_db.get_wms_order_item(order_id)
        for item in order_item:
            item_inv = wms.wms_db.get_inventory_transaction(warehouse, f"COT0{str(warehouse)}", item["item_number"])
            if item_inv:
                items_inv[item["item_number"]] = item_inv["quantity"]

        # 执行打包操作
        items, in_dtm = wms.mof_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_cancel=True)

        # check order shipping_status=72
        wms.wms_db.check_shipping_status(order_id=order_id, status=72)
        # check tote status
        wms.wms_db.check_tote_status(tote_no=tote_no, warehouse=warehouse, status=0)
        # check tote/cart inventory empty
        wms.wms_assert.check_location_empty(warehouse, tote_no)
        # check tote inventory move to cot tote

        for item in items:
            wms.wms_assert.check_non_batch_invenotry(warehouse, f"COT0{str(warehouse)}", item["item_number"], item["quantity"], reference_no=order_id, inventory_type=[801], old_qty=items_inv[item["item_number"]], in_dtm=in_dtm)
            if item["is_batch"]:
                wms.wms_assert.check_batch_invenotry(warehouse, f"COT0{str(warehouse)}", item["item_number"], item["quantity"], batch_no=item["batch_no"], reference_no=order_id, inventory_type=[801], in_dtm=in_dtm)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, tote_no, 3)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)