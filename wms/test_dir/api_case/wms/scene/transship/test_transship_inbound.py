# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestTransshipInbound(weeeTest.TestCase):
    """
    调拨入库流程
    """

    def setup_class(self):
        # 相关接口需要的信息
        self.ts_receive_info = globals()
        # 登录
        account, username = wms.wms_login.common_login()
        self.ts_receive_info["user_id"] = account
        self.ts_receive_info["user_name"] = username
        wms.util.update_header(weee_user=username + "(" + account + ")")

        self.ts_receive_info["warehouse_number"] = global_data.ts_receive_info[
            "warehouse_number"
        ]

    # @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_transship_receive(self):
        """
        【110950】调拨单在入库仓收货
        """
        # 获取可用的调拨入库单
        reference_no_list = wms.label_receive.query_inbound_number(
            self.ts_receive_info["warehouse_number"],
            6,
            wms.util.get_special_date(days=-30),
            wms.util.get_special_date(days=0),
            self.ts_receive_info["user_id"],
        )
        assert reference_no_list != [], "无可用的调拨入库单"
        for order in reference_no_list:
            if (
                order["storage_type_name"] == "Frozen"
                and order["sku_qty"] == 1
                and order["total_unit_qty"] == 1
            ):
                self.ts_receive_info["reference_no"] = order["reference_no"]
                weee_wms_storage_type = wms.get_storage_type(
                    storage_type=order["storage_type_name"]
                )
                wms.util.update_header(weee_wms_storage_type=str(weee_wms_storage_type))
                break
        assert self.ts_receive_info.get("reference_no", "") != "", "可用的调拨入库单"

        # 获取调拨单的出库pallet
        self.ts_receive_info["ob_pallet"] = wms.wms_db.get_transship_ob_pallet(
            self.ts_receive_info["reference_no"]
        )["qc_pallet_no"]
        # 模拟用户进入Label Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        # wms.common_api.record_moudle_log(self.ts_receive_info["warehouse_number"], "label_receive")
        # 扫描PALLET 开始收货
        # wms.label_receive.query_po(self.ts_receive_info["ob_pallet"], self.ts_receive_info["warehouse_number"], 2, 6)
        # 获取ToDo List
        receive_list = wms.label_receive.query_receive_list(
            self.ts_receive_info["reference_no"],
            1,
            self.ts_receive_info["warehouse_number"],
        )["poItemList"]
        for r in receive_list:
            detail = wms.label_receive.label_receive_detail(
                self.ts_receive_info["reference_no"],
                r["item_number"],
                r["expired_dtm"],
                r["receive_dtm"],
                r["pieces_per_pack"],
            )
            # 收货前检查RECG0001好货区的库存
            pre_item_inv = wms.wms_db.get_inventory_transaction(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                detail["item_number"],
            )
            rec_qty = 0
            for lpn in detail["lpnInfoList"]:
                rec_qty += lpn["original_quantity"]
            confirm_res = wms.label_receive.label_receive_confirm(
                self.ts_receive_info["warehouse_number"],
                self.ts_receive_info["reference_no"],
                detail["item_number"],
                rec_qty,
                detail["pieces_per_pack"],
                detail["expire_dtm"],
                self.ts_receive_info["user_id"],
                detail["lpnInfoList"],
            )
            wms.label_receive.label_receive_print_label(
                self.ts_receive_info["warehouse_number"],
                confirm_res["rec_id"],
                self.ts_receive_info["user_id"],
            )

            # 检查库存
            wms.wms_assert.check_non_batch_invenotry(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                detail["item_number"],
                rec_qty,
                reference_no=self.ts_receive_info["reference_no"],
                inventory_type=[305],
                old_qty=pre_item_inv["quantity"],
                in_dtm=wms.util.get_current_time()
            )

            # 检查上架任务生成
            putaway_task = wms.wms_db.get_putaway_task(
                self.ts_receive_info["warehouse_number"],
                self.ts_receive_info["reference_no"],
                6,
                10,
                confirm_res["rec_id"],
            )
            assert putaway_task != [], "收货后未生成putaway task"

        # 检查业务单状态
        order_info = wms.wms_db.get_inbound_order_info(
            self.ts_receive_info["reference_no"]
        )
        assert order_info["status"] == 50, "全部收货后业务单状态未变为50"

    # @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_transship_receive_delete_batch(self):
        """
        【112939】调拨单在入库仓收货后错收冲减
        """
        # 获取调拨单也Done List
        done_list = wms.label_receive.query_receive_list(
            self.ts_receive_info["reference_no"],
            2,
            self.ts_receive_info["warehouse_number"],
        )["batchList"]
        for d in done_list:
            # 冲减前检查RECG0001好货区的库存
            pre_item_inv = wms.wms_db.get_inventory_transaction(
                self.ts_receive_info["warehouse_number"], "RECG0001", d["item_number"]
            )
            wms.label_receive.label_receive_delete_batch(
                self.ts_receive_info["warehouse_number"],
                d["rec_Id"],
                self.ts_receive_info["reference_no"],
                self.ts_receive_info["user_id"],
            )

            # 检查库存
            wms.wms_assert.check_non_batch_invenotry(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                d["item_number"],
                -d["rec_total"],
                reference_no=self.ts_receive_info["reference_no"],
                inventory_type=[306],
                old_qty=pre_item_inv["quantity"],
                in_dtm=wms.util.get_current_time()
            )
        # 检查业务单状态
        order_info = wms.wms_db.get_inbound_order_info(
            self.ts_receive_info["reference_no"]
        )
        assert order_info["status"] == 0, "已收货Batch全部错收冲减后,业务单状态未变为0"


if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
