# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.test_dir.api.wms.wms import wms


class TestTransshipOutbound(weeeTest.TestCase):
    """
    调拨出库流程
    """

    def setup_class(self):
        # 相关接口需要的信息
        self.ts_info = globals()


    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_truck_load(self):
        """
        【112815】truck load
        """
        # 登录
        self.ts_info['warehouse_number'] = "25"
        account, username = wms.wms_login.common_login()
        self.ts_info["user_id"] = account
        self.ts_info["user_name"] = username

        # 开始truck
        self.ts_info['ts_number'] = "*********"
        self.ts_info['truck_id'] = "TR-LAX1-ACL-********-1931"
        wms.ts_api.ts_check_truck(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"])
        wms.ts_api.truck_load_save_record(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"])
        wms.ts_api.truck_load_scan_dock(self.ts_info['warehouse_number'], self.ts_info['truck_id'], "Dock020", self.ts_info["user_id"])
        palletList = wms.ts_api.get_truck_pallet_list(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"])["body"]["palletList"]
        for pallet in palletList:
            pallet_no = pallet["pallet_no"]
            location_no = pallet["location_no"]
            wms.ts_api.truck_load_scan_pallet(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"], pallet_no)
            wms.ts_api.truck_load_pallet(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"], pallet_no, location_no)
        dock_log_id = wms.ts_api.get_truck_record(self.ts_info['warehouse_number'], self.ts_info['truck_id'])["body"][0]["dock_log_id"]
        wms.ts_api.truck_load_check_complete(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"])
        wms.ts_api.truck_load_complete(self.ts_info['warehouse_number'], self.ts_info['truck_id'], self.ts_info["user_id"], dock_log_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)