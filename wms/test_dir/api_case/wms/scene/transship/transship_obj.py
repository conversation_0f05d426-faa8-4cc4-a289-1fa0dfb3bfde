# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  transship_obj.py
@Description    :  
@CreateTime     :  2025/3/7 14:02
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/7 14:02
"""
import weeeTest
from wms.test_dir.api.wms.wms import wms



class TransshipObj(weeeTest.TestCase):
    def transship_ts_preoccupancy(self):
        ts_list = wms.ts_api.query_ts_number(
            self.ts_info["warehouse_number"],
            self.ts_info["ship_in_warehouse"],
            wms.util.get_special_date(days=-30),
            wms.util.get_special_date(days=0),
        )["body"]["data"]
        assert ts_list != [], "无可用的调拨单"
        for order in ts_list:
            # 使用ERP脚本下发的调拨单
            if "7169981" in order["create_user"] or "10060559" in order["create_user"] or "10937481" in order[
                "create_user"]:
                self.ts_info["ts_number"] = order["ts_number"]
                break
        assert self.ts_info.get("ts_number", "") != "", "无可用的调拨单"
        self.ts_info["estimate_pickup_date"] = ts_list[0]["estimate_pickup_date"]

        # 更改调拨单预占方式为LIFO,方便预占到指定数据
        wms.wms_db.update_transship_order_item(self.ts_info["ts_number"])

        # 获取调拨单详情
        ts_detail = wms.ts_api.query_ts_details(self.ts_info["warehouse_number"], self.ts_info["ts_number"],
                                                self.ts_info["storage_type"])["body"]

        for item in ts_detail["itemList"]:
            # 检查库存数据, 无库存则加库存
            item_inv = wms.wms_db.get_location_inventory(self.ts_info["warehouse_number"], self.ts_info["location_no"],
                                                         item["item_number"])
            item_qty = 0
            if item_inv != []:
                item_qty = item_inv[0]["quantity"]
            if item_qty < item["order_qty"]:
                expire_dtm = wms.util.get_special_date(days=3650)
                receive_dtm = wms.util.get_special_date(days=0)
                wms.adjust_api.create_batch_inv(
                    item["item_number"],
                    self.ts_info["warehouse_number"],
                    self.ts_info["user_id"],
                    self.ts_info["location_no"],
                    10,
                    is_lpn=True,
                    pieces_per_pack=1.00,
                    expire_dtm=expire_dtm,
                    receive_date=receive_dtm,
                    lpn_qty=10
                )

            # 解锁库位
            inv_res = wms.adjust_api.query_location_inv(
                item["item_number"],
                self.ts_info["location_no"],
                self.ts_info["warehouse_number"],
            )["body"]["invAdjustList"]
            lpn_list = list(map(lambda i: i["lpn_no"], inv_res[0]["lpnInfos"]))
            wms.adjust_api.lock_inv(0, item["item_number"], self.ts_info["location_no"],
                                    self.ts_info["warehouse_number"], True, lpn_list, None)

        # 开始预占
        wms.ts_api.ts_auto_preoccupy(self.ts_info["ts_number"])
