#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  test_expire_control.py
@Description    :  通用拣货流程测试用例
@CreateTime     :  2024/8/29 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/29 17:01
"""
import weeeTest
from jsonpath import jsonpath
import time
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestExpireControl(weeeTest.TestCase):
    """效期控制测试用例"""

    def setup_class(self):
        self.rep_info = globals()
        self.warehouse_number = global_data.expire_control['warehouse_number']
        self.storage_type = global_data.expire_control['storage_type']
        self.item_Number = global_data.expire_control['item_Number']
        self.location_no = global_data.expire_control['location_no']
        self.user_id, self.user_name = wms.wms_login.common_login(self.warehouse_number)
        self.equipment_code = "LM-RT-11"  # 设备编码
        self.biz_type = 70
        self.pallet_code = \
        wms.common_api.get_avaiable_location(warehouse_number=self.warehouse_number, location_type=38, flag=0)[0][
            'location_no']
        self.slot_code_dexpire = "SLOT999"
        self.slot_code_dstock = "D-stock"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expire_create(self):
        '''
        测试生成效期下架任务
        '''
        # 查询任务
        recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                        item_number=self.item_Number, location_no=self.location_no)
        if recid['data'] != []:
            recids = jsonpath(recid, "$.data[0].removal_task_info_list[0].rec_id")
            # 取消任务
            wms.expire_control.cancelRemovalTask(warehouse_number=self.warehouse_number, rec_ids=recids,
                                                 edit_user=self.user_id)
        wms.expire_control.expire_control_create()
        # 校验生成任务
        start_time = time.time()
        while time.time() - start_time < 10:
            data = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                           item_number=self.item_Number)
            if data['data'] != []:
                break
            time.sleep(0.5)
        assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] in [10, 20], "生成任务失败"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_expire_gettask(self):
        '''
        测试领取效期下架任务
        '''
        # 修改配置
        wms.batch.update_config(config_key="common_pick_config",
                                config_value="{\n  \"supportModules\": [\n    \"VendorReturn\",\n    \"ExpirationRemoval\"\n  ],\n  \"palletVolumeLimit\": 45000,\n  \"palletGridNum\": 1,\n  \"palletVolumeRate\": 0.8,\n  \"sortUseCommon\": true,\n  \"bizConfig\": {\n    \"VendorReturn\": {\n      \"destinationType\": 39,\n      \"destinationNo\": \"\",\n      \"specialDestinationNo\": \"SLOT999\",\n      \"fullPalletTaskFilter\": {\n        \"dry\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        },\n        \"fresh\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        },\n        \"frozen\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        }\n      }\n    },\n    \"ExpirationRemoval\": {\n      \"destinationType\": 28,\n      \"destinationNo\": \"D-Expire\",\n      \"dStockDestinationNo\": \"D-stock\",\n      \"specialDestinationNo\": \"SLOT999\",\n      \"fullPalletTaskFilter\": {\n        \"dry\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        },\n        \"fresh\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        },\n        \"frozen\": {\n          \"min_task_carton\": 10,\n          \"max_sku_carton_lb\": 20,\n          \"max_task_total_carton_lb\": 150\n        }\n      }\n    }\n  }\n}",
                                warehouse_number=self.warehouse_number, rec_id=3065)
        # 扫描设备领取任务
        scan_equipment_resp = wms.common_pick.scan_Equipment(
            warehouseNumber=self.warehouse_number,
            storageType=self.storage_type,
            userId=self.user_id,
            Equipment_code=self.equipment_code
        )
        # 验证扫描设备结果
        assert len(scan_equipment_resp["taskDispatchList"]) > 0, "任务分派列表为空"

        # 获取任务信息
        global ispallet, task_rec_id, location_no, item_Number, bizSubType
        ispallet = scan_equipment_resp["palletNo"]
        task_rec_id = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
        location_no = scan_equipment_resp["taskDispatchList"][0]["locationNo"]
        item_Number = scan_equipment_resp["taskDispatchList"][0]["itemNumber"]
        bizSubType = scan_equipment_resp["taskDispatchList"][0]["bizSubType"]

        res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
        if jsonpath(res, "$.data[0].status")[0] not in [45, 55]:
            assert res['data'][0]['status'] == 40, "领取任务失败-get task"
            recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                            item_number=item_Number, location_no=location_no)
            assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 40, "领取任务失败"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_pick_exipre(self):
        '''
        测试效期下架任务拣货下架
        '''
        # 扫描托盘
        # 需要判断当前任务是否已经扫描绑定过pallet，若已绑定则要扫描绑定的pallet号，若未绑定pallet，则扫新的未占用的pallet
        if ispallet == None:
            scan_pallet_resp = wms.common_pick.scan_pallet(
                bizType=self.biz_type,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                pallet_code=self.pallet_code
            )
            biz_order_id = scan_pallet_resp["bizOrderId"]

            # pick list获取拣货方式
            pickway_resp = wms.common_pick.pick_list(bizOrderId=biz_order_id, warehouseNumber=self.warehouse_number,
                                                     storageType=self.storage_type, userId=self.user_id,
                                                     pickingPalletNo=self.pallet_code, type=bizSubType)
            pickway_resp = jsonpath(pickway_resp, "$.waitPickList[0].pickingWay")[0]
        else:
            scan_pallet_resp = wms.common_pick.scan_pallet(
                bizType=self.biz_type,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                pallet_code=ispallet
            )
            self.pallet_code = ispallet
            biz_order_id = scan_pallet_resp["bizOrderId"]

            # pick list获取拣货方式
            pickway_resp = wms.common_pick.pick_list(bizOrderId=biz_order_id, warehouseNumber=self.warehouse_number,
                                                     storageType=self.storage_type, userId=self.user_id,
                                                     pickingPalletNo=self.pallet_code, type=bizSubType)
            # 已经pick confirm
            if jsonpath(pickway_resp, "$.pickConfirmList")[0] == []:
                pickway_resp = jsonpath(pickway_resp, "$.waitPickList[0].pickingWay")[0]
            else:
                if bizSubType == 70:
                    wms.common_pick.scan_slot(
                        slotNo=self.slot_code_dexpire,
                        warehouseNumber=self.warehouse_number,
                        storageType=self.storage_type,
                        userId=self.user_id,
                        type=bizSubType,
                        bizOrderId=biz_order_id,
                        pickingPalletNo=self.pallet_code
                    )
                else:
                    wms.common_pick.scan_slot(
                        slotNo=self.slot_code_dstock,
                        warehouseNumber=self.warehouse_number,
                        storageType=self.storage_type,
                        userId=self.user_id,
                        type=bizSubType,
                        bizOrderId=biz_order_id,
                        pickingPalletNo=self.pallet_code
                    )
                res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number,
                                                     pickno="ER" + str(task_rec_id))
                assert res['data'][0]['status'] == 60, "扫描slot失败-finish"

                recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                                status_list=[60],
                                                                item_number=item_Number, location_no=location_no)
                assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 60, "扫描slot失败-finish"
                return

        if pickway_resp == 1:
            # 步骤3: 扫描库位获取UPC拣货详情
            pick_detail_resp = wms.common_pick.get_upc_pick_detail(
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                bizType=bizSubType,
                bizOrderId=biz_order_id,
                pallet_code=self.pallet_code,
                userId=self.user_id
            )
            res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
            assert res['data'][0]['status'] == 45, "扫描库位失败-Inprogress"

            recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                            item_number=item_Number, location_no=location_no)
            assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 45, "扫描库位失败-Inprogress"

            # 获取商品信息和推荐拣货数量
            picked_quantity = pick_detail_resp["itemQuantity"]

            # 步骤4: 确认UPC拣货
            wms.common_pick.confirm_upc_pick(
                item_number=item_Number,
                picking_action=0,
                picked_quantity=picked_quantity,
                warehouse_number=self.warehouse_number,
                task_rec_id=task_rec_id,
                location_no=location_no,
                biz_type=bizSubType,
                biz_order_id=biz_order_id,
                user_id=self.user_id,
                picking_pallet_no=self.pallet_code
            )
            res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
            assert res['data'][0]['status'] == 55, "确认拣货失败-move to slot"

            recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                            item_number=item_Number, location_no=location_no)
            assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 55, "确认拣货失败-move to slot"
        else:
            # 扫描库位获取lpn拣货详情
            pick_detail_resp = wms.common_pick.get_lpn_pick_detail(
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                type=bizSubType,
                bizOrderId=biz_order_id,
                pickingPalletNo=self.pallet_code,
                userId=self.user_id
            )
            res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
            assert res['data'][0]['status'] == 45, "扫描库位失败-Inprogress"

            recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                            item_number=item_Number, location_no=location_no)
            assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 45, "扫描库位失败-Inprogress"

            # 获取商品信息和推荐拣货数量
            picked_quantity = pick_detail_resp["itemQuantity"]
            lpnNoList = jsonpath(pick_detail_resp, "$.allLpnList[*].lpn_no")

            # 步骤4: 确认LPN拣货
            wms.common_pick.confirm_lpn_pick(
                lpnNoList=lpnNoList,
                itemNumber=item_Number,
                pickingPalletNo=self.pallet_code,
                pickingAction=0,
                pickedQuantity=picked_quantity,
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                type=bizSubType,
                bizOrderId=biz_order_id,
                userId=self.user_id
            )
            res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
            assert res['data'][0]['status'] == 55, "确认拣货失败-move to slot"

            recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number,
                                                            item_number=item_Number, location_no=location_no)
            assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 55, "确认拣货失败-move to slot"

        # 步骤6: 扫描货位
        if bizSubType == 70:
            wms.common_pick.scan_slot(
                slotNo=self.slot_code_dexpire,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                type=bizSubType,
                bizOrderId=biz_order_id,
                pickingPalletNo=self.pallet_code
            )
        else:
            wms.common_pick.scan_slot(
                slotNo=self.slot_code_dstock,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                type=bizSubType,
                bizOrderId=biz_order_id,
                pickingPalletNo=self.pallet_code
            )
        res = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, pickno="ER" + str(task_rec_id))
        assert res['data'][0]['status'] == 60, "扫描slot失败-finish"

        recid = wms.expire_control.queryRemovalInfoList(warehouse_number=self.warehouse_number, status_list=[60],
                                                        item_number=item_Number, location_no=location_no)
        assert jsonpath(recid, "$.data[0].removal_task_info_list[0].status")[0] == 60, "扫描slot失败-finish"


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
