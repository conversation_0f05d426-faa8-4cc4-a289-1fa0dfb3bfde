# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_return_order.py
@Description    :
@CreateTime     :  2024/8/15 15:54
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/15 15:54
"""
import random
import time
from time import sleep

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from jsonpath import jsonpath


class Testreturnorder(weeeTest.TestCase):

    def setup_class(self):
        self.rep_info = globals()

        self.warehouse_number = global_data.vendor_order['warehouse_number']
        self.storage_type = global_data.vendor_order['storage_type']
        self.itemNumber1 = global_data.vendor_order['item_Number1']
        self.current_time = time.strftime("%Y-%m-%d", time.localtime())
        self.equipment_code = "LM-RT-11"  # 设备编码
        self.biz_type = 50                # 业务类型

        # self.rep_info['pallet'] = wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.warehouse_number, location_type=38, flag=0,
        #                                                  info='location_no')
        # self.rep_info['vrppallet'] = wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.warehouse_number, location_type=80, flag=0,
        #                                                  info='location_no')
        # self.rep_info['slotNo']=wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.warehouse_number, location_type=39, flag=0,
        #                                                  info='location_no')
        # self.rep_info['orderNOcreate'] = random.randint(1, 1000000)
        self.mkpluser_id, self.mkpluser_name = wms.wms_login.mkpl_login()
        self.user_id, self.user_name = wms.wms_login.common_login(self.warehouse_number)
        self.pallet_code = wms.common_api.get_avaiable_location(warehouse_number=self.warehouse_number,location_type=38,flag=0)[0]['location_no']
        self.slot_code = wms.common_api.get_avaiable_location(warehouse_number=self.warehouse_number,location_type=39,flag=0)[0]['location_no']


    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_create(self):
        '''
        测试创建订单的功能
        '''
        self.rep_info['orderNOcreate']=wms.return_order.createReturnOrder(seller_id=9012, warehouse_number=self.warehouse_number, return_type=1, return_date=self.current_time,
                                              product_id=self.itemNumber1, quantity=2, product_level="1", outbound_policy="12")
        wms.return_order.confirmReturnOrder(return_no=self.rep_info['orderNOcreate'])
        return_detail = wms.return_order.getOrderDetail(warehouseNumber=self.warehouse_number, returnNo=self.rep_info['orderNOcreate'], type=2)
        assert jsonpath(return_detail, "$.returnOrder.returnNo")[0] == str(self.rep_info['orderNOcreate']),"无创建订单数据"
        assert jsonpath(return_detail, "$.itemList[0].itemNumber")[0] == self.itemNumber1,"无创建订单商品数据"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_preoccupy(self):
        '''
        测试预占库存的功能
        '''
        #     # 开始预占订单
        wms.return_order.startpreoccupy(orderNO=self.rep_info['orderNOcreate'])

        # 获取预占结果
        preoccupyresult = wms.return_order.getpreoccupyresult(orderNO=self.rep_info['orderNOcreate'])
        hastask = jmespath(preoccupyresult, "hasTask")
        if hastask == True:
            return_detail = wms.return_order.getOrderDetail(warehouseNumber=self.warehouse_number,
                                                            returnNo=self.rep_info['orderNOcreate'], type=2)
            assert jsonpath(return_detail, "$.returnOrder.status")[0] == 40,"预占生成任务失败"
        else:
            # 取消订单
            wms.return_order.cancelorder(orderNO=self.rep_info['orderNOcreate'])

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_pickselfpickup(self):
        '''
        测试拣货的功能
        '''
        # 点击start pick
        recid = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, statusList=[10],itemNumber=self.itemNumber1)
        if recid['data'] == []:
            recidlist = wms.common_pick.get_task_pages(warehouseNumber=self.warehouse_number, statusList=[20])
            recid = jsonpath(recidlist, f"$.data[?(@.referenceNo=='{self.rep_info['orderNOcreate']}')].recId")[0]
            # 调整任务优先级
            wms.common_pick.adjust_priority(task_rec_id=recid, priority=99999999999999, warehouseNumber=self.warehouse_number)
        else:
            recid = jsonpath(recid, f"$.data[?(@.referenceNo=='{self.rep_info['orderNOcreate']}')].recId")[0]
            wms.common_pick.start_pick(recId=recid, warehouseNumber=self.warehouse_number)
            wms.common_pick.adjust_priority(task_rec_id=recid, priority=99999999999999, warehouseNumber=self.warehouse_number)

        # 扫描设备
        scan_equipment_resp = wms.common_pick.scan_Equipment(
            warehouseNumber=self.warehouse_number,
            storageType=self.storage_type,
            userId=self.user_id,
            Equipment_code=self.equipment_code
        )

        # 验证扫描设备结果
        assert len(scan_equipment_resp["taskDispatchList"]) > 0, "任务分派列表为空"

        # 获取任务信息
        task_rec_id = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
        location_no = scan_equipment_resp["taskDispatchList"][0]["locationNo"]
        ispallet = scan_equipment_resp["palletNo"]

        # 扫描托盘
        #需要判断当前任务是否已经扫描绑定过pallet，若已绑定则要扫描绑定的pallet号，若未绑定pallet，则扫新的未占用的pallet
        if ispallet == None:
            scan_pallet_resp = wms.common_pick.scan_pallet(
                bizType=self.biz_type,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                pallet_code=self.pallet_code
            )
        else:
            scan_pallet_resp = wms.common_pick.scan_pallet(
                bizType=self.biz_type,
                warehouseNumber=self.warehouse_number,
                storageType=self.storage_type,
                userId=self.user_id,
                pallet_code=ispallet
            )


        # 获取业务订单ID
        #vendorreturn仍需要判断是否领取到多个任务，且要做的当前任务不是创建的订单任务，根据/wms/commonPick/pickList接口返回waitPickList数组
        #判断当前要做的第一个任务
        biz_order_id = scan_pallet_resp["bizOrderId"]

        #pick list获取拣货方式
        pickway_resp=wms.common_pick.pick_list(bizOrderId=biz_order_id, warehouseNumber=self.warehouse_number, storageType=self.storage_type, userId=self.user_id, pickingPalletNo=self.pallet_code)
        pickway_resp= jsonpath(pickway_resp, "$..waitPickList[0].pickingWay")[0]

        if pickway_resp == 1:
            # 步骤3: 扫描库位获取UPC拣货详情
            pick_detail_resp = wms.common_pick.get_upc_pick_detail(
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                bizType=self.biz_type,
                bizOrderId=biz_order_id,
                pallet_code=self.pallet_code,
                userId=self.user_id
            )

            # 获取商品信息和推荐拣货数量
            picked_quantity = pick_detail_resp["itemQuantity"]

            # 步骤4: 确认UPC拣货
            wms.common_pick.confirm_upc_pick(
                item_number=self.itemNumber1,
                picking_action=0,
                picked_quantity=picked_quantity,
                warehouse_number=self.warehouse_number,
                task_rec_id=task_rec_id,
                location_no=location_no,
                biz_type=self.biz_type,
                biz_order_id=biz_order_id,
                user_id=self.user_id,
                picking_pallet_no=self.pallet_code
            )
        else:
            # 扫描库位获取lpn拣货详情
            pick_detail_resp = wms.common_pick.get_lpn_pick_detail(
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                type=pickway_resp,
                bizOrderId=biz_order_id,
                pickingPalletNo=self.pallet_code,
                userId=self.user_id
            )

            # 获取商品信息和推荐拣货数量
            picked_quantity = pick_detail_resp["itemQuantity"]
            lpnNoList = jsonpath(pick_detail_resp, "$.allLpnList[0].lpn_no")

            # 步骤4: 确认LPN拣货
            wms.common_pick.confirm_lpn_pick(
                lpnNoList=lpnNoList,
                itemNumber=self.itemNumber1,
                pickingPalletNo=self.pallet_code,
                pickingAction=0,
                pickedQuantity=picked_quantity,
                warehouseNumber=self.warehouse_number,
                taskRecId=task_rec_id,
                locationNo=location_no,
                type=pickway_resp,
                bizOrderId=biz_order_id,
                userId=self.user_id
            )

        # 步骤6: 扫描货位
        wms.common_pick.scan_slot(
            slotNo=self.slot_code,
            warehouseNumber=self.warehouse_number,
            storageType=self.storage_type,
            userId=self.user_id,
            type=pickway_resp,
            bizOrderId=biz_order_id,
            pickingPalletNo=self.pallet_code
        )

        # 验证vendor return状态
        return_detail = wms.return_order.getOrderDetail(warehouseNumber=self.warehouse_number,
                                                        returnNo=self.rep_info['orderNOcreate'], type=2)
        assert jsonpath(return_detail, "$.returnOrder.status")[0] == 60,"订单状态不是等待提货"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_vendor_pickup(self):
        '''
        测试vendorpickup功能
        '''
        # vendor pick up
        # pickupinfo = wms.wms_db.pick_up_info(order_no=self.rep_info['orderNOcreate'])

        picklist = wms.vendorpickup.queryCompulsoryList(warehouseNumber=self.warehouse_number, userId=self.user_id)
        for i in picklist:
            if i['returnNo'] == str(self.rep_info['orderNOcreate']):
                orderrecid = i['returnRecId']
        # 获取待提货pallet信息
        palletinfo = wms.vendorpickup.getpickupPallet(warehouseNumber=self.warehouse_number, userId=self.user_id,
                                                      RecId=orderrecid)
        palletlist = [palletinfo[i]['palletNo'] for i in range(len(palletinfo))]
        # pallet装车
        wms.vendorpickup.pickupload(warehouseNumber=self.warehouse_number, userId=self.user_id, RecId=orderrecid,
                                    pallets=palletlist)
        # create bol
        wms.vendorpickup.createbol(warehouseNumber=self.warehouse_number, userId=self.user_id, RecId=orderrecid,
                                   orderNO=self.rep_info['orderNOcreate'], pallets=palletlist)

        return_detail = wms.return_order.getOrderDetail(warehouseNumber=self.warehouse_number,
                                                        returnNo=self.rep_info['orderNOcreate'], type=2)
        assert jsonpath(return_detail, "$.returnOrder.status")[0] == 80,"订单状态未出库"

        vendorpickup= wms.return_order.getPickUpInfoPage(warehouse_number=self.warehouse_number, business_no=self.rep_info['orderNOcreate']
                                                         , in_user=self.user_id)
        assert jsonpath(vendorpickup, "$.data[0].pickUpStatus")[0] == "Finished","vendor pickup状态不是已出库"


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

    #
    #
    #
    #
    #
    #     # for i in range(len(self.rep_info['taskdb'])):
    #     #     workType = self.rep_info['taskdb'][i]['work_type']
    #     #     itemLevel = self.rep_info['taskdb'][i]['item_level']
    #     #     location = self.rep_info['taskdb'][i]['location_no']
    #     #     recid = self.rep_info['taskdb'][i]['rec_id']
    #     #     pickway = self.rep_info['taskdb'][i]['picking_way']
    #     #     # 获取退货拣选任务列表，参数为1表示获取未扫pallet的任务列表
    #     #     wms.return_pick.gettasklist(action=1, workType=workType, warehouseNumber=self.warehouse_number,
    #     #                                 userId=self.rep_info['user_id'])
    #     #
    #     #     # 扫好货or坏货pallet
    #     #     if itemLevel == 1:
    #     #         wms.return_pick.checkpallet(pallet_no=self.rep_info['pallet'], orderid=self.rep_info['orderNOcreate'],
    #     #                                     itemLevel=itemLevel, workType=workType, userId=self.rep_info['user_id'])
    #     #         # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
    #     #         wms.return_pick.gettasklist(action=0, workType=workType,
    #     #                                     warehouseNumber=self.warehouse_number, userId=self.rep_info['user_id'])
    #     #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
    #     #         assert taskdb[i]['picking_pallet_no'] == self.rep_info['pallet'],"生成任务的pallet不正确"
    #     #     else:
    #     #         wms.return_pick.checkpallet(pallet_no=self.rep_info['vrppallet'], orderid=self.rep_info['orderNOcreate'],
    #     #                                     itemLevel=itemLevel, workType=workType, userId=self.rep_info['user_id'])
    #     #         # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
    #     #         wms.return_pick.gettasklist(action=0, workType=workType,
    #     #                                     warehouseNumber=self.warehouse_number,
    #     #                                     userId=self.rep_info['user_id'])
    #     #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
    #     #         assert taskdb[i]['picking_pallet_no'] == self.rep_info['vrppallet'],"生成任务的pallet不正确"
    #     #
    #     #     if pickway == 2:
    #     #         # 查看lpn详情，提出扫描的lpn
    #     #         lpn = wms.return_pick.pickDetailLpn(orderNO=self.rep_info['orderNOcreate'], recid=recid, locationNo=location)
    #     #         # 获取扫描的lpn个数list
    #     #         itemQuantity = jmespath(lpn, "itemQuantity")
    #     #         piecesPerPack = jmespath(lpn, "piecesPerPack")
    #     #         lpnquantity = itemQuantity / piecesPerPack
    #     #         lpnlist = [lpn['allLpnList'][0]['lpn_no'] for i in range(int(lpnquantity))]
    #     #         # pickconfirm提交
    #     #         wms.return_pick.pickConfirmLpn(itemNumber=self.itemNumber1, pallet=self.rep_info['pallet'],
    #     #                                        lpnNoList=lpnlist, warehouseNumber=self.warehouse_number,
    #     #                                        returnNo=self.rep_info['orderNOcreate'], recid=recid, location=location)
    #     #         # 扫slot
    #     #         wms.return_pick.scanSlot(orderNO=self.rep_info['orderNOcreate'], slotNo=self.rep_info['slotNo'],
    #     #                                  pallet_no=self.rep_info['pallet'])
    #     #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
    #     #         assert taskdb[i]['slot_no'] == self.rep_info['slotNo'],"生成任务的slot_no不正确"
    #     #         assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
    #     #     else:
    #     #         # 查看详情，提出扫描的upc
    #     #         upc = wms.return_pick.pickDetail(orderNO=self.rep_info['orderNOcreate'], recid=recid, locationNo=location)
    #     #         quantity = jmespath(upc, "recommendTotalQty")
    #     #         wms.return_pick.pickConfirm(itemNumber=self.itemNumber1, pallet=self.rep_info['vrppallet'],
    #     #                                     quantity=quantity, orderNO=self.rep_info['orderNOcreate'], recid=recid,
    #     #                                     locationNo=location)
    #     #         wms.return_pick.scanSlot(orderNO=self.rep_info['orderNOcreate'], slotNo=self.rep_info['slotNo'],
    #     #                                  pallet_no=self.rep_info['vrppallet'])
    #     #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
    #     #         assert taskdb[i]['slot_no'] == self.rep_info['slotNo'],"生成任务的slot_no不正确"
    #     #         assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"

    # # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_order_pickdispose(self):
    #     '''
    #     测试拣货的功能Weee! dispose
    #     '''
    #     self.rep_info['palletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(
    #         warehouse=self.warehouse_number, location_type=38, flag=0,
    #         info='location_no')
    #     self.rep_info['vrppalletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(
    #         warehouse=self.warehouse_number, location_type=80, flag=0,
    #         info='location_no')
    #     orderNOpickdispose = random.randint(1, 1000000)
    #     # 登录WMS系统
    #     user_id, user_name = wms.wms_login.common_login(self.warehouse_number)
    #
    #     # 获取return单数据
    #     wms.return_order.createOrUpdate(orderNO=orderNOpickdispose, pickUpDate=self.current_time,deliveryType=2,itemNumber1=self.itemNumber1)
    #     # 校验订单数据
    #     return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
    #     orderitem = wms.wms_db.get_vendor_orderitem(orderNOpickdispose)
    #     assert return_no['return_no'] == str(orderNOpickdispose),"无创建订单数据"
    #     assert orderitem[0]['item_number'] == self.itemNumber1,"无创建订单商品数据"
    #
    #     #     # 开始预占订单
    #     wms.return_order.startpreoccupy(orderNO=orderNOpickdispose)
    #
    #     # 获取预占结果
    #     preoccupyresult = wms.return_order.getpreoccupyresult(orderNO=orderNOpickdispose)
    #     hastask = jmespath(preoccupyresult, "hasTask")
    #     if hastask == True:
    #         # check封装 校验预占生成任务
    #         time.sleep(2)
    #         count = wms.wms_db.get_batch_preoccupancy_inventory(reference_no=orderNOpickdispose)
    #         time.sleep(2)
    #         taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
    #         assert len(count) == len(taskdb),"预占生成任务数量与预占数量不一致"
    #     else:
    #         # 取消订单
    #         wms.return_order.cancelorder(orderNO=orderNOpickdispose)
    #         return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
    #         assert return_no['status'] == 100,"订单状态异常"
    #
    #     # 调整任务优先级
    #     time.sleep(1)
    #     wms.wms_db.update_vendor_taskpriority(orderNOpickdispose)
    #     time.sleep(1)
    #
    #     for i in range(len(taskdb)):
    #         workType = taskdb[i]['work_type']
    #         itemLevel = taskdb[i]['item_level']
    #         location = taskdb[i]['location_no']
    #         recid = taskdb[i]['rec_id']
    #         pickway = taskdb[i]['picking_way']
    #         # 获取退货拣选任务列表，参数为1表示获取未扫pallet的任务列表
    #         wms.return_pick.gettasklist(action=1, workType=workType, warehouseNumber=self.warehouse_number,
    #                                     userId=user_id)
    #
    #         # 扫好货or坏货pallet
    #         if itemLevel == 1:
    #             wms.return_pick.checkpallet(pallet_no=self.rep_info['palletdis'], orderid=orderNOpickdispose,
    #                                         itemLevel=itemLevel, workType=workType, userId=user_id)
    #             # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
    #             wms.return_pick.gettasklist(action=0, workType=workType,
    #                                         warehouseNumber=self.warehouse_number, userId=user_id)
    #             taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
    #             assert taskdb[i]['picking_pallet_no'] == self.rep_info['palletdis'],"生成任务的pallet不正确"
    #         else:
    #             wms.return_pick.checkpallet(pallet_no=self.rep_info['vrppalletdis'], orderid=orderNOpickdispose,
    #                                         itemLevel=itemLevel, workType=workType, userId=user_id)
    #             # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
    #             wms.return_pick.gettasklist(action=0, workType=workType,
    #                                         warehouseNumber=self.warehouse_number,
    #                                         userId=user_id)
    #             taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
    #             assert taskdb[i]['picking_pallet_no'] == self.rep_info['vrppalletdis'],"生成任务的pallet不正确"
    #
    #         if pickway == 2:
    #             # 查看lpn详情，提出扫描的lpn
    #             lpn = wms.return_pick.pickDetailLpn(orderNO=orderNOpickdispose, recid=recid, locationNo=location)
    #             # 获取扫描的lpn个数list
    #             itemQuantity = jmespath(lpn, "itemQuantity")
    #             piecesPerPack = jmespath(lpn, "piecesPerPack")
    #             lpnquantity = itemQuantity / piecesPerPack
    #             lpnlist = [lpn['allLpnList'][0]['lpn_no'] for i in range(int(lpnquantity))]
    #             # pickconfirm提交
    #             wms.return_pick.pickConfirmLpn(itemNumber=self.itemNumber1, pallet=self.rep_info['palletdis'],
    #                                            lpnNoList=lpnlist, warehouseNumber=self.warehouse_number,
    #                                            returnNo=orderNOpickdispose, recid=recid, location=location)
    #             # 扫slot
    #             wms.return_pick.scanSlot(orderNO=orderNOpickdispose, slotNo='SLOT999',
    #                                      pallet_no=self.rep_info['palletdis'])
    #             taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
    #             assert taskdb[i]['slot_no'] == 'SLOT999',"生成任务的slot_no不正确"
    #             assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
    #         else:
    #             # 查看详情，提出扫描的upc
    #             upc = wms.return_pick.pickDetail(orderNO=orderNOpickdispose, recid=recid, locationNo=location)
    #             quantity = jmespath(upc, "recommendTotalQty")
    #             wms.return_pick.pickConfirm(itemNumber=self.itemNumber1, pallet=self.rep_info['vrppalletdis'],
    #                                         quantity=quantity, orderNO=orderNOpickdispose, recid=recid,
    #                                         locationNo=location)
    #             wms.return_pick.scanSlot(orderNO=orderNOpickdispose, slotNo='SLOT999',
    #                                      pallet_no=self.rep_info['vrppalletdis'])
    #             taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
    #             assert taskdb[i]['slot_no'] == 'SLOT999',"生成任务的slot_no不正确"
    #             assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
    #     time.sleep(1)
    #     return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
    #     time.sleep(1)
    #     assert return_no['status'] == 80,"订单状态未出库"
    #
    # def test_order_ff(self):
    #     self._order_create()
    #     # self._order_preoccupy()
    #     # self._order_pickselfpickup()
    #
    # # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def _scan_equipment_flow(self):
    #     '''
    #     测试扫描设备流程
    #     '''
    #     #25仓构造vendor return数据跑pick流程，as下架数据构造
    #     #48仓构造效期下架数据，自动领取任务跑pick流程，效期下架没有价格不生成任务，需构造sku价格数据
    #     # 设置测试参数
    #     warehouse_number = self.warehouse_number
    #     storage_type = self.storage_type
    #     equipment_code = "LM-RT-11"  # 设备编码
    #     user_id=self.rep_info['user_id']
    #     pallet_code = self.rep_info['pallet']      # 托盘编码
    #     biz_type = 50                # 业务类型
    #
    #     # 步骤1: 扫描设备
    #     scan_equipment_resp = wms.common_pick.scan_Equipment(
    #         warehouseNumber=warehouse_number,
    #         storageType=storage_type,
    #         userId=user_id,
    #         Equipment_code=equipment_code
    #     )
    #
    #     # 验证扫描设备结果
    #     assert scan_equipment_resp is not None, "扫描设备返回结果为空"
    #     assert "taskDispatchList" in scan_equipment_resp, "未返回任务分派列表"
    #     assert len(scan_equipment_resp["taskDispatchList"]) > 0, "任务分派列表为空"
    #
    #     # 获取任务信息
    #     task_rec_id = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
    #     location_no = scan_equipment_resp["taskDispatchList"][0]["locationNo"]
    #
    #     # 步骤2: 扫描托盘
    #     #需要判断当前任务是否已经扫描绑定过pallet，若已绑定则要扫描绑定的pallet号，若未绑定pallet，则扫新的未占用的pallet
    #     scan_pallet_resp = wms.common_pick.scan_pallet(
    #         bizType=biz_type,
    #         warehouseNumber=warehouse_number,
    #         storageType=storage_type,
    #         userId=user_id,
    #         pallet_code=pallet_code
    #     )
    #
    #     # 验证扫描托盘结果
    #     assert scan_pallet_resp is not None, "扫描托盘返回结果为空"
    #     assert "bizOrderId" in scan_pallet_resp, "未返回业务订单ID"
    #
    #     # 获取业务订单ID
    #     #vendorreturn仍需要判断是否领取到多个任务，且要做的当前任务不是创建的订单任务，根据/wms/commonPick/pickList接口返回waitPickList数组
    #     #判断当前要做的第一个任务
    #     biz_order_id = scan_pallet_resp["bizOrderId"]
    #
    #     # 步骤3: 获取UPC拣货详情
    #     pick_detail_resp = wms.common_pick.get_upc_pick_detail(
    #         warehouseNumber=warehouse_number,
    #         taskRecId=task_rec_id,
    #         locationNo=location_no,
    #         bizType=biz_type,
    #         bizOrderId=biz_order_id,
    #         pallet_code=pallet_code,
    #         userId=user_id
    #     )
    #
    #     # 获取商品信息和推荐拣货数量
    #     item_number = pick_detail_resp["itemNumber"]
    #     picked_quantity = pick_detail_resp["recommendTotalQty"]
    #
    #     # 步骤4: 确认UPC拣货
    #     wms.common_pick.confirm_upc_pick(
    #         item_number=item_number,
    #         picking_action=0,
    #         picked_quantity=picked_quantity,
    #         warehouse_number=warehouse_number,
    #         task_rec_id=task_rec_id,
    #         location_no=location_no,
    #         biz_type=biz_type,
    #         biz_order_id=biz_order_id,
    #         user_id=user_id,
    #         picking_pallet_no=pallet_code
    #     )
    #
    #     # 步骤5: 拣货完成
    #     complete_resp = wms.common_pick.pick_complete(
    #         warehouse_number=warehouse_number,
    #         picking_pallet_no=pallet_code,
    #         storage_type=storage_type,
    #         user_id=user_id,
    #         type=biz_type,
    #         biz_order_id=biz_order_id
    #     )
    #
    #     # 获取货位信息
    #     slot_no = complete_resp["slotNo"]
    #
    #     # 步骤6: 扫描货位
    #     wms.common_pick.scan_slot(
    #         slotNo=slot_no,
    #         warehouseNumber=warehouse_number,
    #         storageType=storage_type,
    #         userId=user_id,
    #         type=biz_type,
    #         bizOrderId=biz_order_id,
    #         pickingPalletNo=pallet_code
    #     )
    #
    #     # 验证托盘状态
    #     pallet_info = wms.wms_db.check_tote_status(tote_no=pallet_code, warehouse=warehouse_number)
    #     assert pallet_info["flag"] == 0, f"托盘状态验证失败，期望：0，实际：{pallet_info['flag']}"

