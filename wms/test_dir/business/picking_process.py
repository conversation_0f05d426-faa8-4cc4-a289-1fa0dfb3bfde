# !/usr/bin/python3
# -*- coding: utf-8 -*-


from weeeTest import jmespath
from weeeTest import log

from wms.test_dir.api.wms.wms import wms


def general_picking_operate(warehouse_number, storage_type, location_type=50, picking_channel=0, module_name="picking", is_mof=False, is_bulk=False, is_oneitem=False, is_oos = False):
    """
    该拣货拣货流程,适用于Normal Batch, FBW Batch, Mail Order batch，Mail order fresh batch,Frozen-Dry-Ice batch, Bulk batch, Oneitem batch
    :param warehouse_number:
    :param location_type:50:normal_batch,mail_order,mof/51:alcohol/52:one_item/73:bulk/75:fbw/tote:6
    :param storage_type:1:常温,包含D,MO,酒类 2:冷藏,包含Fresh 3:冷冻Frozen 6:Multiple, Mof, 7:Frozen-Dry-Ice
    :param picking_channel:0:picking 1:mailOrder 2:alcohol
    :return:
    """
    # 进入菜单（计算绩效要用）
    wms.picking.enter_module(warehouse_number, module_name)
    # 查询可用的cart
    location_no = wms.common_api.get_avaiable_location(warehouse_number, location_type, flag=0)[0]["location_no"]
    # 查询当前账号下拣货任务
    user_info = wms.picking.query_user_task(cart_no=location_no, warehouse_number=warehouse_number,
                                                    storage_type=storage_type, picking_channel=picking_channel)
    # 检查当前账号下是否存在任务
    assert user_info["messageId"] == "10000", f'{user_info["body"]}'

    # 创建拣货任务
    resp = wms.picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number,
                                                    storage_type=storage_type, picking_channel=picking_channel)
    # 检查接口是不是出现错误
    assert resp['messageId'] != "99998", f"{resp['body']}"

    # 未拉取到任务则返回
    if resp['messageId'] == "32003":
        return

    next_item = jmespath(resp, "body.nextItem")
    box_size = jmespath(resp, "body.box_size")
    picking_task_id = jmespath(resp, "body.picking_task_id")
    order_ids = jmespath(resp, "body.picking_order[*].order_id")
    shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")


    if not is_bulk and not is_oneitem:
        # 查询可用的tote
        box_size_dict = {"S": 5, "M": 10, "L": 15}
        tote_nos = wms.common_api.get_avaiable_location(warehouse_number, 6, flag=0, toteSizes=[box_size_dict[box_size]])
            # 判断订单与数据库中查询的tote数量
        total_order_num = len(order_ids)
        assert len(tote_nos) >= total_order_num, "picking时获取的可用Tote数量小于待picking的订单数量"

        # 订单绑定拣货框
        log.info("共" + str(total_order_num) + "个订单需要绑定拣货框")
        for i in range(total_order_num):
            resp = wms.picking.bind_tote_number(picking_task_id=picking_task_id,
                                                        tote_no=tote_nos[i]["location_no"],
                                                        order_id=order_ids[i],
                                                        shipping_type=shipping_types[i],
                                                        warehouse_number=warehouse_number)

    # 开始拣货
    while True:
        next_item = jmespath(resp, "body.nextItem")
        if next_item is None:
            break

        if is_oos:
            # 如果 is_oos 为 True，则调用 outOfStock 方法
            resp = wms.picking.outOfStock(
                warehouse_number=warehouse_number,
                tote_no=next_item["tote_no"],
                location_no=next_item["location_no"],
                item_number=next_item["item_number"],
                picking_quantity=next_item["item_quantity"],
                order_id=next_item["order_id"],
                picking_type=jmespath(resp, "body.picking_type"),
                picking_task_id=jmespath(resp, "body.picking_task_id")
            )
        else:
            # 原有的 adjust_location 逻辑
            resp = wms.picking.adjust_location(
                warehouse_number=warehouse_number,
                tote_no=next_item["tote_no"],
                location_no=next_item["location_no"],
                item_number=next_item["item_number"],
                order_id=next_item["order_id"],
                picking_quantity=next_item["item_quantity"],
                picking_type=jmespath(resp, "body.picking_type"),
                picking_task_id=jmespath(resp, "body.picking_task_id")
            )

        # resp = wms.picking.adjust_location(warehouse_number=warehouse_number,
        #                                             tote_no=next_item["tote_no"],
        #                                             location_no=next_item["location_no"],
        #                                             item_number=next_item["item_number"],
        #                                             order_id=next_item["order_id"],
        #                                             picking_quantity=next_item["item_quantity"],
        #                                             picking_type=jmespath(resp, "body.picking_type"),
        #                                             picking_task_id=jmespath(resp, "body.picking_task_id"))
    packing_line=jmespath(resp, "body.packing_line")

    if is_mof:
        # 结束生鲜拣货任务
        wms.mof_picking.scan_picking_region(picking_region_no=jmespath(resp, "body.picking_region_no"),
                                            picking_task_id=picking_task_id, warehouse_number=warehouse_number)
        # 再扫cart，继续剩下的作业
        resp = wms.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)

        log.info("再次扫cart，继续作业MOF干货")
        while True:
            next_item = jmespath(resp, "body.nextItem")
            if next_item is None:
                log.info("干货作业也完成，扫描最终打包线")
                break

            resp = wms.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                    tote_no=next_item["tote_no"],
                                                    location_no=next_item["location_no"],
                                                    item_number=next_item["item_number"],
                                                    order_id=next_item["order_id"],
                                                    picking_quantity=next_item["item_quantity"],
                                                    picking_type=jmespath(resp, "body.picking_type"),
                                                    picking_task_id=jmespath(resp, "body.picking_task_id"))
        packing_line = jmespath(resp, "body.picking_region_no")

    # 结束拣货
    wms.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                            picking_type=jmespath(resp, "body.picking_type"),
                                            packing_line=packing_line,
                                            warehouse_number=warehouse_number,
                                            picking_task_status=jmespath(resp, "body.status"),
                                            picking_channel=picking_channel)
    # 退出菜单（计算绩效要用）
    wms.picking.enter_module(warehouse_number, module_name, action=0)

