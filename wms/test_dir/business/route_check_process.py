# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json
from weeeTest import jmespath, log

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms

def route_check_operation(warehouse, delivery_dtm, route_id, order_type, delivery_id, account=global_data.wms_user_id,
                              username=global_data.user_name):
        # 进入菜单（计算绩效要用）
        wms.picking.enter_module(warehouse, "route_check")
        # route check 接口
        wms.route_check.user = username + '(' + str(account) + ')'
        storage_type = wms.select_order_storage_type(order_type)

        # 获取route下的订单数据
        order_data = wms.route_check.get_route_package(warehouse, delivery_dtm, route_id, storage_type)
        for order in order_data:
            if order["confirm_package_number"] < order["package_number"] and order["shipping_status"] in [70, 71]:
                order_id = order["order_id"]
                order_info = wms.batch.query_order_detail(warehouse, order_id)["orderInfo"]
                log.error(f"""物流号信息：{order_info["tracking_number_list"]}""")
                tracking_list = order_info["tracking_number_list"].split(",") if "," in order_info["tracking_number_list"] else [order_info["tracking_number_list"]]
                for tracking_num in tracking_list:
                    wms.route_check.package_confirm(warehouse, delivery_dtm, route_id, tracking_num, storage_type)
        # 退出菜单（计算绩效要用）
        wms.picking.enter_module(warehouse, "route_check", action=0)
        route_check_details = wms.route_check.query_route_check_details(warehouse, delivery_dtm, route_id, delivery_id, order_type)
        assert route_check_details == [], f"仓库{warehouse},delivery_date:{delivery_dtm},route_id:{route_id}, order_type:{order_type}下存在未RouteCheck的数据"
            
