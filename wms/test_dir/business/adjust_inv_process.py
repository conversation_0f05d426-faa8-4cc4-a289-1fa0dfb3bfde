# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
Business Process: Inventory Adjustment

This module contains functions for performing bin and stock location adjustments,
including creating inventory, processing approvals, and automatically unlocking LPNs.
"""
import math

from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.adjust_approve_process import AdjustApproveProcess


def _create_inventory_and_approve(item_number, warehouse_number, location_no, in_user, adjust_number, is_lpn):
    """
    Private Helper: Create and approve an inventory adjustment.

    This function fetches item details, creates a batch inventory adjustment,
    and then approves it. It serves as a common utility for both bin and stock adjustments.

    Args:
        item_number (str): The item ID to be adjusted.
        warehouse_number (str): The warehouse code.
        location_no (str): The location number within the warehouse.
        in_user (str): The user performing the operation.
        adjust_number (int): The quantity to adjust.
        is_lpn (bool): True for a stock adjustment (with LPNs), False for a bin adjustment.
    """
    item_info = wms.common_api.query_item_info(warehouse_number, [14], in_user, item_numbers=[item_number], hasInventoryOnly="N")[0]
    storage_type = item_info["storage_type"]
    pieces_per_pack = item_info["case_spec"]
    expire_date = None
    batch_no = None
    if item_info["expiration_control"] == "Y":
        expire_date = wms.util.get_special_date(days=item_info["shelf_life"])
    receive_date = wms.util.get_special_date()

    if not location_no:
        if not is_lpn:
            locations = wms.common_api.get_avaiable_bin(warehouse_number, storage_type=storage_type)
            if not locations:
                raise ValueError(f"No available bin locations found for storage type {storage_type}")
            location_no = locations[0]["bin_sn"]
        else:
            locations = wms.common_api.get_avaiable_stock(warehouse_number, storage_type=storage_type)
            if not locations:
                raise ValueError(f"No available stock locations found for storage type {storage_type}")
            location_no = locations[0]["stock_sn"]
    else:
        # if sku has bin, use this bin
        if not is_lpn:
            inv_res = wms.adjust_api.query_location_inv(item_number, location_no, warehouse_number)["body"]["invAdjustList"]
            if inv_res:
                expire_date = inv_res[0]["expire_dtm"]
                pieces_per_pack = inv_res[0]["pieces_per_pack"]
                batch_no = inv_res[0]["batch_no"]
                if inv_res[0]["is_lock"] == 1:
                    wms.adjust_api.lock_inv(0, item_number, location_no, warehouse_number, is_lpn, [], batch_no)

    create_inv_args = {
        "item_number": item_number,
        "warehouse_number": warehouse_number,
        "in_user": in_user,
        "location_no": location_no,
        "adjust_number": adjust_number,
        "is_lpn": is_lpn,
        "pieces_per_pack": pieces_per_pack,
        "expire_dtm": expire_date,
        "receive_date": receive_date,
    }

    if is_lpn:
        create_inv_args["lpn_qty"] = math.ceil(adjust_number / int(float(pieces_per_pack)))

    if batch_no:
        create_inv_args["batch_no"] = batch_no

    wms.adjust_api.create_batch_inv(**create_inv_args)

    # Approve the inventory adjustment
    AdjustApproveProcess().inv_approve_operate(in_user, item_number, warehouse_number, location_no, action_type=3)


def bin_adjust(item_number, warehouse_number, in_user, location_no=None, adjust_number=100):
    """
    Performs a bin adjustment (non-LPN).

    This process creates inventory for a specified item in a bin location
    and automatically approves the adjustment. If location_no is not provided,
    it will automatically find an available one based on storage_type.

    Args:
        item_number (str): The item ID to be adjusted.
        warehouse_number (str): The warehouse code.
        in_user (str): The user performing the operation.
        location_no (str, optional): Location number. If None, it will be auto-discovered.
        adjust_number (int, optional): The quantity to adjust. Defaults to 100.
    """
    _create_inventory_and_approve(item_number, warehouse_number, location_no, in_user, adjust_number, is_lpn=False)


def stock_adjust(item_number, warehouse_number, in_user, location_no=None, adjust_number=100):
    """
    Performs a stock adjustment (with LPNs) and unlocks the LPNs.

    This process creates LPNs for a specified item in a stock location,
    approves the adjustment, and then unlocks the newly created LPNs.
    If location_no is not provided, it will automatically find an available one based on storage_type.

    Args:
        item_number (str): The item ID to be adjusted.
        warehouse_number (str): The warehouse code.
        in_user (str): The user performing the operation.
        location_no (str, optional): Location number. If None, it will be auto-discovered.
        adjust_number (int, optional): The quantity to adjust. Defaults to 100.
    """
    _create_inventory_and_approve(item_number, warehouse_number, location_no, in_user, adjust_number, is_lpn=True)
    # UnLock LPN
    inv_res = wms.adjust_api.query_location_inv(
        item_number,
        location_no,
        warehouse_number
    )["body"]["invAdjustList"]

    if inv_res and inv_res[0].get("lpnInfos"):
        lpn_list = [i["lpn_no"] for i in inv_res[0]["lpnInfos"]]
        wms.adjust_api.lock_inv(0, item_number, location_no, warehouse_number, True, lpn_list, None)