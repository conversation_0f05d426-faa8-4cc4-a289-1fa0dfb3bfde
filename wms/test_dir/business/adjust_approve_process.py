# !/usr/bin/python3
# -*- coding: utf-8 -*-

from weeeTest import jmespath, log
from wms.test_dir.api.wms.wms import wms


class AdjustApproveProcess:
    """库存审核通过&拒绝"""

    def __init__(self):
        pass

    def inv_approve_operate(self, in_user, item_number, warehouse_number, location_no, action_type, action_types=None, approve_type=1):
        res = wms.adjust_api.query_approve_task(warehouse_number, item_number, location_no, wms.util.get_special_date(days=-1), wms.util.get_special_date(),
                                                action_types, status_list=None)
        if not res:
            log.info(f'{warehouse_number}仓库{location_no}没有待审核的任务')
            return
        rec_id = jmespath(res, "[0].dailyReportItem[0].rec_id")
        info_id = jmespath(res, "[0].dailyReportItem[0].info_id")
        detail_id = jmespath(res, "[0].dailyReportItem[0].detail_id")
        item_number = jmespath(res, "[0].item_number")

        # 调整前总库存
        qty_old = jmespath(res, "[0].dailyReportItem[0].real_inv_qty")
        wms.adjust_api.approve_task(item_number, approve_type, in_user, warehouse_number, rec_id, action_type, info_id, detail_id, location_no)
        if approve_type == 1:
            # 断言审核状态是不是30
            res1 = wms.adjust_api.query_approve_task(warehouse_number, item_number, location_no, wms.util.get_special_date(days=-1),
                                                     wms.util.get_special_date(), action_types, status_list=[30, 21, 20])
            task_status = jmespath(res1, f"[0].dailyReportItem[?rec_id == `{rec_id}`].status")
            assert task_status == [30], f"任务审核后状态不等于30,实际为{task_status}"

            adjust_qty = jmespath(res1, f"[0].dailyReportItem[?rec_id == `{rec_id}`].final_diff")[0]
            # 断言库存是否调整正确
            qty_new = jmespath(res1, "[0].dailyReportItem[0].real_inv_qty")
            assert int(qty_new) == int(qty_old) + adjust_qty, "审核前后总库存不一致"
        else:
            # 断言审核状态是不是40
            res1 = wms.adjust_api.query_approve_task(warehouse_number, item_number, location_no, wms.util.get_special_date(days=-1),
                                                     wms.util.get_special_date(), action_types, status_list=[40, 30, 21, 20])
            task_status = jmespath(res1, f"[0].dailyReportItem[?rec_id == `{rec_id}`].status")
            assert task_status == [40], f"任务审核后状态不等于40,实际为{task_status}"

            # 断言库存是否调整正确, real_inv_qty是当前库位总库存，默认取list中第一条判断即可
            qty_new = jmespath(res1, "[0].dailyReportItem[0].real_inv_qty")
            assert int(qty_new) == int(qty_old), "审核前后总库存不一致"
