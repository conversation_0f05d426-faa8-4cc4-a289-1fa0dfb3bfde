# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
Business Process: Packing Operations

This module encapsulates packing workflows into the PackingProcess class,
making it easy to import and use in other test files.
"""

import json
from weeeTest import jmespath, log

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms

class PackingProcess:
    """
    Encapsulates business processes for packing operations, including general
    (tote-based) and bulk (cart-based) packing, along with all necessary verifications.
    """

    # region Verification Helpers
    def _verify_order_status(self, warehouse, order_ids, expected_status):
        """Verifies that a list of orders has the expected shipping status."""
        if not isinstance(order_ids, list):
            order_ids = [order_ids]
        for order_id in order_ids:
            shipping_status = wms.batch.query_order_info(warehouse, order_id=order_id)[0]["status"]
            assert shipping_status in expected_status if isinstance(expected_status, tuple) else shipping_status == expected_status, \
                f"Order {order_id} status should be {expected_status}, but was {shipping_status}"

    def _verify_location_released(self, warehouse, location_no):
        """Verifies that the packing location (tote/cart) has been released."""
        location_info = wms.common_api.get_avaiable_location(warehouse, None, location_no=location_no, flag=0)
        assert len(location_info) != 0, \
            f"Location {location_no} in warehouse {warehouse} was not released after packing."

    def _verify_inventory_cleared(self, warehouse, items, location_no):
        """Verifies that item inventory has been cleared from the packing location."""
        for item in items:
            inv_list = wms.adjust_api.query_location_inv(item["item_number"], location_no, warehouse)["body"]["invAdjustList"]
            assert len(inv_list) == 0, \
                f"Inventory for item {item['item_number']} in location {location_no} was not cleared after packing."

    def _verify_inventory_log(self, warehouse, items, order_id, oos_item=None):
        """Verifies that the correct inventory logs (261/263) were created for an order."""
        if oos_item is None:
            oos_item = {}
        for item in items:
            type_code = 263 if item["item_number"] == oos_item.get("item_number") else 261
            log_list = wms.adjust_api.query_inventory_log(
                warehouse, wms.util.get_special_date(days=-1), wms.util.get_special_date(),
                item_number=item["item_number"], reference_id=order_id, type_code_list=[type_code])
            assert len(log_list) > 0, \
                f"Inventory log (type {type_code}) not found for item {item['item_number']} in order {order_id}."

    def _verify_hook_reports(self, order_ids, in_dtm, is_mod=False, is_oos=False):
        """Verifies that required hook reports were sent to external systems."""
        if not isinstance(order_ids, list):
            order_ids = [order_ids]
        for order_id in order_ids:
            order_info = wms.wms_db.select_wms_order_info(order_id)
            if not is_mod:
                wms.wms_assert.check_hook_report(order_info["source_order_id"], "TMS_PACKAGE", in_dtm)
            if is_oos:
                wms.wms_assert.check_hook_report(order_info["source_order_id"], "ITEM_OOS_FPO", in_dtm)

            invoice_no = order_info["invoice_no"]
            order_list = wms.wms_db.get_invoice_order(invoice_no)
            is_markout = all(order["shipping_status"] >= 70 or order["order_status"] != 0 for order in order_list)
            if is_markout:
                wms.wms_assert.check_hook_report(invoice_no, "NOTIFY_2WMS_ORDER_MARK_OUT", in_dtm)

    def _get_packing_station(self, warehouse, is_mod):
        """Determines the correct packing station, especially for Mail Orders."""
        if is_mod:
            mo_prefix_config = wms.get_central_config(warehouse, "wms:downorder:mail_order_wave")
            return json.loads(mo_prefix_config)[0]["prefix"] + "1"
        return "1-1"

    def general_packing_operate(self, warehouse, tote_no, storage_type, account=global_data.wms_user_id,
                                username=global_data.user_name, is_oos=False, is_replenish=False, is_mod=False,
                                is_cancel=False):
        """Handles the entire general (tote-based) packing process and verifies the results."""
        wms.util.update_header(weee_warehouse=str(warehouse), weee_wms_storage_type=str(storage_type))
        wms.normal_packing.user = f'{username}({account})'
        wms.normal_packing.warehouse = warehouse
        # wms.normal_packing.packing_login_record_log(warehouse)
        in_dtm = wms.util.get_current_time()

        station = self._get_packing_station(warehouse, is_mod)
        resp = wms.normal_packing.query_packing_task(tote_no, station)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
        items, order_id, recommend_package = wms.normal_packing.query_packing_info(tote_no, station)

        if is_cancel:
            wms.normal_packing.move_to_cot(tote_no, order_id)
            wms.normal_packing.create_cot_label(order_id)
            return items, in_dtm

        self._verify_order_status(warehouse, order_id, (60, 61))

        if is_replenish:
            return wms.normal_packing.oos_qc(items, order_id), items, in_dtm

        oos_item = wms.normal_packing.oos_qc(items, order_id) if is_oos else {}
        expected_status = 71 if is_oos else 70
        if is_oos:
            wms.normal_packing.packing_force_stock(tote_no, order_id)
        else:
            wms.normal_packing.packing_qc(items, order_id)

        recommend_box = [i["barcode"] for i in recommend_package]
        if str(storage_type) != "1":
            recommend_box.extend(["MB22", "MB19"])
        wms.normal_packing.scan_box(list(set(recommend_box)), order_id)
        
        if is_mod:
            wms.normal_packing.mo_label_create(order_id)
        else:
            wms.normal_packing.label_create(order_id)
        
        wms.normal_packing.normal_ship(tote_no, order_id)
        # wms.normal_packing.packing_logout_record_log(warehouse, wms.util.get_current_milli_time())

        self._verify_order_status(warehouse, order_id, expected_status)
        self._verify_location_released(warehouse, tote_no)
        self._verify_inventory_cleared(warehouse, items, tote_no)
        self._verify_inventory_log(warehouse, items, order_id, oos_item)
        if not global_data.is_prod:
            self._verify_hook_reports([order_id], in_dtm, is_mod, is_oos)

    def bulk_packing_operate(self, warehouse, cart_no, storage_type, account=global_data.wms_user_id,
                             username=global_data.user_name):
        """Handles the entire bulk (cart-based) packing process and verifies the results."""
        wms.util.update_header(weee_warehouse=str(warehouse), weee_wms_storage_type=str(storage_type))
        in_dtm = wms.util.get_current_time()
        wms.bulk_packing.user = f'{username}({account})'
        wms.bulk_packing.warehouse = warehouse
        # wms.normal_packing.packing_login_record_log(warehouse)

        resp = wms.bulk_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
        packing_info = wms.bulk_packing.query_bulk_packing_info(cart_no)
        order_ids = packing_info["order_ids"]
        items = packing_info["items"]

        wms.bulk_packing.bulk_packing_qc(cart_no, items[0])
        self._verify_order_status(warehouse, order_ids, 60)
        wms.bulk_packing.bulk_complete(cart_no, items)
        wms.bulk_packing.bulk_label_creates(cart_no, items[0])
        wms.bulk_packing.bulk_ship(cart_no)
        # wms.normal_packing.packing_logout_record_log(warehouse, wms.util.get_current_milli_time())

        self._verify_order_status(warehouse, order_ids, 70)
        self._verify_location_released(warehouse, cart_no)
        for order_id in order_ids:
            self._verify_inventory_cleared(warehouse, items, cart_no)
            self._verify_inventory_log(warehouse, items, order_id)
        if not global_data.is_prod:
            self._verify_hook_reports(order_ids, in_dtm)

    def oneitem_packing_operate(self, warehouse, cart_no, storage_type, account=global_data.wms_user_id,
                             username=global_data.user_name, orderId_list=[]):
        """Handles the entire oneitem (cart-based) packing process and verifies the results."""
        wms.util.update_header(weee_warehouse=str(warehouse), weee_wms_storage_type=str(storage_type))
        wms.normal_packing.user = f'{username}({account})'
        wms.normal_packing.warehouse = warehouse
        # wms.normal_packing.packing_login_record_log(warehouse)
        in_dtm = wms.util.get_current_time()

        # 开始打包
        resp = wms.normal_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))

        ret = wms.normal_packing.query_packing_info(cart_no)
        waiting_packing_order_num = ret["waitingPackingOrderNum"]
        log.info(f'{cart_no}中待打包订单数量:{waiting_packing_order_num}')

        #查询cart 下待打包的订单
        if storage_type in ["2", "3"]:
            # 干货订单无法使用此接口，后端会校验
            order_list = wms.normal_packing.query_oneitem_order_info(warehouse, cart_no)
            assert order_list, f"{cart_no}中无待打包订单"
        else:
            order_list = []
            for order_id in orderId_list:
                order_detail = wms.batch.query_order_detail(warehouse, order_id)
                orderItem = order_detail["orderItem"][0]
                upc = orderItem["upc"].split("","")[0] if "," in orderItem["upc"] else orderItem["upc"]
                order_list.append({"upc": upc, "item_quantity":orderItem["quantity"]})

        for order in order_list:
            item_info = order["items"][0]
            items, order_id, recommend_package = wms.normal_packing.one_item_get_order(cart_no, item_info["upc"])
            recommend_box = [i["barcode"] for i in recommend_package]
            if item_info["item_quantity"] > 1:
                wms.normal_packing.packing_qc(items, order_id)
            self._verify_order_status(warehouse, order_id, (60, 61))
            wms.normal_packing.scan_box(list(set(recommend_box)), order_id)
            wms.normal_packing.label_create(order_id)
            wms.normal_packing.normal_ship(cart_no, order_id)
            self._verify_order_status(warehouse, order_id, 70)
            self._verify_inventory_cleared(warehouse, order["items"], cart_no)
            self._verify_inventory_log(warehouse, order["items"], order_id)
            if not global_data.is_prod:
                self._verify_hook_reports([order_id], in_dtm)
        wms.normal_packing.one_item_complete(cart_no)
        # wms.normal_packing.packing_logout_record_log(warehouse, wms.util.get_current_milli_time())
        self._verify_location_released(warehouse, cart_no)

# Create a single instance for easy import and use
packing_process = PackingProcess()
