# 是否是生产环境
is_prod = True
# ---------------------------------------------TMS参数---------------------------------------------
# ---------------------------------------------INV参数---------------------------------------------
# query参数
pantry_inv_query = {"sales_org_id": 10,
                    "zipcode": "77070",
                    "sales_model": "inventory",
                    "region_id": "20",
                    "warehouse_number": "25"
                    }
global_fbw_query = {"sales_org_id": 9,
                    "zipcode": "92069",
                    "product_id": 2116282,
                    "sales_model": "inventory",
                    "product_type": "mkpl_fbw",
                    "region_id": "10",
                    "warehouse_number": "25",
                    "seller_id": 6887}
local_fbw_query = {"sales_org_id": 9,
                   "zipcode": "92069",
                   "product_id": 2209633,
                   "sales_model": "schedule",
                   "product_type": "fbw",
                   "region_id": "10",
                   "warehouse_number": "25",
                   "seller_id": 8668
                   }
global_query = {"sales_org_id": 1,
                "zipcode": "94501",
                "product_id": 2121775,
                "sales_model": "inventory",
                "product_type": "seller",
                "region_id": "0",
                "warehouse_number": None,
                "seller_id": 7611
                }
grocery_inventory_query = {"sales_org_id": 1,
                           "zipcode": "94501",
                           "product_id": 9450,
                           "sales_model": "inventory",
                           "product_type": "normal",
                           "region_id": "5",
                           "warehouse_number": "25"
                           }
grocery_schedule_query = {"sales_org_id": 1,
                          "zipcode": "94501",
                          "product_id": 97078,
                          "sales_model": "schedule",
                          "product_type": "normal",
                          "region_id": "5",
                          "warehouse_number": "7"
                          }

# ---------------------------------------------WMS参数---------------------------------------------
# WMS 账号设置
wms_user_id = "7226349"
wms_user_password = "123456"
user_name = "janine.cheng.1"

mkpl_user_id = '<EMAIL>'
mkpl_user_password = "296262"

# order type 与storage type mapping关系
storage_type_mapping = {"1": [0, 4, 5], "2": [2], "3": [3], "6": [6], "7": [7]}

# Order Outbound
outbound_info = {'warehouse_number': "48", "delivery_date": "2025-08-21", "order_type": [0, 2, 3, 7], "region_id": "43", "region_name":"CN - Test", "zipcode": "99991"}
create_order_ptoduct = [{"product_id": "82071", "quantity": 1}, {"product_id": "105443", "quantity": 1},{"product_id": "79521", "quantity": 1},{"product_id": "98507", "quantity": 1}]

#restock
restock = {'warehouse_number': '48','storage_type': '1','item_number': '1495','jobitem_number':'91788'}  # 1 dry; 2 fresh; 3 frozen
restockjob = {'delivery_date': '2025-06-01','configid':2651,'itemlist':['14659', '1829', '91788', '3608']}

#collect extra
collect_extra_data = {"warehouse_number": "48", "location_no": "EIT108", "item_number": "10023", "upc": "2000000010023"}

# Adjust
adjust = {
    "warehouse_number": "48",
    "item_number": "1",
    "bin_location": "E0315-2-1",
    "stock_location": "F0101-2-3",
    "storage_type": "frozen",
    "expire_dtm": "2025-06-28"
}

# moving_tool
moving_tool = {
    "warehouse_number": "48",
    "item_number": "96627",
    "upc_code": "2000000096627",
    # upc location需要支持Multiple batch 
    "upc_location": "E0315-5-3",
    "lpn_location": "F1003-1-4",
    "new_upc_location": "E0315-2-1",
    "e_tote": "E0001",
    "lpn_e_tote": "E0008",
    "pieces_per_pack": 20,
    "storage_type": "frozen",
    "receive_date": "2024-06-25",
    "expire_date": "2028-06-25"
}

# Repack
repack_picking = {
    "warehouse_number": "48",
    "storage_type": "2",
    "item_number": "8254",
    "item_per_pack": 10.00,
    "target_item": "684",
    "target_item_per_pack": 10.00,
    "target_quantity": 10,
    "conversion_rate": 1
    }

# receive
receive_info = {"warehouse_number": "48", "item_number": "106483","vendor_id":1,"storage_type":1}

# mkpl
erp_header = {
    'Cookie': 'site_lang=zh; b_cookie=1322371236; _ga_GJCVPXJH2L=GS2.1.s1755482830$o1$g1$t1755482881$j9$l0$h0; weee_session_token=1366474059; _gid=GA1.2.1703683483.1755482892; _gcl_au=1.1.1698728763.1755482898; _fbp=fb.1.1755482905692.847705827575933942; _ga_MGBY6PWBE0=GS2.1.s1755482894$o1$g1$t1755482914$j40$l0$h0; _ga_S6Y3RBT7R9=GS2.1.s1755482891$o1$g1$t1755482914$j37$l0$h0; _uetsid=39ba55007bd811f09830b5838e6e5634; _uetvid=0f24a1e062d411f0be98b9b7bf7cfae5; _gat_UA-56885317-2=1; keycloak_user_email=<EMAIL>; keycloak_user_id=7226349; keycloak_token=eyJraWQiOiJkZjBlZDI1MC1mMmIxLTQ1ZDMtYjk5MS01ODE3MjM3ZmYzMzIiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kWnywR0ER57V8yTCX2Vbw1iFMvEaTdgC-4OtTHxiBFO0vhGX5NPAEb0rdSzoF3TRyBkYWfqL7z90fp75U-_50UushoZkIpNls_6M3D7ZWFsXl-yMWF8d1jzGnjMTr_BugYLqRiNwZ_CxmCd91AXok2zkFuZgvLwv-y-s14Vj81A; ci_session=a%3A6%3A%7Bs%3A10%3A%22session_id%22%3Bs%3A32%3A%223e4f81f5a8b3b1cdf4c6338aca8e49bb%22%3Bs%3A10%3A%22ip_address%22%3Bs%3A10%3A%2245.8.204.7%22%3Bs%3A10%3A%22user_agent%22%3Bs%3A120%3A%22Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F139.0.0.0+Safari%2F537.36+Edg%2F139.%22%3Bs%3A13%3A%22last_activity%22%3Bi%3A1755482882%3Bs%3A9%3A%22user_data%22%3Bs%3A0%3A%22%22%3Bs%3A12%3A%22session_user%22%3Ba%3A5%3A%7Bs%3A14%3A%22Global_User_ID%22%3Bs%3A7%3A%227226349%22%3Bs%3A6%3A%22userId%22%3Bs%3A36%3A%22df2575bb-7d63-4fce-951d-8e9d5877b08c%22%3Bs%3A8%3A%22roleType%22%3Bs%3A5%3A%22CHUEI%22%3Bs%3A10%3A%22headImgUrl%22%3Bs%3A68%3A%22https%3A%2F%2Fimg06.weeecdn.com%2Fsocial%2Fimage%2F097%2F841%2Fsocial_avatar_10.jpeg%22%3Bs%3A11%3A%22wxSnsOpenId%22%3BN%3B%7D%7D945048436b58bfccdbae53533cf29168a9c8bf64; auth_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; weee_inventory_multi=7; _ga=GA1.1.2116773105.1755482831; weee_inventory=7; _ga_SETRDTEPCY=GS2.1.s1755482921$o1$g1$t1755482953$j28$l0$h0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
}
