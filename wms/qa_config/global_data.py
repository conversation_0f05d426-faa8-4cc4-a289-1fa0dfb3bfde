# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  global_data.py
@Description    :  
@CreateTime     :  2023/8/23 16:46
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/23 16:46
"""
# 是否是生产环境
is_prod = False
# ---------------------------------------------TMS参数---------------------------------------------
# 司机账户
driver_email = "<EMAIL>"
driver_account_name = "k001"
driver_password = "1234abcd"
driver_user_id = ********
driver_user_name = "k001 - BA-R1a"
# central账户
user_id = 7642085
password = "chuan1992"
# 派车单
dispatch_id = 471802
# ---------------------------------------------INV参数---------------------------------------------
# query参数
pantry_inv_query = {"sales_org_id": 10,
                    "zipcode": "77070",
                    "sales_model": "inventory",
                    "region_id": "20",
                    "warehouse_number": "25"
                    }
global_fbw_query = {"sales_org_id": 1,
                    "zipcode": "94127",
                    "product_id": 2132647,
                    "sales_model": "inventory",
                    "product_type": "mkpl_fbw",
                    "region_id": "5",
                    "warehouse_number": "25",
                    "seller_id": 8028}
local_fbw_query = {"sales_org_id": 1,
                   "zipcode": "94127",
                   "product_id": 2896639,
                   "sales_model": "schedule",
                   "product_type": "fbw",
                   "region_id": "5",
                   "warehouse_number": "7",
                   "seller_id": 9224
                   }
global_query = {"sales_org_id": 1,
                "zipcode": "94501",
                "product_id": 2149353,
                "sales_model": "inventory",
                "product_type": "seller",
                "region_id": "0",
                "warehouse_number": None,
                "seller_id": 8285
                }
grocery_inventory_query = {"sales_org_id": 1,
                           "zipcode": "94501",
                           "product_id": 9450,
                           "sales_model": "inventory",
                           "product_type": "normal",
                           "region_id": "5",
                           "warehouse_number": "25"
                           }
grocery_schedule_query = {"sales_org_id": 1,
                          "zipcode": "94501",
                          "product_id": 97078,
                          "sales_model": "schedule",
                          "product_type": "normal",
                          "region_id": "5",
                          "warehouse_number": "7"
                          }
reserve = {"sales_org_id": 1,
           "product_id": 100178,
           "reserve_qty": 10,
           "product_type": "normal",
           "zipcode": "94501"
           }
special = {"sales_org_id": 1,
           "product_id": 100178,
           "change_qty": 10,
           "product_type": "normal",
           "zipcode": "94501"
           }
mapping = {"mappingProductId": 2896784,
           "subProductId": 92462,
           "ratio": 10,
           "sales_org_id": 1,
           "zipcode": "94501",
           "warehouse_number": "7"
           }

# ---------------------------------------------WMS参数---------------------------------------------
# WMS 账号设置
wms_user_id = "7226349"
wms_user_password = "123456"
user_name = "janine.cheng.1"

mkpl_user_id = '<EMAIL>'
mkpl_user_password = "123456"


# count
get_count = {
    "warehouse_number": "48",
    "storage_type": "2",
    "bin_location_type": 4,
    "stock_location_type": 3,
    "status": 0,
    "bin_module_name": "random_bin_cycle_count",
    "stock_module_name": "stock_cycle_count",
    "restock_type": 2,
    "scan_location_module_name": "scan_location_cycle_count",
    "scan_location_location_no": "D2105-2-1"
}

# Adjust
adjust = {
    "warehouse_number": "48",
    "item_number": "48728",
    "bin_location": "A1621-3-1",
    "stock_location": "B8110-1-1",
    "storage_type": "dry",
    "expire_dtm": "2025-06-28"
}
# moving_tool
moving_tool = {
    "warehouse_number": "48",
    "item_number": "96627",
    "upc_code": "2000000096627",
    # upc location需要支持Multiple batch 
    "upc_location": "E0315-5-3",
    "lpn_location": "F1003-1-4",
    "new_upc_location": "E0315-2-1",
    "e_tote": "E0001",
    "lpn_e_tote": "E0008",
    "pieces_per_pack": 20,
    "storage_type": "frozen",
    "receive_date": "2024-06-25",
    "expire_date": "2028-06-25"
}
# expiration_check
expiration_check = {
    "warehouse_number": "48",
    "storage_type": "1"
}
# packing
normal_packing = {
    "warehouse": "33",
    "tote_no": "TM1051",
    "order_id": "17866123",
    "storage_type": "1",
}
geekplus_packing = {
    "warehouse": "41",
    "tote_no": "TG0236",
    "order_id": "242514390",
    "storage_type": "1",
}
geekplus_packing_to_replenish = {
    "warehouse": "41",
    "tote_no": "TG0744",
    "order_id": "242515507",
    "storage_type": "1",
}
as_packing_to_replenish = {
    "warehouse": "25",
    "tote_no": "TM1001",
    "order_id": "242500975",
    "storage_type": "1",
    "hot_tote": "H0080",
}
as_packing = {
    "warehouse": "25",
    "tote_no": "TL0135",
    "order_id": "242506772",
    "storage_type": "1",
}
normal_forcestock_packing = {
    "warehouse": "20",
    "tote_no": "TL0004",
    "order_id": "1786780502",
    "storage_type": "2",
}
normal_cancel_packing = {
    "warehouse": "29",
    "tote_no": "TS0336",
    "order_id": "17891564",
    "storage_type": "1",
}
mof_cancel_packing = {
    "warehouse": "29",
    "tote_no": "TM0300",
    "order_id": "242506705",
    "storage_type": "6",
}
sowing_packing = {
    "warehouse": "8",
    "tote_no": "X011",
    "order_id": "24245104902",
    "storage_type": "2",
}
sowing_forcestock_packing = {
    "warehouse": "8",
    "tote_no": "X329",
    "order_id": "1787595502",
    "storage_type": "2",
}
mof_packing = {
    "warehouse": "33",
    "tote_no": "TS0305",
    "order_id": "23838138",
    "storage_type": "6",
}
mof_forcestock_packing = {
    "warehouse": "33",
    "tote_no": "TM0237",
    "order_id": "242503926",
    "storage_type": "6",
}
one_item_packing = {
    "warehouse": "33",
    "cart_no": "OM02",
    "order_id": "17732467",
    "storage_type": "1",
}
mod_packing = {
    "warehouse": "33",
    "tote_no": "TS0004",
    "order_id": "18650464",
    "storage_type": "1",
}
bulk_packing = {
    "warehouse": "33",
    "cart_no": "BM010",
    "order_id": [21718932, 21718938, 21718967],
    "storage_type": "1",
}
mof_packing_to_replenish = {
    "warehouse": "33",
    "tote_no": "TS0183",
    "order_id": "242504079",
    "storage_type": "6",
}
normal_packing_to_replenish = {
    "warehouse": "33",
    "tote_no": "TM0456",
    "order_id": "23836114",
    "storage_type": "1",
    "hot_tote": "H0050"
}
replenish_oos = {
    "warehouse": "8",
    "tote_no": "TS0003",
    "order_id": "24255968602",
    "storage_type": "2",
    "hot_tote": "H0018"
}
replenish_unbind = {
    "warehouse": "8",
    "tote_no": "TS0971",
    "order_id": "24255653902",
    "storage_type": "2"
}
replenish_force = {
    "warehouse": "8",
    "tote_no": "TS0970",
    "order_id": "24255653802",
    "storage_type": "2"
}
bulk_packing_to_replenish = {
    "warehouse": "33",
    "cart_no": "BM050",
    "order_id": [2383707902],
    "storage_type": "2",
}
one_item_packing_to_replenish = {
    "warehouse": "33",
    "cart_no": "OM45",
    "order_id": "2383755002",
    "storage_type": "2",
}
normal_packing_query = {
    "warehouse": "33",
    "day": "2024-09-22",
    "order_packing_num": "24252213103",
    "order_type": "3",
    "storage_type": "3"
}

# picking
normal_picking = {
    "warehouse_number": "25",
    "storage_type": "1",
}  # 1 dry; 2 fresh; 3 frozen
bulk_picking = {"warehouse_number": "25"}
mo_picking = {"warehouse_number": "25"}
mof_picking = {"warehouse_number": "33"}
one_item_picking = {"warehouse_number": "25"}
fbw_picking = {"warehouse_number": "25", "storage_type": "2"}
alcohol_picking = {"warehouse_number": "8"}
as_picking = {"warehouse_number": "25", "storage_type": "1"}
geekPlus_picking = {"warehouse_number": "41", "storage_type": "1"}
repack_picking = {
    "warehouse_number": "48",
    "storage_type": "2",
    "item_number": "8254",
    "item_per_pack": 10.00,
    "target_item": "684",
    "target_item_per_pack": 10.00,
    "target_quantity": 10,
    "conversion_rate": 1
    }

# route_check
route_check = {
    "warehouse": "37",
    "delivery_date": "2024-01-15",
    "route_id": 204,
    "storage_type": "3",
}

# route check move to cot
route_check_cancel = {
    "warehouse_number": "29",
    "delivery_date": "2024-03-15",
    "route_id": 2,
    "storage_type": "3",
    "tracking_num": "24250619903965",
    "order_id": "24250619903"
}

# receive
# receive_info = {"warehouse_number": "20", "reference_no": "2655913", "item_number": "30702"}
receive_info = {"warehouse_number": "48", "item_number": "112","vendor_id":10,"storage_type":3}
receive_delete_info = {"warehouse_number": "20", "reference_no": "2655914", "item_number": "96024"}
# putaway
putaway_info = {
    "warehouse_number": "20",
    "location_no": "B0432-1-1",
    "item_number": "30702"
}

# as_receive
as_receive_info = {"warehouse_number": "25", "reference_no": "2655915", "item_number": "510"}
# as_putaway
as_putaway_info = {
    "warehouse_number": "25",
    "location_no": "AUTOSTORE",
    "item_number": "510"
}

# geek_receive
geek_receive_info = {"warehouse_number": "41", "reference_no": "2655916", "item_number": "5660"}
# geek_putaway
geek_putaway_info = {
    "warehouse_number": "41",
    "location_no": "GEEKPLUS",
    "item_number": "5660"
}

# ro_receive
ro_receive_info = {"warehouse_number": "25", "reference_no": "RO0000001300", "item_number": "44838"}
# ro_putaway
ro_putaway_info = {
    "warehouse_number": "25",
    "item_number": "44838"
}

# order type 与storage type mapping关系
storage_type_mapping = {"1": [0, 4, 5], "2": [2], "3": [3], "6": [6], "7": [7]}

# Cancel Order
cancel_order = {"warehouse_number": "29", "order_id": "17891564", "order_type": 0}

# OOS Refund Order
oos_refund_order = {"warehouse_number": "29", "order_id": "242506200", "order_type": 0}

# order item oos
partial_oos = {"warehouse_number": "20", "order_id": "242527339", "delivery_dtm": "2024-12-03", "item_number": "10395", "order_type": 0}
all_oos = {"warehouse_number": "20", "order_id": "24252829803", "delivery_dtm": "2024-12-12", "item_number": "2068", "order_type": 3}

#vendor return
vendor_order = {'warehouse_number': '41', 'storage_type': '1','item_Number1': '2846292'}  # 1 dry; 2 fresh; 3 frozen

#expire control
expire_control = {'warehouse_number': '29','storage_type': '2','item_Number': '19231','location_no':'C0207-1-1'}

#transship
ts_info = {"ship_out_warehouse": "25", "ship_in_warehouse": "20", "item_number": "44218", "location_no": "F0102-2-3", "storage_type": "3"}

#transship Receive
ts_receive_info = {'warehouse_number': '20'}

#collect extra
collect_extra_data = {"warehouse_number": "48", "location_no": "EIT108", "item_number": "10023", "upc": "2000000010023"}

#restock
restock = {'warehouse_number': '48', 'storage_type': '1', 'item_number': '1495', 'jobitem_number': '91788'}  # 1 dry; 2 fresh; 3 frozen
restockjob = {'delivery_date': '2025-06-01', 'configid': 2651, 'itemlist': ['14659', '1829', '91788', '3608']}

# IO
IOitem_number = {"item_Number": "2896577"}

# Order Outbound
outbound_info = {'warehouse_number': "48", "delivery_date": "2025-08-21", "order_type": [0, 2, 3, 7], "region_id": "43", "region_name":"CN - Test", "zipcode": "99991"}
create_order_ptoduct = [{"product_id": "95364", "quantity": 1}, {"product_id": "104615", "quantity": 1},{"product_id": "95886", "quantity": 1},{"product_id": "100954", "quantity": 1}]

erp_header = {
    'Cookie': 'site_lang=zh; b_cookie=5445242; auth_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjljYjNiOGJhLTEwYzEtNDEwZS04ODQ5LWY0YWFlNTg3NzRmMyJ9.************************************************************************************************************************************************************************************************************************************.Yjh7sND4iY5qS5vGhIgP480zKeNNSVwl9PxPPmDLV4eRswD4rjgJRzgrPoqKgnrOru_r4nhQze46MKtYHqRBSqfXZpgEx-L4ORRIpFQpIAT0SwiaozvrAhvOKdcWG49VPFLZtXD8mFBDmEI0thTckfyyw5clCRuynngyZR8cA2c; _gcl_au=1.1.1600085234.1753670587; weee_inventory=7; site_lang=zh; auth_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjljYjNiOGJhLTEwYzEtNDEwZS04ODQ5LWY0YWFlNTg3NzRmMyJ9.************************************************************************************************************************************************************************************************************************************.Yjh7sND4iY5qS5vGhIgP480zKeNNSVwl9PxPPmDLV4eRswD4rjgJRzgrPoqKgnrOru_r4nhQze46MKtYHqRBSqfXZpgEx-L4ORRIpFQpIAT0SwiaozvrAhvOKdcWG49VPFLZtXD8mFBDmEI0thTckfyyw5clCRuynngyZR8cA2c; user_id=10953840; affiliate_referral_id=10953840; NEW_ZIP_CODE=94538; NEW_ZIP_CITY=Fremont; NEW_SALES_ORG_ID=1; shipping_free_fee=35; shipping_fee=5.99; IS_LOGIN=1; is_support_global=0; is_support_change_date=1; keycloak_user_email=<EMAIL>; keycloak_user_id=10953840; keycloak_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; _ga_PQGRT8DXDE=GS2.1.s1753863127$o1$g1$t1753863379$j60$l0$h0; b_cookie=5475694; afUserId=95bd4144-1193-440d-ac72-e5b0a821b047-p; AF_SYNC=1753865385648; _uetvid=ec86d8d02f9a11f083db1176329d73c0; keycloak_user_email=<EMAIL>; keycloak_user_id=10953840; keycloak_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; _ga_S6Y3RBT7R9=GS2.1.s1753865384$o4$g1$t1753865396$j48$l0$h0; _ga_MGBY6PWBE0=GS2.1.s1753865384$o4$g1$t1753865396$j48$l0$h0; _ga=GA1.1.1727129579.1753155952; weee_sales_org=1; ftu_cookie=5475694; landing_url=%2Fzh%2Fmember%2Fportal; _fbp=fb.1.1753865443169.500794232415902062; checkout_type=normal; weee_session_token=5482086; checkout_type=normal; weee_session_token=5482087; DELIVERY_DATE=2025-08-02; assets_cdn=%7B%220%22%3A0%2C%221%22%3A0%2C%222%22%3A0%2C%223%22%3A0%2C%224%22%3A0%2C%225%22%3A0%7D; ci_session=a%3A9%3A%7Bs%3A10%3A%22session_id%22%3Bs%3A32%3A%2235f8c09406dce2dae4630dd396720e08%22%3Bs%3A10%3A%22ip_address%22%3Bs%3A10%3A%2245.8.204.7%22%3Bs%3A10%3A%22user_agent%22%3Bs%3A111%3A%22Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F138.0.0.0+Safari%2F537.36%22%3Bs%3A13%3A%22last_activity%22%3Bi%3A1754014056%3Bs%3A9%3A%22user_data%22%3Bs%3A0%3A%22%22%3Bs%3A12%3A%22session_user%22%3Ba%3A5%3A%7Bs%3A14%3A%22Global_User_ID%22%3Bs%3A8%3A%2210953840%22%3Bs%3A6%3A%22userId%22%3Bs%3A36%3A%228327ddad-f4c3-4928-ab56-92a37812fcf4%22%3Bs%3A8%3A%22roleType%22%3Bs%3A45%3A%22Y2K1Y1Y0Y6DJVCWBY8FQHY9SGMPY7Z0ROY5LZ1KUY3Y4A%22%3Bs%3A10%3A%22headImgUrl%22%3Bs%3A78%3A%22https%3A%2F%2Fimg06.test.weeecdn.com%2Fuser_avatar%2Fimage%2F741%2F222%2F24701E7CC953AD47.jpeg%22%3Bs%3A11%3A%22wxSnsOpenId%22%3BN%3B%7Ds%3A20%3A%22session_pre_order_id%22%3Bi%3A10953840%3Bs%3A27%3A%22flash%3Aold%3Ais_shipping_order%22%3Bb%3A0%3Bs%3A33%3A%22flash%3Aold%3Amail_order_show_zipcode%22%3Bs%3A5%3A%2294538%22%3B%7D336155498af1bd68665fdd47ffe314ba3981bbec; weee_inventory_multi=7%2C8%2C16%2C20%2C25%2C29%2C35%2C38%2C39%2C40%2C41; _ga_GJCVPXJH2L=GS2.1.s1754014050$o24$g1$t1754014195$j54$l0$h0; ci_session=a%3A7%3A%7Bs%3A10%3A%22session_id%22%3Bs%3A32%3A%2235f8c09406dce2dae4630dd396720e08%22%3Bs%3A10%3A%22ip_address%22%3Bs%3A10%3A%2245.8.204.7%22%3Bs%3A10%3A%22user_agent%22%3Bs%3A111%3A%22Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F138.0.0.0+Safari%2F537.36%22%3Bs%3A13%3A%22last_activity%22%3Bi%3A1754014056%3Bs%3A9%3A%22user_data%22%3Bs%3A0%3A%22%22%3Bs%3A12%3A%22session_user%22%3Ba%3A5%3A%7Bs%3A14%3A%22Global_User_ID%22%3Bs%3A8%3A%2210953840%22%3Bs%3A6%3A%22userId%22%3Bs%3A36%3A%228327ddad-f4c3-4928-ab56-92a37812fcf4%22%3Bs%3A8%3A%22roleType%22%3Bs%3A45%3A%22Y2K1Y1Y0Y6DJVCWBY8FQHY9SGMPY7Z0ROY5LZ1KUY3Y4A%22%3Bs%3A10%3A%22headImgUrl%22%3Bs%3A78%3A%22https%3A%2F%2Fimg06.test.weeecdn.com%2Fuser_avatar%2Fimage%2F741%2F222%2F24701E7CC953AD47.jpeg%22%3Bs%3A11%3A%22wxSnsOpenId%22%3BN%3B%7Ds%3A20%3A%22session_pre_order_id%22%3Bi%3A10953840%3B%7D7844f020e3f56d321fbd006544e8b0e8b9ff4d1f; _ga_SETRDTEPCY=GS2.1.s1754014058$o46$g1$t1754014197$j52$l0$h0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
}
