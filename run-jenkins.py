# !/usr/bin/python3
# -*- coding: utf-8 -*-

import os

import weeeTest
from weeeTest import log


# ============================ 请不要修改配置 ====================================
# 请不要修改一下配置
def run():
    mark_orders = []
    log.info(f"env:os.getenv('mark')")
    if os.getenv("mark"):
        mark_order_str = " or ".join(os.getenv('mark').split(","))
        mark_orders.append('-m')
        mark_orders.append(mark_order_str)
        # 拼接desc
        desc = "app: " + os.getenv("JOB_NAME", None) + "<br/>" + "env: " + 'tb1' + "<br/>" + "mark: " + os.getenv(
            "mark", None),

    else:

        desc = "app: " + os.getenv("JOB_NAME", None) + "<br/>" + "env: " + 'tb1' + "<br/>",

    # junit report
    mark_orders.append("--junit-xml=report.xml")
    # junit report prefix
    if os.getenv("mark") is not None:
        jenkins_report = f"--junit-prefix={os.getenv('mark')}"
        mark_orders.append(jenkins_report)

    # 添加allure-result生成目录
    mark_orders.append("--alluredir=./allure-results")

    weeeTest.main(
        base_url="https://api.tb1.sayweee.net",
        debug=True,
        title="app:" + os.getenv("JOB_NAME", None) + "[" + os.getenv("BUILD_NUMBER", None) + "]" + "  env:" + 'tb1',
        tester=os.getenv("BUILD_USER", 'Anybody'),
        description=desc,
        language="en",
        ext=mark_orders,
        weixin_access_token='d3fe1975-2ffc-448a-8118-c17cfc015012',
        email_to='zhenguo.li,charles.kou,lin.zhang,qa-fulfillment-engineering',

    )


# ============================ 请不要修改配置 ====================================
if __name__ == '__main__':
    run()
