# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  tms.py
@Description    :  
@CreateTime     :  2023/8/7 16:10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/7 16:10
"""
import time

import weeeTest

from tms.qa_config import global_data
from tms.test_dir.api.tms import driver_header, central_header
from tms.test_dir.api.tms.driver_app.driver import TmsDriver
from tms.test_dir.api.tms.tms_central.central import TmsCentral
from tms.test_dir.api_case.tms import utils
from tms.test_dir.api_case.tms.tms_sql import TmsDB


class TMS(weeeTest.TestCase):
    """
    TMS系统公共方法封装
    """
    central = TmsCentral()
    driver = TmsDriver()
    tms_db = TmsDB()
    util = utils

    def login(self, driver_email=None, central_id=None, driver_password='1234abcd', central_password=None):
        """
        登录账户
        :param driver_email: 司机邮箱
        :param driver_password: 司机密码
        :param central_id: central ID
        :param central_password: central 密码
        :return:
        """
        if weeeTest.weeeConfig.base_url != 'https://api.sayweee.net':
            if driver_email:
                self.driver.driver_login(email=driver_email, password=driver_password)
            if not driver_header["authorization"]:
                self.driver.driver_login(email=global_data.driver_email, password=global_data.driver_password)
        else:
            if driver_email:
                self.driver.driver_login(email=driver_email, password=driver_password)
        if central_id:
            self.central.central_login(user_id=central_id, password=central_password)
        if not central_header["authorization"]:
            self.central.central_login(user_id=global_data.user_id, password=global_data.password)

    def route_info(self, delivery_date, sub_region_ids, delivery_type=None, status=None):
        """
        获取路线相关信息
        :param delivery_date: '2022-12-18'
        :param sub_region_ids: [1, 2, 3, 4]
        :param delivery_type: 派车单类型 delivery,hot_delivery
        :param status: 30
        :return:
        """
        # 获取草稿列表
        draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                sub_region_ids=sub_region_ids,
                                                                delivery_type=delivery_type,
                                                                status=status)
        draft_info = self.util.get_response_values(draft_list_res, 'draft_id[0]', 'delivery_plan_id[0]')
        # 获取路线信息
        plan_detail_res = self.central.route_plan.get_plan_detail(draft_id=draft_info[0])
        plan_detail_info = self.util.get_response_values(plan_detail_res, 'rec_id', 'route_id')
        # 获取可用司机
        if isinstance(plan_detail_info[0], int):
            rec_id = plan_detail_info[0]
        else:
            rec_id = plan_detail_info[0][0]
        route_drivers_res = self.central.route_plan.get_route_drivers(delivery_plan_id=draft_info[1],
                                                                      draft_id=draft_info[0],
                                                                      rec_id=rec_id)
        return {"route_rec_ids": plan_detail_info[0],  # 路线rec_id,值为列表
                'route_ids': plan_detail_info[1],  # 路线id,值为列表
                "draft_id": draft_info[0],  # 草稿ID
                "delivery_plan_id": draft_info[1],  # 派车计划ID
                "driver_id": route_drivers_res['object'][-1]['driver_user_id'],  # 路线司机ID
                "driver_name": route_drivers_res['object'][-1]['driver_name']  # 路线司机姓名
                }

    def get_draft_id(self, delivery_date, sub_region_ids, delivery_type, status=30, index=0):
        """
        获取一个满足条件的Draft ID
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion id
        :param delivery_type: 配送类型
        :param status: 状态
        :param index: 序列号
        :return:
        """
        draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                sub_region_ids=sub_region_ids,
                                                                delivery_type=delivery_type, status=status)
        draft_id = self.util.get_response_values(draft_list_res, f'draft_id[{index}]')
        return draft_id[0]

    def update_dispatch(self, username, delivery_date_old, delivery_type, status="load_finish"):
        """
        更新派车单信息
        @param username: 账号名
        @param delivery_date_old: 原配送日期
        @param delivery_type: 派车单类型 delivery,hot_delivery
        @param status: 派车单状态
        @return:
        """
        delivery_date_new = self.util.us_current_date()
        account_id = self.tms_db.get_user_id(username)  # 获取账户ID
        sales_org_id = self.tms_db.get_driver_org_id(account_id)  # 获取销售组织ID
        inventory_id = self.tms_db.get_inventory_id(sales_org_id)  # 获取仓库ID
        delivery_plan_id = self.tms_db.get_delivery_plan_id(inventory_id=inventory_id, delivery_type=delivery_type,
                                                            delivery_date=delivery_date_old)  # 获取指定条件的派车计划
        dispatch_id = self.tms_db.get_dispatch_id(delivery_plan_id)  # 获取派车计划下的一条派车单ID
        self.tms_db.update_dispatch_status(dispatch_id=dispatch_id, delivery_date=delivery_date_new,
                                           status=status)  # 更新派车单状态
        self.tms_db.change_package_status(dispatch_id=dispatch_id)  # 修改包裹状态
        self.tms_db.delete_task(dispatch_id)  # 删除历史task数据
        self.central.driver_manage.sync_tasks(dispatch_id)  # 生成task数据
        self.util.wait(5)  # 等待task数据生成
        return dispatch_id, account_id, username

    def reset_dispatch(self, dispatch_id, status="load_finish", package_status="packed"):
        """
        重置派车单信息
        @param dispatch_id: 派车单ID
        @param status: 派车单状态
        @param package_status: 包裹状态
        @return:
        """
        delivery_date_new = self.util.us_current_date()
        self.tms_db.update_dispatch_status(dispatch_id=dispatch_id, delivery_date=delivery_date_new,
                                           status=status)  # 更新派车单状态
        self.tms_db.change_package_status(dispatch_id=dispatch_id, statues=package_status)  # 修改包裹状态
        self.tms_db.delete_task(dispatch_id)  # 删除历史task数据
        while self.tms_db.check_task_created(dispatch_id):
            self.util.wait(3)  # 等待task数据删除
        self.central.driver_manage.sync_tasks(dispatch_id=dispatch_id)  # 生成task数据
        start_time = time.time()
        while not self.tms_db.check_task_created(dispatch_id):
            if (time.time() - start_time) > 60:
                raise Exception(f'派车单:{dispatch_id}生成task任务超时')
            self.util.wait(5)  # 等待task数据生成

    def sync_tasks_if_no_task(self, dispatch_id):
        """
        如无task,则生成task
        :param dispatch_id:
        :return:
        """
        if not self.tms_db.check_task_created(dispatch_id):
            delivery_plan_id = self.tms_db.get_delivery_plan_id_by_dispatch_id(dispatch_id)
            self.central.driver_manage.sync_tasks(delivery_plan_id=delivery_plan_id)  # 生成task数据
            start_time = time.time()
            while not self.tms_db.check_task_created(dispatch_id):
                if (time.time() - start_time) > 60:
                    raise Exception(f'派车单:{dispatch_id}生成task任务超时')
                self.util.wait(5)  # 等待task数据生成

    def fpo_generate_delivery_plan(self, delivery_date: str = None):
        """
        FPO生成发货计划
        :param delivery_date: 2024-09-10
        :return:
        """
        if delivery_date:
            delivery_time = self.util.get_time_stamp(delivery_date)
        else:
            delivery_time = tms.util.get_time()
        cutoff_time_list = self.tms_db.get_cutoff_time(delivery_time)
        for cutoff_time in cutoff_time_list:
            self.central.route_plan.auto_generate_and_send(time_delivery=cutoff_time, check_result=True)

    def assign_driver_dispatch(self, username, delivery_date_old, delivery_type):
        """
        分配司机派车单
        @param username: 账号名
        @param delivery_date_old: 原配送日期
        @param delivery_type: 派车单类型 delivery,hot_delivery
        @return:
        """
        # 获取派车单数据
        dispatch_info = self.update_dispatch(username=username, delivery_date_old=delivery_date_old,
                                             delivery_type=delivery_type)
        # 更新派车单司机,生成task数据
        self.central.driver_manage.update_drivers(dispatch_id=dispatch_info[0], driver_id=dispatch_info[1],
                                                  driver_name=dispatch_info[2])
        return dispatch_info

    def change_deployed_draft_to_plan(self, delivery_date=None, sub_region_ids=None, delivery_type='delivery',
                                      page_size=20, delivery_plan_id=None):
        """
        将已应用的Draft改为未应用
        :param delivery_plan_id: 发货计划ID
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion ids, [1,2,3,4]
        :param delivery_type: delivery,hot_delivery,alcohol_delivery
        :param page_size:
        :return:
        """
        if delivery_plan_id:
            draft_list_res = self.central.route_plan.get_draft_list(sub_region_ids=sub_region_ids,
                                                                    delivery_plan_id=delivery_plan_id, status=40)
        else:
            draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                    sub_region_ids=sub_region_ids,
                                                                    delivery_type=delivery_type,
                                                                    page_size=page_size, status=40)
        data_num = self.util.get_response_values(draft_list_res, 'data')[0]
        if len(data_num) > 0:
            draft_ids = self.util.get_response_values(draft_list_res, 'draft_id')[0]
            if isinstance(draft_ids, int):
                self.central.route_plan.delete_dispatch(draft_ids)
            else:
                for draft_id in draft_ids:
                    self.central.route_plan.delete_dispatch(draft_id)

    def delete_draft(self, delivery_date=None, sub_region_ids=None, delivery_type=None, page_size=50,
                     delivery_plan_id=None):
        """
        删除所有满足条件的Draft
        :param delivery_plan_id:
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion ids, [1,2,3,4]
        :param delivery_type: delivery,hot_delivery,alcohol_delivery
        :param page_size:
        :return:
        """
        while True:
            if delivery_plan_id:
                sub_region_id = self.tms_db.get_sub_region_id_by_delivery_plan(delivery_plan_id)
                draft_list_res = self.central.route_plan.get_draft_list(sub_region_ids=[sub_region_id],
                                                                        delivery_plan_id=delivery_plan_id, status=30)
                draft_plan_num = self.util.get_response_values(draft_list_res, 'data')[0]
                if len(draft_plan_num) < 1:
                    draft_list_res = self.central.route_plan.get_draft_list(sub_region_ids=[sub_region_id],
                                                                            delivery_plan_id=delivery_plan_id,
                                                                            status=60)
            else:
                draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                        sub_region_ids=sub_region_ids,
                                                                        delivery_type=delivery_type,
                                                                        page_size=page_size, status=30)
            data_num = self.util.get_response_values(draft_list_res, 'data')[0]
            if len(data_num) > 0:
                draft_ids = self.util.get_response_values(draft_list_res, 'draft_id')[0]
                if isinstance(draft_ids, int):
                    self.central.route_plan.delete_draft(draft_ids)
                else:
                    for draft_id in draft_ids:
                        self.central.route_plan.delete_draft(draft_id)
            else:
                break

    def cancel_draft(self, delivery_date=None, sub_region_ids=None, delivery_type=None, page_size=50,
                     delivery_plan_id=None):
        """
        停止正在运行的Draft
        :param delivery_plan_id:
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion ids, [1,2,3,4]
        :param delivery_type: delivery,hot_delivery,alcohol_delivery
        :param page_size:
        :return:
        """
        while True:
            if delivery_plan_id:
                sub_region_id = self.tms_db.get_sub_region_id_by_delivery_plan(delivery_plan_id)
                draft_list_res = self.central.route_plan.get_draft_list(sub_region_ids=[sub_region_id],
                                                                        delivery_plan_id=delivery_plan_id, status=20)
            else:
                draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                        sub_region_ids=sub_region_ids,
                                                                        delivery_type=delivery_type,
                                                                        page_size=page_size, status=20)
            data_num = self.util.get_response_values(draft_list_res, 'data')[0]
            if len(data_num) > 0:
                draft_ids = self.util.get_response_values(draft_list_res, 'draft_id')[0]
                if isinstance(draft_ids, int):
                    self.central.route_plan.cancel_draft(draft_ids)
                else:
                    for draft_id in draft_ids:
                        self.central.route_plan.cancel_draft(draft_id)
            else:
                break

    def delete_all_delivery_plan(self, delivery_date=None, sub_region_ids=None, delivery_plan_id=None):
        """
        删除所有已应用的派车计划
        :param delivery_plan_id:
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion ids, [1,2,3,4]
        :return:
        """
        if delivery_plan_id:
            self.central.route_plan.delete_delivery_plan(delivery_plan_id=delivery_plan_id)
            self.util.wait(5)
        else:
            draft_list_res = self.central.route_plan.get_region_dispatch_list(sub_region_ids=sub_region_ids,
                                                                              delivery_date=delivery_date)
            data_num = self.util.get_response_values(draft_list_res, 'data')[0]
            if len(data_num) > 0:
                plan_ids = self.util.get_response_values(draft_list_res, 'id')[0]
                if isinstance(plan_ids, int):
                    self.central.route_plan.delete_delivery_plan(plan_ids)
                    self.util.wait(5)
                else:
                    for plan_id in plan_ids:
                        self.central.route_plan.delete_delivery_plan(plan_id)
                    self.util.wait(5)

    def get_driver_info(self, info):
        """
        获取司机信息
        :param info: driver_user_id,email,driver_name,sub_region_id
        :return:
        """
        driver_res = self.driver.get_driver_info()
        driver_info = self.util.get_json_values(driver_res, f"object.{info}")
        return driver_info

    def get_dispatch_packages(self, dispatch_id):
        """
        根据站点编号返回包裹信息
        :param dispatch_id:
        :return:
        """
        package_info = self.tms_db.get_package_info(dispatch_id=dispatch_id)
        tracking_num_info = self.util.get_tracking_num_info(package_info)
        return tracking_num_info

    def get_packages(self, dispatch_id, package_type, stop_seq=None):
        """
        获取指定包裹类型的信息
        :param dispatch_id:
        :param package_type: F,Z,D
        :param stop_seq: 站点序号
        :return:
        """
        package_info = self.tms_db.get_package_by_type(dispatch_id=dispatch_id, package_type=package_type)
        stop_packages = []
        if stop_seq:
            for package in package_info:
                if package[0] == stop_seq:
                    stop_packages.append(package)
            return stop_packages
        else:
            return package_info

    def delete_delivery_plan(self, delivery_date, sub_region_ids, delivery_type=None):
        """
        删除Region下某一天的Delivery Plan数据
        :param delivery_date: 日期
        :param sub_region_ids: subRegion列表[]
        :param delivery_type: 派车单类型
        :return:
        """
        self.change_deployed_draft_to_plan(delivery_date, sub_region_ids, delivery_type)
        self.delete_draft(delivery_date, sub_region_ids, delivery_type)
        self.delete_all_delivery_plan(delivery_date, sub_region_ids)

    def get_package_list(self, dispatch_id, package_type):
        """
        获取包裹码列表
        :param dispatch_id: 派车单ID
        :param package_type: 包裹类型
        :return:
        """
        package_info = self.tms_db.get_package_by_type(dispatch_id=dispatch_id, package_type=package_type)
        package_list = []
        for package in package_info:
            package_list.append(package[1])
        return package_list

    def delete_delivery_data(self, delivery_date: str = None, region_id: int = None, sub_region_ids: list = None,
                             delivery_plan_id: int = None):
        """
        删除排车数据,包括Dispatch, Draft, Delivery Plan
        :param delivery_plan_id:
        :param delivery_date: 配送日期
        :param region_id: 区域ID
        :param sub_region_ids: 子区域ID
        :return:
        """
        if delivery_plan_id:
            sub_region_ids = [self.tms_db.get_sub_region_id_by_delivery_plan(delivery_plan_id)]
            self.tms_db.check_delivery_plan_data(delivery_plan_id=delivery_plan_id)
            self.change_deployed_draft_to_plan(delivery_plan_id=delivery_plan_id, sub_region_ids=sub_region_ids)
            self.util.wait(5)
            self.delete_draft(delivery_plan_id=delivery_plan_id, sub_region_ids=sub_region_ids)
            self.util.wait(5)
            self.delete_all_delivery_plan(delivery_plan_id=delivery_plan_id)
        else:
            if region_id:
                sub_region_ids = self.tms_db.get_sub_region_ids(region_id)
            delivery_plan_res = self.central.route_plan.get_region_dispatch_list(sub_region_ids, delivery_date)
            delivery_plan_info = self.util.get_response_values(delivery_plan_res, 'data')[0]
            if len(delivery_plan_info) > 0:
                delivery_plan_list = self.util.get_response_values(delivery_plan_res, 'id')[0]
                if isinstance(delivery_plan_list, int):
                    self.tms_db.check_delivery_plan_data(delivery_plan_id=delivery_plan_list)
                    self.change_deployed_draft_to_plan(delivery_plan_id=delivery_plan_list,
                                                       sub_region_ids=sub_region_ids)
                    self.util.wait(5)
                    self.delete_draft(delivery_plan_id=delivery_plan_list, sub_region_ids=sub_region_ids)
                    self.util.wait(5)
                    self.delete_all_delivery_plan(delivery_plan_id=delivery_plan_list)
                elif isinstance(delivery_plan_list, list):
                    for delivery_plan_id in delivery_plan_list:
                        self.tms_db.check_delivery_plan_data(delivery_plan_id=delivery_plan_id)
                        self.change_deployed_draft_to_plan(delivery_plan_id=delivery_plan_id,
                                                           sub_region_ids=sub_region_ids)
                        self.util.wait(5)
                        self.delete_draft(delivery_plan_id=delivery_plan_id, sub_region_ids=sub_region_ids)
                        self.util.wait(5)
                        self.delete_all_delivery_plan(delivery_plan_id=delivery_plan_id)
        self.util.wait(5)  # 等待数据更新
        return sub_region_ids

    def generate_delivery(self, delivery_ids, delivery_date=None):
        """
        生成送货点
        :param delivery_ids:
        :param delivery_date:
        :return:
        """
        if not delivery_date:
            delivery_date = self.tms_db.get_delivery_date(delivery_id=delivery_ids)
        delivery_res = self.central.route_plan.generate_delivery_point(delivery_ids=delivery_ids,
                                                                       delivery_date=delivery_date)
        delivery_plan_id = self.util.get_response_values(delivery_res, 'delivery_plan_id')[0]
        return delivery_plan_id

    def wait_algorithm_finish(self, draft_id):
        """
        等待算法执行完成
        :param draft_id:
        :return:
        """
        i = 0
        while True:
            pre_draft_res = self.central.route_plan.get_process_for_draft_run(draft_id=draft_id)
            # 判断是否返回预计完成时间,未返回时等待1秒
            estimate_finish_time = self.util.get_response_values(pre_draft_res, 'estimate_milliseconds')[0]
            if not estimate_finish_time:
                if i < 100:  # 防止算法执行失败出现死循环
                    i = i + 1
                    self.util.wait(6)
                    plan_detail_res = self.central.route_plan.get_plan_detail(draft_id)
                    if self.util.get_response_values(plan_detail_res, 'object')[0]:
                        break
                else:
                    self.util.print_log('算法执行时间超时', level='error')
                    break
            else:
                # 返回预计完成时间时,与当前时间比对,等待对应时长
                eta = int(estimate_finish_time) / 1000
                current_time = self.util.get_time()
                if eta - current_time > 0:
                    self.util.wait((eta - current_time) * 3 // 4)  # 先等待预计完成时间的3/4时间
                    plan_detail_res = self.central.route_plan.get_plan_detail(draft_id)
                    if self.util.get_response_values(plan_detail_res, 'object')[0]:
                        break
                    else:
                        self.util.wait((eta - current_time) // 4)  # 算法仍未执行完毕, 再等待预计完成时间的1/4时间
                else:
                    # 时间达到后判断Draft是否正常返回详情信息
                    if i < 30:  # 防止算法执行失败出现死循环,超过3分钟自动退出
                        i = i + 1
                        plan_detail_res = self.central.route_plan.get_plan_detail(draft_id)
                        if self.util.get_response_values(plan_detail_res, 'object')[0]:
                            break
                        self.util.wait(6)
                    else:
                        self.util.print_log('算法执行时间超时', level='error')
                        break

    def check_route_change_result(self, draft_id):
        """
        校验路线修改结果
        :param draft_id:
        :return:
        """
        while True:
            res = self.central.route_plan.get_change_result(draft_id)
            if not res:
                raise Exception(f'草稿{draft_id}路线修改失败!!!')
            else:
                status = self.util.get_response_values(res, 'status')[0]
                if status == 2:
                    break
                elif status == 0:
                    self.util.wait(5)
                else:
                    raise Exception(f'草稿{draft_id}路线修改失败!!!')

    def create_dispatch_check(self, draft_id):
        """
        校验创建派车单结果
        :param draft_id:
        :return:
        """
        i = 0
        while i < 20:
            create_res = self.central.route_plan.create_dispatch(draft_id)
            delivery_list_info = self.util.get_response_values(create_res, 'message_id', 'message')
            if delivery_list_info[0] == '110007':
                if 'There are stops no seq' in delivery_list_info[1]:
                    self.util.wait(3)
                    i += 1  # 只有在特定错误时才增加重试次数
                elif 'exist not split route' in delivery_list_info[1]:
                    break  # 遇到这个错误时退出循环
                elif 'exist same routeId under deliveryRegion' in delivery_list_info[1]:
                    delivery_plan_id = self.tms_db.get_delivery_plan_id_by_draft(draft_id)
                    if tms.tms_db.get_dispatch_id(delivery_plan_id=delivery_plan_id):
                        break
                    else:
                        raise Exception(f'create_dispatch error, response:{create_res}')
                else:
                    raise Exception(f'create_dispatch error, response:{create_res}')
            elif delivery_list_info[0] == '10000':
                self.util.wait(3)  # 等待路线运费数据计算
                delivery_plan_id = self.tms_db.get_delivery_plan_id_by_draft(draft_id)
                if tms.tms_db.get_dispatch_id(delivery_plan_id=delivery_plan_id):
                    break  # 成功创建，退出循环
                else:
                    i += 1
            else:
                raise Exception(f'create_dispatch error, response:{create_res}')

    def hot_delivery_dispatch(self, dispatch_id):
        """
        配送热送派车单
        :param dispatch_id:
        :return:
        """
        # 获取餐馆任务相关信息
        restaurant_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                             task_type="restaurant")
        package_types = self.tms_db.get_package_types(dispatch_id)
        # 循环执行餐馆任务
        for seq in range(len(restaurant_info)):
            # Travel操作
            self.driver.driver_delivery.update_task(task_id=restaurant_info[seq][0], task_type='restaurant',
                                                    task_status=20,
                                                    task_step='travel', latitude=restaurant_info[seq][1],
                                                    longitude=restaurant_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.driver.driver_delivery.update_task(task_id=restaurant_info[seq][0], task_type='restaurant',
                                                    task_status=20,
                                                    task_step='arrived', latitude=restaurant_info[seq][1],
                                                    longitude=restaurant_info[seq][2])
            # 餐馆扫码
            for package_type in package_types:
                package_list = self.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
                self.driver.driver_delivery.load_package(task_id=restaurant_info[seq][0], scan_type=1,
                                                         storage_type=package_type[0],
                                                         latitude=restaurant_info[seq][1],
                                                         longitude=restaurant_info[seq][2],
                                                         is_fast_load_skip=True, tracking_num_list=package_list)
            # 完成餐馆任务
            self.driver.driver_delivery.update_task(task_id=restaurant_info[seq][0], task_type='restaurant',
                                                    task_status=30,
                                                    task_step='finish', latitude=restaurant_info[seq][1],
                                                    longitude=restaurant_info[seq][2])
        # 站点操作
        packages = self.get_dispatch_packages(dispatch_id)
        stop_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                       task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # Travel操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='travel', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='arrived', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                self.driver.driver_delivery.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                                  package_status='received',
                                                                  latitude=stop_info[seq][1],
                                                                  longitude=stop_info[seq][2])
            # 更新task任务
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                                    task_step='finish', latitude=stop_info[seq][1],
                                                    cancel_alcohol=False,
                                                    longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                                    delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')
        # 完成派车单
        self.driver.driver_delivery.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30)

    def delivery_dispatch(self, dispatch_id):
        """
        配送普通单类型
        :param dispatch_id:
        :return:
        """
        # 获取仓库任务相关信息
        inventory_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                            task_type="inventory")
        package_types = self.tms_db.get_package_types(dispatch_id)
        # Travel操作
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                                task_step='travel', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])
        # 等待1秒
        self.util.wait(1)
        # Arrived操作
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                                task_step='arrived', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])
        # 仓库扫码
        for package_type in package_types:
            package_list = self.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
            self.driver.driver_delivery.load_package(task_id=inventory_info[0][0], scan_type=1,
                                                     storage_type=package_type[0], latitude=inventory_info[0][1],
                                                     longitude=inventory_info[0][2], is_fast_load_skip=True,
                                                     tracking_num_list=package_list)
        # 完成仓库任务
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=30,
                                                task_step='finish', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])
        # 站点操作
        packages = self.get_dispatch_packages(dispatch_id)
        stop_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                       task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # 站点Travel操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='travel', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='arrived', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                self.driver.driver_delivery.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                                  package_status='received', latitude=stop_info[seq][1],
                                                                  longitude=stop_info[seq][2])
            # 更新task任务
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                                    task_step='finish', latitude=stop_info[seq][1],
                                                    cancel_alcohol=False,
                                                    longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                                    delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')
        # 完成派车单
        self.driver.driver_delivery.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30)

    def reset_deployed_draft(self, delivery_date: str, sub_region_ids: list):
        """
        将已应用的draft重置为未应用
        :param delivery_date:
        :param sub_region_ids:
        :return:
        """
        draft_list_res = self.central.route_plan.get_draft_list(delivery_date=delivery_date,
                                                                sub_region_ids=sub_region_ids,
                                                                status=40)
        draft_list_info = self.util.get_response_values(draft_list_res, 'data')[0]
        if draft_list_info:
            for draft_info in draft_list_info:
                draft_id = draft_info['draft_id']
                self.central.route_plan.delete_dispatch(draft_id=draft_id)
        else:
            self.util.print_log(f'sub_region_ids:{sub_region_ids}在日期:{delivery_date}无已应用的draft')

    def finish_inventory_task(self, dispatch_id):
        """
        完成仓库任务(IC配送流程)
        :param dispatch_id:
        :return:
        """
        # 获取仓库任务相关信息
        inventory_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                            task_type="inventory")
        package_types = self.tms_db.get_package_types(dispatch_id)
        # Travel操作
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                                task_step='travel', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])
        # 等待1秒
        self.util.wait(1)
        # Arrived操作
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                                task_step='arrived', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])
        # 仓库扫码
        for package_type in package_types:
            package_list = self.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
            self.driver.driver_delivery.load_package(task_id=inventory_info[0][0], scan_type=1,
                                                     storage_type=package_type[0], latitude=inventory_info[0][1],
                                                     longitude=inventory_info[0][2], is_fast_load_skip=True,
                                                     tracking_num_list=package_list)
        # 完成仓库任务
        self.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=30,
                                                task_step='finish', latitude=inventory_info[0][1],
                                                longitude=inventory_info[0][2])

    def finish_stop_task(self, dispatch_id):
        """
        完成站点任务(IC配送流程)
        :param dispatch_id:
        :return:
        """
        # 站点操作
        packages = self.get_dispatch_packages(dispatch_id)
        stop_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                       task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # 站点Travel操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='travel', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                    task_step='arrived', latitude=stop_info[seq][1],
                                                    longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                self.driver.driver_delivery.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                                  package_status='received', latitude=stop_info[seq][1],
                                                                  longitude=stop_info[seq][2])
            # 更新task任务
            self.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                                    task_step='finish', latitude=stop_info[seq][1],
                                                    cancel_alcohol=False,
                                                    longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                                    delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')


tms = TMS()
