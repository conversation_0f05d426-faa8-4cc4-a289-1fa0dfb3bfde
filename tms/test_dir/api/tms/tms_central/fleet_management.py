# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  fleet_management.py
@Description    :  
@CreateTime     :  2024/10/11 10:17
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/10/11 10:17
"""
import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath
from tms.test_dir.api.tms import central_header
from tms.test_dir.api_case.tms import utils


class FleetManage(weeeTest.TestCase):
    """
    TMS Central平台Fleet Management菜单相关接口
    """

    def get_vehicle_type(self, check_result=False):
        """
        获取车辆品牌信息
        :param check_result:
        :return:
        """
        self.get(url='/tms/vehicleType/typeMakeAndModel', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆品牌信息成功。')
        elif check_result:
            raise Exception(f'获取车辆品牌信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆品牌信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_inspection_list(self, vehicle_id, check_result=False):
        """
        获取车辆DVIR信息列表
        :param vehicle_id: 车辆ID
        :param check_result:
        :return:
        """
        body = {
            "pageNumber": 1,
            "pageSize": 10,
            "vehicleId": vehicle_id
        }
        self.post(url='/tms/vehicle/report/queryVehicleInspectionList', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆DVIR信息列表成功。')
        elif check_result:
            raise Exception(f'获取车辆DVIR信息列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆DVIR信息列表失败, Error response:{self.response}')
        return self.response

    def get_vehicle_operation_history(self, vehicle_id, check_result=False):
        """
        获取车辆使用记录信息
        :param vehicle_id: 车辆ID
        :param check_result:
        :return:
        """
        body = {
            "pageNumber": 1,
            "pageSize": 10,
            "vehicleId": vehicle_id
        }
        self.post(url='/tms/vehicle/getVehicleOpHis', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆使用记录信息成功。')
        elif check_result:
            raise Exception(f'获取车辆使用记录信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆使用记录信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_type_sorted_select(self, check_result=False):
        """
        按时间顺序获取车辆品牌信息
        :param check_result:
        :return:
        """
        self.get(url='/tms/vehicleType/getSortedSelect', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'按时间顺序获取车辆品牌信息成功。')
        elif check_result:
            raise Exception(f'按时间顺序获取车辆品牌信息失败, Error response:{self.response}')
        else:
            log.warning(f'按时间顺序获取车辆品牌信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_info(self, vehicle_id, check_result=False):
        """
        获取单个车辆信息
        :param vehicle_id: 车辆ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/vehicle/getOne/{vehicle_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取单个车辆信息成功。')
        elif check_result:
            raise Exception(f'获取单个车辆信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取单个车辆信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_delivery_station(self, check_result=False):
        """
        获取配送仓库信息
        :param check_result:
        :return:
        """
        body = [1, 2, 3, 4, 5, 6, 7]
        self.post(url='/tms/vehicle/deliveryStations', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取配送仓库信息成功。')
        elif check_result:
            raise Exception(f'获取配送仓库信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取配送仓库信息失败, Error response:{self.response}')
        return self.response

    def update_vehicle_info(self, vehicle_id, delivery_station_id, current_mileage, acquisition, license_plate,
                            vehicle_number, vehicle_type_id, vehicle_vin, vehicle_status, start_date, end_date,
                            registration_end_date, last_trans_fluid_change_date, last_tire_replacement_date,
                            last_oil_change_date, last_brake_replacement_date, last_alignment_date, check_result=False):
        """
        更新车辆信息
        :param vehicle_id: 车辆ID
        :param delivery_station_id: 出发地ID
        :param current_mileage: 当前里程
        :param acquisition: 车辆来源
        :param license_plate: 车辆牌照
        :param vehicle_number: 车辆编号
        :param vehicle_type_id: 车辆类型ID
        :param vehicle_vin: 车辆vin号
        :param vehicle_status: 车辆状态
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param registration_end_date: 注册结束日期
        :param last_trans_fluid_change_date: 最近一次换气日期
        :param last_tire_replacement_date: 最后一次更换轮胎日期
        :param last_oil_change_date: 最后一次换油日期
        :param last_brake_replacement_date: 最后一次更换刹车日期
        :param last_alignment_date 最后一次alignment日期
        :param check_result:
        :return:
        """
        body = {
            "id": f"{vehicle_id}",
            "deliveryStationId": delivery_station_id,
            "currentMileage": float(current_mileage),
            "acquisition": acquisition,
            "licensePlate": f"{license_plate}",
            "vehicleNumber": f"{vehicle_number}",
            "vehicleTypeId": vehicle_type_id,
            "vehicleVin": f"{vehicle_vin}",
            "vehicleStatus": vehicle_status,
            "startDate": f"{start_date}",
            "endDate": "" if end_date is None else f"{end_date}",
            "registrationEndDate": "" if registration_end_date is None else f"{registration_end_date}",
            "lastTransFluidChangeDate": "" if last_trans_fluid_change_date is None else f"{last_trans_fluid_change_date}",
            "lastTireReplacementDate": "" if last_tire_replacement_date is None else f"{last_tire_replacement_date}",
            "lastOilChangeDate": "" if last_oil_change_date is None else f"{last_oil_change_date}",
            "lastBrakeReplacementDate": "" if last_brake_replacement_date is None else f"{last_brake_replacement_date}",
            "lastAlignmentDate": "" if last_brake_replacement_date is None else f"{last_brake_replacement_date}"
        }
        print(f"body:{body}")
        self.post(url='/tms/vehicle/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新车辆信息成功。')
        elif check_result:
            raise Exception(f'更新车辆信息失败, Error response:{self.response}')
        else:
            log.warning(f'更新车辆信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_list(self, region_id: int = None, delivery_station: int = None, vehicle_status: int = None,
                         vehicle_type: int = None, vehicle_number: str = None, license_plate: str = None,
                         vehicle_vin: str = None, check_result=False):
        """
        获取车辆列表信息
        :param region_id:  区域ID, 1,2,3,4,5,6,7
        :param delivery_station:  配送仓库/散货点名, 1,2,3
        :param vehicle_status:  车辆状况
        :param vehicle_type:  车辆品牌
        :param vehicle_number: 车辆编号
        :param license_plate:  车牌号
        :param vehicle_vin:  车辆识别号码
        :param check_result:
        :return:
        """
        body = {
            "regionId": region_id,
            "deliveryStation": delivery_station,
            "vehicleStatus": vehicle_status,
            "vehicleType": vehicle_type,
            "vehicleNumber": vehicle_number,
            "licensePlate": license_plate,
            "vehicleVin": vehicle_vin,
            "pageNumber": 1,
            "pageSize": 20,
        }
        self.post(url='/tms/vehicle/page', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆列表信息成功。')
        elif check_result:
            raise Exception(f'获取车辆列表信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆列表信息失败, Error response:{self.response}')
        return self.response

    def add_vehicle(self, delivery_station_id, current_mileage, acquisition, license_plate, vehicle_number,
                    vehicle_type_id, vehicle_vin, end_date=None, check_result=False):
        """
        添加车辆
        :param delivery_station_id:
        :param current_mileage: 当前里程
        :param acquisition: 车辆来源
        :param license_plate: 车辆牌照
        :param vehicle_number: 车辆编号
        :param vehicle_type_id: 车辆类型
        :param vehicle_vin: Vin号
        :param end_date: 结束日期
        :param check_result:
        :return:
        """
        body = {
            "deliveryStationId": delivery_station_id,
            "currentMileage": float(current_mileage),
            "acquisition": acquisition,
            "licensePlate": f"{license_plate}",
            "vehicleNumber": f"{vehicle_number}",
            "vehicleTypeId": vehicle_type_id,
            "vehicleVin": f"{vehicle_vin}",
            "startDate": utils.current_date(),
            "endDate": "" if end_date is None else f"{end_date}",
            "registrationEndDate": "2099-12-01"
        }
        self.post(url='/tms/vehicle/save', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加车辆库信息成功。')
        elif check_result:
            raise Exception(f'添加车辆信息失败, Error response:{self.response}')
        else:
            log.warning(f'添加车辆信息失败, Error response:{self.response}')
        return self.response

    def add_vehicle_type(self, vehicle_year, vehicle_make, vehicle_model, vehicle_capacity, payload, mpg_city=None,
                         mpg_highway=None, check_result=False):
        """
         添加车辆类型信息
        :param vehicle_year:
        :param vehicle_make:
        :param vehicle_model:
        :param vehicle_capacity:
        :param payload:
        :param mpg_city:
        :param mpg_highway:
        :param check_result:
        :return:
        """
        body = {
            "vehicleYear": vehicle_year,
            "vehicleMake": vehicle_make,
            "vehicleModel": vehicle_model,
            "vehicleCapacity": float(vehicle_capacity),
            "payload": float(payload),
            "mpgCity": mpg_city,
            "mpgHighway": mpg_highway
        }
        self.post(url='/tms/vehicleType/save', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加车辆类型信息成功。')
        elif check_result:
            raise Exception(f'添加车辆类型信息失败, Error response:{self.response}')
        else:
            log.warning(f'添加车辆类型信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_select_data(self, check_result=False):
        """
        获取车辆筛选信息
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/vehicleType/getSelectData', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆筛选信息成功。')
        elif check_result:
            raise Exception(f'获取车辆筛选信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆筛选信息失败, Error response:{self.response}')
        return self.response

    def get_vehicle_type_list(self, vehicle_make=None, vehicle_model=None, vehicle_year=None, check_result=False):
        """
        获取车辆类型信息
        :param vehicle_make: 车辆厂商
        :param vehicle_model: 车辆类型
        :param vehicle_year: 车辆年限
        :param check_result:
        :return:
        """
        body = {
            "pageNumber": 1,
            "pageSize": 20,
            "vehicleMake": vehicle_make,
            "vehicleModel": vehicle_model,
            "vehicleYear": vehicle_year
        }
        self.post(url='/tms/vehicleType/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取车辆类型信息成功。')
        elif check_result:
            raise Exception(f'获取车辆类型信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取车辆类型信息失败, Error response:{self.response}')
        return self.response

    def del_vehicle_type(self, vehicle_id: int, check_result=False):
        """
        删除车辆类型
        :param vehicle_id:
        :param check_result:
        :return:
        """
        self.delete(url=f'/tms/vehicleType/delete/{vehicle_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'删除车辆类型信息成功。')
        elif check_result:
            raise Exception(f'删除车辆类型信息失败, Error response:{self.response}')
        else:
            log.warning(f'删除车辆类型信息失败, Error response:{self.response}')
        return self.response
