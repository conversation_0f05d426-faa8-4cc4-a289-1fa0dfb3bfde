# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  carrier_management.py
@Description    :  
@CreateTime     :  2025/5/28 16:12
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/28 16:12
"""
import weeeTest
from weeeTest import jmespath, log

from tms.test_dir.api.tms import central_header


class Carrier(weeeTest.TestCase):
    """
    TMS系统Carrier Management菜单下相关接口封装类
    """

    def get_carrier_list(self, check_result=False):
        """
        获取物流列表
        :return:
        """
        self.get(url='/tms/zipcode/carrier/getDirectConnectConfig', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取物流列表成功。')
        elif check_result:
            raise Exception(f'获取物流列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取物流列表失败, Error response:{self.response}')
        return self.response

    def get_carrier_zipcode(self, carrier_name=None, zipcode=None, pos=None, order_type=None, check_result=False):
        """
        获取zipcode的物流配置信息
        :param carrier_name:
        :param zipcode:
        :param pos:
        :param order_type:
        :param check_result:
        :return:
        """
        body = {
            "zipcode": zipcode,
            "carrier": carrier_name,
            "type": order_type,
            "pos": pos,
            "pageNumber": 1,
            "pageSize": 10
        }
        self.post(url='/tms/zipcode/carrier/queryByParam', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取zipcode的物流配置信息成功。')
        elif check_result:
            raise Exception(f'获取zipcode的物流配置信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取zipcode的物流配置信息失败, Error response:{self.response}')
        return self.response

    def update_carrier_zipcode(self, rec_id, carrier_name, zipcode, pos, order_type, zipcode_district=None,
                               check_result=False):
        """
        更新zipcode的物流配置信息
        :param rec_id:
        :param carrier_name:
        :param zipcode:
        :param pos:
        :param order_type:
        :param zipcode_district:
        :param check_result:
        :return:
        """
        body = {
            "recId": rec_id,
            "carrier": carrier_name,
            "zipcode": zipcode,
            "pos": pos,
            "type": order_type,
            "zipcodeDistrict": zipcode_district
        }
        self.put(url='/tms/zipcode/carrier/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新zipcode的物流配置信息成功。')
        elif check_result:
            raise Exception(f'更新zipcode的物流配置信息失败, Error response:{self.response}')
        else:
            log.warning(f'更新zipcode的物流配置信息失败, Error response:{self.response}')
        return self.response

    def add_carrier_zipcode(self, carrier_name, zipcode, pos, order_type, zipcode_district=None, check_result=False):
        """
        添加zipcode的物流配置信息
        :param carrier_name:
        :param zipcode:
        :param pos:
        :param order_type:
        :param zipcode_district:
        :param check_result:
        :return:
        """
        body = {
            "carrier": carrier_name,
            "zipcode": zipcode,
            "pos": pos,
            "type": order_type,
            "zipcodeDistrict": zipcode_district
        }
        self.post(url='/tms/zipcode/carrier/add', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加zipcode的物流配置信息成功。')
        elif check_result:
            raise Exception(f'添加zipcode的物流配置信息失败, Error response:{self.response}')
        else:
            log.warning(f'添加zipcode的物流配置信息失败, Error response:{self.response}')
        return self.response

    def delete_carrier_zipcode(self, rec_id, check_result=False):
        """
        删除zipcode的物流配置信息
        :param rec_id:
        :param check_result:
        :return:
        """
        self.delete(url=f'/tms/zipcode/carrier/delete/?rec_id={rec_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'删除zipcode的物流配置信息成功。')
        elif check_result:
            raise Exception(f'删除zipcode的物流配置信息失败, Error response:{self.response}')
        else:
            log.warning(f'删除zipcode的物流配置信息失败, Error response:{self.response}')
        return self.response
