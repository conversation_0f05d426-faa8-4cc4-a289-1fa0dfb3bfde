# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
:File           :  driver_manage.py
@Description    :
@CreateTime     :  2023/6/12 11:14
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/12 11:14
"""
import re
import time
from datetime import datetime, timedelta

import jsonpath
import weeeTest
from bs4 import BeautifulSoup
from weeeTest import log
from weeeTest.utils import jmespath

from tms.test_dir.api.tms import central_header


class DriverManage(weeeTest.TestCase):
    """
    TMS Central平台DriverManage菜单相关接口
    """

    def get_dispatch_info(self, sub_region_id: int, delivery_date: str, check_result=False):
        """
        获取派车单信息
        :param sub_region_id: 子区域ID
        :param delivery_date: 配送时间： 2022-12-10
        :param check_result:
        :return:
        """
        body = {
            "subRegionId": sub_region_id,
            "deliveryDate": delivery_date,
            "isDesc": 'false'
        }
        self.post(url='/tms/dispatch/queryDispatchInfos', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取派车单信息成功。')
        elif check_result:
            raise Exception(f'获取派车单信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车单信息失败, Error response:{self.response}')
        return self.response

    def get_invoice_info(self, delivery_type: str, delivery_date: str, route_ids: list, sub_region_id: int,
                         check_result=False):
        """
        获取invoice信息
        :param delivery_type: 配送类型：Normal
        :param delivery_date: 配送时间： 2022-12-10
        :param route_ids: 路线号：["101"]
        :param sub_region_id: 子区域ID
        :param check_result:
        :return:
        """
        body = {
            "deliveryDate": delivery_date,
            "deliveryType": delivery_type,
            "routeId": route_ids,
            "subRegionId": sub_region_id
        }
        self.post(url='/tms/delivery/getDeliveryInvoiceInfo', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取invoice信息成功。')
        elif check_result:
            raise Exception(f'获取invoice信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取invoice信息失败, Error response:{self.response}')
        return self.response

    def get_offer_list(self, sub_region_id: int, dispatch_id: str = None, group_point_id: str = None,
                       delivery_date: str = None, dispatch_type: str = None, status: str = None, check_result=False):
        """
        获取派车任务列表
        :param dispatch_id: 派车单ID 477331
        :param group_point_id: 路线ID
        :param delivery_date: 配送时间
        :param sub_region_id: 子区域ID
        :param dispatch_type: 派车单类型：Normal
        :param status: 派车单状态：planed
        :param check_result:
        :return:
        """
        body = {
            "dispatch_id": dispatch_id,
            "group_point_id": group_point_id,
            "delivery_date": delivery_date,
            "subRegionId": sub_region_id,
            "type": dispatch_type,
            "status": status,
            "pageSize": 20,
            "pageNumber": 1
        }
        self.post(url='/tms/driver/offer/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取派车任务列表成功。')
        elif check_result:
            raise Exception(f'获取派车任务列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车任务列表失败, Error response:{self.response}')
        return self.response

    def generate_track_file(self, check_result=False):
        """
        生成轨迹文件
        :param check_result:
        :return:
        """
        self.get(url='/tms/job/checkDispatchGPSTrackFile', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'生成轨迹文件成功。')
        elif check_result:
            raise Exception(f'生成轨迹文件失败, Error response:{self.response}')
        else:
            log.warning(f'生成轨迹文件失败, Error response:{self.response}')
        return self.response

    def edit_driver_info(self, sub_region_id, driver_user_id, driver_name, email, driver_type='P', status='A',
                         login_phone='5229067655', check_result=False):
        """
        编辑司机信息
        :param sub_region_id: 子区域ID
        :param driver_user_id: 司机ID
        :param driver_name: 司机名称
        :param email: 司机邮箱
        :param driver_type: 司机类型
        :param login_phone: 登录手机号
        :param status: 账户状态
        :param check_result:
        :return:
        """
        if driver_type == 'F':
            body = {
                "subRegionId": f"{sub_region_id}",
                "driver_user_id": driver_user_id,
                "driver_name": driver_name,
                "legal_name": driver_name,
                "alias": driver_name,
                "email": email,
                "firstName": driver_name,
                "lastName": driver_name,
                "login_phone": login_phone,
                "status": status,
                "phone_number": "**********",
                "type": driver_type,
                "latestPhoneNumber": "**********",
                "vehicle_type": "SUV",
                "score": None,
                "position": "37.4763416,-121.9397266",
                "start_day": "",
                "adp_id": "1234",
                "considerPosition": 0,
                "driver_level": "normal",
                "driver_stop_count": None,
                "delivery_latitude": 37.4763416,
                "delivery_longitude": -121.9397266,
                "expired_date": "",
                "license_expire_date": "",
                "rec_creator_id": "7642085",
                "driver_attachment_list": [],
                "considerRouteTime": 0,
                "driverTagVOList": [
                    {
                        "tagId": 3,
                        "tagName": "ghk",
                        "tagDescription": "测试3",
                        "isBelong": False
                    }
                ]
            }

        else:
            body = {
                "subRegionId": f"{sub_region_id}",
                "driver_user_id": driver_user_id,
                "driver_name": driver_name,
                "legal_name": driver_name,
                "alias": driver_name,
                "email": email,
                "firstName": driver_name,
                "lastName": driver_name,
                "login_phone": login_phone,
                "status": status,
                "phone_number": "**********",
                "type": driver_type,
                "latestPhoneNumber": "**********",
                "vehicle_type": "SUV",
                "score": None,
                "position": "37.4763416,-121.9397266",
                "start_day": "",
                "driver_stop_count": None,
                "delivery_latitude": 37.4763416,
                "delivery_longitude": -121.9397266,
                "expired_date": "",
                "license_expire_date": "",
                "rec_creator_id": "7642085",
                "driver_attachment_list": [],
                "considerPosition": 0,
                "considerRouteTime": 0,
                "driverTagVOList": [
                    {
                        "tagId": 1,
                        "tagName": "abc",
                        "tagDescription": "测试",
                        "isBelong": False
                    },
                    {
                        "tagId": 2,
                        "tagName": "def",
                        "tagDescription": "测试2",
                        "isBelong": False
                    }
                ]
            }

        self.put(url='/tms/driver/manage/updateDriver', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'编辑司机信息成功。')
        elif check_result:
            raise Exception(f'编辑司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'编辑司机信息失败, Error response:{self.response}')
        return self.response

    def update_driver_info(self, sub_region_id=3, driver_user_id=2289173, driver_name='FS5-FS-5', driver_type='P',
                           status='A', check_result=False):
        """
        更新司机信息
        :param sub_region_id: 子区域ID
        :param driver_user_id: 司机ID
        :param driver_name: 司机名称
        :param driver_type: 司机类型
        :param status: 账户状态
        :param check_result:
        :return:
        """
        body = {
            "subRegionId": sub_region_id,
            "driver_user_id": driver_user_id,
            "driver_name": driver_name,
            "legal_name": 'null',
            "alias": 'null',
            "type": driver_type,
            "adp_id": 'null',
            "phone_number": 'null',
            "start_day": 'null',
            "vehicle_type": 'null',
            "company_id": 2,
            "company_name": 'null',
            "score": 100,
            "expired_date": 'null',
            "license_number": 'null',
            "license_expire_date": 'null',
            "license_issuing_state": 'null',
            "status": status
        }
        self.put(url='/tms/driver/manage/updateDriverStatus', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新司机信息成功。')
        elif check_result:
            raise Exception(f'更新司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'更新司机信息失败, Error response:{self.response}')
        return self.response

    def add_driver(self, sub_region_id=1, driver_user_id=10928171, driver_name='t114', driver_type='P',
                   status='A', check_result=False):
        """
        添加司机
        :param sub_region_id: 子区域ID
        :param driver_user_id: 司机ID
        :param driver_name: 司机名称
        :param driver_type: 司机类型
        :param status: 账户状态
        :param check_result:
        :return:
        """
        body = {
            "subRegionId": sub_region_id,
            "driver_user_id": driver_user_id,
            "driver_name": driver_name,
            "legal_name": driver_name,
            "alias": driver_name,
            "status": status,
            "phone_number": "**********",
            "type": driver_type,
            "license_expire_date": "2030-04-28",
            "vehicle_type": "SUV",
            "position": "37.47698865514064,-121.93916868266447",
            "start_day": "",
            "expired_date": "2023-05-15",
            "adp_id": "45665",
            "considerPosition": 0,
            "driver_level": "normal",
            "delivery_latitude": 37.47699,
            "delivery_longitude": -121.93917,
            "rec_creator_id": "7642085",
            "driver_attachment_list": [],
            "considerRouteTime": 0
        }
        self.put(url='/tms/driver/manage/addDriver', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加司机成功。')
        elif check_result:
            raise Exception(f'添加司机失败, Error response:{self.response}')
        else:
            log.warning(f'添加司机失败, Error response:{self.response}')
        return self.response

    def get_pending_driver_list(self, sub_region_id, check_result=False):
        """
        获取待审核司机列表
        :param sub_region_id: 子区域ID
        :param check_result:
        :return:
        """
        body = {
            "subRegionId": sub_region_id,
            "pageSize": 10,
            "pageNumber": 1
        }
        self.post(url='/tms/approval/pending/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取待审核司机列表成功。')
        elif check_result:
            raise Exception(f'获取待审核司机列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取待审核司机列表失败, Error response:{self.response}')
        return self.response

    def approve_driver(self, driver_user_id: str, apply_status: str, check_result=False):
        """
        审核司机
        :param driver_user_id: 司机ID
        :param apply_status: 审核状态，P:审核通过、R:审核拒绝
        :param check_result:
        :return:
        """
        body = {
            "driver_user_id": driver_user_id,
            "apply_status": apply_status,
            "apply_note": "test"
        }
        self.post(url='/tms/approval/approve', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'审核司机成功。')
        elif check_result:
            raise Exception(f'审核司机失败, Error response:{self.response}')
        else:
            log.warning(f'审核司机失败, Error response:{self.response}')
        return self.response

    def get_pending_driver_info(self, driver_user_id, check_result=False):
        """
        获取待审核司机详情
        :param driver_user_id: 司机ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/approval/pending/detail?driver_user_id={driver_user_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取待审核司机详情成功。')
        elif check_result:
            raise Exception(f'获取待审核司机详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取待审核司机详情失败, Error response:{self.response}')
        return self.response

    def search_driver_by_id(self, driver_user_id, check_result=False):
        """
        通过司机ID搜索司机信息
        :param driver_user_id: 司机ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/driver/manage/queryDriversByDriverUserId/{driver_user_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'搜索司机信息成功。')
        elif check_result:
            raise Exception(f'搜索司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'搜索司机信息失败, Error response:{self.response}')
        return self.response

    def get_driver_list(self, sub_region_id_list, status="A", driver_type="F", check_result=False):
        """
        获取司机列表
        :param status: 司机账户状态：A、P
        :param sub_region_id_list: region列表：[1, 2, 3, 4, 38]
        :param driver_type: 司机类型 P、F
        :param check_result:
        :return:
        """
        body = {
            "status": status,
            "pageSize": 10,
            "pageNumber": 1,
            "type": driver_type,
            "subRegionIdList": sub_region_id_list
        }
        self.post(url='/tms/driver/manage/driversList', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机列表成功。')
        elif check_result:
            raise Exception(f'获取司机列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机列表失败, Error response:{self.response}')
        return self.response

    def get_travel_distance(self, sub_region_ids: list, dispatch_id=None, start_date=None, end_date=None,
                            driver_type=None, check_result=False):
        """
        获取行程距离
        :param dispatch_id:
        :param sub_region_ids:
        :param start_date:
        :param end_date:
        :param driver_type:
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "subRegionIdList": sub_region_ids,
            "deliveryStartDate": start_date,
            "deliveryEndDate": end_date,
            "driverType": driver_type,
            "pageNumber": 1,
            "pageSize": 10
        }
        self.post(url='/tms/dispatch/mileageRecord/queryByParam', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取行程距离成功。')
        elif check_result:
            raise Exception(f'获取行程距离失败, Error response:{self.response}')
        else:
            log.warning(f'获取行程距离失败, Error response:{self.response}')
        return self.response

    def get_signup_list(self, driver_user_id=None, sub_region_id=None, check_result=False):
        """
        获取signup_list
        :param driver_user_id: 司机ID
        :param sub_region_id: sub_region ID
        :param check_result:
        :return:
        """
        body = {
            "pageNumber": 1,
            "pageSize": 10,
            "subRegionId": sub_region_id,
            "delivery_date": time.strftime("%Y-%m-%d", time.localtime()),
            "driver_user_id": driver_user_id
        }
        self.post(url='/tms/driver/sign-up/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取signup_list成功。')
        elif check_result:
            raise Exception(f'获取signup_list失败, Error response:{self.response}')
        else:
            log.warning(f'获取signup_list失败, Error response:{self.response}')
        return self.response

    def update_signup(self, signup_id: int, status: str = None, assigned_address_total: int = 35,
                      group_point_id: int = None, driver_level: str = None, check_result=False):
        """
        审核司机signup
        :param signup_id: signup ID
        :param status: 审核状态,P 通过， A 拒绝
        :param assigned_address_total: 站点数量
        :param group_point_id: 路线号
        :param driver_level: 路线号
        :param check_result:
        :return:
        """
        body = {
            "id": signup_id,
            "type": f"{status}",
            "assigned_address_total": f"{assigned_address_total}",
            "assigned_delivery_area": "No preference",
            "group_point_id": group_point_id,
            "driver_level": driver_level
        }
        self.post(url='/tms/driver/sign-up/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'审核司机signup成功。')
        elif check_result:
            raise Exception(f'审核司机signup失败, Error response:{self.response}')
        else:
            log.warning(f'审核司机signup失败, Error response:{self.response}')
        return self.response

    def generate_schedule(self, check_result=False):
        """
        生成w2 schedule数据
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/job/generateSchedule?manualRequest=1', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'生成w2 schedule数据成功。')
        elif check_result:
            raise Exception(f'生成w2 schedule数据失败, Error response:{self.response}')
        else:
            log.warning(f'生成w2 schedule数据失败, Error response:{self.response}')
        return self.response

    def edit_schedule_config(self, driver_user_id: int, days: list, check_result=False):
        """
        编辑schedule配置
        :param driver_user_id: 司机ID
        :param days: 一周的日期 [1,2,3]
        :param check_result:
        :return:
        """
        body = {
            "driverUserId": driver_user_id,
            "days": days
        }
        self.post(url='/tms/schedule/editScheduleConfig', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'编辑schedule配置成功。')
        elif check_result:
            raise Exception(f'编辑schedule配置失败, Error response:{self.response}')
        else:
            log.warning(f'编辑schedule配置失败, Error response:{self.response}')
        return self.response

    def get_schedule_config(self, region_id: int, sub_region_ids: list, check_result=False):
        """
        获取schedule配置
        :param region_id: 1
        :param sub_region_ids: 子区域id [1, 2, 3, 4, 38]
        :param check_result:
        :return:
        """
        body = {
            "regionId": region_id,
            "subRegionIds": sub_region_ids,
            "pageSize": 10,
            "pageNumber": 1
        }
        self.post(url='/tms/schedule/queryScheduleConfig', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取schedule配置成功。')
        elif check_result:
            raise Exception(f'获取schedule配置失败, Error response:{self.response}')
        else:
            log.warning(f'获取schedule配置失败, Error response:{self.response}')
        return self.response

    def save_driver_schedule(self, delivery_date: str, scheduled_drivers: list, not_scheduled_drivers: list,
                             check_result=False):
        """
        调整某一天的司机schedule信息
        :param delivery_date: 2023-06-06
        :param scheduled_drivers: 配置schedule的司机 []
        :param not_scheduled_drivers: 未配置schedule的司机 []
        :param check_result:
        :return:
        """
        body = {
            "deliveryDate": delivery_date,
            "scheduledDrivers": scheduled_drivers,
            "notScheduledDrivers": not_scheduled_drivers
        }
        self.post(url='/tms/schedule/saveDriverListByDay', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'调整某一天的司机schedule信息成功。')
        elif check_result:
            raise Exception(f'调整某一天的司机schedule信息失败, Error response:{self.response}')
        else:
            log.warning(f'调整某一天的司机schedule信息失败, Error response:{self.response}')
        return self.response

    def get_driver_schedule_list(self, delivery_date: str, sub_region_ids: list, check_result=False):
        """
        获取某一天的司机schedule信息
        :param delivery_date: 2023-06-06
        :param sub_region_ids: 子区域ID列表
        :param check_result:
        :return:
        """
        body = {
            "subRegionIds": sub_region_ids,
            "deliveryDate": delivery_date
        }
        self.post(url='/tms/schedule/queryDriverListByDay', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取某一天的司机schedule信息成功。')
        elif check_result:
            raise Exception(f'获取某一天的司机schedule信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取某一天的司机schedule信息失败, Error response:{self.response}')
        return self.response

    def get_schedule_list(self, region_id: int, start_date: str, end_date: str, sub_region_ids: list,
                          check_result=False):
        """
        获取某一周的司机schedule信息
        :param region_id: region ID
        :param start_date: 一周起始日
        :param end_date: 一周终止日
        :param sub_region_ids: 子区域ID列表 []
        :param check_result:
        :return:
        """
        body = {
            "regionId": region_id,
            "startDate": start_date,
            "endDate": end_date,
            "subRegionIds": sub_region_ids
        }
        self.post(url='/tms/schedule/queryScheduleList', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取某一周的司机schedule信息成功。')
        elif check_result:
            raise Exception(f'获取某一周的司机schedule信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取某一周的司机schedule信息失败, Error response:{self.response}')
        return self.response

    def save_w2_mod_info(self, w2_mod_info, check_result=False):
        """
        保存W2司机signup mod信息
        :param w2_mod_info: json obj
        :param check_result:
        :return:
        """
        body = []
        for values in w2_mod_info:
            driver_user_ids = []
            for driver_info in values['mod_list']:
                driver_user_ids.append(driver_info['driver_user_id'])
            region_info = {
                "delivery_date": values['delivery_date'],
                "region_name": values['region_name'],
                "region_id": values['region_id'],
                "driver_user_ids": driver_user_ids
            }
            body.append(region_info)

        self.post(url='/tms/driverFullMod/saveDriverInformation', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'保存W2司机signup mod信息成功。')
        elif check_result:
            raise Exception(f'保存W2司机signup mod信息失败, Error response:{self.response}')
        else:
            log.warning(f'保存W2司机signup mod信息失败, Error response:{self.response}')
        return self.response

    def get_w2_mod_info(self, delivery_date: str, delivery_region_id: int, check_result=False):
        """
        获取W2 mod信息(以湾区为例)
        :param delivery_date: 配送日期 2022-12-22
        :param delivery_region_id: 区域ID:1
        :param check_result:
        :return:
        """
        body = {
            "deliveryRegionId": delivery_region_id,
            "delivery_date": delivery_date
        }
        self.post(url='/tms/driverFullMod/getDriverInformation', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取W2 mod信息成功。')
        elif check_result:
            raise Exception(f'获取W2 mod信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取W2 mod信息失败, Error response:{self.response}')
        return self.response

    def get_multi_device_list(self, driver_user_id=None, check_result=False):
        """
        获取多设备登录列表
        :param driver_user_id:
        :param check_result:
        :return:
        """
        body = {
            "driverUserId": driver_user_id,
            "pageSize": 10,
            "pageNumber": 1
        }
        self.post(url='/tms/multi-device/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取多设备登录列表成功。')
        elif check_result:
            raise Exception(f'获取多设备登录列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取多设备登录列表失败, Error response:{self.response}')
        return self.response

    def delete_multi_device(self, driver_user_id, check_result=False):
        """
        删除账户多设备登录
        :param driver_user_id: 司机ID
        :param check_result:
        :return:
        """
        body = {
            "driverUserId": driver_user_id,
            "remark": ""
        }
        self.post(url='/tms/multi-device/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'删除账户多设备登录成功。')
        elif check_result:
            raise Exception(f'删除账户多设备登录失败, Error response:{self.response}')
        else:
            log.warning(f'删除账户多设备登录失败, Error response:{self.response}')
        return self.response

    def add_multi_device(self, driver_user_id, check_result=False):
        """
        添加账户多设备登录
        :param driver_user_id: 司机ID
        :param check_result:
        :return:
        """
        body = {
            "driverUserId": driver_user_id,
            "remark": ""
        }
        self.post(url='/tms/multi-device/save', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加账户多设备登录成功。')
        elif check_result:
            raise Exception(f'添加账户多设备登录失败, Error response:{self.response}')
        else:
            log.warning(f'添加账户多设备登录失败, Error response:{self.response}')
        return self.response

    def get_feedback_list(self, driver_user_id=None, name=None, status='N', check_result=False):
        """
        获取feedback列表
        :param status:
        :param name:
        :param driver_user_id:
        :param check_result:
        :return:
        """
        body = {
            "driver_user_id": driver_user_id,
            "name": name,
            "status": status,
            "pageSize": 10,
            "pageNumber": 1
        }
        self.post(url='/tms/driver/feedback/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取feedback列表成功。')
        elif check_result:
            raise Exception(f'获取feedback列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取feedback列表失败, Error response:{self.response}')
        return self.response

    def update_feedback(self, rec_id: int, driver_user_id: int, content: str, check_result=False):
        """
        审核司机反馈建议
        :param rec_id: 反馈列表ID
        :param driver_user_id: 司机ID
        :param content: 反馈文本
        :param check_result:
        :return:
        """
        body = {
            "rec_id": rec_id,
            "driver_user_id": driver_user_id,
            "status": "Y",
            "content": content,
            "lang": "en_US",
            "send_flag": "N",
            "reply_content": "test",
            "in_dtm": int(time.time())
        }
        self.put(url='/tms/driver/feedback/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'审核司机反馈建议成功。')
        elif check_result:
            raise Exception(f'审核司机反馈建议失败, Error response:{self.response}')
        else:
            log.warning(f'审核司机反馈建议失败, Error response:{self.response}')
        return self.response

    def get_task_list(self, dispatch_id, check_result=False):
        """
        获取派车单站点列表
        :param dispatch_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/driver/offer/detail/task_list?dispatch_id={dispatch_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取派车单站点列表成功。')
        elif check_result:
            raise Exception(f'获取派车单站点列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车单站点列表失败, Error response:{self.response}')
        return self.response

    def change_task_status(self, task_id, status, check_result=False):
        """
        修改站点状态
        :param task_id:
        :param status:30 完成 60 失败
        :param check_result:
        :return:
        """
        if status == 60:
            reason = 'Change Task Status Failure'
            body = {
                "task_id": task_id,
                "option": status,
                "reason": reason,
                "failureNote": "failureReason5"
            }
        else:
            if status == 30:
                reason = 'Change Task Status Finished'
            else:
                reason = f'Test change status is {status}'
            body = {
                "task_id": task_id,
                "option": status,
                "reason": reason
            }
        self.post(url=f'/tms/driver/offer/detail/task/skip', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'{reason}成功。')
        elif check_result:
            raise Exception(f'{reason}失败, Error response:{self.response}')
        else:
            log.warning(f'{reason}失败, Error response:{self.response}')
        return self.response

    def sync_tasks(self, dispatch_id=None, delivery_plan_id=None, check_result=False):
        """
        生成task任务
        :param dispatch_id: 派车单ID
        :param delivery_plan_id: 排车计划ID
        :param check_result:
        :return:
        """
        body = {
            "deliveryPlanId": delivery_plan_id,
            "dispatchId": dispatch_id,
            "invokeType": "sync"
        }

        self.post("/tms/dispatch/syncTasks", headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'调用生成task任务job。')
        elif check_result:
            raise Exception(f'调用生成task任务job失败, Error response:{self.response}')
        else:
            log.warning(f'调用生成task任务job失败, Error response:{self.response}')
        return self.response

    def update_drivers(self, dispatch_id, driver_id, driver_name, check_result=False):
        """
        派车单分配司机
        :param dispatch_id: 派车单ID
        :param driver_id: 司机ID
        :param driver_name: 司机名称
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "driverUserId": driver_id,
            "driverUserName": driver_name,
            "comment": "null"
        }

        self.post("/tms/deliveryPlanDispatch/updateDrivers", headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'派车单分配司机成功。')
        elif check_result:
            raise Exception(f'派车单分配司机失败,Response:{self.response}')
        else:
            log.warning(f'派车单分配司机失败,Response:{self.response}')
        return self.response

    def revert_task_status(self, task_id, task_refer_id, status=60, is_alcohol="N", check_result=False):
        """
        失败站点状态重置为未配送
        :param task_id:
        :param task_refer_id:
        :param status:
        :param is_alcohol:
        :param check_result:
        :return:
        """
        body = {
            "task_id": task_id,
            "task_refer_id": task_refer_id,
            "task_status": status,
            "is_alcohol": is_alcohol,
            "operator_id": "7642085"
        }
        self.post(url=f'/tms/driver/offer/detail/task/revertTask', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'失败站点状态重置为未配送成功。')
        elif check_result:
            raise Exception(f'失败站点状态重置为未配送失败, Error response:{self.response}')
        else:
            log.warning(f'失败站点状态重置为未配送失败, Error response:{self.response}')
        return self.response

    def edit_order_comment(self, delivery_plan_point_id, order_comment, check_result=False):
        """
        编辑订单备注
        :param delivery_plan_point_id:
        :param order_comment:
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_point_id": delivery_plan_point_id,
            "order_comment": order_comment
        }
        self.post(url=f'/tms/driver/offer/detail/task/editOrderComment', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'编辑订单备注成功。')
        elif check_result:
            raise Exception(f'编辑订单备注失败, Error response:{self.response}')
        else:
            log.warning(f'编辑订单备注失败, Error response:{self.response}')
        return self.response

    def update_login_phone(self, login_phone, check_result=False):
        """
                编辑订单备注
                :param login_phone:
                :param check_result:
                :return:
                """
        data = {"subRegionId": "46", "driver_user_id": 13348530, "driver_name": "la_auto 0124",
                "legal_name": "la_auto 0124", "alias": "", "email": "<EMAIL>", "firstName": "la_auto",
                "lastName": "0124", "login_phone": login_phone, "status": "A", "phone_number": "**********",
                "type": "F", "latestPhoneNumber": "", "vehicle_type": "SUV", "score": None,
                "position": "37.4703131,-121.9347913", "start_day": "", "adp_id": "20250425", "considerPosition": 0,
                "driver_level": "New_Driver_I", "delivery_latitude": 37.4703131, "delivery_longitude": -121.9347913,
                "expired_date": "", "license_expire_date": "", "rec_creator_id": "13347611",
                "driver_attachment_list": [], "considerRouteTime": 0,
                "driverTagVOList": [{"tagId": 3, "tagName": "ghk", "tagDescription": "测试3", "isBelong": False}],
                "firstPriorities": [], "secondPriorities": []}
        self.put(url='/tms/driver/manage/updateDriver', headers=central_header, json=data)
        result = jmespath(self.response, "result")
        if not result:
            log.info(f'修改电话失败校验成功')
        elif check_result:
            raise Exception(f'修改电话失败校验失败, Error response:{self.response}')
        else:
            log.warning(f'修改电话失败校验失败, Error response:{self.response}')
        return self.response

    def get_verify_code(self, to_search):
        """
        获取并解析验证码
        :param to_search:
        :return:
        """
        message_type = 4 if '@' in to_search else 1
        # Get message list
        if message_type == 4:
            data = {"current":1,"pageSize":20,"subject":"Verification Code","recipient":to_search,"pageNum":1}
            self.post(url="/central/common/email/list", headers=central_header, json=data)
            log.info(f"获取到的消息列表是:{self.response}")
            total = jmespath(self.response, "total")
        else:
            list_data = {"pageNum": 1,
                         "pageSize": 20,
                         # "dateRange":["2025-04-27T16:00:00.000Z","2025-04-27T16:00:00.000Z"],
                         "timezone": "Asia/Shanghai",
                         "all": to_search,
                         "message_type": [],
                         "from_time": int(datetime.combine(datetime.now().date(), datetime.min.time()).timestamp() * 1000),
                         "to_time": int(datetime.combine(datetime.now().date() + timedelta(days=1),
                                                         datetime.min.time()).timestamp() * 1000)}
            self.post(url="/central/common/message/list", headers=central_header, json=list_data)
            log.info(f"获取到的消息列表是:{self.response}")
            total = jmespath(self.response, "total")
        if total == 0:
            return None
        else:
            track_id = jsonpath.jsonpath(self.response, "$..track_id")[0]
            # Get message content
            msg_params = {"messageType": message_type, "trackId": track_id}
            if message_type == 4:
                self.get(url=F"/central/common/email/body/{track_id}", headers=central_header)
                log.info(f"message是:{self.response}")
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(self.response["object"], 'html.parser')

                # 查找class为'tip-code'的div元素，并获取其文本
                verification_code = soup.find('div', {'class': 'tip-code'}).text.strip()
            else:
                self.get(url="/central/common/message/content", headers=central_header, params=msg_params)
                log.info(f"message是:{self.response}")
                # 使用正则表达式提取数字
                verification_code = re.search(r'\d+', self.response["object"]).group()

            return verification_code
