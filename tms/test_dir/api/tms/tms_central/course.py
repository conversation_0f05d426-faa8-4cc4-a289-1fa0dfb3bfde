# Author: bo.liu
# Time: 2025/2/6 17:51
# E-Mail: <EMAIL>
import logging

import weeeTest
from weeeTest import jmespath,log
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api.tms import central_header


class Course(weeeTest.TestCase):
    def add_and_edit_course(self, data):
        """
        添加或者修改课程信息
        id 有值则是修改
        cycleType ：1 -4 99为首次初始化课程使用
        deliveryRegionId: 指定region
        """
        self.post(url='/tms/safetyTraining/course/save', headers=central_header, json=data)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info(f"创建/编辑课程成功，title: {data['title']}")
        else:
            log.warning(f"创建/编辑课程失败，title: {data['title']}")
        return self.response
    def get_course_list(self , title , pageSize=10, pageNum=1,deliveryRegionId = None):
        """
        获取课程列表
        deliveryRegionId 默认给空，查全部
        """
        body = {
            "courseTitle": title,
            "pageSize": pageSize,
            "pageNumber": pageNum,
            "deliveryRegionId": deliveryRegionId,
            "status": "A"
        }
        self.post(url='/tms/safetyTraining/course/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info(f"获取课程列表成功，title: {title}")
        else:
            log.warning(f"获取课程列表失败，title: {title}")
        return self.response
    def preview_course(self , id):
        '''
        预览课程
        '''
        self.get(url=f'/tms/safetyTraining/course/preview?courseId={id}', headers=central_header)
        result = jmespath(self.response, "object")
        if result is not None and result:
            log.info(f"预览课程成功，id: {id}")
        else:
            log.warning(f"预览课程失败，id: {id}")
        return self.response
    def create_course_task(self):
        '''
        执行job实现课程任务的生成
        逻辑是day1、week1、month1的课程只会对新司机(当前时间往前推7天)生成学习任务
        其他类型会对所有的课程创建时的指定类型司机(默认w2)生成学习任务
        '''
        self.get(url='/tms/job/training/createCourseTaskAndNotification', headers=central_header)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info(f"创建课程任务成功")
        else:
            log.warning(f"创建课程任务失败")
        return self.response
