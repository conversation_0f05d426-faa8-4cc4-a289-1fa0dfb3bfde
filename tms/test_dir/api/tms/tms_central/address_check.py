# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  address_check.py
@Description    :
@CreateTime     :  2023/6/19 16:46
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/19 16:46
"""
import weeeTest
from weeeTest import log
from tms.test_dir.api.tms import central_header
from weeeTest.utils import jmespath


class Address(weeeTest.TestCase):
    """
    Address相关接口
    """

    def get_driver_note_list(self, user_id=None, address=None, check_result=False):
        """
        获取司机地址备注列表
        :param user_id:  1186996
        :param address:  "12255 West Avenue 104 QQ SPA, San Antonio, Texas, 78216, United States"
        :param check_result: 
        :return:
        """
        body = {
            "userId": user_id,
            "address": address,
            "pageSize": 10,
            "pageNumber": 1
        }
        self.post(url='/tms/driverNote/getDriverNoteList', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机地址备注列表成功。')
        elif check_result:
            raise Exception(f'获取司机地址备注列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机地址备注列表失败, Error response:{self.response}')
        return self.response

    def delete_address_note(self, rec_id, user_id, address, edit_user, check_result=False):
        """
        删除司机地址备注
        :param rec_id: 列表ID
        :param user_id: 用户ID
        :param address: 用户地址文本
        :param edit_user: 修改人
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "userId": user_id,
            "address": address,
            "editUser": edit_user
        }
        self.delete(url='/tms/driverNote/deleteDriverNote', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'删除司机地址备注成功。')
        elif check_result:
            raise Exception(f'删除司机地址备注失败, Error response:{self.response}')
        else:
            log.warning(f'删除司机地址备注失败, Error response:{self.response}')
        return self.response

    def get_driver_note_by_param(self, user_id: int = None, address: str = None, check_result=False):
        """
        通过参数获取地址备注
        :param user_id:
        :param address:
        :param check_result:
        :return:
        """
        body = {
            "userId": user_id,
            "address": address
        }
        self.post(url='/tms/driverNote/getDriverNoteByParam', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'通过参数获取地址备注成功。')
        elif check_result:
            raise Exception(f'通过参数获取地址备注失败, Error response:{self.response}')
        else:
            log.warning(f'通过参数获取地址备注失败, Error response:{self.response}')
        return self.response

    def edit_address_note(self, rec_id, driver_comment, user_id, address, edit_user, check_result=False):
        """
        修改地址备注
        :param rec_id: 列表ID
        :param driver_comment: 司机备注
        :param user_id: 账户ID
        :param address: 地址文本
        :param edit_user: 编辑人员
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "driverComment": driver_comment,
            "userId": user_id,
            "address": address,
            "editUser": edit_user
        }
        self.put(url='/tms/driverNote/getDriverNoteByParam', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'修改地址备注成功。')
        elif check_result:
            raise Exception(f'修改地址备注失败, Error response:{self.response}')
        else:
            log.warning(f'修改地址备注失败, Error response:{self.response}')
        return self.response

    def delete_risk_address(self, risk_id, audit_user=7642085, check_result=False):
        """
        删除风险地址
        :param audit_user: 编辑人员
        :param risk_id: 风险地址ID
        :param check_result: 
        :return:
        """
        self.get(url=f'/tms/risk/delete?auditUser={audit_user}&riskId={risk_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'删除风险地址成功。')
        elif check_result:
            raise Exception(f'删除风险地址失败, Error response:{self.response}')
        else:
            log.warning(f'删除风险地址失败, Error response:{self.response}')
        return self.response

    def verify_address(self, addr_address, addr_city, addr_country, addr_state, address_type, addr_zipcode, address,
                       address_id, check_result=False):
        """
        地址验证
        :param addr_address: 街道
        :param addr_city: 城市
        :param addr_country:
        :param addr_state:
        :param address_type:
        :param addr_zipcode:
        :param address:
        :param address_id:
        :param check_result: 
        :return:
        """
        body = [{
            "addr_address": addr_address,
            "addr_apt": "",
            "addr_city": addr_city,
            "addr_country": addr_country,
            "addr_state": addr_state,
            "type": address_type,
            "addr_zipcode": addr_zipcode,
            "address": address,
            "address_id": address_id
        }]
        self.post(url='/v1/address/verify', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'地址验证成功。')
        elif check_result:
            raise Exception(f'地址验证失败, Error response:{self.response}')
        else:
            log.warning(f'地址验证失败, Error response:{self.response}')
        return self.response

    def get_address_map_info(self, address, check_result=False):
        """
        获取地址地图信息
        :param address:
        :param check_result: 
        :return:
        """

        self.get(url=f'/tms/address/getAddressMapInfo?address="{address}"', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取地址:[{address}]地图信息成功。')
        elif check_result:
            raise Exception(f'获取地址:[{address}]地图信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取地址:[{address}]地图信息失败, Error response:{self.response}')
        return self.response

    def get_risk_list(self, address=None, risk_type=None, status='A', check_result=False):
        """
        获取风险地址列表
        :param address: "6945 Rodling dr Unit A, San Jose"
        :param risk_type: job, job insert, driver report
        :param status:
        :param check_result: 
        :return:
        """
        body = {
            "address": address,
            "type": risk_type,
            "distanceMin": "",
            "distanceMax": "",
            "status": status,
            "inDtmMin": "",
            "inDtmMax": "",
            "pageNumber": 1,
            "pageSize": 10
        }
        self.post(url='/tms/risk/riskList', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取风险地址列表成功。')
        elif check_result:
            raise Exception(f'获取风险地址列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取风险地址列表失败, Error response:{self.response}')
        return self.response

    def get_address_by_address(self, addresses: list, check_result=False):
        """
        通过地址获取地址信息
        :param addresses: 地址列表
        :param check_result: 
        :return:
        """
        body = addresses
        self.post(url='/tms/address/getAddressByAddress', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'通过地址获取地址信息成功。')
        elif check_result:
            raise Exception(f'通过地址获取地址信息失败, Error response:{self.response}')
        else:
            log.warning(f'通过地址获取地址信息失败, Error response:{self.response}')
        return self.response

    def get_history_address_info(self, rec_id, address, check_result=False):
        """
        获取地址历史经纬度集合
        :param rec_id: 地址ID
        :param address: 地址列
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "address": address
        }
        self.post(url='/tms/risk/getHistoryNautica', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取地址历史经纬度集合成功。')
        elif check_result:
            raise Exception(f'获取地址历史经纬度集合失败, Error response:{self.response}')
        else:
            log.warning(f'获取地址历史经纬度集合失败, Error response:{self.response}')
        return self.response

    def uncertain_address(self, rec_id, audit_user=7642085, check_result=False):
        """
        将risk设置为不确定状态
        :param rec_id: 地址ID
        :param audit_user: 编辑人员
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "auditUser": audit_user
        }
        self.put(url='/tms/risk/uncertain', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'将risk设置为不确定状态成功。')
        elif check_result:
            raise Exception(f'将risk设置为不确定状态失败, Error response:{self.response}')
        else:
            log.warning(f'将risk设置为不确定状态失败, Error response:{self.response}')
        return self.response

    def approve_risk_address(self, rec_id, address, init_lat, init_long, fix_lat, fix_long, status, audit_user,
                             check_result=False):
        """
        审批风险地址
        :param rec_id: 地址ID 58283
        :param address: 地址 1649 Whispering Wind Drive, Tracy, California, 95377, United States
        :param init_lat: 初始纬度 37.6988988
        :param init_long:  初始经度 -121.4457741
        :param fix_lat: 修正纬度 37.6888988
        :param fix_long:  修正经度 -122.4457741
        :param status:  状态, P
        :param audit_user:  修改人员 7862430
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "address": address,
            "street": "",
            "initLatitude": init_lat,
            "initLongitude": init_long,
            "fixedAddress": "",
            "fixedLatitude": fix_lat,
            "fixedLongitude": fix_long,
            "status": status,
            "auditUser": audit_user
        }
        self.put(url='/tms/risk/approveRisk', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'审批风险地址成功。')
        elif check_result:
            raise Exception(f'审批风险地址失败, Error response:{self.response}')
        else:
            log.warning(f'审批风险地址失败, Error response:{self.response}')
        return self.response

    def edit_driver_address_note(self, rec_id, driver_comment, user_id, address, check_result=False):
        """
        编辑司机地址备注
        :param rec_id:
        :param driver_comment:
        :param user_id:
        :param address:
        :param check_result: 
        :return:
        """
        body = {
            "recId": rec_id,
            "driverComment": driver_comment,
            "userId": user_id,
            "address": address,
            "editUser": "7642085"
        }
        self.put(url='/tms/driverNote/editDriverNote', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'编辑司机地址备注成功。')
        elif check_result:
            raise Exception(f'编辑司机地址备注失败, Error response:{self.response}')
        else:
            log.warning(f'编辑司机地址备注失败, Error response:{self.response}')
        return self.response

    def get_w2_driver_payment_list(self, start_date, end_date, check_result=False):
        """
        获取w2司机运费基础数据
        :param start_date:
        :param end_date:
        :param check_result: 
        :return:
        """
        body = {
            "deliveryDateStart": start_date,
            "deliveryDateEnd": end_date,
            "pageNumber": 1,
            "pageSize": 20
        }
        self.post(url='/tms/driver/queryDriverFeeReportW2', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取w2司机运费基础数据成功。')
        elif check_result:
            raise Exception(f'获取w2司机运费基础数据失败, Error response:{self.response}')
        else:
            log.warning(f'获取w2司机运费基础数据失败, Error response:{self.response}')
        return self.response

    def get_ic_driver_payment_list(self, start_date, end_date, check_result=False):
        """
        获取IC司机运费基础数据
        :param start_date:
        :param end_date:
        :param check_result: 
        :return:
        """
        body = {
            "deliveryDateStart": start_date,
            "deliveryDateEnd": end_date,
            "pageNumber": 1,
            "pageSize": 20
        }
        self.post(url='/tms/driver/queryDriverFeeReportIC', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取IC司机运费基础数据成功。')
        elif check_result:
            raise Exception(f'获取IC司机运费基础数据失败, Error response:{self.response}')
        else:
            log.warning(f'获取IC司机运费基础数据失败, Error response:{self.response}')
        return self.response
