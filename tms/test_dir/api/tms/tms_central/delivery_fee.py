# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  delivery_fee.py
@Description    :  
@CreateTime     :  2025/6/5 16:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/5 16:13
"""
import json

from weeeTest.utils.logging import log

from tms.test_dir.api_case.tms import utils
from tms.test_dir.api_case.tms.tms_sql import TmsDB


class DeliveryFee:
    def __init__(self, dispatch_id):
        """
        初始化参数
        """
        self.tms_db = TmsDB()
        self.dispatch_info = self.tms_db.get_dispatch_data(dispatch_id)
        # 排车计划ID
        self.delivery_plan_id = self.dispatch_info[0]
        # 路线ID
        self.group_point_id = self.dispatch_info[1]
        # 获取派车单相关信息
        delivery_dispatch_info = self.tms_db.get_delivery_type(self.delivery_plan_id, self.group_point_id)
        # 运费版本
        self.version = delivery_dispatch_info[1]
        # 派车单类型, delivery/ hot_delivery
        self.delivery_type = delivery_dispatch_info[2]
        result = self.tms_db.get_delivery_info(self.delivery_plan_id, self.group_point_id)
        # 子区域ID
        self.sub_region_id = result[-3]  # 子区域ID
        self.tms_db.get_sub_region_name(self.sub_region_id)
        # 获取配送时间
        self.engagement_time = self.tms_db.get_engagement_time_by_point(self.delivery_plan_id, self.group_point_id)
        # Dispatch类型, G,S,D
        self.dispatch_type = result[-2]
        # 车辆类型
        self.vehicle_type = result[-1]
        # Flex状态
        self.flex_flag = result[1]
        # 派送距离
        if self.flex_flag and self.dispatch_type in ['S'] and self.delivery_type == 'delivery':
            self.distance = float(result[4])
            # dispatch_res = self.tms_db.get_dispatch_distance(self.delivery_plan_id, self.group_point_id)[0]
            # self.distance = float(json.loads(dispatch_res).get('distances').get('v'))
        elif self.flex_flag and self.dispatch_type in ['S'] and self.delivery_type == 'hot_delivery':
            dispatch_res = self.tms_db.get_dispatch_distance(self.delivery_plan_id, self.group_point_id)[1]
            self.distance = float(json.loads(dispatch_res).get('distance'))
        else:
            self.distance = float(result[4])  # 派送距离
        # 司机ID
        self.driver_user_id = delivery_dispatch_info[4]
        # 配送日期
        self.delivery_date = delivery_dispatch_info[5]
        # 司机小费
        self.dispatch_tip = float(result[5])
        # 派送点数
        self.address_count = result[6]
        # 区域费用
        self.zipcode_fee = float(result[7])
        # APT数量
        self.apt_num = float(result[9])
        #  实际配送站点数量
        stop_count = self.tms_db.get_point_count(self.delivery_plan_id, self.group_point_id)
        if self.address_count != stop_count and self.flex_flag == 0:
            log.info(f"\033[1;31m此路线有Cancel的站点,实际配送站点数量:{stop_count}\033[0m")
            self.address_count = stop_count
            cancel_fee = self.tms_db.get_cancel_task_info(self.delivery_plan_id, self.group_point_id)
            self.zipcode_fee = self.zipcode_fee - float(cancel_fee[1])
            log.info(f"\033[1;31m实际zipcode_fee减少:{cancel_fee[1]}\033[0m")
            self.apt_num = self.apt_num - float(cancel_fee[0])
            log.info(f"\033[1;31m实际APT数量减少:{cancel_fee[0]}\033[0m")
        # 司机类型, F,P,Flex, DSP
        self.driver_type = self.tms_db.get_driver_info(self.delivery_plan_id, self.group_point_id)
        # 司机子类型, 1-全职, 2-私家车, 3-DSP
        self.driver_sub_type = self.tms_db.get_driver_info(self.delivery_plan_id, self.group_point_id,
                                                           info='driver_sub_type')
        # 司机所在公司ID
        self.driver_company_id = self.tms_db.get_driver_info(self.delivery_plan_id, self.group_point_id,
                                                             info="company_id")
        # 路线下的zipcode集合
        self.zipcodes = self.tms_db.get_delivery_zipcode(self.delivery_plan_id, self.group_point_id)
        # 纽约zipcode集合
        self.new_york_zipcodes = self.tms_db.get_new_york_zipcode()

    def get_distance_fee(self):
        """
        获取距离费用
        """
        distance_rate_config = self.tms_db.get_distance_rate(self.sub_region_id, self.version)
        distance_fee = utils.calculator_distance_fee(distance_rate_config)(self.distance)
        return float(distance_fee)

    def get_address_count_rate(self):
        """
        获取送货点费率
        """
        distance_rate_config = self.tms_db.get_address_count_rate(self.sub_region_id, self.version)
        address_count_rate = utils.get_address_count_rate(distance_rate_config)(self.address_count)
        return float(address_count_rate)

    def get_prop_22_formula(self):
        """
        22法令公式参数
        """
        prop_22_fee_config = self.tms_db.get_prop_22_fee(self.sub_region_id, self.version)
        formula_rate = [float(x) for x in prop_22_fee_config.split(',')[1:]]
        return formula_rate

    def get_fee_additional_of_apartment(self):
        """
        获取APT（公寓）系数
        """
        apt_rate = self.tms_db.get_apartment_rate(self.sub_region_id, self.version)
        return float(apt_rate)

    def get_special_zipcode_fee(self):
        """
        获取特殊zipcode费用
        """
        dispatch_zipcodes = self.tms_db.get_delivery_zipcode(self.delivery_plan_id, self.group_point_id)
        special_fee = 0.0
        special_zipcodes = self.tms_db.get_special_zipcode(self.version)
        special_zipcode_list = special_zipcodes.split(';')
        for special_zipcode in special_zipcode_list:
            zipcodes = [int(x) for x in special_zipcode.split(':')[1].split(',')]
            inter_zipcode = list(set(dispatch_zipcodes) & set(zipcodes))
            if inter_zipcode:
                log.info(f"注意该派车单包含特殊的zipcode，需加{special_zipcode.split(':')[0]}$费用")
                special_fee = special_fee + int(special_zipcode.split(':')[0])
        return special_fee

    def get_min_points_num(self):
        """
        获取最小派送点数
        """
        # 1,2,3,4,5,6,7,38,39,45,46,47
        min_num = self.tms_db.get_mini_point_num(sub_region_id=self.sub_region_id,
                                                 version=self.version,
                                                 flex_flag=self.flex_flag)
        log.info(f"此区域最小派送点数{min_num}")
        return min_num

    def get_route_type(self):
        """
        获取路线类型
        :return:
        """
        if self.delivery_type == "alcohol_delivery":
            route_type = 3
        elif self.delivery_type == "hot_delivery":
            route_type = 2
        elif self.flex_flag == 1:
            if self.vehicle_type in ('SUV', 'Sedan'):
                route_type = 6
            else:
                route_type = 4
        elif self.driver_type == 'F' and self.driver_sub_type == 2:
            route_type = 1
        elif self.driver_sub_type == 3:
            route_type = 5
        else:
            route_type = 0
        return route_type

    def get_distance_point_rate(self):
        """
        获取距离系数及派送点系数
        线路类型 线路类型 0-all 1-normal 2-hot 3-alcohol 4-flex 5-三方司机与company_id对应 6-flex-suv
        """
        route_type = self.get_route_type()
        if route_type == 5:
            special_distance_rate = self.tms_db.get_special_distance_rate(sub_region_id=self.sub_region_id,
                                                                          version=self.version, route_type=route_type,
                                                                          company_id=self.driver_company_id)
            special_point_rate = self.tms_db.get_special_point_rate(sub_region_id=self.sub_region_id,
                                                                    version=self.version, route_type=route_type,
                                                                    company_id=self.driver_company_id)
        else:
            special_distance_rate = self.tms_db.get_special_distance_rate(sub_region_id=self.sub_region_id,
                                                                          version=self.version, route_type=route_type)
            special_point_rate = self.tms_db.get_special_point_rate(sub_region_id=self.sub_region_id,
                                                                    version=self.version, route_type=route_type)
        if special_distance_rate and special_point_rate:
            log.info(f"此销售组织有特殊运费计算公式，距离系数：{special_distance_rate},派送点系数：{special_point_rate}")
            return special_distance_rate, special_point_rate
        else:
            log.info(f"此销售组织无特殊运费计算公式")
            return False

    def get_test_rate(self):

        if self.sub_region_id == 18 and self.delivery_type == "alcohol_delivery":
            rate = 0.56, 4.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [18, 42] and self.driver_type != 'F' and self.driver_sub_type != 2:
            rate = 0.5432, 3.395
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id == 4:
            rate = 0.8, 3.3
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [1, 2, 3, 38] and self.delivery_type == "hot_delivery":
            rate = 0.56, 3.2
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [14, 15, 40, 43, 44, 54, 55] and self.delivery_type == "hot_delivery":
            rate = 0.8, 4
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [34, 35, 41] and self.delivery_type == "hot_delivery":
            rate = 0.83, 3.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [14, 15, 54, 55] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 0.5, 1.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [47] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 1.12, 0.7
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [18, 42] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 0.5432, 3.395
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [40, 43, 44] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 1.3, 1.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        else:
            return False

    @staticmethod
    def get_seafood_fee():
        """
        获取活生鲜费用
        :return:
        """
        seafood_fee = 0  # 活生鲜费用
        return seafood_fee

    def get_prop_22_fee(self):
        """
        获取22法令费用
        @return:
        """
        formula_rate = self.get_prop_22_formula()
        if formula_rate:
            if self.delivery_type == 'delivery':
                prop_22_fee = 1.2 * formula_rate[0] * self.engagement_time + self.distance * 0.34
                log.info(
                    f"22法令基本费用: prop_22_fee({prop_22_fee:.2f}) = 1.2 * formula_rate({formula_rate[0]}) * "
                    f"engagement_time({self.engagement_time:.2f})  + distance({self.distance}) * 0.34")
                return prop_22_fee
            else:
                prop_22_fee = 1.2 * formula_rate[0] * (self.engagement_time + formula_rate[1]) + self.distance * 0.34
                log.info(
                    f"22法令基本费用: prop_22_fee({prop_22_fee:.2f}) = 1.2 * formula_rate({formula_rate[0]}) * (engagement_time("
                    f"{self.engagement_time:.2f}) + formula_rate({formula_rate[1]})) + distance({self.distance}) * 0.34")
                return prop_22_fee
        else:
            log.info('此地区无22法令运费要求')
            return 0

    def get_delivery_fee(self):
        """
        获取司机派送费用（不包含小费）
        :return:
        """
        distance_fee = self.get_distance_fee()  # 距离费用
        special_zipcode_fee = self.get_special_zipcode_fee()  # 特殊zipcode区域运费
        apt_fee = self.get_fee_additional_of_apartment() * self.apt_num  # APT运费
        address_rate = self.get_address_count_rate()  # 送货点系数
        min_num = self.get_min_points_num()  # 最小派送点数
        rate = self.get_distance_point_rate()  # 距离系数及派送点系数
        seafood_fee = self.get_seafood_fee()  # 活生鲜费用
        clock_time = self.tms_db.driver_clock_time(self.driver_user_id, self.delivery_date)  # 打卡时长

        if self.driver_type == 'F' and self.driver_sub_type == 2:
            log.info(f'此司机为私家车W2司机,需要计算额外运费!')
            log.info(
                f"类型：{self.delivery_type},距离：{self.distance},小费：{self.dispatch_tip},派送数：{self.address_count},"
                f"工作时长：{clock_time:.2f}")
            working_hour_fee = clock_time * 19.97
            distance_fee = self.distance * 0.67
            delivery_fee = working_hour_fee + distance_fee
            log.info(f'working_hour_fee:{working_hour_fee:.2f}, distance_fee:{distance_fee:.2f}')
            log.info(
                f'delivery_fee({delivery_fee:.2f}) = working_time({clock_time:.2f}) * 19.97 + self.distance({self.distance}) * 0.67')
            return round(delivery_fee, 2)
        elif self.driver_type == 'F':
            log.info("此司机为全职司机，无配送费")
            return 0
        elif self.driver_type == 'DSP' or (self.driver_sub_type == 3 and self.driver_type == 'P'):
            log.info(f'此司机为DSP兼职司机')
            if self.sub_region_id in [14, 15, 34, 35, 43, 44]:
                delivery_fee = 4.75 * self.address_count
                log.info(
                    f'delivery_fee({delivery_fee:.2f}) = count_of_points({self.address_count}) * 4.75')
            else:
                delivery_fee = 3.5 * self.address_count
                log.info(
                    f'delivery_fee({delivery_fee:.2f}) = count_of_points({self.address_count}) * 3.5')
            return round(delivery_fee, 2)
        else:
            log.info("-" * 30 + "\033[1;35m 基础数据 \033[0m" + "-" * 30)
            log.info(
                f"类型：{self.delivery_type},距离：{self.distance},小费：{self.dispatch_tip},派送数：{self.address_count},zipcode费用：{self.zipcode_fee},APT数量：{self.apt_num}")
            # 该地区有特殊运费计算公式时，按公式计算
            if rate:
                # 司机运费 = 距离 * 系数 + 派送点数 * 系数 + 司机小费
                delivery_fee = self.distance * rate[0] + self.address_count * rate[1]
                log.info(
                    f"司机1配送费用({delivery_fee:.2f}) = 距离({self.distance}) * 距离系数({rate[0]}) + "
                    f"派送点数({self.address_count}） * 派送点系数({rate[1]})")

            elif self.sub_region_id in [5, 6, 13, 39, 47] and self.delivery_type == "hot_delivery":
                log.info(
                    f"订单为LA热送贴，不用加 活生鲜运费、APT运费、特殊区域运费，也不用乘以送货点系数及热送系数")
                delivery_fee = (self.zipcode_fee + distance_fee) * 0.88
                log.info(
                    f"司机2配送费用为：{delivery_fee:.2f} =  (zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f})） * 0.88")

            elif self.address_count > min_num or self.sub_region_id in [3, 46]:
                # 司机运费 = {zipcode运费 + 距离运费 + 活生鲜运费 + 特殊zipcode区域运费 + APT运费} * 送货点系数 + 司机小费
                log.info("-" * 30 + "\033[1;35m 运费组成 \033[0m" + "-" * 30)
                log.info(
                    f"zipcode运费：{self.zipcode_fee},距离运费：{distance_fee:.2f}, 活生鲜运费：{seafood_fee}, APT运费：{apt_fee:.2f}, 特殊区域运费：{special_zipcode_fee},送货点系数：{address_rate}")

                # 销售组织条件判断
                if self.sub_region_id in [1, 2, 38] and self.driver_company_id == 2:
                    log.info("此司机为湾区Openforce 3P司机，需要扣除额外费用")
                    delivery_fee = (
                                           self.zipcode_fee + distance_fee + seafood_fee + apt_fee + special_zipcode_fee - 0.5 *
                                           self.address_count) * address_rate
                    log.info(
                        f"司机3配送费用：{delivery_fee:.2f} = [zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f}) + 活生鲜运费"
                        f"({seafood_fee}) + APT运费({apt_fee:.2f}) + 特殊区域运费({special_zipcode_fee}) - 0.5 *"
                        f" {self.address_count}] * 送货点系数({address_rate})")

                else:
                    delivery_fee = (
                                           self.zipcode_fee + distance_fee + seafood_fee + apt_fee + special_zipcode_fee) * address_rate
                    log.info(
                        f"司机4配送费用：{delivery_fee:.2f} = [zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f}) + "
                        f"活生鲜运费({seafood_fee}) + APT运费({apt_fee:.2f}) + 特殊区域运费({special_zipcode_fee})]"
                        f" * 送货点系数({address_rate})")
            else:
                # 小于等于最小派送数时，司机运费 = 派送数 * 5 + 司机小费
                delivery_fee = self.address_count * 5
                log.info(
                    f"小于最小派送数，按公式:司机5总费用({delivery_fee:.2f}) = 派送点数({self.address_count}) * 5")
            return delivery_fee

    def get_driver_free(self):
        """
        获取司机运费
        """

        delivery_fee = self.get_delivery_fee()
        out_layer_rate = self.tms_db.get_out_layer_rate(sub_region_id=self.sub_region_id,
                                                        version=self.version,
                                                        flex_flag=self.flex_flag)
        log.info("-" * 30 + "\033[1;35m 运费计算 \033[0m" + "-" * 30)
        if self.driver_sub_type == 2 and self.driver_type == 'F':
            final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
            log.info(
                f'私家车W2司机运费({final_fee}) = 公司成本({delivery_fee}) * 外围系数({out_layer_rate}) + 小费({self.dispatch_tip})')
            return round(final_fee, 2)
        elif self.driver_type == 'DSP' or (self.driver_sub_type == 3 and self.sub_region_id in [18, 19, 42, 50]):
            final_fee = delivery_fee + self.dispatch_tip
            log.info(
                f'DSP兼职司机运费({final_fee}) = 派送费用({delivery_fee}) + 小费({self.dispatch_tip})')
            return round(final_fee, 2)
        elif self.delivery_type == "delivery" and self.driver_type != 'F':
            prop_22 = self.get_prop_22_fee()
            if self.flex_flag == 1 and self.sub_region_id in [1, 2, 3, 5, 51, 46]:
                log.info("此路线为满足合规要求的Flex路线, 单路线运费计算")
                delivery_route_fee = 2.2 * self.address_count + 0.3 * self.distance
                log.info(
                    f"\033[1;35m单路线Flex费用({delivery_route_fee:.2f}) = 2.2 * 站点数量({self.address_count}) + 0.3 * 距离({self.distance})\033[0m")
                final_fee = delivery_route_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线100%比例：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_route_fee:.2f}) * 1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 110%运费比例
                final_fee_110 = delivery_route_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线110%比例：司机总费用 ({final_fee_110:.2f}) = 派送费用({delivery_route_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")

            if self.flex_flag == 1 and self.sub_region_id in [6]:
                log.info("此路线为满足合规要求的Flex路线, 单路线运费计算")
                delivery_route_fee = 3.16 * self.address_count + 0.34 * self.distance + 0.14 * self.apt_num
                log.info(
                    f"\033[1;35m单路线Flex费用({delivery_route_fee:.2f}) = 3.16 * 站点数量({self.address_count}) + 0.34 * 距离({self.distance}) + 0.14 * APT数量({self.apt_num}) \033[0m")

            if prop_22:
                prop_22_fee = prop_22 + self.dispatch_tip
                log.info(f"需要满足最新22法令最低工资要求，最低工资为：\033[1;35m{prop_22_fee:.2f}\033[0m")
            if self.flex_flag == 1:
                log.info("-" * 30 + "此路线为Flex路线" + "-" * 30)
                # Flex 100%运费比例
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线100%比例：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 110%运费比例
                final_fee_110 = delivery_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线110%比例：司机总费用 ({final_fee_110:.2f}) = 派送费用({delivery_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 125%运费比例
                final_fee_125 = delivery_fee * 1.25 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线125%比例：司机总费用 ({final_fee_125:.2f}) = 派送费用({delivery_fee:.2f}) * 1.25 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            else:
                if self.sub_region_id in [18, 19, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 42, 48, 50, 52]:
                    log.info("-" * 30 + "此地区运费需扣减 3%" + "-" * 30)
                    final_fee = delivery_fee * 0.97 + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.97 + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)

                elif self.sub_region_id in [14, 15, 20, 21, 22, 40, 43, 44, 5, 6, 7, 39, 45, 46, 47, 54,
                                            55] and self.flex_flag == 0:
                    log.info("-" * 30 + "此地区运费需扣减 5%" + "-" * 30)
                    final_fee = delivery_fee * 0.95 + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.95 + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)
                elif self.sub_region_id in [16, 17]:
                    log.info("-" * 30 + "此地区运费需扣减 10%" + "-" * 30)
                    final_fee = delivery_fee * 0.9 + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.9 + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)
                else:
                    final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)
        elif self.delivery_type == "hot_delivery":
            prop_22 = self.get_prop_22_fee()
            if prop_22:
                prop_22_fee = prop_22 + self.dispatch_tip
                log.info(f"需要满足最新22法令最低工资要求，最低工资为：\033[1;35m{prop_22_fee:.2f}\033[0m")

            if self.sub_region_id in [34, 35, 41]:
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [14, 15, 40, 54, 55]:
                log.info("-" * 30 + "此地区运费需扣减 5%" + "-" * 30)
                final_fee = delivery_fee * 0.95 + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.95 + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [18]:
                log.info("-" * 30 + "此地区运费需扣减 3%" + "-" * 30)
                final_fee = delivery_fee * 0.97 + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.97 + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [38, 39]:
                flex_pay = 2.2 * self.address_count + 0.3 * self.distance
                pay = flex_pay * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：Flex费用({flex_pay:.2f}) = 2.2 * 站点数量({self.address_count}) + 0.3 * 距离({self.distance})\033[0m")
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：Flex总费用({pay:.2f}) = Flex运费({flex_pay}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：100%司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                final_fee_110 = delivery_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：110%司机总费用({final_fee_110:.2f}) = 派送费用({delivery_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            else:
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
        else:
            final_fee = delivery_fee * 0.8 * out_layer_rate + self.dispatch_tip
            log.info(
                f"\033[1;35m订单可能为专贴：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.8 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
            return round(final_fee, 2)
