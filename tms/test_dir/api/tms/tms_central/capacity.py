# Author: bo.liu
# Time: 2025/5/21 17:04
# E-Mail: <EMAIL>
"""
TMS系统运力管理相关接口封装
提供查询运力计划和运力摘要的功能
"""
import weeeTest
from weeeTest import jmespath, log
from tms.test_dir.api.tms import central_header


class Capacity(weeeTest.TestCase):
    """
    TMS系统运力管理相关接口封装类

    提供以下功能:
    1. 查询运力计划 (query_capacity_plan)
    2. 查询运力摘要 (query_capacity_summary)
    """

    def query_capacity_plan(self, sub_region_ids, start_date, end_date, check_result=False):
        """
        查询指定sub_region在指定日期范围内的运力计划详情

        该接口返回每个sub_region在指定日期范围内的详细运力数据，包括各类型司机的运力值

        Args:
            sub_region_ids (list): 子区域ID列表，例如 [1, 2, 3]
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            check_result (bool, optional): 是否在请求失败时抛出异常. 默认为 False

        Returns:
            dict: 接口返回的完整响应数据

        Raises:
            Exception: 当check_result为True且请求失败时抛出异常

        Example:
            >>> capacity = Capacity()
            >>> result = capacity.query_capacity_plan([5], '2025-05-21', '2025-05-27')
        """
        # 构建请求参数
        data = {
            "subRegionIds": sub_region_ids,
            "startDate": start_date,
            "endDate": end_date
        }

        # 发送POST请求
        self.post(url='/tms/driverSchedule/capacityPlan', headers=central_header, json=data)

        # 检查请求结果
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取capacityPlan信息成功，日期范围: {start_date}至{end_date}, sub_region_ids: {sub_region_ids}')
        elif check_result:
            raise Exception(f'获取capacityPlan信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取capacityPlan信息失败, Error response:{self.response}')

        return self.response

    def query_capacity_summary(self, sub_region_ids, start_date, end_date, check_result=False):
        """
        查询指定sub_region在指定日期范围内的运力摘要数据

        该接口返回每个sub_region在指定日期范围内的运力摘要数据，包括各类型司机的数量统计

        Args:
            sub_region_ids (list): 子区域ID列表，例如 [1, 2, 3]
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            check_result (bool, optional): 是否在请求失败时抛出异常. 默认为 False

        Returns:
            dict: 接口返回的完整响应数据

        Raises:
            Exception: 当check_result为True且请求失败时抛出异常

        Example:
            >>> capacity = Capacity()
            >>> result = capacity.query_capacity_summary([5], '2025-05-21', '2025-05-21')
        """
        # 构建请求参数
        data = {
            "subRegionIds": sub_region_ids,
            "startDate": start_date,
            "endDate": end_date
        }

        # 发送POST请求
        self.post(url='/tms/driverSchedule/capacitySummary', headers=central_header, json=data)

        # 检查请求结果
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取capacitySummary信息成功，日期范围: {start_date}至{end_date}, sub_region_ids: {sub_region_ids}')
        elif check_result:
            raise Exception(f'获取capacitySummary信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取capacitySummary信息失败, Error response:{self.response}')

        return self.response

    def query_driver_shift(self, sub_region_ids, start_date, end_date, status="scheduled", page_num=1, page_size=1000, check_result=False):
        """
        查询指定sub_region在指定日期范围内的司机排班信息

        该接口返回每个司机在指定日期范围内的排班详情，包括各天的capacity值

        Args:
            sub_region_ids (list): 子区域ID列表，例如 [14, 15, 16]
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            status (str, optional): 排班状态，默认为 "scheduled"
            page_num (int, optional): 页码，默认为 1
            page_size (int, optional): 每页记录数，默认为 1000
            check_result (bool, optional): 是否在请求失败时抛出异常. 默认为 False

        Returns:
            dict: 接口返回的完整响应数据

        Raises:
            Exception: 当check_result为True且请求失败时抛出异常

        Example:
            >>> capacity = Capacity()
            >>> result = capacity.query_driver_shift([14, 15, 16], '2025-05-22', '2025-05-28')
        """
        # 构建请求参数
        data = {
            "subRegionIds": sub_region_ids,
            "startDate": start_date,
            "endDate": end_date,
            "status": status,
            "pageNum": page_num,
            "pageSize": page_size
        }

        # 发送POST请求
        self.post(url='/tms/driverSchedule/driverShift', headers=central_header, json=data)

        # 检查请求结果
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取driverShift信息成功，日期范围: {start_date}至{end_date}, sub_region_ids: {sub_region_ids}')
        elif check_result:
            raise Exception(f'获取driverShift信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取driverShift信息失败, Error response:{self.response}')

        return self.response