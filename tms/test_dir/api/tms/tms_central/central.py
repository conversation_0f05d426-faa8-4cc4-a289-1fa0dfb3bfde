# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :
@CreateTime     :  2023/6/9 15:29
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/9 15:29
"""
import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath

from tms.test_dir.api.tms import central_header
from tms.test_dir.api.tms.tms_central.address_check import Address
from tms.test_dir.api.tms.tms_central.capacity import Capacity
from tms.test_dir.api.tms.tms_central.carrier_management import Carrier
from tms.test_dir.api.tms.tms_central.driver_manage import DriverManage
from tms.test_dir.api.tms.tms_central.fleet_management import FleetManage
from tms.test_dir.api.tms.tms_central.payment import Payment
from tms.test_dir.api.tms.tms_central.route_plan import RoutePlan
from tms.test_dir.api.tms.tms_central.yard_management import YardManagement


class TmsCentral(weeeTest.TestCase):
    """
    Central公共方法封装
    """
    driver_manage = DriverManage()
    route_plan = RoutePlan()
    address = Address()
    fleet = FleetManage()
    payment = Payment()
    capacity = Capacity()
    yard_management = YardManagement()
    carrier = Carrier()

    def anon_auth(self):
        """
        获取匿名token
        :return:
        """
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        central_header["authorization"] = 'Bearer ' + auth
        return auth

    def central_login(self, user_id: int = None, password: str = None, login_platform: str = 'TMS'):
        """
        central登录
        :param login_platform: 平台
        :param user_id: 账户ID
        :param password: 密码
        :return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        body = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.anon_auth()
        self.post(url='/hub/auth/user/login', headers=central_header, json=body)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Central登录成功,CentralToken:{auth}')
            central_header["authorization"] = 'Bearer ' + auth
        else:
            raise Exception(f'Central登录失败,msg:{jmespath(self.response, "message")}')
        return self.response

    def update_dispatch_distance(self, dispatch_id, distance, check_result=False):
        """
        更新派车单距离
        :param dispatch_id: 派车单ID
        :param distance: 派车单距离
        :param check_result:
        :return:
        """

        body = {
            "updateType": 3,
            "dispatchId": dispatch_id,
            "distance": distance
        }
        self.post(url='/tms/dispatch/updateDispatchDistance', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新派车单{dispatch_id}距离为{distance}成功。')
        elif check_result:
            raise Exception(f'更新派车单{dispatch_id}距离为{distance}失败, Error response:{self.response}')
        else:
            log.warning(f'更新派车单{dispatch_id}距离为{distance}失败, Error response:{self.response}')
        return self.response

    def allow_route_modification(self, draft_id, flex_unlock, check_result=False):
        """
        允许Flex路线可调整
        :param draft_id: 草稿ID
        :param flex_unlock: 1可调整, 0不可调整
        :param check_result:
        :return:
        """

        body = {
            "draftId": "456",
            "flexUnlock": flex_unlock,
            "partUnlock": 1,
            "fullUnlock": 1,
            "comment": "Autotest"
        }
        self.post(url='/tms/draftUnlockConfig/save', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'设置草稿ID:{draft_id}的Flex路线为可调整成功。')
        elif check_result:
            raise Exception(f'设置草稿ID:{draft_id}的Flex路线为可调整失败, Error response:{self.response}')
        else:
            log.warning(f'设置草稿ID:{draft_id}的Flex路线为可调整失败, Error response:{self.response}')
        return self.response

    def change_delivery_date(self, dispatch_id, delivery_date, driver_user_id, pass_word, check_result=False):
        """
        更改派车单日期
        :param dispatch_id:
        :param delivery_date:
        :param driver_user_id:
        :param pass_word:
        :param check_result:
        :return:
        """
        body = {
            "updateType": 2,
            "dispatchId": dispatch_id,
            "deliveryDate": delivery_date,
            "driverUserId": driver_user_id,
            "password": pass_word
        }
        self.post(url='/tms/dispatch/changeDeliveryDate', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更改派车单{dispatch_id}日期为{delivery_date}成功。')
        elif check_result:
            raise Exception(f'更改派车单{dispatch_id}日期为{delivery_date}失败, Error response:{self.response}')
        else:
            log.warning(f'更改派车单{dispatch_id}日期为{delivery_date}失败, Error response:{self.response}')
        return self.response

    def change_route_status(self, route_id, delivery_id, status, check_result=False):
        """
        更改路线状态
        :param route_id:
        :param delivery_id:
        :param status:
        :param check_result:
        :return:
        """
        body = {
            "route_id": route_id,
            "delivery_id": delivery_id,
            "status": status,
            "in_user": "Jesus Contreras(14511467)",
            "cancel_tracking_num_list": [],
            "is_block_list": [],
            "is_cancel_list": []
        }
        self.post(url='/tms/dispatch/route/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更改路线{route_id}状态为{status}成功。')
        elif check_result:
            raise Exception(f'更改路线{route_id}状态为{status}失败, Error response:{self.response}')
        else:
            log.warning(f'更改路线{route_id}状态为{status}失败, Error response:{self.response}')
        return self.response

    def release_all_slot(self, check_result=False):
        """
        释放所有slot
        :param check_result:
        :return:
        """
        self.get(url='/tms/job/parking/clearParkSlots', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'释放车位job执行成功。')
        elif check_result:
            raise Exception(f'释放车位job执行失败, Error response:{self.response}')
        else:
            log.warning(f'释放车位job执行失败, Error response:{self.response}')
        return self.response

    def get_flex_route_info(self, sub_region_list: list, delivery_date: str, check_result=False):
        """
        获取Flex路线信息
        :param sub_region_list: [5]
        :param delivery_date:
        :param check_result:
        :return:
        """
        body = {
            "subRegionIdList": sub_region_list,
            "deliveryDate": delivery_date
        }
        self.post(url='/tms/route/queryRouteInfos', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取Flex路线信息成功。')
        elif check_result:
            raise Exception(f'获取Flex路线信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取Flex路线信息失败, Error response:{self.response}')
        return self.response

    def get_route_operations(self, dispatch_id, check_result=False):
        """
        获取路线操作记录
        :param dispatch_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/route/queryRouteOperations?dispatchId={dispatch_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取路线:{dispatch_id}操作记录成功。')
        elif check_result:
            raise Exception(f'获取路线:{dispatch_id}操作记录失败, Error response:{self.response}')
        else:
            log.warning(f'获取路线:{dispatch_id}操作记录失败, Error response:{self.response}')
        return self.response

    def update_route_load_finish(self, dispatch_id, check_result=False):
        """
        更新路线装货完成
        :param dispatch_id:
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "reason": "AutoTest"
        }
        self.post(url='/tms/dispatch/update/routeStatus/loadFinish', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新路线{dispatch_id}装货完成成功。')
        elif check_result:
            raise Exception(f'更新路线{dispatch_id}装货完成失败, Error response:{self.response}')
        else:
            log.warning(f'更新路线{dispatch_id}装货完成失败, Error response:{self.response}')
        return
    def get_dispatch_detail(self, dispatch_id, check_result=False):
        """
        获取派车单详情
        :param dispatch_id:
        :param check_result:
        :return:
        """
        params = {
            "dispatch_id": dispatch_id
        }
        self.get(url=f'/tms/driver/offer/detail/header', headers=central_header ,params=params)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取派车单{dispatch_id}详情成功。')
        elif check_result:
            raise Exception(f'获取派车单{dispatch_id}详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车单{dispatch_id}详情失败, Error response:{self.response}')
        return self.response
    def get_config_key_value(self, key, check_result=False):
        """
        获取配置项
        :param key:
        :param check_result:
        :return:
        """
        params = {
            "config_key": key
        }
        self.get(url="/tms/config/list", headers=central_header, params=params)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取配置项{key}成功。')
            # 遍历 object 列表，找到与传入 key 匹配的 config_key 并返回其 config_value
            for item in jmespath(self.response, "object"):
                log.info("获取到的item是",item)
                if item["config_key"] == key:
                    return item["config_value"]
            return None  # 如果没有找到匹配的 config_key，则返回 None
        elif check_result:
            raise Exception(f'获取配置项{key}失败, Error response:{self.response}')
        else:
            log.warning(f'获取配置项{key}失败, Error response:{self.response}')

    def get_draft_route_drivers(self, draft_id, delivery_plan_id=None, driver_type="F", driver_info="driver_user_id"):
        """
        获取草稿可用司机
        :param draft_id:
        :param delivery_plan_id:
        :param driver_type:
        :param driver_info:
        :return:
        """
        if not delivery_plan_id:
            delivery_plan_id = self.route_plan.get_draft_info(draft_id=draft_id)['object']['delivery_plan_id']
        route_drivers_res = self.route_plan.get_route_drivers(delivery_plan_id=delivery_plan_id,
                                                              draft_id=draft_id, )
        route_drivers = [driver[driver_info] for driver in route_drivers_res["object"] if
                         driver.get("driver_type") == f"{driver_type}"]
        log.info(f'可使用的司机类型为{driver_type}的{driver_info}为{route_drivers}')
        return route_drivers
    def get_alert_config(self, region_id, sub_region_id ,check_result=False):
        """
        获取报警配置
        :param region_id:
        :param sub_region_id
        :param check_result:
        :return:
        """
        params = {
            "regionId": region_id
        }
        alert_config_res = self.get(url=f'/tms/subRegion/alertConfig', headers=central_header ,params=params)
        result = jmespath(self.response, "result")
        if result:
            # 循环查询alert_config_res的object列表中subRegionId是sub_region_id的latitude和longitude的值并返回
            for item in jmespath(self.response, "object"):
                if item["subRegionId"] == sub_region_id:
                    # print("进来了")
                    # print(F"经纬度是{item["latitude"]}")
                    return item["latitude"], item["longitude"]
            return None,None  # 如果没有找到匹配的 config_key，则返回 None
        elif  check_result:
            raise Exception(f'获取报警配置失败, Error response:{self.response}')
        else:
            log.warning(f'获取报警配置失败, Error response:{self.response}')

