# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  route_plan.py
@Description    :  
@CreateTime     :  2023/6/13 14:57
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/13 14:57
"""
import weeeTest
from weeeTest import jmespath, log

from tms.test_dir.api.tms import central_header


class RoutePlan(weeeTest.TestCase):
    """
    Route Plan菜单相关接口
    """

    def delete_delivery_plan(self, delivery_plan_id: int, check_result=False):
        """
        删除发货计划
        :param delivery_plan_id: 发货计划ID
        :param check_result:
        :return:
        """

        self.delete(url=f'/tms/deliveryPlanDispatch/deleteDeliveryPlan/{delivery_plan_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('删除发货计划成功。')
        elif check_result:
            raise Exception(f'删除发货计划失败, Error response:{self.response}')
        else:
            log.warning(f'删除发货计划失败, Error response:{self.response}')
        return self.response

    def get_delivery_list(self, sub_region_ids: list, delivery_date: str, check_result=False):
        """
        获取发货批次列表
        :param sub_region_ids: 子区域ID列表 [1,2,3,4,38]
        :param delivery_date: 配送时间： 2022-12-10
        :param check_result:
        :return:
        """
        body = {
            "subRegionIds": sub_region_ids,
            "deliveryDate": delivery_date
        }
        self.post(url='/tms/deliveryPointGenerate/listDelivery', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取发货批次列表成功。')
        elif check_result:
            raise Exception(f'获取发货批次列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取发货批次列表失败, Error response:{self.response}')
        return self.response

    def get_delivery_plan_dispatch_list(self, delivery_plan_id: int, check_result=False):
        """
        获取派车计划的派车单列表
        :param delivery_plan_id: 派车计划ID
        :param check_result:
        :return:
        """
        body = {
            "deliveryPlanId": delivery_plan_id
        }
        self.post(url='/tms/deliveryPlanDispatch/listDispatch', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取派车计划的派车单列表成功。')
        elif check_result:
            raise Exception(f'获取派车计划的派车单列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车计划的派车单列表失败, Error response:{self.response}')
        return self.response

    def get_region_dispatch_list(self, sub_region_ids: list, delivery_date: str = None, check_result=False):
        """
        获取Region下的派车单列表
        :param sub_region_ids: 子区域ID列表 [1,2,3,4,38]
        :param delivery_date: 配送日期 2022-12-08
        :param check_result:
        :return:
        """
        body = {
            "subRegionIds": sub_region_ids,
            "deliveryDate": delivery_date,
            "pageNumber": 1,
            "pageSize": 10
        }
        self.post(url='/tms/deliveryPlanDispatch/list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取Region下的派车单列表成功。')
        elif check_result:
            raise Exception(f'获取Region下的派车单列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取Region下的派车单列表失败, Error response:{self.response}')
        return self.response

    def generate_delivery_point(self, delivery_ids: list, delivery_date: str, check_result=False):
        """
        生成派车计划
        :param delivery_ids: 发货批次列表
        :param delivery_date: 发货日期
        :param check_result:
        :return:
        """
        body = {
            "deliveryIds": delivery_ids,
            'deliveryDate': delivery_date
        }
        self.post(url='/tms/deliveryPointGenerate/generate', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'发货批次:{delivery_ids}正在生成送货点...')
        elif check_result:
            raise Exception(f'发货批次:{delivery_ids}生成送货点失败, Error response:{self.response}')
        else:
            log.warning(f'发货批次:{delivery_ids}生成送货点失败, Error response:{self.response}')
        return self.response

    def auto_generate_and_send(self, time_delivery, check_result=False):
        """
        校验生成派车计划状态
        @param time_delivery: cut delivery time
        @param check_result:
        @return:
        """
        self.get(url=f'/wms-order/job/fpo/autoGenerateAndSend?time={time_delivery}', headers=central_header)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'Cutoff Time为:{time_delivery}生成排车计划成功')
        elif check_result:
            raise Exception(f'Cutoff Time为:{time_delivery}生成排车计划失败, Error response:{self.response}')
        else:
            log.warning(f'Cutoff Time为:{time_delivery}生成排车计划失败, Error response:{self.response}')
        return self.response

    def check_generate_point(self, delivery_ids: list, check_result=False):
        """
        检查生成送货点
        :param delivery_ids: 发货批次列表
        :param check_result:
        :return:
        """
        body = {
            "deliveryIds": delivery_ids
        }
        self.post(url='/tms/deliveryPointGenerate/checkStatus', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('检查生成送货点成功。')
        elif check_result:
            raise Exception(f'检查生成送货点失败, Error response:{self.response}')
        else:
            log.warning(f'检查生成送货点失败, Error response:{self.response}')
        return self.response

    def get_available_drivers(self, sub_region_id: int, delivery_date: str, check_result=False):
        """
        获取可用司机列表
        :param delivery_date: 配送日期
        :param sub_region_id: region ID
        :param check_result:
        :return:
        """
        body = {
            "subRegionId": sub_region_id,
            "deliveryDate": delivery_date
        }
        self.post(url='/tms/deliveryPlanDispatch/listAvailableDrivers', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取可用司机列表成功。')
        elif check_result:
            raise Exception(f'获取可用司机列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取可用司机列表失败, Error response:{self.response}')
        return self.response

    def print_dispatch_info(self, delivery_plan_id, check_result=False):
        """
        打印派车单
        :param delivery_plan_id: 发货计划ID
        :param check_result:
        :return:
        """
        body = {
            "deliveryPlanId": delivery_plan_id
        }
        self.post(url='/tms/deliveryPlanDispatch/print', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('打印派车单成功。')
        elif check_result:
            raise Exception(f'打印派车单失败, Error response:{self.response}')
        else:
            log.warning(f'打印派车单失败, Error response:{self.response}')
        return self.response

    def update_dispatch_driver(self, dispatch_id, driver_user_id, driver_user_name, check_result=False):
        """
        更新派车单司机
        :param dispatch_id: 派车单ID
        :param driver_user_id: 司机ID
        :param driver_user_name: 司机名称
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "driverUserId": driver_user_id,
            "driverUserName": driver_user_name,
            "comment": None
        }
        self.post(url='/tms/deliveryPlanDispatch/updateDrivers', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新派车单{dispatch_id}为司机{driver_user_name}-{driver_user_id}成功。')
        elif check_result:
            raise Exception(
                f'更新派车单{dispatch_id}为司机{driver_user_name}-{driver_user_id}失败, Error response:{self.response}')
        else:
            log.warning(
                f'更新派车单{dispatch_id}为司机{driver_user_name}-{driver_user_id}失败, Error response:{self.response}')
        return self.response

    def delete_dispatch(self, draft_id, check_result=False):
        """
        删除draft对应的派车单
        :param draft_id: 排车草稿ID
        :param check_result:
        :return:
        """
        self.delete(url=f'/tms/plan/delete/dispatch/{draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('删除draft对应的派车单成功。')
        elif check_result:
            raise Exception(f'删除draft对应的派车单失败, Error response:{self.response}')
        else:
            log.warning(f'删除draft对应的派车单失败, Error response:{self.response}')
        return self.response

    def delete_draft(self, draft_id, check_result=False):
        """
        删除draft
        :param draft_id: 排车草稿ID
        :param check_result:
        :return:
        """
        self.delete(url=f'/tms/plan/delete_deliver_plan_draft/{draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('删除draft成功。')
        elif check_result:
            raise Exception(f'删除draft失败, Error response:{self.response}')
        else:
            log.warning(f'删除draft失败, Error response:{self.response}')
        return self.response

    def get_route_region_id(self, check_result=False):
        """
        获取路线的RegionID
        :param check_result:
        :return:
        """
        self.get(url='/tms/plan/getDisplayRouteIdRegionId', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取路线的RegionID成功。')
        elif check_result:
            raise Exception(f'获取路线的RegionID失败, Error response:{self.response}')
        else:
            log.warning(f'获取路线的RegionID失败, Error response:{self.response}')
        return self.response

    def risk_address_pre_check(self, delivery_plan_id, check_result=False):
        """
        风险地址预校验
        :param delivery_plan_id: 派车计划ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/plan/pre-check/{delivery_plan_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('风险地址预校验成功。')
        elif check_result:
            raise Exception(f'风险地址预校验失败, Error response:{self.response}')
        else:
            log.warning(f'风险地址预校验失败, Error response:{self.response}')
        return self.response

    def get_subregion_by_draft(self, draft_id, check_result=False):
        """
        获取draft对应的subRegion信息
        :param draft_id: 排车草稿ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/route/get/subRegionInfo?draftId={draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取draft对应的subRegion信息成功。')
        elif check_result:
            raise Exception(f'获取draft对应的subRegion信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取draft对应的subRegion信息失败, Error response:{self.response}')
        return self.response

    def get_plan_detail(self, draft_id, check_result=False):
        """
        获取计划详情
        :param draft_id: Draft ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/plan/plan_detail?draft_id={draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取计划详情成功。')
        elif check_result:
            raise Exception(f'获取计划详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取计划详情失败, Error response:{self.response}')
        return self.response

    def get_draft_info(self, draft_id, check_result=False):

        """
        获取草稿详情信息
        :param draft_id: Draft ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id
        }
        self.post(url=f'/tms/plan/infoForDetailSettingResponse', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取草稿详情信息成功。')
        elif check_result:
            raise Exception(f'获取草稿详情信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取草稿详情信息失败, Error response:{self.response}')
        return self.response

    def route_change(self, route_rec_id, stop_ids, draft_id, new_route_rec_id=None, driver_id=None,
                     custom_route_id=None, check_result=False):
        """
        修改路线站点
        :param route_rec_id: 路线ID(原路线)
        :param stop_ids: 站点ID []
        :param draft_id: 草稿ID
        :param new_route_rec_id: 新路线ID(目标路线)
        :param driver_id: 司机ID
        :param custom_route_id: 新增的路线ID
        :param check_result:
        :return:
        """
        body = {
            "old_route_stops": [
                {
                    "route_rec_id": route_rec_id,
                    "stop_ids": stop_ids
                }
            ],
            "draft_id": draft_id,
            "new_route_rec_id": new_route_rec_id,
            "driver_id": driver_id,
            "customRouteId": custom_route_id
        }
        self.post(url='/tms/route/change', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('请求路线修改中...')
            return self.response
        elif check_result:
            raise Exception(f'修改路线站点失败, Error response:{self.response}')
        else:
            log.warning(f'修改路线站点失败, Error response:{self.response}')
            return False

    def v18_run_draft(self, delivery_plan_id: int, check_result=False):
        """
        v18算法执行
        :param delivery_plan_id: 发货计划ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": '',
            "setting": {
                "desc": "V18 + Yandex Bay Area",
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": 'false',
                    "max_duration_s": 410,
                    "test_key": True,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": 3,
                            "delivery_area": "BA-R2",
                            "setting": {
                                "avg_stop_IC": 40,
                                "num_of_IC": 0,
                                "avg_stop_W2": 65,
                                "num_of_W2": 4,
                                "avg_stop_default": 43,
                                "default_driver": 8
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": 'true'
                }
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1671741000,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v18算法执行成功。')
        elif check_result:
            raise Exception(f'v18算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v18算法执行失败, Error response:{self.response}')
        return self.response

    def v10_run_draft(self, delivery_plan_id: int, check_result=False):
        """
        v10算法执行
        :param delivery_plan_id: 发货计划ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": 'null',
            "setting": {
                "desc": "V10 + manual operation",
                "is_default": "",
                "version": 10,
                "calculation_param": {
                    "min_stop_num": 50,
                    "max_stop_num": 55,
                    "relative_distance": 1,
                    "use_system_route_id": 'true',
                    "wave_num": "",
                    "last_wave_max_stop_num": "",
                    "delivery_area_stop_num_setting": [],
                    "seed_move_threshold": "",
                    "iterations": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_driver_sign_up_capacity": 'true'
                },
                "driver_info": []
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1671395400,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v10算法执行成功。')
        elif check_result:
            raise Exception(f'v10算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v10算法执行失败, Error response:{self.response}')
        return self.response

    def v12_run_draft(self, delivery_plan_id: int, check_result=False):
        """
        v12算法执行
        :param delivery_plan_id: 发货计划ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": 'null',
            "setting": {
                "desc": "V12 + Restaurant manual operation",
                "is_default": "",
                "version": 12,
                "calculation_param": {
                    "min_stop_num": 50,
                    "max_stop_num": 55,
                    "relative_distance": 1,
                    "use_system_route_id": 'true'
                },
                "driver_info": []
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1670704200,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        log.info(f'Delivery Plan ID:{delivery_plan_id}开始执行v12算法...')
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v12算法执行成功。')
        elif check_result:
            raise Exception(f'v12算法执行失败,Response:{self.response}')
        else:
            log.warning(f'v12算法执行失败,Response:{self.response}')
        return self.response

    def v14_run_draft(self, delivery_plan_id: int, min_stop_num=35, max_stop_num=65, check_result=False):
        """
        v14算法执行(湾区)
        :param delivery_plan_id: 发货计划ID
        :param min_stop_num: 最小配送数
        :param max_stop_num: 最大配送数
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": 'null',
            "setting": {
                "desc": "V14 + By Vehicle Capacities & Automatically Assign Delivery Warehouse",
                "is_default": 'true',
                "version": 14,
                "calculation_param": {
                    "use_driver_sign_up_capacity": 'true',
                    "min_stop_num": min_stop_num,
                    "max_stop_num": max_stop_num,
                    "seed_move_threshold": 30,
                    "relative_distance": 1,
                    "p_driver_choice_radius": 20,
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": 'true',
                    "delivery_area_stop_num_setting": [
                        {
                            "delivery_area": "Sacramento",
                            "setting": {
                                "min_stop_num": 35,
                                "max_stop_num": 55
                            }
                        }
                    ],
                    "iterations": ""
                },
                "driver_info": []
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1668630600,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        log.info(f'Delivery Plan ID:{delivery_plan_id}开始执行v14算法...')
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v14算法执行成功。')
        elif check_result:
            raise Exception(f'v14算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v14算法执行失败, Error response:{self.response}')
        return self.response

    def run_v14(self, delivery_plan_id: int, min_stop_num=35, max_stop_num=65, check_result=False):
        """
        v14算法执行
        :param delivery_plan_id: 发货计划ID
        :param min_stop_num: 最小配送数
        :param max_stop_num: 最大配送数
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": 'null',
            "setting": {
                "desc": "V14 + By Vehicle Capacities & Automatically Assign Delivery Warehouse",
                "is_default": 'true',
                "version": 14,
                "calculation_param": {
                    "use_driver_sign_up_capacity": 'true',
                    "min_stop_num": min_stop_num,
                    "max_stop_num": max_stop_num,
                    "seed_move_threshold": 30,
                    "relative_distance": 1,
                    "p_driver_choice_radius": 20,
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": 'true',
                    "delivery_area_stop_num_setting": [],
                    "iterations": ""
                },
                "driver_info": []
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1668630600,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v14算法执行成功。')
        elif check_result:
            raise Exception(f'v14算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v14算法执行失败, Error response:{self.response}')
        return self.response

    def get_draft_list(self, delivery_date=None, sub_region_ids=None, delivery_plan_id=None, delivery_type=None,
                       status=None, page_size=20, check_result=False):
        """
        获取草稿列表
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion IDs [1,2,3,4,38]
        :param delivery_plan_id: 发货计划ID
        :param delivery_type:
        :param page_size:
        :param status:
        :param check_result:
        :return:
        """
        body = {
            "delivery_date": delivery_date,
            "delivery_plan_id": delivery_plan_id,
            "subRegionIds": sub_region_ids,
            "status": status,
            "delivery_type": delivery_type,
            "pageNumber": 1,
            "pageSize": page_size
        }
        self.post(url='/tms/plan/query_deliver_plan_draft_list', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取草稿列表成功。')
        elif check_result:
            raise Exception(f'获取草稿列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取草稿列表失败, Error response:{self.response}')
        return self.response

    def get_replan_dispatches(self, draft_id, check_result=False):
        """
        获取可以replan的路线信息
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/routeReplan/requiresReplanDispatches?draftId={draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取Draft:{draft_id}下可以replan的路线信息成功。')
        elif check_result:
            raise Exception(f'获取Draft:{draft_id}下可以replan的路线信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取Draft:{draft_id}下可以replan的路线信息失败, Error response:{self.response}')
        return self.response

    def create_replan_point_mapping(self, draft_id, dispatch_ids, check_result=False):
        """
        创建Replan站点路线映射
        :param draft_id:
        :param dispatch_ids: 列表, [866473, 866474, 866475, 866477, 866478]
        :param check_result:
        :return:
        """
        body = {
            "draftId": draft_id,
            "dispatchIds": dispatch_ids
        }
        self.post(url='/tms/routeReplan/createReplanPointsMapping', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'创建Replan站点路线映射成功。')
        elif check_result:
            raise Exception(f'创建Replan站点路线映射失败, Error response:{self.response}')
        else:
            log.warning(f'创建Replan站点路线映射失败, Error response:{self.response}')
        return self.response

    def get_replan_drivers(self, pre_draft_id, check_result=False):
        """
        获取Replan可用的司机信息
        :param pre_draft_id: 预排车草稿ID
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/routeReplan/getDriversBeforeCreateDraft?preDraftId={pre_draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取Replan预排车草稿:{pre_draft_id}可用的司机信息成功。')
        elif check_result:
            raise Exception(f'获取Replan预排车草稿:{pre_draft_id}可用的司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取Replan预排车草稿:{pre_draft_id}可用的司机信息失败, Error response:{self.response}')
        return self.response

    def get_replan_pre_route_info(self, pre_draft_id, check_result=False):
        """
        获取Replan预排车草稿的路线信息
        :param pre_draft_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/routeReplan/getOriginalRouteInfo?preDraftId={pre_draft_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取Replan预排车草稿:{pre_draft_id}的路线信息成功。')
        elif check_result:
            raise Exception(f'获取Replan预排车草稿:{pre_draft_id}的路线信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取Replan预排车草稿:{pre_draft_id}的路线信息失败, Error response:{self.response}')
        return self.response

    def create_replan_draft(self, pre_draft_id: str, total_points_count: int, original_routes_seq: list,
                            start_route_no: int, assign_driver_list: list, replan_route_num=3, check_result=False):
        """
        创建Replan Draft
        :param pre_draft_id: 预排车草稿ID
        :param total_points_count: 总站点数量
        :param original_routes_seq: 原路线信息 []
        :param start_route_no: 新路线起始路线号
        :param assign_driver_list: 可分配司机列表 []
        :param replan_route_num: replan路线数量
        :param check_result:
        :return:
        """
        # 计算每个路线的站点数量, 当不能整除时, 将余数分配给第一个路线
        # 取余
        remainder_points = total_points_count % replan_route_num
        # 向下取整
        points_count = int(total_points_count / replan_route_num)
        new_routes = []
        for i in range(replan_route_num):
            if i == 0:
                new_routes.append({
                    "routeNo": start_route_no,
                    "driverUserId": assign_driver_list[i],
                    "splitRoute": False,
                    "pointCount": points_count + remainder_points})
            else:
                new_routes.append({
                    "routeNo": start_route_no + i,
                    "driverUserId": assign_driver_list[i],
                    "splitRoute": False,
                    "pointCount": points_count})
        body = {
            "preDraftId": pre_draft_id,
            "originalRoutesSeq": original_routes_seq,
            "newRoutes": new_routes
        }
        self.post(url='/tms/routeReplan/createDraft', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'创建Replan Draft成功。')
        elif check_result:
            raise Exception(f'创建Replan Draft失败, Error response:{self.response}')
        else:
            log.warning(f'创建Replan Draft失败, Error response:{self.response}')
        return self.response

    def get_replan_original_dispatch_ids(self, route_rec_id, check_result=False):
        """
        获取Replan原路线下的派车单ID
        :param route_rec_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/draft/get/replanOriginalDispatchIds?routeRecId={route_rec_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取Replan原路线:{route_rec_id}下的派车单ID成功。')
        elif check_result:
            raise Exception(f'获取Replan原路线:{route_rec_id}下的派车单ID失败, Error response:{self.response}')
        else:
            log.warning(f'获取Replan原路线:{route_rec_id}下的派车单ID失败, Error response:{self.response}')
        return self.response

    def change_start_and_end_location(self, draft_id, route_rec_id, origin_dispatch_ids, address_info,
                                      check_result=False):
        """
        修改路线起始点及终止点位置
        :param draft_id:
        :param route_rec_id:
        :param origin_dispatch_ids:
        :param address_info: address_info值从tms_db.get_pick_up_location_info中获取
        :param check_result:
        :return:
        """
        route_locations = []
        for dispatch_id in origin_dispatch_ids:
            route_locations.append({
                "locationType": "start",
                "originDispatchId": origin_dispatch_ids[dispatch_id],
                "address": address_info[0],
                "lat": float(address_info[1]),
                "lon": float(address_info[2])
            })
        body = {
            "draftId": draft_id,
            "routeRecId": route_rec_id,
            "routeLocationVOs": route_locations
        }
        self.post(url='/tms/route/changeStartAndEndLocation', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'修改路线起始点及终止点位置成功。')
        elif check_result:
            raise Exception(f'修改路线起始点及终止点位置失败, Error response:{self.response}')
        else:
            log.warning(f'修改路线起始点及终止点位置失败, Error response:{self.response}')
        return self.response

    def get_delivery_plan_list(self, delivery_date, sub_region_ids, check_result=False):
        """
        获取派车计划列表
        :param delivery_date: 配送日期
        :param sub_region_ids: subregion IDs [1,2,3,4,38]
        :param check_result:
        :return:
        """
        body = {
            "subRegionIds": sub_region_ids,
            "delivery_date": delivery_date,
            "action": 1
        }
        self.post(url='/tms/plan/query_plan_ids', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取派车计划列表成功。')
        elif check_result:
            raise Exception(f'获取派车计划列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车计划列表失败, Error response:{self.response}')
        return self.response

    def get_process_for_draft_run(self, draft_id=None, check_result=False):
        """
        获取排车草稿基础信息
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            'draft_id': draft_id
        }
        self.post(url='/tms/plan/getProcessForDraftRun', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取排车草稿基础信息成功。')
        elif check_result:
            raise Exception(f'获取排车草稿基础信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取排车草稿基础信息失败, Error response:{self.response}')
        return self.response

    def get_pre_draft_setting_before_run(self, delivery_plan_id, check_result=False):
        """
        获取排车草稿基础信息
        :param delivery_plan_id: 派车计划ID
        :param check_result:
        :return:
        """
        body = {
            'delivery_plan_id': delivery_plan_id
        }
        self.post(url='/tms/plan/preDraftInfoForSettingBeforeRun', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取排车草稿基础信息成功。')
        elif check_result:
            raise Exception(f'获取排车草稿基础信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取排车草稿基础信息失败, Error response:{self.response}')
        return self.response

    def cancel_draft(self, draft_id, check_result=False):
        """
        取消派车计划草稿
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id
        }
        self.post(url='/tms/plan/draft/cancel', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('取消派车计划草稿成功。')
        elif check_result:
            raise Exception(f'取消派车计划草稿失败, Error response:{self.response}')
        else:
            log.warning(f'取消派车计划草稿失败, Error response:{self.response}')
        return self.response

    def get_vender(self, delivery_plan_id, check_result=False):
        """
        获取Vender信息
        :param delivery_plan_id: 派车计划ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id
        }
        self.post(url='/tms/plan/draft/vender', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取Vender信息成功。')
        elif check_result:
            raise Exception(f'获取Vender信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取Vender信息失败, Error response:{self.response}')
        return self.response

    def clear_driver(self, draft_id, route_rec_ids, check_result=False):
        """
        清除路线司机
        :param route_rec_ids: 路线IDs[]
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id,
            "route_rec_ids": route_rec_ids,
            "action": 1
        }
        self.post(url='/tms/driver/clear', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('清除路线司机成功。')
        elif check_result:
            raise Exception(f'清除路线司机失败, Error response:{self.response}')
        else:
            log.warning(f'清除路线司机失败, Error response:{self.response}')
        return self.response

    def change_route_region(self, sub_region_id, route_rec_id, check_result=False):
        """
        修改路线region
        :param route_rec_id: 路线ID
        :param sub_region_id: subregion ID
        :param check_result:
        :return:
        """
        body = {
            "route_rec_id": route_rec_id,
            "sub_region_id": sub_region_id
        }
        self.post(url='/tms/route/set/start_inventory', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('修改路线region成功。')
        elif check_result:
            raise Exception(f'修改路线region失败, Error response:{self.response}')
        else:
            log.warning(f'修改路线region失败, Error response:{self.response}')
        return self.response

    def get_route_drivers(self, delivery_plan_id, draft_id, rec_id=None, check_result=False):
        """
        获取路线可用司机
        :param delivery_plan_id: 派车计划ID
        :param draft_id: 草稿ID
        :param rec_id: 路线ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": draft_id,
            "rec_id": rec_id,
            "specialMark": 'null'
        }
        self.post(url='/tms/driver/drivers', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取路线可用司机成功。')
        elif check_result:
            raise Exception(f'获取路线可用司机失败, Error response:{self.response}')
        else:
            log.warning(f'获取路线可用司机失败, Error response:{self.response}')
        return self.response

    def get_change_result(self, draft_id, check_result=False):
        """
        获取路线修改结果
        :param draft_id:
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id
        }
        self.post(url='/tms/route/getChangeResult', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取草稿{draft_id}路线修改结果状态正常')
            return self.response
        elif check_result:
            raise Exception(f'获取草稿{draft_id}路线修改结果状态失败, Error response:{self.response}')
        else:
            log.warning(f'获取草稿{draft_id}路线修改结果状态失败, Error response:{self.response}')
            return False

    def split_flex_route(self, draft_id, check_result=False):
        """
        拆分Flex路线
        :param draft_id:
        :param check_result:
        :return:
        """
        body = {
            "draftId": draft_id
        }
        self.post(url='/tms/route/splitFlexMiniVan', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'草稿{draft_id}路线拆分正常')
        elif check_result:
            raise Exception(f'草稿{draft_id}路线拆分失败, Error response:{self.response}')
        else:
            log.warning(f'草稿{draft_id}路线拆分失败, Error response:{self.response}')
        return self.response

    def set_routes_default_driver(self, draft_id, check_result=False):
        """
        将所有路线设置为默认司机
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id
        }
        self.post(url='/tms/route/setDefaultDriver', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('将所有路线设置为默认司机成功。')
        elif check_result:
            raise Exception(f'将所有路线设置为默认司机失败, Error response:{self.response}')
        else:
            log.warning(f'将所有路线设置为默认司机失败, Error response:{self.response}')
        return self.response

    def re_sequence_route(self, draft_id, edit_user='7642085', check_result=False):
        """
        路线一键排序
        :param edit_user: 编辑人员ID
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id,
            "edit_user": edit_user
        }
        self.post(url='/tms/route/re_sequence_routeId', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('路线一键排序成功。')
        elif check_result:
            raise Exception(f'路线一键排序失败, Error response:{self.response}')
        else:
            log.warning(f'路线一键排序失败, Error response:{self.response}')
        return self.response

    def get_draft_routes(self, draft_id, check_result=False):
        """
        获取草稿路线信息
        :param draft_id: 草稿ID
        :param check_result:
        :return:
        """
        body = {
            "draft_id": draft_id
        }
        self.post(url='/tms/route/route_ids', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取草稿路线信息成功。')
        elif check_result:
            raise Exception(f'获取草稿路线信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取草稿路线信息失败, Error response:{self.response}')
        return self.response

    def get_draft_points(self, draft_id, delivery_plan_id, route_nums, check_result=False):
        """
        获取配送点信息
        :param delivery_plan_id:
        :param draft_id:
        :param route_nums: 路线号[[1, 2, 3, 4,]
        :param check_result:
        :return:
        """
        if isinstance(route_nums, int):
            route_nums = [route_nums]
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": draft_id,
            "routes": route_nums,
            "subRegionIds": [],
            "timeOffset": -1728000
        }
        self.post(url='/tms/plan/draft/points', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取配送点信息成功。')
        elif check_result:
            raise Exception(f'获取配送点信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取配送点信息失败, Error response:{self.response}')
        return self.response

    def change_route_driver(self, route_rec_id, draft_id, driver_user_id, driver_name, check_result=False):
        """
        修改路线司机
        :param route_rec_id: 路线ID
        :param draft_id: 草稿ID
        :param driver_user_id: 司机ID
        :param driver_name: 司机名称
        :param check_result:
        :return:
        """
        body = {
            "route_rec_id": route_rec_id,
            "draft_id": draft_id,
            "route_name": "",
            "driver_user_id": driver_user_id,
            "driver_name": driver_name,
            "route_id": ""
        }
        self.post(url='/tms/route/change_route_info', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('修改路线司机成功。')
        elif check_result:
            raise Exception(f'修改路线司机失败, Error response:{self.response}')
        else:
            log.warning(f'修改路线司机失败, Error response:{self.response}')
        return self.response

    def get_driver_info_1(self, delivery_plan_id, sub_region_id, driver_nums, check_result=False):
        """
        V18算法查询排车司机信息-湾区单region
        :param delivery_plan_id:
        :param sub_region_id:
        :param driver_nums:
        :param check_result:
        :return:
        """
        region_info = ["BA-R1a", "BA-R1b", "BA-R2", "BA-SAC"]
        delivery_area_stop_info = {
            "delivery_area": region_info[sub_region_id - 1],
            "sub_region_id": sub_region_id,
        }
        delivery_area_stop_info.update(driver_nums)
        body = {
            "deliveryPlanId": delivery_plan_id,
            "deliveryAreaStopInfos": [delivery_area_stop_info]
        }
        self.post(url='/tms/plan/draft/getDriverInfo', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('V18算法查询排车司机信息成功。')
        elif check_result:
            raise Exception(f'V18算法查询排车司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'V18算法查询排车司机信息失败, Error response:{self.response}')
        return self.response

    def get_driver_info_2(self, delivery_plan_id, stop_num_setting, check_result=False):
        """
        V18算法查询排车司机信息-多region
        :param delivery_plan_id:
        :param stop_num_setting:
        :param check_result:
        :return:
        """
        delivery_area_stop_info = []
        if isinstance(stop_num_setting, list):
            for i in stop_num_setting:
                delivery_area_stop = {
                    "delivery_area": i['delivery_area'],
                    "sub_region_id": i['sub_region_id'],
                }
                delivery_area_stop.update(i['setting'])
                delivery_area_stop_info.append(delivery_area_stop)
        else:
            delivery_area_stop = {
                "delivery_area": stop_num_setting['delivery_area'],
                "sub_region_id": stop_num_setting['sub_region_id'],
            }
            delivery_area_stop.update(stop_num_setting['setting'])
            delivery_area_stop_info.append(delivery_area_stop)
        body = {
            "deliveryPlanId": delivery_plan_id,
            "deliveryAreaStopInfos": delivery_area_stop_info,
            "first": True
        }
        self.post(url='/tms/plan/draft/getDriverInfo', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('V18算法查询排车司机信息成功。')
        elif check_result:
            raise Exception(f'V18算法查询排车司机信息失败, Error response:{self.response}')
        else:
            log.warning(f'V18算法查询排车司机信息失败, Error response:{self.response}')
        return self.response

    def run_flex_v18(self, delivery_plan_id, sub_region_id, sub_region_name, flex_suv_stop, flex_suv_num, w2_stop,
                     w2_num, df_stop, df_num, check_result=False):
        """
        v18算法执行-flex
        :param delivery_plan_id:
        :param sub_region_id:
        :param sub_region_name:
        :param flex_suv_stop:
        :param flex_suv_num:
        :param w2_stop:
        :param w2_num:
        :param df_stop:
        :param df_num:
        :param check_result:
        :return:
        """

        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": None,
            "setting": {
                "desc": "V18 + Yandex NJ",
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": False,
                    "test_key": True,
                    "max_duration_s": 540,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": sub_region_id,
                            "delivery_area": sub_region_name,
                            "setting": {
                                "avg_stop_W2": w2_stop,
                                "num_of_W2": w2_num,
                                "avg_stop_flex_Suv": flex_suv_stop,
                                "num_of_flex_Suv": flex_suv_num,
                                "avg_stop_default": df_stop,
                                "default_driver": df_num
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": True
                },
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1669926600,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('flex v18算法执行成功。')
        elif check_result:
            raise Exception(f'flex v18算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'flex v18算法执行失败, Error response:{self.response}')
        return self.response

    def run_ic_complaince_v18(self, delivery_plan_id, sub_region_id, sub_region_name, mini_van_stop, mini_van_num,
                              w2_stop, w2_num, df_stop, df_num, suv_stop, suv_num, check_result=False):
        """
        v18算法执行-单Region IC Complaince
        :param delivery_plan_id: 配送计划ID
        :param sub_region_id: 区域ID
        :param sub_region_name: 区域名称
        :param mini_van_stop: flex_mini_van司机配送数量
        :param mini_van_num: flex_mini_van司机数量
        :param suv_stop: suv司机配送数量
        :param suv_num: suv司机数量
        :param w2_num: w2司机配送数量
        :param w2_stop: w2司机数量
        :param df_stop: default司机配送数量
        :param df_num: default司机数量
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": None,
            "setting": {
                "desc": "V18 + Yandex Bay Area",
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": False,
                    "max_duration_s": 540,
                    "test_key": True,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": sub_region_id,
                            "delivery_area": sub_region_name,
                            "setting": {
                                "avg_stop_W2": w2_stop,
                                "num_of_W2": w2_num,
                                "avg_stop_flex_Mini_Van": mini_van_stop,
                                "num_of_flex_Mini_Van": mini_van_num,
                                "avg_stop_flex_Suv": suv_stop,
                                "num_of_flex_Suv": suv_num,
                                "avg_stop_default": df_stop,
                                "default_driver": df_num
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": True
                },
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1671741000,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('ic complaince v18算法执行成功。')
        elif check_result:
            raise Exception(f'ic complaince v18算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'ic complaince v18算法执行失败, Error response:{self.response}')
        return self.response

    def run_v18(self, delivery_plan_id, sub_region_id, sub_region_name, ic_stop, ic_num, w2_stop, w2_num, df_stop,
                df_num, check_result=False):
        """
        v18算法执行-单Region
        :param delivery_plan_id: 配送计划ID
        :param sub_region_id: 区域ID
        :param sub_region_name: 区域名称
        :param ic_stop: ic司机配送数量
        :param ic_num: ic司机数量
        :param w2_stop: w2司机配送数量
        :param w2_num: w2司机数量
        :param df_stop: default司机配送数量
        :param df_num: default司机数量
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": '',
            "setting": {
                "desc": 'V18 + Yandex',
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": 'false',
                    "max_duration_s": 540,
                    "test_key": True,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": sub_region_id,
                            "delivery_area": sub_region_name,
                            "setting": {
                                "avg_stop_IC": ic_stop,
                                "num_of_IC": ic_num,
                                "avg_stop_W2": w2_stop,
                                "num_of_W2": w2_num,
                                "avg_stop_default": df_stop,
                                "default_driver": df_num
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": 'true'
                }
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1671741000,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v18算法执行成功。')
        elif check_result:
            raise Exception(f'v18算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v18算法执行失败, Error response:{self.response}')
        return self.response

    def run_v18_2(self, delivery_plan_id, sub_region_id, sub_region_name, ic_stop, ic_num, w2_stop, w2_num, df_stop,
                  df_num, sub_region_id_2, sub_region_name_2, ic_stop_2, ic_num_2, w2_stop_2, w2_num_2, df_stop_2,
                  df_num_2, total_stop, total_stop_2, check_result=False):
        """
        v18算法执行-双Region
        :param total_stop_2:
        :param total_stop:
        :param delivery_plan_id:
        :param sub_region_id:
        :param sub_region_name:
        :param ic_stop:
        :param ic_num:
        :param w2_stop:
        :param w2_num:
        :param df_stop:
        :param df_num:
        :param sub_region_id_2:
        :param sub_region_name_2:
        :param ic_stop_2:
        :param ic_num_2:
        :param w2_stop_2:
        :param w2_num_2:
        :param df_stop_2:
        :param df_num_2:
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": '',
            "setting": {
                "desc": "V18 + Yandex settle",
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": False,
                    "test_key": True,
                    "max_duration_s": 540,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": sub_region_id,
                            "delivery_area": sub_region_name,
                            "setting": {
                                "avg_stop_IC": ic_stop,
                                "num_of_IC": ic_num,
                                "avg_stop_W2": w2_stop,
                                "num_of_W2": w2_num,
                                "avg_stop_default": df_stop,
                                "default_driver": df_num
                            }
                        },
                        {
                            "sub_region_id": sub_region_id_2,
                            "delivery_area": sub_region_name_2,
                            "setting": {
                                "avg_stop_IC": ic_stop_2,
                                "num_of_IC": ic_num_2,
                                "avg_stop_W2": w2_stop_2,
                                "num_of_W2": w2_num_2,
                                "avg_stop_default": df_stop_2,
                                "default_driver": df_num_2
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": True
                },
                "driver_info": [
                    {
                        "numberOfIc": ic_num,
                        "numOfW2": w2_num,
                        "totalStop": total_stop,
                        "deliverArea": sub_region_name,
                        "subRegionId": sub_region_id,
                        "serviceableTotalStops": w2_num * w2_stop + ic_num * ic_stop,
                        "sort": 1,
                        "detailInfos": [
                            {
                                "driverType": "F",
                                "driverTypeDesc": "W2",
                                "driverNum": w2_num,
                                "driverStopNum": w2_stop,
                                "totalStop": w2_num * w2_stop,
                                "rowKey": "0"
                            },
                            {
                                "driverType": "P",
                                "driverTypeDesc": "IC",
                                "driverNum": ic_num,
                                "driverStopNum": ic_stop,
                                "totalStop": ic_num * ic_stop,
                                "rowKey": "1"
                            },
                            {
                                "driverType": "D",
                                "driverTypeDesc": "Default",
                                "driverNum": df_num,
                                "driverStopNum": df_stop,
                                "totalStop": df_num * df_stop,
                                "rowKey": "2"
                            }
                        ],
                        "rowKey": "0"
                    },
                    {
                        "numberOfIc": ic_num_2,
                        "numOfW2": w2_num_2,
                        "totalStop": total_stop_2,
                        "deliverArea": sub_region_name_2,
                        "subRegionId": sub_region_id_2,
                        "serviceableTotalStops": w2_num_2 * w2_stop_2 + ic_num_2 * ic_stop_2,
                        "sort": 2,
                        "detailInfos": [
                            {
                                "driverType": "F",
                                "driverTypeDesc": "W2",
                                "driverNum": w2_num_2,
                                "driverStopNum": w2_stop_2,
                                "totalStop": w2_num_2 * w2_stop_2,
                                "rowKey": "0"
                            },
                            {
                                "driverType": "P",
                                "driverTypeDesc": "IC",
                                "driverNum": ic_num_2,
                                "driverStopNum": ic_stop_2,
                                "totalStop": ic_num_2 * ic_stop_2,
                                "rowKey": "1"
                            },
                            {
                                "driverType": "D",
                                "driverTypeDesc": "Default",
                                "driverNum": df_num_2,
                                "driverStopNum": df_stop_2,
                                "totalStop": df_num_2 * df_stop_2,
                                "rowKey": "2"
                            }
                        ],
                        "rowKey": "1"
                    }
                ]
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1670877000,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v18算法执行成功。')
        elif check_result:
            raise Exception(f'v18算法执行失败, Error response:{self.response}')
        else:
            log.warning(f'v18算法执行失败, Error response:{self.response}')
        return self.response

    def run_v18_3(self, delivery_plan_id, sub_region_id, sub_region_name, ic_stop, ic_num, w2_stop, w2_num, df_stop,
                  df_num, sub_region_id_2, sub_region_name_2, ic_stop_2, ic_num_2, w2_stop_2, w2_num_2, df_stop_2,
                  df_num_2, sub_region_id_3, sub_region_name_3, ic_stop_3, ic_num_3, w2_stop_3, w2_num_3, df_stop_3,
                  df_num_3, total_stop, total_stop_2, total_stop_3, check_result=False):
        """
        v18算法执行-三Region
        :param delivery_plan_id:
        :param sub_region_id:
        :param sub_region_name:
        :param ic_stop:
        :param ic_num:
        :param w2_stop:
        :param w2_num:
        :param df_stop:
        :param df_num:
        :param sub_region_id_2:
        :param sub_region_name_2:
        :param ic_stop_2:
        :param ic_num_2:
        :param w2_stop_2:
        :param w2_num_2:
        :param df_stop_2:
        :param df_num_2:
        :param sub_region_id_3:
        :param sub_region_name_3:
        :param ic_stop_3:
        :param ic_num_3:
        :param w2_stop_3:
        :param w2_num_3:
        :param df_stop_3:
        :param df_num_3:
        :param total_stop:
        :param total_stop_2:
        :param total_stop_3:
        :param check_result:
        :return:
        """
        body = {
            "delivery_plan_id": delivery_plan_id,
            "draft_id": '',
            "setting": {
                "desc": "V18 + Yandex NJ",
                "is_default": "",
                "version": 18,
                "calculation_param": {
                    "use_driver_sign_up_capacity": False,
                    "test_key": True,
                    "max_duration_s": 540,
                    "delivery_area_stop_num_setting": [
                        {
                            "sub_region_id": sub_region_id,
                            "delivery_area": sub_region_name,
                            "setting": {
                                "avg_stop_IC": ic_stop,
                                "num_of_IC": ic_num,
                                "avg_stop_W2": w2_stop,
                                "num_of_W2": w2_num,
                                "avg_stop_default": df_stop,
                                "default_driver": df_num
                            }
                        },
                        {
                            "sub_region_id": sub_region_id_2,
                            "delivery_area": sub_region_name_2,
                            "setting": {
                                "avg_stop_IC": ic_stop_2,
                                "num_of_IC": ic_num_2,
                                "avg_stop_W2": w2_stop_2,
                                "num_of_W2": w2_num_2,
                                "avg_stop_default": df_stop_2,
                                "default_driver": df_num_2
                            }
                        },
                        {
                            "sub_region_id": sub_region_id_3,
                            "delivery_area": sub_region_name_3,
                            "setting": {
                                "avg_stop_IC": ic_stop_3,
                                "num_of_IC": ic_num_3,
                                "avg_stop_W2": w2_stop_3,
                                "num_of_W2": w2_num_3,
                                "avg_stop_default": df_stop_3,
                                "default_driver": df_num_3
                            }
                        }
                    ],
                    "min_stop_num": "",
                    "max_stop_num": "",
                    "seed_move_threshold": "",
                    "iterations": "",
                    "relative_distance": "",
                    "p_driver_choice_radius": "",
                    "p_driver_starting_capacity": "",
                    "use_system_route_id": True
                },
                "driver_info": [
                    {
                        "numberOfIc": ic_num,
                        "numOfW2": w2_num,
                        "totalStop": total_stop,
                        "deliverArea": sub_region_name,
                        "subRegionId": sub_region_id,
                        "serviceableTotalStops": w2_num * w2_stop + ic_num * ic_stop,
                        "sort": 1,
                        "detailInfos": [
                            {
                                "driverType": "F",
                                "driverTypeDesc": "W2",
                                "driverNum": w2_num,
                                "driverStopNum": w2_stop,
                                "totalStop": w2_num * w2_stop,
                                "rowKey": "0"
                            },
                            {
                                "driverType": "P",
                                "driverTypeDesc": "IC",
                                "driverNum": ic_num,
                                "driverStopNum": ic_stop,
                                "totalStop": ic_num * ic_stop,
                                "rowKey": "1"
                            },
                            {
                                "driverType": "D",
                                "driverTypeDesc": "Default",
                                "driverNum": df_num,
                                "driverStopNum": df_stop,
                                "totalStop": df_num * df_stop,
                                "rowKey": "2"
                            }
                        ],
                        "rowKey": "0"
                    },
                    {
                        "numberOfIc": ic_num_2,
                        "numOfW2": w2_num_2,
                        "totalStop": total_stop_2,
                        "deliverArea": sub_region_name_2,
                        "subRegionId": sub_region_id_2,
                        "serviceableTotalStops": w2_num_2 * w2_stop_2 + ic_num_2 * ic_stop_2,
                        "sort": 2,
                        "detailInfos": [
                            {
                                "driverType": "F",
                                "driverTypeDesc": "W2",
                                "driverNum": w2_num_2,
                                "driverStopNum": w2_stop_2,
                                "totalStop": w2_num_2 * w2_stop_2,
                                "rowKey": "0"
                            },
                            {
                                "driverType": "P",
                                "driverTypeDesc": "IC",
                                "driverNum": ic_num_2,
                                "driverStopNum": ic_stop_2,
                                "totalStop": ic_num_2 * ic_stop_2,
                                "rowKey": "1"
                            },
                            {
                                "driverType": "D",
                                "driverTypeDesc": "Default",
                                "driverNum": df_num_2,
                                "driverStopNum": df_stop_2,
                                "totalStop": df_num_2 * df_stop_2,
                                "rowKey": "2"
                            }
                        ],
                        "rowKey": "1"
                    },
                    {
                        "numberOfIc": ic_num_3,
                        "numOfW2": w2_num_3,
                        "totalStop": total_stop_3,
                        "deliverArea": sub_region_name_3,
                        "subRegionId": sub_region_id_3,
                        "serviceableTotalStops": w2_num_3 * w2_stop_3 + ic_num_3 * ic_stop_3,
                        "sort": 2,
                        "detailInfos": [
                            {
                                "driverType": "F",
                                "driverTypeDesc": "W2",
                                "driverNum": w2_num_3,
                                "driverStopNum": w2_stop_3,
                                "totalStop": w2_num_3 * w2_stop_3,
                                "rowKey": "0"
                            },
                            {
                                "driverType": "P",
                                "driverTypeDesc": "IC",
                                "driverNum": ic_num_3,
                                "driverStopNum": ic_stop_3,
                                "totalStop": ic_num_3 * ic_stop_3,
                                "rowKey": "1"
                            },
                            {
                                "driverType": "D",
                                "driverTypeDesc": "Default",
                                "driverNum": df_num_3,
                                "driverStopNum": df_stop_3,
                                "totalStop": df_num_3 * df_stop_3,
                                "rowKey": "2"
                            }
                        ],
                        "rowKey": "1"
                    }
                ]
            },
            "thresholdSetting": {
                "max_order_fee": 4800,
                "max_box_count": 80,
                "max_distance": 100,
                "max_delivery_time": 600,
                "late_time": 1670877000,
                "min_stop_num": 50,
                "max_stop_num": 55,
                "total_working_time": 480
            }
        }
        self.post(url='/tms/plan/draft/run', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info('v18算法三Region执行成功。')
        elif check_result:
            raise Exception(f'v18算法三Region执行失败, Error response:{self.response}')
        else:
            log.warning(f'v18算法三Region执行失败, Error response:{self.response}')
        return self.response

    def create_dispatch(self, draft_id, check_result=False):
        """
        应用派车单
        :param draft_id:
        :param check_result:
        :return:
        """
        body = {
            "draftId": draft_id,
            "autoGenerate": False,
            "sendWms": False}
        self.put(url='/tms/plan/use_deliver_plan_draft', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'将Draft:{draft_id}应用派车单成功。')
        elif check_result:
            raise Exception(f'将Draft:{draft_id}应用派车单失败, Error response:{self.response}')
        else:
            log.warning(f'将Draft:{draft_id}应用派车单失败, Error response:{self.response}')
        return self.response

    def hot_dish_initialize(self, delivery_id, check_result=False):
        """
        初始化热送包裹
        :param delivery_id: 批次ID
        :param check_result:
        :return:
        """
        body = {
            "delivery_id": delivery_id
        }
        self.post(url='/tms/package/hot_dish/initialize', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'批次{delivery_id}初始化热送包裹成功。')
        elif check_result:
            raise Exception(f'批次{delivery_id}初始化热送包裹失败, Error response:{self.response}')
        else:
            log.warning(f'批次{delivery_id}初始化热送包裹失败, Error response:{self.response}')
        return self.response

    def address_pre_check(self, delivery_plan_id, check_result=False):
        """
        地址预校验
        :param delivery_plan_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/plan/pre-check/{delivery_plan_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'派车计划{delivery_plan_id}地址预校验成功.')
        elif check_result:
            raise Exception(f'派车计划{delivery_plan_id}地址预校验失败, Error response:{self.response}')
        else:
            log.warning(f'派车计划{delivery_plan_id}地址预校验失败, Error response:{self.response}')
        return self.response
