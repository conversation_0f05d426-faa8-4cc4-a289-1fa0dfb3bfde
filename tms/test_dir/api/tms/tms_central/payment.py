import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath
from tms.test_dir.api.tms import central_header


class Payment(weeeTest.TestCase):
    """
    TMS Central平台Payment菜单相关接口
    """

    def get_pay_adjustment_list(self, region_id, start_date, end_date, driver_type="ALL", status='All',
                                check_result=False):
        """
        获取pay adjustment记录
        :param region_id: 0,1,2,3,4,5,6,7
        :param start_date:
        :param end_date:
        :param driver_type: ALL, W2, IC/Flex
        :param status: ALL, Pending, Approved, Rejected
        :param check_result:
        :return:
        """
        body = {
            "regionId": region_id,
            "startDate": start_date,
            "endDate": end_date,
            "deliveryType": "Grocery",
            "driverType": driver_type,
            "status": status,
            "downloadStatus": "All"
        }
        self.post(url='/tms/adjustment/list/record', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取pay adjustment记录成功')
        elif check_result:
            raise Exception(f'获取pay adjustment记录失败, Error response:{self.response}')
        else:
            log.warning(f'获取pay adjustment记录失败, Error response:{self.response}')
        return self.response

    def get_adjustment_record(self, fee_id, check_result=False):
        """
        获取adjustment fee信息
        :param fee_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/adjustment/record?id={fee_id}', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取adjustment fee信息成功')
        elif check_result:
            raise Exception(f'获取adjustment fee信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取adjustment fee信息失败, Error response:{self.response}')
        return self.response

    def add_adjustment_record(self, driver_user_id, delivery_date, adjustment_fee, reason_code, check_result=False):
        """
        添加adjustment fee信息
        :param driver_user_id:
        :param delivery_date:
        :param adjustment_fee:
        :param reason_code:
        :param check_result:
        :return:
        """
        body = {
            "driverUserId": driver_user_id,
            "deliveryDate": delivery_date,
            "adjustmentFee": f"{adjustment_fee}",
            "reasonCode": f"{reason_code}"
        }
        self.post(url=f'/tms/adjustment/record', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'添加adjustment fee信息成功')
        elif check_result:
            raise Exception(f'添加adjustment fee信息失败, Error response:{self.response}')
        else:
            log.warning(f'添加adjustment fee信息失败, Error response:{self.response}')
        return self.response

    def approve_adjustment_fee(self, fee_id, operate, check_result=False):
        """
        审核adjustment fee信息
        :param fee_id:
        :param operate
        :param check_result:
        :return:
        """
        body = {"id": fee_id, "operate": operate}
        self.put(url=f'/tms/adjustment/approve', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'审核adjustment fee信息成功')
        elif check_result:
            raise Exception(f'审核adjustment fee信息失败, Error response:{self.response}')
        else:
            log.warning(f'审核adjustment fee信息失败, Error response:{self.response}')
        return self.response
