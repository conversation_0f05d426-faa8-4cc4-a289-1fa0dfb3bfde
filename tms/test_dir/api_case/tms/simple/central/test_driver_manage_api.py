# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_driver_manage.py
@Description    :  
@CreateTime     :  2023/7/20 13:57
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/20 13:57
"""
import weeeTest
import allure
from weeeTest import jmespath

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestDriverManageApi(weeeTest.TestCase):
    """
    DriverManage菜单相关接口
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS-Online', 'TMS', 'DriverManageApi')
    @allure.title("获取指定日期及Region下所有的Dispatch信息")
    def test_get_dispatch_info(self):
        """
        获取指定日期及Region下所有的Dispatch信息
        """
        # 账户登录
        tms.login()
        # 获取派车单信息
        tms.central.driver_manage.get_dispatch_info(sub_region_id=1, delivery_date='2022-12-10', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS-Online', 'TMS', 'DriverManageApi')
    @allure.title("获取DeliveryInvoice信息")
    def test_get_invoice_info(self):
        """
        获取DeliveryInvoice信息
        """
        tms.login()
        tms.central.driver_manage.get_invoice_info(delivery_type='Normal', delivery_date='2022-11-11', route_ids=[101],
                                                   sub_region_id=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("Job生成缺失的GPS轨迹文件")
    def test_generate_track_file(self):
        """
        Job生成缺失的GPS轨迹文件
        """
        tms.central.driver_manage.generate_track_file(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("获取OfferList详情")
    def test_get_offer_list(self):
        """
        获取OfferList详情
        """
        tms.central.driver_manage.get_offer_list(dispatch_id=477331, group_point_id=45, delivery_date='2022-12-22',
                                                 sub_region_id=1, dispatch_type='Normal', status='planed',
                                                 check_result=True)

    @weeeTest.mark.list('TMS-Online', 'DriverManageApi')
    @allure.title("获取OfferList详情")
    def test_get_offer_list_online(self):
        """
        获取OfferList详情
        """
        tms.login()
        tms.central.driver_manage.get_offer_list(delivery_date='2024-12-22', sub_region_id=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'DriverManageApi')
    @allure.title("查看司机详情")
    def test_search_driver_by_id(self):
        """
        查看司机详情
        """
        tms.central.driver_manage.search_driver_by_id(driver_user_id=7180613, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'DriverManageApi')
    @allure.title("查询司机列表")
    def test_get_driver_list(self):
        """
        查询司机列表
        """
        tms.central.driver_manage.get_driver_list(sub_region_id_list=[1, 2, 3, 4], status="A", driver_type="F",
                                                  check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("更新司机账户状态")
    def test_update_driver_info(self):
        """
        更新司机账户状态
        """
        tms.central.driver_manage.update_driver_info(sub_region_id=3, driver_user_id=2289173, driver_name='FS5-FS-5',
                                                     driver_type='P', status='A', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'DriverManageApi')
    @allure.title("获取司机signup列表")
    def test_get_signup_list(self):
        """
        获取司机signup列表
        """
        tms.central.driver_manage.get_signup_list(sub_region_id=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("审核司机signup状态")
    def test_update_signup(self):
        """
        审核司机signup状态
        """
        tms.login()
        tms.tms_db.update_signup(signup_id=438778)
        tms.central.driver_manage.update_signup(signup_id=438778, status='P', assigned_address_total=45,
                                                group_point_id=1, driver_level='normal', check_result=True)
        sign_info = tms.tms_db.get_signup_info(signup_id=438778)
        assert sign_info == (1, 'P', 45)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("生成Schedule数据")
    def test_generate_schedule(self):
        """
        生成Schedule数据
        """
        delivery_data = tms.util.get_date(days=27)
        tms.tms_db.delete_signup(driver_user_id=10923029, delivery_date=delivery_data)
        assert not tms.tms_db.get_signup_id(driver_user_id=10923029, delivery_date=delivery_data)
        tms.central.driver_manage.generate_schedule(check_result=True)
        tms.util.wait(5)
        assert tms.tms_db.get_signup_id(driver_user_id=10923029, delivery_date=delivery_data)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("提交Schedule数据")
    def test_save_driver_schedule(self):
        """
        提交Schedule数据
        """
        scheduled = [10430367, 7708279, 9336716, 9449746, 8253690, 9981471, 7708284, 9449756, 7708280, 8324848,
                     10553137, 9677316, 9336726, 9249047, 10149761, 7878758, 10243587, 10829626, 10087225,
                     10890247, 7739733, 8555566, 10605969, 10479528, 7663689, 10445601, 9981479, 10444253,
                     8454469, 8454473, 9514713, 10605929, 10087251, 9177031, 10398418, 9981513, 9772226,
                     9772237, 10087233, 9677262, 9177042, 10087239, 10861307, 10374142, 9677303, 10243579, 10447480,
                     10865218, 7676824, 9336702, 7707780, 8324878, 9772260, 7935370, 9177087, 10243567, 9772257,
                     7722511, 10580954, 8253699, 9677293]
        not_scheduled = [7533517, 7633229, 7689074, 7707758, 7707760, 7728556, 7763272, 7838117, 7838118,
                         7867382, 7867392, 8324854, 8663353, 8555624, 8565455, 8160378, 7643889, 9677247, 9677298,
                         9677329, 9992169, 10087244, 10087248, 10149741, 10149753, 10149759, 10149766, 10243556,
                         10243570, 10158965, 10374150, 10355598, 10243582, 10445634, 10580961, 10623861, 10665937,
                         10654584, 10698754, 10776470, 10784229, 10825794, 10857250, 10923090, 10928060]
        tms.central.driver_manage.save_driver_schedule(delivery_date='2023-04-03',
                                                       scheduled_drivers=scheduled,
                                                       not_scheduled_drivers=not_scheduled, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'DriverManageApi')
    @allure.title("获取某一天司机的排版列表")
    def test_get_driver_schedule_list(self):
        """
        获取某一天司机的排版列表
        """
        tms.central.driver_manage.get_driver_schedule_list(delivery_date='2023-07-25', sub_region_ids=[1, 2, 3],
                                                           check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online','DriverManageApi')
    @allure.title("获取某一周的司机schedule信息")
    def test_get_schedule_list(self):
        """
        获取某一周的司机schedule信息
        """
        tms.central.driver_manage.get_schedule_list(region_id=1, start_date='2023-04-02', end_date="2023-04-08",
                                                    sub_region_ids=[1, 2, 3, 4, 38], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online','DriverManageApi')
    @allure.title("获取schedule配置")
    def test_get_schedule_config(self):
        """
        获取schedule配置
        """
        tms.central.driver_manage.get_schedule_config(region_id=1, sub_region_ids=[1, 2, 3, 4, 38], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改司机schedule配置")
    def test_edit_schedule_config(self):
        """
        修改司机schedule配置
        """
        tms.central.driver_manage.edit_schedule_config(driver_user_id=7533517, days=[1, 2, 3, 4, 5], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS-Online','TMS', 'DriverManageApi')
    @allure.title("获取各Region W2司机的报名信息")
    def test_get_w2_mod_info(self):
        """
        获取各Region W2司机的报名信息
        """
        delivery_date = tms.util.get_date()
        tms.central.driver_manage.get_w2_mod_info(delivery_date=delivery_date, delivery_region_id=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("查询待审核司机详情")
    def test_get_pending_driver_info(self):
        """
        查询待审核司机详情
        """
        tms.central.driver_manage.get_pending_driver_info(driver_user_id=7748221, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online','DriverManageApi')
    @allure.title("获取待审核司机列表")
    def test_get_pending_driver_list(self):
        """
        获取待审核司机列表
        """
        tms.central.driver_manage.get_pending_driver_list(sub_region_id=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("将司机置为待审核")
    def test_approve_driver_review(self):
        """
        将司机置为待审核
        """
        tms.central.driver_manage.approve_driver(driver_user_id=7738400, apply_status='A', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("将司机置为审核通过")
    def test_approve_driver_pass(self):
        """
        将司机置为审核通过
        """
        tms.central.driver_manage.approve_driver(driver_user_id=7738400, apply_status='P', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("删除司机多设备登录权限")
    def test_delete_multi_device(self):
        """
        删除司机多设备登录权限
        """
        tms.central.driver_manage.delete_multi_device(driver_user_id=10923090, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("添加司机多设备登录权限")
    def test_add_multi_device(self):
        """
        添加司机多设备登录权限
        """
        tms.central.driver_manage.add_multi_device(driver_user_id=10923090, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("获取多设备登录账户列表")
    def test_get_multi_device_list(self):
        """
        获取多设备登录账户列表
        """
        tms.central.driver_manage.get_multi_device_list(driver_user_id=7738400, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS','TMS-Online', 'DriverManageApi')
    @allure.title("获取司机反馈列表")
    def test_get_feedback_list(self):
        """
        获取司机反馈列表
        """
        tms.central.driver_manage.get_feedback_list(driver_user_id=10923090, name=None, status='N', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("审核反馈建议")
    def test_update_feedback(self):
        """
        审核反馈建议
        """
        tms.login()
        feedback_list_res = tms.central.driver_manage.get_feedback_list(driver_user_id=10923090)
        feedback_info = tms.util.get_response_values(feedback_list_res, 'rec_id[0]', 'content[0]')
        # 审核反馈内容
        tms.central.driver_manage.update_feedback(feedback_info[0], 10923090, feedback_info[1], check_result=True)
        # 校验审核状态为Y
        assert tms.tms_db.get_feedback_status(rec_id=feedback_info[0]) == "Y"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("获取派车单task列表")
    def test_get_task_list(self):
        """
        获取派车单task列表
        """
        tms.central.driver_manage.get_task_list(dispatch_id=global_data.dispatch_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi' ,'login_phone')
    @allure.title("修改司机电话为已用电话")
    def test_update_login_phone(self):
        """
        修改司机电话为已用电话
        """
        res = tms.central.driver_manage.update_login_phone(login_phone=5802910343, check_result=True)
        msg = jmespath(res, "message")
        assert "13347611 - LiuBo" in msg

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
