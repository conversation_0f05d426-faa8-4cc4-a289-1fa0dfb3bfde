# Author: bo.liu
# Time: 2025/5/21 15:19
# E-Mail: <EMAIL>

import allure
import weeeTest
from weeeTest import jmespath, log

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestCapacityApi(weeeTest.TestCase):
    mapping = {'w2': 'F', 'flex': 'Flex', 'ic': 'P', 'od': 'Od', 'dsp': 'Dsp'}
    # 查询LA地址的运力数据 (region=2 表示LA)
    region = 2
    # 根据region获取所有的sub region
    sub_region_ids = tms.tms_db.get_sub_region_ids(region)
    #获取代码运行环境参数
    is_prod = global_data.is_prod

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CapacityApi', 'TMS-Online')
    @allure.title('查询当天以及未来7天的运力统计数据')
    @allure.description('查询当天以及未来7天的运力统计数据，并在tb1验证接口返回数据与数据库数据一致')
    def test_query_capacity(self):
        """
        测试查询当天及未来7天的运力统计数据
        """
        # 登录系统
        tms.login()

        # 获取日期范围
        today = tms.util.get_date(days=0)
        seven_days = tms.util.get_date(days=6)

        # 获取运力数据
        capacity_list = tms.central.capacity.query_capacity_plan(
            sub_region_ids=self.sub_region_ids,
            start_date=today,
            end_date=seven_days,
            check_result=True
        )
        assert capacity_list["result"] is True, f"获取运力数据失败: {capacity_list.get('message', '')}"
        if not self.is_prod:
            # 取出返回的当天的所有sub region的接口返回数据
            res = jmespath(capacity_list, "object[0].scheduleCapacity")
            assert res, "接口返回的scheduleCapacity数据为空"

            # 取出所有sub region的不同司机的运力数据进行比较
            for i, sub_region_id in enumerate(self.sub_region_ids):
                # 从数据库获取该sub_region的运力数据
                schedule_capacity = tms.tms_db.get_capacity(sub_region_id, day=today)
                assert schedule_capacity is not None, f"未能从数据库获取sub_region_id={sub_region_id}的运力数据"

                # 从接口返回中获取对应的运力摘要
                capacity_summary = res[i]["capacitySummary"]

                # 将数据库返回的数据转换为字典格式，便于比较
                list_values = {item[0]: item[1] for item in schedule_capacity}

                # 比较每种类型司机的运力数据
                for key, list_key in self.mapping.items():
                    dict_value = capacity_summary[key]
                    list_value = list_values.get(list_key, 0)
                    assert dict_value == list_value, (
                        f"sub_region_id={sub_region_id}, 司机类型={key}({list_key})的运力数据不一致: "
                        f"接口返回={dict_value}, 数据库={list_value}"
                    )

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CapacityApi', 'TMS-Online')
    @allure.title('查询指定的sub region的7天的运力统计数据')
    @allure.description('查询指定的sub region的7天的运力统计数据并在tb1验证与数据库一致')
    def test_query_capacity_summary_one_region_seven_days(self):
        """
        测试查询指定sub region的7天运力统计数据
        """
        # 获取日期范围
        today = tms.util.get_date(days=0)
        seven_days = tms.util.get_date(days=6)

        # 获取运力数据
        capacity_summary_list = tms.central.capacity.query_capacity_summary(
            sub_region_ids=self.sub_region_ids[0:1],
            start_date=today,
            end_date=seven_days,
            check_result=True
        )
        assert capacity_summary_list[
                   "result"] is True, f"获取运力摘要数据失败: {capacity_summary_list.get('message', '')}"
        if not self.is_prod:
            # 取出返回的当天的sub region的接口返回数据
            capacity_summary = jmespath(capacity_summary_list, "object[0].subRegionSummaryList[0]")
            assert capacity_summary, "接口返回的subRegionSummaryList数据为空"

            # 取出sub region的不同司机的数量进行比较
            sub_region_id = self.sub_region_ids[0]  # 修复：使用sub_region_ids[0]替代未定义的sub_region_id
            schedule_capacity = tms.tms_db.get_capacity(sub_region_id, day=today)
            assert schedule_capacity is not None, f"未能从数据库获取sub_region_id={sub_region_id}的运力数据"

            # 将数据库返回的数据转换为字典格式，便于比较
            # 注意这里使用item[2]而不是item[1]，表示获取司机数量而非运力值
            list_values = {item[0]: item[2] for item in schedule_capacity}

            # 比较每种类型司机的数量数据
            for key, list_key in self.mapping.items():
                dict_value = capacity_summary[key + 'Num']
                list_value = list_values.get(list_key, 0)
                assert dict_value == list_value, (
                    f"sub_region_id={sub_region_id}, 司机类型={key}({list_key})的数量数据不一致: "
                    f"接口返回={dict_value}, 数据库={list_value}"
                )

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CapacityApi', 'TMS-Online')
    @allure.title('查询指定大region下的所有sub region的当天运力统计数据')
    @allure.description('查询指定大region下的所有sub region的当天运力统计数据并在tb1验证与数据库一致')
    def test_query_capacity_summary_all_region_one_day(self):
        """
        测试查询指定大region下所有sub region的当天运力统计数据
        """

        # 获取当天日期
        today = tms.util.get_date(days=0)

        # 获取运力数据
        capacity_summary_list = tms.central.capacity.query_capacity_summary(
            sub_region_ids=self.sub_region_ids,
            start_date=today,
            end_date=today,
            check_result=True
        )
        assert capacity_summary_list[
                   "result"] is True, f"获取运力摘要数据失败: {capacity_summary_list.get('message', '')}"

        if not self.is_prod:
            # 取出返回的当天的所有sub region的接口返回数据
            sub_region_summary_list = jmespath(capacity_summary_list, "object[0].subRegionSummaryList")
            assert sub_region_summary_list, "接口返回的subRegionSummaryList数据为空"
            assert len(sub_region_summary_list) == len(self.sub_region_ids), (
                f"接口返回的sub region数量({len(sub_region_summary_list)})与请求的数量({len(self.sub_region_ids)})不一致"
            )

            # 取出所有sub region的不同司机的数量数据进行比较
            for i, sub_region_id in enumerate(self.sub_region_ids):
                # 从数据库获取该sub_region的运力数据
                schedule_capacity = tms.tms_db.get_capacity(sub_region_id, day=today)
                assert schedule_capacity is not None, f"未能从数据库获取sub_region_id={sub_region_id}的运力数据"

                # 从接口返回中获取对应的运力摘要
                capacity_summary = sub_region_summary_list[i]

                # 将数据库返回的数据转换为字典格式，便于比较
                list_values = {item[0]: item[2] for item in schedule_capacity}

                # 比较每种类型司机的运力数据
                for key, list_key in self.mapping.items():
                    dict_value = capacity_summary[key + 'Num']
                    list_value = list_values.get(list_key, 0)
                    assert dict_value == list_value, (
                        f"sub_region_id={sub_region_id}, 司机类型={key}({list_key})的运力数据不一致: "
                        f"接口返回={dict_value}, 数据库={list_value}"
                    )

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CapacityApi', 'TMS-Online')
    @allure.title('查询指定大region下的所有sub region的7天运力统计数据')
    @allure.description('查询指定大region下的所有sub region的7天运力统计数据并在tb1验证与数据库一致')
    def test_query_capacity_summary_all_region_seven_days(self):
        """
        测试查询指定大region下所有sub region的7天运力统计数据
        """
        # 获取日期范围
        today = tms.util.get_date(days=0)
        seven_days = tms.util.get_date(days=6)

        # 获取运力数据
        capacity_summary_list = tms.central.capacity.query_capacity_summary(
            sub_region_ids=self.sub_region_ids,
            start_date=today,
            end_date=seven_days,
            check_result=True
        )
        assert capacity_summary_list[
                   "result"] is True, f"获取运力摘要数据失败: {capacity_summary_list.get('message', '')}"
        if not self.is_prod:
            # 验证返回的数据包含7天的数据
            date_summary_list = jmespath(capacity_summary_list, "object")
            assert date_summary_list, "接口返回的object数据为空"
            assert len(date_summary_list) > 0, "接口返回的日期数据为空"

            # 取出返回的当天的所有sub region的接口返回数据
            first_day_summary = date_summary_list[0]
            sub_region_summary_list = jmespath(first_day_summary, "subRegionSummaryList")
            assert sub_region_summary_list, "接口返回的subRegionSummaryList数据为空"
            assert len(sub_region_summary_list) == len(self.sub_region_ids), (
                f"接口返回的sub region数量({len(sub_region_summary_list)})与请求的数量({len(sub_region_ids)})不一致"
            )

            # 取出所有sub region的不同司机的数量数据进行比较
            for i, sub_region_id in enumerate(self.sub_region_ids):
                # 从数据库获取该sub_region的运力数据
                schedule_capacity = tms.tms_db.get_capacity(sub_region_id, day=today)
                assert schedule_capacity is not None, f"未能从数据库获取sub_region_id={sub_region_id}的运力数据"

                # 从接口返回中获取对应的运力摘要
                capacity_summary = sub_region_summary_list[i]

                # 将数据库返回的数据转换为字典格式，便于比较
                list_values = {item[0]: item[2] for item in schedule_capacity}

                # 比较每种类型司机的数量数据
                for key, list_key in self.mapping.items():
                    dict_value = capacity_summary[key + 'Num']
                    list_value = list_values.get(list_key, 0)
                    assert dict_value == list_value, (
                        f"sub_region_id={sub_region_id}, 司机类型={key}({list_key})的数量数据不一致: "
                        f"接口返回={dict_value}, 数据库={list_value}"
                    )

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CapacityApi', 'TMS-Online')
    @allure.title('查询指定sub region的司机排班信息')
    @allure.description('查询指定sub region的司机排班信息并在tb1验证当天的capacity值与数据库一致')
    def test_query_driver_shift(self):
        """
        测试查询指定sub region的司机排班信息
        """
        # 获取日期范围
        today = tms.util.get_date(days=0)
        seven_days = tms.util.get_date(days=6)

        # 获取司机排班信息
        driver_shift_result = tms.central.capacity.query_driver_shift(
            sub_region_ids=self.sub_region_ids,
            start_date=today,
            end_date=seven_days,
            status="scheduled",
            check_result=True
        )
        assert driver_shift_result["result"] is True, f"获取司机排班信息失败: {driver_shift_result.get('message', '')}"
        if not self.is_prod:
            # 验证返回的数据结构
            driver_data = jmespath(driver_shift_result, "object.data")
            assert driver_data is not None, "接口返回的司机数据为空"

            # 记录验证的司机数量
            driver_count = len(driver_data)
            log.info(f"获取到 {driver_count} 个司机的排班信息")

            # 循环验证每个司机的排班信息
            for driver in driver_data:
                driver_user_id = driver.get("driverUserId")
                driver_name = driver.get("driverName")
                driver_type = driver.get("type")
                driver_sub_region_id = driver.get("driverSubRegionId")

                log.info(
                    f"验证司机 {driver_name}(ID:{driver_user_id}, 类型:{driver_type}, 区域:{driver_sub_region_id})的排班信息")

                # 获取司机的排班列表
                schedules = driver.get("schedules", [])
                assert schedules, f"司机 {driver_name}(ID:{driver_user_id}) 没有排班信息"

                # 筛选出当天的排班信息
                today_schedules = [s for s in schedules if s.get("deliveryDate") == today]

                if not today_schedules:
                    log.warning(f"司机 {driver_name}(ID:{driver_user_id}) 在当天 {today} 没有排班信息")
                    continue

                # 验证当天的排班信息
                for schedule in today_schedules:
                    sub_region_id = schedule.get("subRegionId")
                    capacity = schedule.get("capacity")

                    # 从数据库获取该司机在当天的capacity值
                    db_capacity_data = tms.tms_db.get_driver_capacity(driver_user_id, today)
                    assert db_capacity_data is not None, f"未能从数据库获取driver_user_id={driver_user_id}的运力数据"
                    db_sub_region_id, db_capacity = db_capacity_data
                    # 校验司机当天有排班的region和capacity和数据库是一致的
                    assert sub_region_id == db_sub_region_id, f"api_sub_region_id={sub_region_id}, db_sub_region_id={db_sub_region_id}"
                    assert capacity == db_capacity, f"api_capacity={capacity}, db_capacity={db_capacity}"
                    log.info(
                        f"验证司机 {driver_name}(ID:{driver_user_id}) 在当天 {today} 的排班是在sub_region{sub_region_id}，且返回的capacity值一致: 接口返回={capacity}, 数据库={db_capacity}")
