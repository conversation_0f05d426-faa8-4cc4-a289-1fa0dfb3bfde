# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_fleet_management_api.py
@Description    :  
@CreateTime     :  2024/12/16 13:51
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/16 13:51
"""
import weeeTest
import allure
from tms.test_dir.api.tms.tms import tms


class TestFleetManageApi(weeeTest.TestCase):
    """
    车队管理测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("获取车辆品牌信息")
    def test_get_vehicle_type(self):
        """
        获取车辆品牌信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_type(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("获取车辆DVIR信息列表")
    def test_get_vehicle_inspection_list(self):
        """
        获取车辆DVIR信息列表
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_inspection_list(vehicle_id=80, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("获取车辆使用记录信息")
    def test_get_vehicle_inspection_list(self):
        """
        获取车辆使用记录信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_operation_history(vehicle_id=80, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("按时间顺序获取车辆品牌信息")
    def test_get_vehicle_type_sorted_select(self):
        """
        按时间顺序获取车辆品牌信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_type_sorted_select(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManageApi')
    @allure.title("获取车辆列表")
    def test_edit_vehicle_info(self):
        """
        获取车辆列表
        """
        # 账号登录
        tms.login()
        # 获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=2, vehicle_status=1,
                                                              vehicle_type=15, vehicle_number="W09531",
                                                              license_plate="3288941", check_result=True)
        vehicle_vin = tms.util.get_response_values(vehicle_list_res, 'vehicleVin')[0]
        assert vehicle_vin == "rs123"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("获取单个车辆信息")
    def test_get_vehicle_info(self):
        """
        获取单个车辆信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_info(vehicle_id=80, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManageApi')
    @allure.title("获取车辆类型信息")
    def test_get_vehicle_type_list(self):
        """
        获取车辆类型信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_type_list(vehicle_make="Honda", vehicle_model="24", vehicle_year=2024,
                                                check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'FleetManageApi')
    @allure.title("获取车辆筛选信息")
    def test_get_vehicle_select_data(self):
        """
        获取车辆筛选信息
        """
        # 账号登录
        tms.login()
        tms.central.fleet.get_vehicle_select_data(check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
