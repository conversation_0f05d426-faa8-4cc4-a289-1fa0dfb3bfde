# Author: bo.liu
# Time: 2025/2/6 17:41
# E-Mail: <EMAIL>
# 实现新增课程、查询课程、修改课程、预览课程、执行生成课程task job 流程
# 司机安全培训功能下线，注释代码，待后续删除
# import allure
# import weeeTest
# from weeeTest import jmespath
# from tms.qa_config import global_data,case_data
# from tms.test_dir.api.tms.tms import tms
# from tms.test_dir.api.tms.tms_central.course import Course
#
#
# class TestCourseApi(weeeTest.TestCase):
#     course = Course()
#     tms.login()
#     title = global_data.course_title
#     title_edit = global_data.course_title_edite
#     fileLink = global_data.fileLink
#     driver_user_id = global_data.course_driver_user_id
#
#     # 新增课程
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
#     @allure.title('新增课程')
#     @allure.description('删除表中已有的课程和task,然后新增课程')
#     def test_add_course(self):
#         """
#          新增课程
#          清除表中已有的课程和task
#         """
#         tms.tms_db.delete_course_and_task(title=self.title)
#         # 调接口
#         data = {"title":self.title,
#                 "fileLink":'https://video.weeecdn.com/tms/video/516/344/12C22669FAECC318.mp4',
#                 "id":None,
#                 "deliveryRegionIds":'2',
#                 "subject": "Driver safety",
#                 "contentType": 1,
#                 "timeValidity":False,
#                 "fileExtension":'mp4',
#                 "cycleType":1,
#                 "newDriverCycle": True,
#                 "newDriverCycleDay": 7,
#                 "oldDriverCycle": False,
#                 "oldDriverCycleDay": None,
#                 "miniDurationTime":1}
#         res = self.course.add_and_edit_course(data)
#         # 检验返回result是True
#         assert res["result"] is True
#
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi' , 'TMS-Online')
#     @allure.title('查询课程')
#     @allure.description('查询课程列表，并校验title是否传入的一致')
#     def test_get_course_list(self):
#         """
#         获取课程列表
#         获取成功后取出id存入下一步编辑课程使用
#         """
#         # 调接口
#         res = self.course.get_course_list(title=self.title)
#         # 将拿到的课程id存入
#         setattr(case_data.CaseData,"id", jmespath(res, "object.data[0].id"))
#         # 需要校验返回的title是否与上一步创建课程输入的title一致
#         assert self.title in jmespath(res, "object.data[0].title")
#
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
#     @allure.title('修改课程')
#     @allure.description('修改课程，修改title和fileLink')
#     def test_edit_course(self):
#         """
#         修改课程
#         修改title和fileLink
#         """
#         data = {"title":self.title_edit,
#                 "fileLink":self.fileLink,
#                 "id":getattr(case_data.CaseData,"id"),
#                 "deliveryRegionIds":'2,5',
#                 "subject": "Driver safety",
#                 "contentType": 1,
#                 "timeValidity":False,
#                 "fileExtension":'mp4',
#                 "cycleType":1,
#                 "newDriverCycle": True,
#                 "newDriverCycleDay": 7,
#                 "oldDriverCycle": False,
#                 "oldDriverCycleDay": None
#                 }
#         res = self.course.add_and_edit_course(data)
#         # 检验返回result是True
#         assert res["result"] is True
#
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi' , 'TMS-Online')
#     @allure.title('预览课程')
#     @allure.description('预览课程')
#     def test_preview_course(self):
#         """
#         预览课程,并校验课程链接
#         """
#         # 调接口
#         res = self.course.preview_course(id=getattr(case_data.CaseData,"id"))
#         # 检验返回的fileLink是否与修改后的fileLink一致
#         assert res["object"] == self.fileLink
#
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
#     @allure.title('执行生成课程task job')
#     @allure.description('执行生成课程task job')
#     def test_create_course_task(self):
#         """
#         执行生成课程task job
#         """
#         # 先要处理司机的创建时间为前1小时--1-3 循环类型的课程需要处理司机创建时间为7天内
#         tms.tms_db.update_driver_create_time(driver_user_id=self.driver_user_id, offset_hour=-1)
#         # 执行job接口
#         self.course.create_course_task()
#
#     @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
#     @allure.title('增加当天开始有效课程失败校验')
#     @allure.description('增加当天开始有效课程失败校验')
#     def test_create_course_fail(self):
#         """
#         增加当天开始有效课程失败校验.日期是美西日期
#         """
#         # tms.login()
#         today = tms.util.get_us_date(zone='US/Pacific',days=0)
#         tomorrow = tms.util.get_us_date(zone='US/Pacific',days=1)
#         print("today:",today)
#         print(tomorrow)
#         data = {"deliveryRegionIds":"2",
#                 "subject":"Driver safety",
#                 "title":self.title,
#                 "contentType":1,
#                 "cycleType":2,
#                 "timeValidity":True,
#                 "enableStartTime":str(today),
#                 "enableEndTime":"2099-01-01",
#                 "fileExtension":"mp4",
#                 "fileLink":self.fileLink}
#         print("data",data)
#         res = self.course.add_and_edit_course(data)
#         # 检验返回result是True
#         assert res["result"] is False
#         assert res["message"] == F"The start time must be greater than or equal to {tomorrow}"
#
