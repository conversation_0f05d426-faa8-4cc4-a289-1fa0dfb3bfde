# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_driver_api.py
@Description    :
@CreateTime     :  2023/8/2 17:56
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/2 17:56
"""
import random

import allure
import weeeTest
from weeeTest import jmespath,log

from tms.qa_config import global_data,case_data
from tms.test_dir.api.tms.tms import tms


class TestDriverApi(weeeTest.TestCase):
    """
    DriverApp相关接口
    """
    # 获取运行环境参数
    is_prod = global_data.is_prod
    # 根据环境获取课程的title，这里做这个区分是因为tb1配置中
    title = global_data.course_title_edite
    fileLink = global_data.fileLink
    driver_user_id = global_data.course_driver_user_id
    course_driver_email = global_data.course_driver_email
    course_driver_pwd = global_data.course_driver_pwd
    w2_driver_email = global_data.w2_driver_email
    region_id = global_data.w2_region_id

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'TMS-Online')
    @allure.title('司机上班打卡')
    @allure.description('司机上班打卡,打卡前清除掉当天的打卡记录，且需要2次打卡成功')
    def test_driver_clock_in(self):
        """
        clock in 不在范围需要2次打卡成功
        """
        tms.login(driver_email=self.w2_driver_email)
        # 获取司机时区
        driver_info = tms.driver.get_driver_info()
        time_zone = driver_info['object']['timezone']
        driver_sub_region_id = driver_info['object']['sub_region_id']
        #获取subregion对应的alert_config配置经纬度
        latitude, longitude = tms.central.get_alert_config(region_id=self.region_id, sub_region_id=driver_sub_region_id ,check_result=True)
        # 获取司机打卡时区的当前时间
        my_date = tms.util.get_timezone_date(timezone=time_zone)
        if not self.is_prod:
            # tb1可以通过数据库清除已有的当天打卡记录
            tms.tms_db.delete_driver_time_card_log(self.driver_user_id, my_date)
        # 纬度+0.1使其第一次打卡上班失败
        res = tms.driver.driver_clock_in(latitude + 0.1, longitude, second_flag=False)
        if not res["result"]:
            res_second = tms.driver.driver_clock_in(latitude + 0.1, longitude, second_flag=True)
            assert res_second["object"]["workingStatus"] == "WORKING"

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'TMS-Online')
    @allure.title('司机下班打卡')
    @allure.description('司机下班打卡,打卡前需要更新上班打卡的时间，一次打卡成功')
    def test_driver_clock_out(self):
        """
        clock out 范围内1次打卡
        """
        tms.login(driver_email=self.w2_driver_email)
        # 获取司机时区
        driver_info = tms.driver.get_driver_info()
        time_zone = driver_info['object']['timezone']
        driver_sub_region_id = driver_info['object']['sub_region_id']
        # 获取subregion对应的alert_config配置经纬度
        latitude, longitude = tms.central.get_alert_config(region_id=self.region_id, sub_region_id=driver_sub_region_id)
        # 获取司机打卡时区的当前时间
        my_time = tms.util.get_timezone_datetime(timezone=time_zone, offset_minutes=-10)
        if not self.is_prod:
            # tb1修改CLOCKIN时间为10分钟之前
            tms.tms_db.update_driver_time_card_log(self.driver_user_id, my_time)
        else:
            tms.util.wait(sleep_time=241, reason='等待4分钟工作完成可以下班打开')
        # 打卡下班
        res = tms.driver.driver_clock_out(latitude, longitude, second_flag=False)
        assert res["object"]["workingStatus"] == "READY TO WORK"
    # 司机安全培训功能下线，注释代码，待后续删除
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi' ,'TMS-Online')
    # @allure.title('判断司机是否有未完成课程')
    # def test_has_unfinished_courses(self):
    #     """
    #      判断司机是否有未完成课程
    #     """
    #     #调试的时候需要sleep 等待课程task任务创建完成
    #     # tms.util.wait(sleep_time=120, reason='等待task生成完成')
    #     # 登陆指定司机的账号
    #     tms.login(driver_email=global_data.course_driver_email,driver_password=global_data.course_driver_pwd)
    #     allure.attach(F"登陆的司机是{global_data.course_driver_email}")
    #     # 通过返回的结果是true判断获取司机有未完成课程
    #     res = tms.driver.has_unfinished_courses(check_result=True)
    #     assert jmespath(res, "object") == True, "司机没有未完成课程"
    #     # tms.login(driver_email='<EMAIL>')
    #
    # # 新增课程
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi' , 'TMS-Online')
    # @allure.title('获取司机的培训课程')
    # def test_get_course_list(self):
    #     """
    #      获取司机的所有培训课程
    #     """
    #     # 登陆指定司机的账号
    #     # tms.login(driver_email=self.course_driver_email,driver_password=self.course_driver_pwd)
    #     # 获取司机账号信息，校验enforceTrainingCourse是True-此时app需要弹窗提示司机进行课程学习
    #     res_account = tms.driver.get_driver_info()
    #     if self.is_prod:
    #         assert not res_account['object']['enforceTrainingCourse']
    #     else:
    #         assert res_account['object']['enforceTrainingCourse']
    #     # 获取司机的培训课程
    #     res = tms.driver.driver_get_course_list()
    #     # 循环课程列表，获取课程名称为指定的课程的courseTaskId并保存到临时文件
    #     courses = jmespath(res, "object")
    #     for course in courses:
    #         log.info(F"进来了这个循环，这个course是{course['courseTitle']}")
    #         if course['courseTitle'] == self.title:
    #             log.info("符合条件的出现了")
    #             setattr(case_data.CaseData,"courseTaskId",course['courseTaskId'])
    #             log.info(F"写入的courseTaskId是{course['courseTaskId']}")
    #             # 校验课程的状态是
    #             assert course['taskStatus'] == 'todo'
    #     # tms.login(driver_email='<EMAIL>')
    #
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi' , 'TMS-Online')
    # @allure.title('获取司机的培训课程详情')
    # def test_get_course_detail(self):
    #     """
    #      获取司机的培训课程详情
    #     """
    #     course_task_id = getattr(case_data.CaseData,"courseTaskId")
    #     log.info(F"课程id{course_task_id}")
    #     # 获取司机的培训课程
    #     res = tms.driver.driver_get_course_detail(id=course_task_id)
    #     # 检验获取到的课程内容
    #     course_content = jmespath(res, "object.courseContent")
    #     assert course_content == self.fileLink
    #
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    # @allure.title('司机开始课程学习')
    # def test_update_course_process(self):
    #     """
    #      司机开始课程学习
    #     """
    #     # 从临时文件中获取课程taskid
    #     course_task_id = getattr(case_data.CaseData,"courseTaskId")
    #     # 取当前的utc时间为开始时间
    #     start_time = tms.util.get_time()
    #     # 调用开始学习接口
    #     res = tms.driver.update_driver_course_process(course_task_id, start_time=start_time)
    #     # 重新获取司机的培训课程列表
    #     res = tms.driver.driver_get_course_list()
    #     # 循环课程列表，获取课程名称为指定的课程的courseTaskId并保存到临时文件
    #     courses = jmespath(res, "object")
    #     for course in courses:
    #         if course['courseTitle'] == self.title:
    #             # 校验课程的状态是进行中
    #             assert course['taskStatus'] == 'processing'
    #
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    # @allure.title('司机结束课程并重新获取课程状态')
    # def test_update_course_status(self):
    #     """
    #      司机结束课程并重新获取课程状态
    #     """
    #     # 从临时文件中获取课程taskid
    #     course_task_id = getattr(case_data.CaseData,"courseTaskId")
    #     # 获取司机的培训课程
    #     start_time = tms.util.get_time() - 65
    #     end_time = tms.util.get_time()
    #     res = tms.driver.update_driver_course_finish(courseTaskId=course_task_id, startTime=start_time,
    #                                                  endTime=end_time, durationTime=1)
    #     # 重新获取司机的培训课程列表
    #     res = tms.driver.driver_get_course_list()
    #     # 循环课程列表，获取课程名称为指定的课程的courseTaskId并保存到临时文件
    #     courses = jmespath(res, "object")
    #     for course in courses:
    #         if course['courseTitle'] == self.title:
    #             # 校验课程的状态是FINISH
    #             assert course['taskStatus'] == 'finished'

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机Feedback')
    def test_add_feedback(self):
        """
        司机Feedback
        """
        # 账户登录
        tms.login(driver_email="<EMAIL>")
        # 提交司机反馈
        tms.driver.add_feedback(content="Feedback By Api Test.", check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取司机账号信息')
    def test_get_driver_info(self):
        """
        获取司机账号信息
        """
        tms.driver.get_driver_info(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机signup普通单')
    def test_save_signup_delivery(self):
        """
        司机signup普通单
        """
        date = tms.util.get_date(days=1)
        tms.driver.save_signup(signup_type='fresh', address_total=35, delivery_date=date, check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机取消signup')
    def test_cancel_signup(self):
        """
        司机取消signup
        """
        tms.login()
        date = tms.util.get_date(days=1)
        driver_user_id = tms.get_driver_info(info='driver_user_id')
        signup_id = tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=date)
        tms.driver.cancel_signup(signup_id=signup_id, check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取当前账户的派车单列表')
    def test_get_dispatch_list(self):
        """
        获取当前账户的派车单列表
        """
        tms.driver.get_dispatch_list(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取当前账户的派车单详情')
    def test_get_dispatch_detail(self):
        """
        获取当前账户的派车单详情
        """
        tms.login()
        tms.driver.get_dispatch_detail(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取打包进度')
    def test_get_package_progress(self):
        """
        获取打包进度
        """
        tms.driver.get_package_progress(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('上送配送开始里程表信息')
    def test_report_start_mileage(self):
        """
        上送配送开始里程表信息
        """
        dispatch_id = global_data.dispatch_id
        # 大于900小于1000的随机数进行初始开始里程数据
        start_mileage_distance = random.randint(900, 999)
        tms.driver.report_start_mileage(dispatch_id=dispatch_id, start_mileage_distance=start_mileage_distance,
                                        check_result=True)
        # 校验开始里程数据入库成功
        assert tms.tms_db.get_start_end_mileage_distance(dispatch_id=dispatch_id)[0] == start_mileage_distance, "结果不一致"

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('上送配送结束里程表信息')
    def test_report_end_mileage(self):
        """
        上送配送结束里程表信息
        """
        dispatch_id = global_data.dispatch_id
        # 大于1000小于1100的随机数进行初始结束里程数据
        end_mileage_distance = random.randint(1000, 1100)
        tms.driver.report_end_mileage(dispatch_id=dispatch_id, end_mileage_distance=end_mileage_distance,
                                      check_result=True)
        # 校验结束里程数据入库成功
        assert tms.tms_db.get_start_end_mileage_distance(dispatch_id=dispatch_id)[1] == end_mileage_distance, "结果不一致"

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'PassWord')
    @allure.title('修改司机的登陆密码')
    @allure.description('测试司机修改密码功能，验证密码修改成功后能够使用新密码登录')
    def test_update_password(self):
        """
        修改司机的登陆密码并验证
        """
        # 测试数据准备
        email = global_data.pwd_driver_email
        old_password = global_data.pwd_driver_password
        new_password = "1234abcd" + "2025"
        # 确保使用原密码可以正常登录
        tms.login(driver_email=email, driver_password=old_password)
        try:
            # 修改密码
            tms.driver.update_password(email=email, old_password=old_password, new_password=new_password,
                                       check_result=True)
            # 使用新密码登录验证修改成功
            tms.login(driver_email=email, driver_password=new_password)
            # 获取司机信息验证登录状态
            driver_info = tms.driver.get_driver_info()
            assert driver_info["object"][
                       "email"] == email, f"登录账号与预期不符: {driver_info['object']['email']} != {email}"
        finally:
            # 测试完成后恢复原密码（即使测试失败也要执行）
            tms.driver.update_password(email=email, old_password=new_password, new_password=old_password,
                                       check_result=True)
            # 恢复原始登录状态
            tms.login(driver_email=email, driver_password=old_password)
