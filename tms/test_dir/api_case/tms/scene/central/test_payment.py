# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_payment.py
@Description    :  
@CreateTime     :  2024/12/26 15:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/26 15:30
"""
import weeeTest
import allure
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestPayment(weeeTest.TestCase):
    """
    Payment测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("获取pay adjustment费用明细")
    def test_get_pay_adjustment_list(self):
        """
        获取pay adjustment费用明细
        """
        tms.login()
        # 获取adjustment列表信息
        start_date = tms.util.get_date(days=-7)
        end_date = tms.util.get_date(days=-1)
        pay_adjustment_list_res = tms.central.payment.get_pay_adjustment_list(region_id=0, start_date=start_date,
                                                                              end_date=end_date)
        adjustment_fee_ids = tms.util.get_response_values(pay_adjustment_list_res, 'id')[0]
        #  获取adjustment费用信息
        tms.central.payment.get_adjustment_record(adjustment_fee_ids[0], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("不能增加当天之后日期的adjustment费用")
    def test_add_later_date_pay_adjustment(self):
        """
        不能增加当天之后日期的adjustment费用
        """
        tms.login()
        delivery_date = tms.util.get_date(days=1)
        driver_user_id = global_data.driver_user_id
        #  添加adjustment费用信息
        add_record_res = tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id,
                                                                   delivery_date=delivery_date, adjustment_fee=10.01,
                                                                   reason_code=2)
        # 结果校验
        add_record_message = tms.util.get_response_values(add_record_res, 'message')
        assert 'Delivery date could not greater than today.' == add_record_message[0]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("审核通过/拒绝adjustment费用(创建人与审核人一致)")
    def test_approve_and_reject_pay_adjustment(self):
        """
        审核通过/拒绝adjustment费用(创建人与审核人一致)
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        #  添加adjustment费用信息
        tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                  adjustment_fee=10.01, reason_code=2, check_result=True)
        fee_id = tms.tms_db.get_adjustment_fee_info(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                    status='pending', info="id")
        # 创建人与审核人一致
        approve_res = tms.central.payment.approve_adjustment_fee(fee_id=fee_id, operate='Approved')
        approve_message = tms.util.get_response_values(approve_res, 'message')
        assert "You can't approve your own payment request." == approve_message[0]
        #  审核拒绝
        approve_res = tms.central.payment.approve_adjustment_fee(fee_id=fee_id, operate='Rejected')
        approve_message = tms.util.get_response_values(approve_res, 'message')
        assert "You can't approve your own payment request." == approve_message[0]
        # 校验审核状态
        fee_status = tms.tms_db.get_adjustment_fee_info(fee_id=fee_id, info='status')
        assert fee_status == 'pending'

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("审核通过adjustment费用(创建人与审核人不一致)")
    def test_approved_pay_adjustment(self):
        """
        审核通过adjustment费用(创建人与审核人不一致)
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        #  添加adjustment费用信息
        tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                  adjustment_fee=10.01, reason_code=2, check_result=True)
        fee_id = tms.tms_db.get_adjustment_fee_info(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                    status='pending', info="id")
        # 更新创建人与审核人不一致
        tms.tms_db.update_adjust_fee_creator_id(fee_id=fee_id)
        # 审核
        tms.central.payment.approve_adjustment_fee(fee_id=fee_id, operate='Approved', check_result=True)
        # 校验审核状态
        fee_status = tms.tms_db.get_adjustment_fee_info(fee_id=fee_id, info='status')
        assert fee_status == 'Approved'

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("审核拒绝adjustment费用(创建人与审核人不一致)")
    def test_reject_pay_adjustment(self):
        """
        审核拒绝adjustment费用(创建人与审核人不一致)
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        #  添加adjustment费用信息
        tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                  adjustment_fee=10.01, reason_code=2, check_result=True)
        fee_id = tms.tms_db.get_adjustment_fee_info(driver_user_id=driver_user_id, delivery_date=delivery_date,
                                                    status='pending', info="id")
        # 更新创建人与审核人不一致
        tms.tms_db.update_adjust_fee_creator_id(fee_id=fee_id)
        # 审核
        tms.central.payment.approve_adjustment_fee(fee_id=fee_id, operate='Rejected', check_result=True)
        # 校验审核状态
        fee_status = tms.tms_db.get_adjustment_fee_info(fee_id=fee_id, info='status')
        assert fee_status == 'Rejected'

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("添加Pay Re-Adjustment费用自动审核通过")
    def test_add_pay_re_adjustment_fee(self):
        """
        添加Pay Re-Adjustment费用自动审核通过
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        adjustment_fee = 0.01
        #  添加adjustment费用信息
        add_adjustment_res = tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id,
                                                                       delivery_date=delivery_date,
                                                                       adjustment_fee=adjustment_fee, reason_code=12)
        message = tms.util.get_response_values(add_adjustment_res, 'message')
        # 如果存在待审核的账期数据则不能添加成功
        if message[0]:
            assert message[0] == 'Can not add new adjustment to a reviewing or completed pay period.'
        else:
            adjustment_fee_info = tms.tms_db.get_adjustment_fee_info(driver_user_id=driver_user_id,
                                                                     reason_code=12,
                                                                     status='Approved',
                                                                     info="driver_user_id,adjustment_fee,reason_code,approver_id")
            assert (adjustment_fee_info[0] == driver_user_id and
                    adjustment_fee_info[1] == adjustment_fee and
                    adjustment_fee_info[2] == 12 and
                    adjustment_fee_info[3] == 0)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("添加小于0的adjustment fee失败")
    def test_add_pay_adjustment_less_than_0(self):
        """
        添加小于0的adjustment fee失败
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        #  添加adjustment费用信息
        add_record_res = tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id,
                                                                   delivery_date=delivery_date, adjustment_fee=-10.01,
                                                                   reason_code=2)
        # 结果校验
        add_record_message = tms.util.get_response_values(add_record_res, 'message')
        assert 'The amount cannot be less than 0.' == add_record_message[0]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Payment')
    @allure.title("添加小于0的Pay Re-Adjustment费用成功")
    def test_add_pay_re_adjustment_less_than_0(self):
        """
        添加小于0的Pay Re-Adjustment费用成功
        """
        tms.login()
        delivery_date = tms.util.get_date(days=-1)
        driver_user_id = global_data.driver_user_id
        adjustment_fee = -0.01
        #  添加adjustment费用信息
        add_adjustment_res = tms.central.payment.add_adjustment_record(driver_user_id=driver_user_id,
                                                                       delivery_date=delivery_date,
                                                                       adjustment_fee=adjustment_fee, reason_code=12)
        message = tms.util.get_response_values(add_adjustment_res, 'message')
        # 如果存在待审核的账期数据则不能添加成功
        if message[0]:
            assert message[0] == 'Can not add new adjustment to a reviewing or completed pay period.'
        else:
            adjustment_fee_info = tms.tms_db.get_adjustment_fee_info(driver_user_id=driver_user_id,
                                                                     reason_code=12,
                                                                     status='Approved',
                                                                     info="driver_user_id,adjustment_fee,reason_code,approver_id")
            assert (adjustment_fee_info[0] == driver_user_id and
                    adjustment_fee_info[1] == adjustment_fee and
                    adjustment_fee_info[2] == 12 and
                    adjustment_fee_info[3] == 0)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
