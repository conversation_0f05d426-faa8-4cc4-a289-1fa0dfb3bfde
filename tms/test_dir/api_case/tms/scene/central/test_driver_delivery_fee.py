# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_driver_delivery_fee.py
@Description    :  
@CreateTime     :  2025/6/5 15:58
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/5 15:58
"""
import allure
import weeeTest

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api.tms.tms_central.delivery_fee import DeliveryFee


class TestDriverDeliveryFee(weeeTest.TestCase):
    """
    司机配送费相关接口
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryFee')
    @allure.title("司机配送费校验")
    def test_check_driver_delivery_fee(self):
        """
        司机配送费校验
        """
        tms.login()
        dispatch_id = global_data.dispatch_id
        # 司机费用类初始化
        fee = DeliveryFee(dispatch_id)
        # 获取司机配送费
        delivery_fee = fee.get_driver_free()
        # 获取系统计算的费用
        db_fee = tms.tms_db.get_dispatch_fee(dispatch_id)
        # 运费比较
        assert db_fee == delivery_fee


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
