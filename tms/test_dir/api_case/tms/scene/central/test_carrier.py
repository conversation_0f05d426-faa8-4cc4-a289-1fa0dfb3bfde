# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_carrier.py
@Description    :  
@CreateTime     :  2025/5/28 16:56
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/28 16:56
"""
import allure
import weeeTest

from tms.test_dir.api.tms.tms import tms


class TestCarrier(weeeTest.TestCase):

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'Carrier')
    @allure.title("获取物流列表")
    def test_get_carrier_list(self):
        """
        获取物流列表
        """
        tms.login()
        tms.central.carrier.get_carrier_list(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Carrier')
    @allure.title("获取zipcode的物流配置信息")
    def test_update_carrier_zipcode(self):
        """
        更新zipcode的物流配置信息
        """
        tms.login()
        carrier_name = 'FedEx'
        zipcode = '01077'
        #  获取zipcode的物流配置信息
        carrier_zipcode_res = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode)
        carrier_zipcode_info = tms.util.get_response_values(carrier_zipcode_res, 'recId[0]', 'pos[0]', 'type[0]')
        #  更新zipcode的物流配置信息
        tms.central.carrier.update_carrier_zipcode(rec_id=carrier_zipcode_info[0], carrier_name=carrier_name,
                                                   zipcode=zipcode, pos=500, order_type=carrier_zipcode_info[2],
                                                   check_result=True)
        #  再次获取zipcode的物流配置信息
        carrier_zipcode_res_1 = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode)
        carrier_zipcode_info_1 = tms.util.get_response_values(carrier_zipcode_res_1, 'recId[0]', 'pos[0]', 'type[0]')
        # 校验pos更新成功
        assert carrier_zipcode_info[1] != carrier_zipcode_info_1[1] and carrier_zipcode_info_1[1] == 500
        #  再次更新zipcode的物流配置信息
        tms.central.carrier.update_carrier_zipcode(rec_id=carrier_zipcode_info[0], carrier_name=carrier_name,
                                                   zipcode=zipcode, pos=carrier_zipcode_info[1],
                                                   order_type=carrier_zipcode_info[2], check_result=True)
        carrier_zipcode_res_2 = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode)
        carrier_zipcode_info_2 = tms.util.get_response_values(carrier_zipcode_res_2, 'recId', 'pos', 'type')
        # 校验pos更新成功
        assert carrier_zipcode_info[1] == carrier_zipcode_info_2[1]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Carrier')
    @allure.title("新增zipcode的物流配置信息")
    def test_add_carrier_zipcode(self):
        """
        新增zipcode的物流配置信息
        """
        tms.login()
        carrier_name = 'FedEx'
        zipcode = '01077'
        pos = 123456
        #  新增zipcode的物流配置信息
        add_res = tms.central.carrier.add_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode, pos=pos, order_type='6')
        add_message = tms.util.get_response_values(add_res, 'message')
        if "type mapping is already exist" in add_message:
            tms.util.print_log('数据已存在, 跳过执行', level='warning')
            pass
        #  再次获取zipcode的物流配置信息
        carrier_zipcode_res = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode, pos=pos)
        carrier_zipcode_info = tms.util.get_response_values(carrier_zipcode_res, 'recId[0]', 'pos[0]', 'type[0]')
        assert carrier_zipcode_info[1] == pos and carrier_zipcode_info[2] == 6

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Carrier')
    @allure.title("删除zipcode的物流配置信息")
    def test_delete_carrier_zipcode(self):
        """
        删除zipcode的物流配置信息
        """
        tms.login()
        carrier_name = 'FedEx'
        zipcode = '01077'
        pos=123456
        carrier_zipcode_res = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode, pos=pos)
        carrier_zipcode_info = tms.util.get_response_values(carrier_zipcode_res, 'recId[0]')
        tms.central.carrier.delete_carrier_zipcode(rec_id=carrier_zipcode_info[0], check_result=True)
        carrier_zipcode_res_1 = tms.central.carrier.get_carrier_zipcode(carrier_name=carrier_name, zipcode=zipcode, pos=pos)
        carrier_zipcode_info_1 = tms.util.get_response_values(carrier_zipcode_res_1, 'data')[0]
        assert len(carrier_zipcode_info_1) == 0


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
