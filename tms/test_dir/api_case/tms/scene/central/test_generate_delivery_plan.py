# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_generate_delivery_plan.py
@Description    :  
@CreateTime     :  2023/10/30 17:36
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/10/30 17:36
"""
import weeeTest
import allure
from tms.test_dir.api.tms.tms import tms


class TestDeliveryPlan(weeeTest.TestCase):
    """
    派车计划测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("FBW订单生成派车计划")
    def test_fbw_generate_delivery_plan(self):
        """
        FBW订单生成派车计划
        """
        tms.login()
        delivery_date = '2025-02-21'
        sub_region_ids = [2]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("LA生成派车计划")
    def test_la_generate_delivery_plan(self):
        """
        LA生成派车计划
        """
        tms.login()
        delivery_date = '2025-03-25'
        sub_region_ids = [47]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("Seattle生鲜生成派车计划")
    def test_seattle_generate_delivery_plan(self):
        """
        Seattle生鲜生成派车计划
        """
        tms.login()
        delivery_date = '2025-05-20'
        sub_region_ids = [18]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            ids = ','.join(map(str, delivery_list_info[0]))
        else:
            ids = delivery_list_info[0]
        delivery_plan_id = tms.generate_delivery(delivery_ids=ids)
        if delivery_plan_id:
            delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
            assert str(delivery_id) == str(ids)
        else:
            raise Exception(f'批次{ids}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("NewYork生成派车计划")
    def test_new_york_generate_delivery_plan(self):
        """
        NewYork生成派车计划
        """
        tms.login()
        delivery_date = '2025-03-19'
        sub_region_ids = [54]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("Houston生成派车计划")
    def test_houston_generate_delivery_plan(self):
        """
        Houston生成派车计划
        """
        tms.login()
        delivery_date = '2025-05-21'
        sub_region_ids = [20]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("Chicago生成派车计划")
    def test_chicago_generate_delivery_plan(self):
        """
        Chicago生成派车计划
        """
        tms.login()
        delivery_date = '2025-04-22'
        sub_region_ids = [48]  # 俄亥俄
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("Tampa生成派车计划")
    def test_tampa_generate_delivery_plan(self):
        """
        Tampa生成派车计划
        """
        tms.login()
        delivery_date = '2025-05-23'
        sub_region_ids = [9]
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], list):
            for delivery in delivery_list_info[0]:
                delivery_plan_id = tms.generate_delivery(delivery_ids=delivery)
                if delivery_plan_id:
                    delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                    assert delivery_id == str(delivery)
                else:
                    raise Exception(f'批次{delivery}生成派车计划失败')
        else:
            delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
            if delivery_plan_id:
                delivery_id = tms.tms_db.get_delivery_id(delivery_plan_id)
                assert delivery_id == str(delivery_list_info[0])
            else:
                raise Exception(f'批次{delivery_list_info[0]}生成派车计划失败')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DeliveryPlan')
    @allure.title("FPO一键生成Delivery plan")
    def test_fpo_generate_delivery_plan(self):
        """
         FPO一键生成Delivery plan
        """
        tms.login()
        tms.fpo_generate_delivery_plan(delivery_date='2025-04-25')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
