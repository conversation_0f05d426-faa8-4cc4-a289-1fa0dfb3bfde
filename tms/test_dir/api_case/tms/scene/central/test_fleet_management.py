# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_fleet_management.py
@Description    :  
@CreateTime     :  2024/12/13 11:41
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/13 11:41
"""
import allure
import weeeTest
from weeeTest import log

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestFleetManage(weeeTest.TestCase):
    """
    车队管理测试
    """

    vehicle_make = global_data.vehicle_make
    is_prod = global_data.is_prod

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("更新车辆信息")
    def test_edit_vehicle_info(self):
        """
        更新车辆信息
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        # 获取某个车辆具体信息
        tms.central.fleet.get_vehicle_info(vehicle_id=vehicle_data[0]["id"], check_result=True)
        vehicle_info = tms.tms_db.get_vehicle_info(vehicle_data[0]["id"])
        # 更新车辆信息
        tms.central.fleet.update_vehicle_info(*vehicle_info, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("添加车辆信息")
    def test_add_vehicle_info(self):
        """
        添加车辆信息
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        vehicle_info = tms.tms_db.get_vehicle_info(vehicle_data[0]["id"])
        #  删除车辆数据
        tms.tms_db.del_vehicle(vehicle_data[0]["id"])
        # 添加车辆信息
        add_vehicle_res = tms.central.fleet.add_vehicle(delivery_station_id=vehicle_info[1],
                                                        current_mileage=vehicle_info[2],
                                                        acquisition=vehicle_info[3], license_plate=vehicle_info[4],
                                                        vehicle_number=vehicle_info[5], vehicle_type_id=vehicle_info[6],
                                                        vehicle_vin=vehicle_info[7], check_result=True)
        add_vehicle_id = tms.util.get_response_values(add_vehicle_res, 'id')[0]
        #  根据新增接口返回的的车辆ID获取相关信息
        add_vehicle_info = tms.tms_db.get_vehicle_info(add_vehicle_id)
        #  校验表中的vehicle_vin与添加的值一致
        assert vehicle_info[7] == add_vehicle_info[7]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage' ,'TMS-Online')
    @allure.title("添加车辆类型")
    def test_add_vehicle_type(self):
        """
        添加车辆类型
        """
        # 账号登录
        tms.login()
        # 删除车辆类型列表
        if not self.is_prod:
            tms.tms_db.del_vehicle_type(vehicle_make=self.vehicle_make)
        # 添加车辆类型信息
        tms.central.fleet.add_vehicle_type(vehicle_year='2025', vehicle_make=self.vehicle_make, vehicle_model='SUV',
                                           vehicle_capacity=1.00, payload=1.00, check_result=True)
        vehicle_list_res = tms.central.fleet.get_vehicle_type_list(vehicle_make=self.vehicle_make, check_result=True)
        vehicle_type_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        assert vehicle_type_data[0]["vehicleMake"] == self.vehicle_make

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage' , 'TMS-Online')
    @allure.title("删除车辆类型")
    def test_del_vehicle_type(self):
        """
        删除车辆类型
        """
        # 账号登录
        tms.login()
        # 获取车辆信息
        vehicle_list_res = tms.central.fleet.get_vehicle_type_list(vehicle_make=self.vehicle_make, check_result=True)
        vehicle_type_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        #  删除车辆数据
        if vehicle_type_data[0]["vehicleMake"] == self.vehicle_make:
            tms.central.fleet.del_vehicle_type(vehicle_type_data[0]["id"])
        vehicle_list_res_1 = tms.central.fleet.get_vehicle_type_list(vehicle_make=self.vehicle_make, check_result=True)
        assert vehicle_list_res_1['object'] is None

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage', 'TMS-Online')
    @allure.title("获取车辆DVIR历史记录")
    def test_get_vehicle_history(self):
        """
        获取车辆DVIR历史记录
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        # 获取某个车辆DVIR历史记录
        tms.central.fleet.get_vehicle_inspection_list(vehicle_id=vehicle_data[0]["id"], check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
