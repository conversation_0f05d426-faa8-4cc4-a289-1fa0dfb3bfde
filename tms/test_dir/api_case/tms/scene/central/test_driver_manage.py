# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :
@CreateTime     :  2023/6/9 15:29
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/9 15:29
"""
import allure
import weeeTest
from weeeTest import jmespath, log

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestDriverManage(weeeTest.TestCase):
    """
    Signup测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("signup提交及审核测试")
    def test_0_signup(self):
        """
        signup提交及审核测试
        """
        # 账号登录
        tms.login()
        # 获取账号信息
        driver_res = tms.driver.get_driver_info()
        driver_value_info = tms.util.get_response_values(driver_res, "driver_user_id", "sub_region_id")
        # 司机signup
        tms.driver.save_signup(signup_type='fresh', address_total=35)
        # 获取signup列表信息
        signup_list_res = tms.central.driver_manage.get_signup_list(driver_value_info[0], driver_value_info[1])
        sign_info = tms.util.get_response_values(signup_list_res, "id[0]", 'signup_type[0]', 'address_total[0]')
        # 校验司机signup成功
        assert sign_info[1] == 'fresh' and int(sign_info[2]) == 35
        # signup审核
        tms.central.driver_manage.update_signup(sign_info[0], "P", 40)

        signup_list_res_1 = tms.central.driver_manage.get_signup_list(driver_value_info[0], driver_value_info[1])
        sign_info_1 = tms.util.get_response_values(signup_list_res_1, 'signup_type[0]', 'assigned_address_total[0]')
        # 校验signup审核成功
        assert sign_info_1[0] == 'fresh' and int(sign_info_1[1]) == 40

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改站点状态为失败")
    def test_1_change_task_status_fail(self):
        """
        修改站点状态为失败
        """
        tms.login()
        dispatch_id = global_data.dispatch_id
        tms.reset_dispatch(dispatch_id)
        task_list_res = tms.central.driver_manage.get_task_list(dispatch_id)
        tms.finish_inventory_task(dispatch_id)
        task_list_info = tms.util.get_response_values(task_list_res, 'task_id[0]', 'task_refer_id[0]', 'task_status[0]')
        # 修改站点状态为失败
        tms.central.driver_manage.change_task_status(task_id=task_list_info[0], status=60)
        # 校验状态
        task_list_res_1 = tms.central.driver_manage.get_task_list(dispatch_id)
        task_list_info_1 = tms.util.get_response_values(task_list_res_1, 'task_id[0]', 'task_refer_id[0]',
                                                        'task_status[0]')
        assert task_list_info[0] == task_list_info_1[0]
        assert task_list_info[1] == task_list_info_1[1]
        assert task_list_info_1[2] == 60

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("重置失败站点状态")
    def test_2_revert_task_status(self):
        """
        重置失败站点状态
        """
        dispatch_id = global_data.dispatch_id
        tms.sync_tasks_if_no_task(dispatch_id)
        task_list_res = tms.central.driver_manage.get_task_list(dispatch_id)
        task_list_info = tms.util.get_response_values(task_list_res, 'task_id[0]', 'task_refer_id[0]', 'task_status[0]')
        # 重置失败站点状态
        tms.central.driver_manage.revert_task_status(task_id=task_list_info[0], task_refer_id=task_list_info[1],
                                                     status=60)
        # 校验状态
        task_list_res_1 = tms.central.driver_manage.get_task_list(dispatch_id)
        task_list_info_1 = tms.util.get_response_values(task_list_res_1, 'task_id[0]', 'task_refer_id[0]',
                                                        'task_status[0]')
        assert task_list_info[0] == task_list_info_1[0]
        assert task_list_info[1] == task_list_info_1[1]
        assert task_list_info_1[2] == 10

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改站点状态为成功")
    def test_3_change_task_status_finished(self):
        """
        修改站点状态为成功
        """
        tms.login()
        dispatch_id = global_data.dispatch_id
        tms.reset_dispatch(dispatch_id)
        tms.finish_inventory_task(dispatch_id)
        task_list_res = tms.central.driver_manage.get_task_list(dispatch_id)
        task_list_info = tms.util.get_response_values(task_list_res, 'task_id[1]', 'task_refer_id[1]', 'task_status[1]')
        # 修改站点状态为成功
        tms.central.driver_manage.change_task_status(task_id=task_list_info[0], status=30)
        # 校验状态
        task_list_res_1 = tms.central.driver_manage.get_task_list(dispatch_id=global_data.dispatch_id)
        task_list_info_1 = tms.util.get_response_values(task_list_res_1, 'task_id[1]', 'task_refer_id[1]',
                                                        'task_status[1]')
        assert task_list_info[0] == task_list_info_1[0]
        assert task_list_info[1] == task_list_info_1[1]
        assert task_list_info_1[2] == 30

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("路线被改为load_able后修改路线站点的状态")
    def test_change_task_status(self):
        """
        路线被改为load_able后修改路线站点的状态
        """
        # 先取出最新一个路线状态为空且不是大路线G路线的路线
        route_info = tms.tms_db.get_route_not_loadable()
        delivery_id = tms.tms_db.get_delivery_id(route_info[2])
        log.info(F"dispatch_id是{route_info[1]}")
        allure.attach("route_info是", route_info)
        allure.attach("delivery_id是", delivery_id)
        # 如果是delivery_ids有多个值拼接，取出第一个进行赋值
        if ',' in delivery_id:
            delivery_id = list(map(int, delivery_id.split(",")))[0]
        # 修改路线打包状态为finish
        tms.central.change_route_status(route_id=route_info[0], delivery_id=delivery_id, status="pack_finish",
                                        check_result=True)
        #后台使用load finish 功能修改仓库任务为finish
        tms.central.update_route_load_finish(dispatch_id=route_info[1], check_result=True)
        # 获取task_list
        task_list_info = tms.central.driver_manage.get_task_list(dispatch_id=route_info[1], check_result=True)
        task_list = jmespath(task_list_info, "object")
        # 后台修改路线的第一个站点为finish
        tms.central.driver_manage.change_task_status(task_id=task_list[0]["task_id"], status=30, check_result=True)
        # 重新获取list校验数据修改成功
        task_list_info = tms.central.driver_manage.get_task_list(dispatch_id=route_info[1], check_result=True)
        task_list = jmespath(task_list_info, "object")
        assert task_list[0]["task_status"] == 30

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改订单备注")
    def test_4_edit_order_comment(self):
        """
        修改订单备注
        """
        tms.login()
        dispatch_id = global_data.dispatch_id
        tms.reset_dispatch(dispatch_id)
        task_list_res = tms.central.driver_manage.get_task_list(dispatch_id)
        task_list_info = tms.util.get_response_values(task_list_res, 'delivery_plan_point_id[2]', 'order_comment[2]')
        # 修改站点状态为成功
        tms.central.driver_manage.edit_order_comment(delivery_plan_point_id=task_list_info[0],
                                                     order_comment='Change Order Comment')
        # 校验状态
        task_list_res_1 = tms.central.driver_manage.get_task_list(dispatch_id=global_data.dispatch_id)
        task_list_info_1 = tms.util.get_response_values(task_list_res_1, 'delivery_plan_point_id[2]',
                                                        'order_comment[2]')
        assert task_list_info[0] == task_list_info_1[0]
        assert task_list_info_1[1] == 'Change Order Comment'

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改司机name信息")
    def test_5_edit_driver_info(self):
        """
        修改司机name信息
        """
        tms.login()
        driver_user_id = 10923090
        # 将司机状态设置为已审核
        tms.central.driver_manage.approve_driver(driver_user_id=driver_user_id, apply_status='P')
        # 获取司机信息
        driver_res = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info = tms.util.get_response_values(driver_res, 'subRegionId[0]', 'driver_name', 'email')
        # 修改司机信息
        tms.central.driver_manage.edit_driver_info(sub_region_id=driver_info[0], driver_user_id=driver_user_id,
                                                   driver_name='', email=driver_info[2])
        driver_res_1 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_1 = tms.util.get_response_values(driver_res_1, 'apply_status')
        # 接口返回校验
        assert driver_info_1[0] == "P"
        #  修改为原来信息
        tms.central.driver_manage.edit_driver_info(sub_region_id=driver_info[0], driver_user_id=driver_user_id,
                                                   driver_name=driver_info[1], email=driver_info[2])
        driver_res_1 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_1 = tms.util.get_response_values(driver_res_1, 'apply_status')
        # 接口返回校验
        assert driver_info_1[0] == "P"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改司机类型信息")
    def test_6_edit_driver_type(self):
        """
        修改司机类型信息
        """
        tms.login()
        driver_user_id = 10923090
        # 将司机状态设置为已审核
        tms.central.driver_manage.approve_driver(driver_user_id=driver_user_id, apply_status='P')
        # 获取司机信息
        driver_res = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info = tms.util.get_response_values(driver_res, 'subRegionId[0]', 'driver_name', 'email', "type")
        # 修改司机信息
        edit_driver_res = tms.central.driver_manage.edit_driver_info(sub_region_id=driver_info[0],
                                                                     driver_user_id=driver_user_id,
                                                                     driver_name=driver_info[1], email=driver_info[2],
                                                                     driver_type='F')
        edit_driver_message = tms.util.get_response_values(edit_driver_res, 'message')
        if edit_driver_message[0]:
            assert 'please un schedule first' in edit_driver_message[0]
            # 取消signup记录
            tms.tms_db.delete_signup(driver_user_id=10923090)
            tms.tms_db.delete_schedule_capacity(driver_user_id=10923090)
            # 再次修改司机信息
            tms.central.driver_manage.edit_driver_info(sub_region_id=driver_info[0],
                                                       driver_user_id=driver_user_id,
                                                       driver_name=driver_info[1], email=driver_info[2],
                                                       driver_type='F', check_result=True)
        driver_res_1 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_1 = tms.util.get_response_values(driver_res_1, 'apply_status')
        # 接口返回校验
        assert driver_info_1[0] == "A"
        # 还原司机类型
        tms.central.driver_manage.edit_driver_info(sub_region_id=driver_info[0], driver_user_id=driver_user_id,
                                                   driver_name=driver_info[1], email=driver_info[2],
                                                   driver_type='P', check_result=True)
        driver_res_2 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_2 = tms.util.get_response_values(driver_res_2, 'apply_status')
        # 接口返回校验
        assert driver_info_2[0] == "A"
        tms.central.driver_manage.approve_driver(driver_user_id=driver_user_id, apply_status='P')
        driver_res_3 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_3 = tms.util.get_response_values(driver_res_3, 'apply_status')
        # 校验司机账户状态
        assert driver_info_3[0] == "P"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改司机状态")
    def test_7_update_driver_info(self):
        """
        修改司机状态
        """
        driver_user_id = 10923090
        # 将司机状态设置为已审核
        tms.central.driver_manage.approve_driver(driver_user_id=driver_user_id, apply_status='P')
        # 获取司机信息
        driver_res = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info = tms.util.get_response_values(driver_res, 'subRegionId', 'driver_name', 'email')
        # 修改司机信息
        tms.central.driver_manage.update_driver_info(sub_region_id=driver_info[0], driver_user_id=driver_user_id,
                                                     driver_name=driver_info[1], status='A')
        driver_res_1 = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info_1 = tms.util.get_response_values(driver_res_1, 'apply_status')
        # 接口返回校验
        assert driver_info_1[0] == "P"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("审核司机")
    def test_8_approve_driver(self):
        """
        审核司机
        """
        driver_user_id = 10923090
        # 将司机状态设置为已审核
        tms.central.driver_manage.approve_driver(driver_user_id=driver_user_id, apply_status='P')
        # 接口返回校验
        driver_res = tms.central.driver_manage.search_driver_by_id(driver_user_id)
        driver_info = tms.util.get_response_values(driver_res, 'apply_status')
        # 接口返回校验
        assert driver_info[0] == "P"

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("保存W2司机signup mod信息")
    def test_9_save_w2_mod_info(self):
        """
        保存W2司机signup mod信息
        """
        # 获取某一天W2 Mod信息
        w2_mod_res = tms.central.driver_manage.get_w2_mod_info(delivery_date='2023-08-26', delivery_region_id=1)
        w2_mod_info = tms.util.get_json_values(w2_mod_res, 'object')
        # 保存W2 Mod信息
        tms.central.driver_manage.save_w2_mod_info(w2_mod_info, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("Feedback提交及审核测试")
    def test_10_driver_feedback(self):
        """
        Feedback提交及审核测试
        """
        # 获取账号信息
        driver_res = tms.driver.get_driver_info()
        driver_user_id_info = tms.util.get_response_values(driver_res, "driver_user_id")
        # Driver提交反馈并获取反馈信息
        tms.driver.add_feedback(content="Content By AutoTest")
        feedback_list_res = tms.central.driver_manage.get_feedback_list(driver_user_id_info[0])
        feedback_info = tms.util.get_response_values(feedback_list_res, 'rec_id[0]', 'driver_user_id[0]', 'content[0]')
        # 审核反馈内容
        tms.central.driver_manage.update_feedback(feedback_info[0], feedback_info[1], feedback_info[2],
                                                  check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'DriverManageApi')
    @allure.title("多设备登录添加及删除")
    def test_11_edit_multi_device(self):
        """
        多设备登录添加及删除
        """
        tms.login()
        # 多设备登录添加
        tms.central.driver_manage.add_multi_device(driver_user_id=global_data.w2_driver_user_id)
        # 获取多设备列表
        multi_device_list_res_1 = tms.central.driver_manage.get_multi_device_list(
            driver_user_id=global_data.w2_driver_user_id)
        multi_device_list_info_1 = tms.util.get_response_values(multi_device_list_res_1, 'driverUserId[0]')
        # 校验添加多设备成功
        assert multi_device_list_info_1[0] == global_data.w2_driver_user_id

        # # 删除多设备
        tms.central.driver_manage.delete_multi_device(driver_user_id=global_data.w2_driver_user_id)
        multi_device_list_res_2 = tms.central.driver_manage.get_multi_device_list(
            driver_user_id=global_data.w2_driver_user_id)
        multi_device_list_info_2 = tms.util.get_response_values(multi_device_list_res_2, 'data[0]')
        # 校验删除成功
        assert len(multi_device_list_info_2[0]) == 0

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("signup修改测试")
    def test_12_signup_edit(self):
        """
        signup修改测试
        """
        # 账号登录
        tms.login()
        # 获取signup列表信息
        signup_list_res = tms.central.driver_manage.get_signup_list(sub_region_id=1)
        sign_info = tms.util.get_response_values(signup_list_res, "id[0]", 'signup_type[0]', 'address_total[0]')
        # 重置signup状态
        tms.tms_db.update_signup(signup_id=sign_info[0])
        # signup审核
        tms.central.driver_manage.update_signup(signup_id=sign_info[0], status="A", assigned_address_total=45,
                                                group_point_id=180, driver_level='normal', check_result=True)
        # 查询数据库并校验数值
        sign_info1 = tms.tms_db.get_signup_info(signup_id=sign_info[0])
        assert sign_info1 == (180, 'A', 45)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改W2司机schedule配置, 校验是否生效")
    def test_13_edit_schedule_config(self):
        """
        修改W2司机schedule配置, 校验是否生效
        """
        tms.login()
        driver_user_id = 10923030
        sub_region_id = tms.tms_db.get_driver_sub_region_id(driver_user_id)
        # signup数据重置
        tms.tms_db.update_signup_rec_update_id(driver_user_id=driver_user_id, sub_region_id=sub_region_id)
        # 修改司机Schedule配置为空
        tms.central.driver_manage.edit_schedule_config(driver_user_id=driver_user_id, days=[], check_result=True)
        # 查询表中数据正常
        assert not tms.tms_db.get_schedule_config(driver_user_id=driver_user_id)
        # 执行Job并等待15秒
        tms.central.driver_manage.generate_schedule(check_result=True)
        tms.util.wait(sleep_time=15, reason="等待Job数据生成")
        # 校验生成signup数据
        delivery_data = tms.util.get_date(days=1)
        assert not tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=delivery_data)
        delivery_data = tms.util.get_date(days=27)
        assert not tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=delivery_data)
        # 修改司机Schedule配置为每天
        tms.central.driver_manage.edit_schedule_config(driver_user_id=driver_user_id, days=[1, 2, 3, 4, 5, 6, 7],
                                                       check_result=True)
        # 查询表中数据正常
        assert tms.tms_db.get_schedule_config(driver_user_id=driver_user_id) == [1, 2, 3, 4, 5, 6, 7]
        # 执行Job并等待15秒数据生成
        tms.central.driver_manage.generate_schedule(check_result=True)
        tms.util.wait(sleep_time=15, reason="等待Job数据生成")
        # 校验生成signup数据
        delivery_data = tms.util.get_date(days=1)
        assert tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=delivery_data)
        delivery_data = tms.util.get_date(days=27)
        assert tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=delivery_data)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("修改W2司机schedule配置, 校验是否生效")
    def test_14_edit_signup_update_id(self):
        """
        signup记录被人为修改后, 验证job不再更新此记录
        """
        tms.login()
        driver_user_id = 10923030
        delivery_date = tms.util.get_date()
        tms.tms_db.update_signup_rec_update_id(driver_user_id=driver_user_id, delivery_date=delivery_date)
        # 修改司机Schedule配置为空
        tms.central.driver_manage.edit_schedule_config(driver_user_id=driver_user_id, days=[], check_result=True)
        # 查询表中数据正常
        assert not tms.tms_db.get_schedule_config(driver_user_id=driver_user_id)
        # 执行Job并等待15秒
        tms.central.driver_manage.generate_schedule(check_result=True)
        tms.util.wait(sleep_time=15, reason="等待Job数据生成")
        # 校验生成signup数据
        assert tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=delivery_date)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverManageApi')
    @allure.title("获取行程距离")
    def test_get_travel_distance(self):
        """
        获取行程距离
        """
        tms.login()
        # 获取行程距离记录
        travel_distance_res = tms.central.driver_manage.get_travel_distance(sub_region_ids=[3], check_result=True)
        travel_distance_info = tms.util.get_response_values(travel_distance_res, 'data')
        # 校验返回数据不为空
        assert len(travel_distance_info[0]) > 0


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
