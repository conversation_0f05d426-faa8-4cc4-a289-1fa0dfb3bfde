# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_delivery.py
@Description    :  
@CreateTime     :  2023/7/19 17:19
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 17:19
"""
import allure
import weeeTest
from weeeTest import jmespath

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestDelivery(weeeTest.TestCase):
    """
    DriverApp配送相关测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Delivery')
    @allure.title("SUV司机抢单及Drop")
    def test_schedule_route01_suv(self):
        """
        SUV司机抢单及Drop
        """
        tms.login(driver_email='<EMAIL>')
        tms.tms_db.delete_driver_activity_log(driver_user_id=13339585)
        delivery_plan_id = 20511
        sub_region_ids = [51]
        flex_cap = 2
        mini_van_cap = 4
        tms.tms_db.reset_delivery_plan(delivery_plan_id)
        tms.tms_db.update_flex_cap(suv_cap=flex_cap, minivan_cap=mini_van_cap, sub_region_id=sub_region_ids[0])
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_setting_before_run(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting')[0][4]
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_ids[0])
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 算法执行
        draft_id = tms.central.route_plan.run_ic_complaince_v18(delivery_plan_id=delivery_plan_id,
                                                                sub_region_id=sub_region_ids[0],
                                                                sub_region_name=sub_region_name,
                                                                mini_van_stop=driver_num_info['mini_van_stop'],
                                                                mini_van_num=driver_num_info['mini_van_num'],
                                                                w2_stop=driver_num_info['w2_stop'],
                                                                w2_num=driver_num_info['w2_num'],
                                                                df_stop=driver_num_info['df_stop'],
                                                                df_num=driver_num_info['df_num'],
                                                                suv_stop=driver_num_info['suv_stop'],
                                                                suv_num=driver_num_info['suv_num'])['object'][
            'draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 1.获取配送点信息
        route_info = tms.tms_db.get_route_info(draft_id=draft_id)
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=[route_info[0][1]])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 1.新增 FlexSUV路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-int(flex_cap + 1):-1],
                                            draft_id=draft_id, driver_id=11947547)
        tms.check_route_change_result(draft_id)

        # 2.获取配送点信息
        draft_routes_res1 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info1 = tms.util.get_response_values(draft_routes_res1, 'object')
        assert max(draft_routes_info[0]) + 1 == max(draft_routes_info1[0])

        draft_points_res1 = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                    delivery_plan_id=delivery_plan_id,
                                                                    route_nums=[route_info[0][1]])
        draft_points_info1 = tms.util.get_response_values(draft_points_res1, 'route_rec_id[0]', 'stop_id')

        # 2.新增 Minivan路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info1[0],
                                            stop_ids=draft_points_info1[1][-int(mini_van_cap + 1):-1],
                                            draft_id=draft_id, driver_id=24012401)
        tms.check_route_change_result(draft_id)
        #  拆分路线再次应用
        tms.tms_db.update_delivery_zone(zone_value='0,0,0', sub_region_id=sub_region_ids[0])
        # 等待路线数据异步计算完成
        tms.util.wait(30)
        tms.central.route_plan.split_flex_route(draft_id, check_result=True)
        tms.create_dispatch_check(draft_id)

        # 设置抢单配置
        time_list = tms.util.calculate_time(time_interval=15)
        tms.tms_db.update_flex_config(release_time=time_list[1], pickup_time=time_list[2],
                                      sub_region_id=sub_region_ids[0], rescue_time=time_list[3])
        tms.tms_db.update_dispatch_date_by_plan_id(plan_id=delivery_plan_id,
                                                   delivery_date=tms.util.us_current_date())
        # 司机抢单
        schedule_list_res = tms.driver.get_schedule_list()
        schedule_list_info = tms.util.get_response_values(schedule_list_res, 'dispatch_id[0]', 'feeId[0]')
        tms.driver.schedule_route(dispatch_id=schedule_list_info[0], fee_id=schedule_list_info[1])
        tms.driver.get_dispatch_list()
        # drop路线
        tms.driver.unschedule_route(dispatch_id=schedule_list_info[0])
        tms.login(driver_email="<EMAIL>")

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Delivery')
    @allure.title("Minivan司机抢单及Rescue")
    def test_schedule_route02_minivan(self):
        """
        Minivan司机抢单及Rescue
        """
        tms.login(driver_email='<EMAIL>')
        tms.tms_db.delete_driver_activity_log(driver_user_id=10954309)
        time_list = tms.util.calculate_time(time_interval=15)
        tms.tms_db.update_flex_config(release_time=time_list[1], pickup_time=time_list[2],
                                      sub_region_id=1, rescue_time=time_list[3])
        # 司机抢单
        schedule_list_res = tms.driver.get_schedule_list()
        schedule_list_info = tms.util.get_response_values(schedule_list_res, 'dispatch_id[0]', 'feeId[0]')
        tms.driver.schedule_route(dispatch_id=schedule_list_info[0], fee_id=schedule_list_info[1])
        tms.driver.get_dispatch_list(check_result=True)

        time_list = tms.util.calculate_time(time_interval=20)
        tms.tms_db.update_flex_config(release_time=time_list[0], pickup_time=time_list[1],
                                      sub_region_id=1, rescue_time=time_list[3])
        # 路线rescue
        # tms.driver.rescue_route(check_result=True)
        tms.login(driver_email="<EMAIL>")

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Delivery')
    @allure.title("分配司机派车单")
    def test_assign_driver_dispatch(self):
        """
        分配司机派车单
        """
        # 账户登录
        tms.login(driver_email="<EMAIL>")
        # 重置派车单数据
        tms.reset_dispatch(dispatch_id=global_data.dispatch_id)
        # 分配司机
        tms.central.driver_manage.update_drivers(dispatch_id=global_data.dispatch_id,
                                                 driver_id=global_data.driver_user_id,
                                                 driver_name=global_data.driver_user_name,
                                                 check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Delivery', 'YardManageApi')
    @allure.title("司机parking")
    def test_park_slot(self):
        """
        司机parking
        """
        # 先执行定时任务释放所有车位
        tms.central.release_all_slot(check_result=True)
        dispatch_id = global_data.dispatch_id
        # 先把派车单对应的参数support_parking改为1
        sub_region_id = tms.tms_db.get_info_by_dispatch_id(info='sub_region_id', dispatch_id=dispatch_id)
        tms.tms_db.update_sub_region_config(sub_region_id=sub_region_id, config_key='support_parking', config_value=1)
        # 先获取派车单是不是有后台assign的车位
        parking_info = tms.driver.driver_delivery.get_assign_park_info(dispatch_id, check_result=True)
        parking_info_res = jmespath(parking_info, "object")
        if parking_info_res is not None:
            slot_data = jmespath(parking_info, "object")
            slot_id = slot_data['parkSlotId']
        else:
            # 查询空闲车位,并取出第一个车位id
            free_park_info = tms.driver.driver_delivery.get_free_park_info(dispatch_id, check_result=True)
            slot_data = jmespath(free_park_info, "object")
            slot_id = slot_data[0]['parkSlotId']
        # 获取需要slot车位的仓库对应的仓库经纬度
        slot_inventory_info = tms.tms_db.get_slot_inventory_info(global_data.dispatch_id)
        # park操作
        tms.driver.driver_delivery.park_slot(dispatch_id, slot_id, latitude=str(slot_inventory_info[0]),
                                             longitude=str(slot_inventory_info[1]), check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Delivery')
    @allure.title("配送生鲜派车单")
    def test_delivery_dispatch(self):
        """
        配送生鲜派车单
        """
        tms.login()
        # 获取指定条件的派车单
        dispatch_id = global_data.dispatch_id
        # 获取仓库任务相关信息
        inventory_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                           task_type="inventory")
        package_types = tms.tms_db.get_package_types(dispatch_id)
        # Travel操作
        tms.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                               task_step='travel', latitude=inventory_info[0][1],
                                               longitude=inventory_info[0][2])
        # 等待1秒
        tms.util.wait(1)
        # Arrived操作
        tms.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                               task_step='arrived', latitude=inventory_info[0][1],
                                               longitude=inventory_info[0][2])
        # 仓库扫码
        for package_type in package_types:
            package_list = tms.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
            tms.driver.driver_delivery.load_package(task_id=inventory_info[0][0], scan_type=1,
                                                    storage_type=package_type[0], latitude=inventory_info[0][1],
                                                    longitude=inventory_info[0][2], is_fast_load_skip=True,
                                                    tracking_num_list=package_list)
        # 完成仓库任务
        tms.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=30,
                                               task_step='finish', latitude=inventory_info[0][1],
                                               longitude=inventory_info[0][2])
        # 站点操作
        packages = tms.get_dispatch_packages(dispatch_id)
        stop_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                      task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # 站点Travel操作
            tms.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                   task_step='travel', latitude=stop_info[seq][1],
                                                   longitude=stop_info[seq][2])
            # 等待1秒
            tms.util.wait(1)
            # Arrived操作
            tms.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                                   task_step='arrived', latitude=stop_info[seq][1],
                                                   longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                tms.driver.driver_delivery.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                                 package_status='received', latitude=stop_info[seq][1],
                                                                 longitude=stop_info[seq][2])
            # 更新task任务
            tms.driver.driver_delivery.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                                   task_step='finish', latitude=stop_info[seq][1], cancel_alcohol=False,
                                                   longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                                   delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')
        # 完成派车单
        tms.driver.driver_delivery.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30, check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
