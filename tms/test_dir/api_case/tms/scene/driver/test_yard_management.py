# Author: bo.liu
# Time: 2025/3/18 17:51
# E-Mail: <EMAIL>
# 这个实际调用的都是tms_central下的接口，但是因为当天有w2司机路线
# 需要依赖上面的class中20511生成的最新的路线，所以先放这里，再优化
import weeeTest
import allure
from weeeTest import jmespath, log

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api.tms.tms_central.yard_management import YardManagement


class TestYardManagement(weeeTest.TestCase):
    yard_management = YardManagement()
    delivery_station_ids = [1]
    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'YardManageApi')
    @allure.title("后台assign slot")
    def test_assign_slot(self):
        """
        后台assign车位
        """
        # 账号登录
        tms.login()
        # #为了避免没有w2路线，需要在这里查出20511下路线并将司机改为w2
        # res = tms.central.route_plan.get_delivery_plan_dispatch_list(delivery_plan_id=20511, check_result=True)
        # dispatch_id = jmespath(res, "object.dispatchList[-1].id")
        # log.info("dispatch_id是",dispatch_id)
        # tms.central.route_plan.update_dispatch_driver(dispatch_id=dispatch_id, driver_user_id=13347987,driver_user_name="li fa - BA-R1c",check_result=True)
        # 更新派车单的日期为当天
        dispatch_id = 858955
        tms.tms_db.update_dispatch_status(dispatch_id=dispatch_id, delivery_date=tms.util.us_current_date(),status='')
        # 获取slot列表
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids,check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        # 取出第一个状态是0-空闲的slot
        for slot in slot_data:
            if slot['status'] == 0:
                slot_id = slot['parkSlotId']
                break
        # 取出车位可以分配的路线
        route_res = self.yard_management.get_park_route(self.delivery_station_ids,check_result=True)
        allure.attach(F'查询可以分配的路线为结果是{route_res}', "text/plain")
        dispatch_id_list = jmespath(route_res, "object[0].dispatchIds")
        log.info(F'获取slot{slot_id}成功。')
        log.info(F'获取dispatch_id{dispatch_id_list}成功。')
        # 分配slot
        self.yard_management.update_slot(dispatch_id = dispatch_id_list,slot_id=slot_id,action="assign",check_result=True)
        # 重新获取slot列表并校验对应车位的状态
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids,check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        for slot in slot_data:
            if slot['parkSlotId'] == slot_id:
                assert slot['status'] == 2, "分配slot失败"
                log.info(F'分配slot{slot_id}成功。')
                break
    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'YardManageApi')
    @allure.title("后台release slot")
    def test_release_slot(self):
        """
        后台release车位
        """
        # 获取slot列表
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids, check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        # 取出第一个状态是1或者2-已经分配的slot
        for slot in slot_data:
            if slot['status'] == 1 or slot['status'] == 2:
                slot_id = slot['parkSlotId']
                break
        # 释放slot
        self.yard_management.update_slot(slot_id=slot_id, action="release",check_result=True)
        # 重新获取slot列表并校验对应车位的状态
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids, check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        for slot in slot_data:
            if slot['parkSlotId'] == slot_id:
                assert slot['status'] == 0, "释放slot失败"
                log.info(F'释放slot{slot_id}成功。')
                break

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'YardManageApi')
    @allure.title("后台锁定解锁 slot")
    def test_lock_slot(self):
        """
        后台锁定后解锁车位
        """
        # 获取slot列表
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids, check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        # 取出第一个状态是0-空闲的slot
        for slot in slot_data:
            if slot['status'] == 0:
                slot_id = slot['parkSlotId']
                break
        # 锁定slot
        self.yard_management.update_slot(slot_id=slot_id, action="lock",check_result=True)
        # 重新获取slot列表并校验对应车位的状态
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids, check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        for slot in slot_data:
            if slot['parkSlotId'] == slot_id:
                assert slot['status'] == 3, "锁定slot失败"
                log.info(F'锁定slot{slot_id}成功。')
        #解锁车位
        self.yard_management.update_slot(slot_id=slot_id, action="unlock",check_result=True)
        # 重新获取slot列表并校验对应车位的状态# 重新获取slot列表并校验对应车位的状态# 重新获取slot列表并校验对应车位的状态
        slot_list_res = self.yard_management.list_park_slots(self.delivery_station_ids, check_result=True)
        slot_data = jmespath(slot_list_res, "object.data")
        for slot in slot_data:
            if slot['parkSlotId'] == slot_id:
                assert slot['status'] == 0, "解锁slot失败"
                log.info(F'解锁slot{slot_id}成功。')