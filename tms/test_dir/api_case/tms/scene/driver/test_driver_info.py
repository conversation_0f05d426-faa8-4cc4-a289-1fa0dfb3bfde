# Author: bo.liu
# Time: 2025/5/15 10:04
# E-Mail: <EMAIL>

import allure
import weeeTest
from weeeTest import log

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestDriverInfoApi(weeeTest.TestCase):
    """
    DriverApp司机基础功能接口
    """

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'PassWord')
    @allure.title('忘记重置密码')
    @allure.description('测试司机忘记密码重置功能，验证完整流程：发送验证码、验证验证码、重置密码、使用新密码登录')
    def test_reset_password(self):
        """
        测试司机忘记密码重置流程
        """
        # 测试数据准备
        email = global_data.pwd_driver_email
        old_password = global_data.pwd_driver_password
        new_password = "1234abcd" + "2025"
        try:
            # 步骤1: 发送重置密码验证码
            tms.driver.send_reset_password_email(email=email, check_result=True)
            verify_code = None
            # 等待验证码发送完成
            tms.util.wait(sleep_time=5, reason="等待验证码发送完成")
            tms.login()
            # 步骤2: 获取验证码（最多尝试12次，每5秒一次）
            for i in range(12):
                verify_code = tms.central.driver_manage.get_verify_code(to_search=email)
                if verify_code is not None:
                    log.info(f"第 {i+1} 次尝试成功获取验证码: {verify_code}")
                    allure.attach(f"第 {i+1} 次尝试成功获取验证码: {verify_code}")
                    break  # 成功获取，跳出循环
                log.info(f"第 {i+1} 次尝试未获取到验证码，5 秒后重试...")
                tms.util.wait(sleep_time=5, reason="继续等待验证码发送完成")
            assert verify_code is not None, "无法获取验证码，请检查邮件发送是否成功"
            # 步骤3: 验证邮箱验证码
            tms.driver.verify_email_code(email=email, verify_code=verify_code, check_result=True)
            # 步骤4: 重置密码
            tms.driver.reset_password(email=email, verify_code=verify_code, driver_password=new_password,
                                      check_result=True)
            # 步骤5: 使用新密码登录验证
            tms.login(driver_email=email, driver_password=new_password)
            # 获取司机信息验证登录状态
            driver_info = tms.driver.get_driver_info()
            assert driver_info["object"][
                       "email"] == email, f"登录账号与预期不符: {driver_info['object']['email']} != {email}"
        except Exception as e:
            # 记录测试过程中的异常
            log.error(f"测试过程中发生异常: {str(e)}")
            raise
        finally:
            # 步骤6: 测试完成后恢复原密码（即使测试失败也要执行）
            try:
                # 确保使用新密码登录状态
                tms.login(driver_email=email, driver_password=new_password)
                # 恢复原密码
                tms.driver.update_password(
                    email=email,
                    old_password=new_password,
                    new_password=old_password,
                    check_result=True
                )
                # 验证恢复成功
                tms.login(driver_email=email, driver_password=old_password)
                log.info(f"成功恢复账号 {email} 的原始密码")
            except Exception as cleanup_error:
                log.error(f"恢复原密码过程中发生错误: {str(cleanup_error)}")
            # 切换至测试账号
            tms.login(driver_email="<EMAIL>")


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])
