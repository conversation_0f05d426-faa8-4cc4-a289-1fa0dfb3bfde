import os
import shutil

from MyQR import myqr

from tms.test_dir.api_case.tms import utils
from tms.test_dir.api_case.tms.tms_sql import TmsDB

tms_db = TmsDB()


def get_package_qr(dispatch_id=None, reset=True):
    """
    生成包裹二维码
    :param dispatch_id: 派车单ID
    :param reset: 是否清除QR文件
    :return:
    """
    if reset:
        shutil.rmtree('package_qr', ignore_errors=True)
        os.mkdir('package_qr')
    if dispatch_id:
        tracking_info = tms_db.get_tracking_info(dispatch_id=dispatch_id)
        for info in tracking_info:
            myqr.run(words=f'https://tb1.sayweee.net/order/p/{info[1]}', save_dir="package_qr",
                     save_name=f"{dispatch_id}_{info[0]}_{info[2]}_{info[1]}.png")
    else:
        with open("package_qr.txt") as f:
            for line in f.readlines():
                text = line.split()
                myqr.run(words=f'/////{text[1]}', save_dir="package_qr",
                         save_name=f"{text[0]}_{text[2]}_{text[1]}.png")
    os.startfile('package_qr')


def get_load_qr():
    """
    生成打包二维码
    :return:
    """
    shutil.rmtree('pack_qr', ignore_errors=True)
    os.mkdir('pack_qr')
    for i in range(1001, 1020):
        myqr.run(words="AAA-" + f"{i}", save_name=f"AAA-" + f"{i}.png", save_dir="pack_qr")  # 生成二维码


def get_cart_qr():
    """
    生成Cart号
    :return:
    """
    shutil.rmtree('cart_qr', ignore_errors=True)
    os.mkdir('cart_qr')
    with open("cart_qr.txt") as f:
        for line in f.readlines():
            text = line.split()[0]
            myqr.run(words=f'{text}', save_name=f"{text}.png", save_dir="cart_qr")  # 生成二维码


def get_passwd_qr():
    myqr.run(words=f'123456', save_name=f"仓库管理员码.png")  # 生成二维码


def get_check_in_qr():
    date = utils.us_current_date()
    a = '''{"region":"Bay Area","date":''' + f'''"{date}"''' + '''}'''
    myqr.run(words=a, save_name="仓库CheckIn码.png")  # 生成二维码


if __name__ == '__main__':
    get_package_qr(dispatch_id=505732)
    # get_check_in_qr()
    # get_cart_qr()
    # get_load_qr()
    # get_passwd_qr()
