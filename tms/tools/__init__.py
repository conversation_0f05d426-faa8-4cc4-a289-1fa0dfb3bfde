# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py.py
@Description    :  
@CreateTime     :  2023/9/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/7 13:35
"""
driver_header = {
    'x-canary-tag': 'tb1',
    'User-Agent': 'DriverApp/20 CFNetwork/1406.0.4 Darwin/22.4.0',
    'Content-Type': 'application/json',
    'authorization': None}

central_header = {
    'x-canary-tag': 'tb1',
    'User-Agent': 'DriverApp/20 CFNetwork/1406.0.4 Darwin/22.4.0',
    'Content-Type': 'application/json',
    'authorization': None}

wms_header = {
    "authorization": "",
    "weee_user": "",
    "weee_warehouse": "",
    "weee_wms_storage_type": "1",
    "weee_wms_packing_type": ""
}
