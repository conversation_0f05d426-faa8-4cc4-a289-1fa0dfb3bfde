# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py.py
@Description    :
@CreateTime     :  2023/9/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/7 13:35
"""
from weeeTest import jmespath
from weeeTest.utils.logging import log
from tms.qa_config import global_data
from tms.test_dir.api_case.tms import utils
from tms.tools import driver_header, central_header
from tms.tools.basic_request import BasicRequest


class TmsInterface:
    def __init__(self):
        self.tms_request = BasicRequest()

    def driver_login(self, email: str, password: str):
        """
        登录司机账户
        @param email: 司机邮箱
        @param password: 密码
        @return:
        """
        if email is None or password is None:
            raise Exception('登录的email,password不能为空')
        body = {
            "email": email,
            "password": password,
            "mock": False
        }
        response = self.tms_request.post(url='/ec/tms/v1/driver/login/email', headers=driver_header, body=body)
        auth = jmespath(response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Driver:{email}登录成功!')
            driver_header["authorization"] = 'Bearer ' + auth
            return response
        else:
            raise Exception(f'Driver:{email}登录失败,msg:{response}')

    def anon_auth(self):
        """
        获取匿名token
        @return:
        """
        response = self.tms_request.get(url='/ec/customer/login/token/generate', headers=central_header)
        auth = jmespath(response, "object.token")
        central_header["authorization"] = 'Bearer ' + auth
        return auth

    def central_login(self, user_id: int = None, password: str = None, login_platform: str = 'TMS'):
        """
        central登录
        @param login_platform: 平台
        @param user_id: 账户ID
        @param password: 密码
        @return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        body = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.anon_auth()
        response = self.tms_request.post(url='/hub/auth/user/login', headers=central_header, body=body)
        auth = jmespath(response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Central:{user_id}登录成功!')
            central_header["authorization"] = 'Bearer ' + auth
        else:
            raise Exception(f'Central:{user_id}登录失败,msg:{jmespath(response, "message")}')
        return response

    def login(self, driver_email=global_data.driver_email, driver_password=global_data.driver_password):
        """
        登录账户
        :param driver_email: 司机账户
        :param driver_password: 司机密码
        :return:
        """
        if not driver_header["authorization"] or driver_email != global_data.driver_email:
            self.driver_login(email=driver_email, password=driver_password)
        if not central_header["authorization"]:
            self.central_login(user_id=global_data.user_id, password=global_data.password)

    def approve_driver(self, driver_id, status="P"):
        """
        审核司机
        @param driver_id: 司机ID
        @param status: 审核状态
        @return:
        """
        url = "/tms/approval/approve"
        body = {
            "driver_user_id": driver_id,
            "apply_status": status,
            "apply_note": ""
        }

        response = self.tms_request.post(url=url, headers=central_header, body=body)
        if response['result']:
            log.info(f"审核司机：{driver_id}状态为{status}成功!")
        else:
            log.error(f"审核司机：{driver_id}状态为{status}失败!\nError response:{response}")
        return response

    def update_drivers(self, dispatch_id, driver_id, driver_name):
        """
        派车单分配司机
        @param dispatch_id: 派车单ID
        @param driver_id: 司机ID
        @param driver_name: 司机名称
        @return:
        """
        url = "/tms/deliveryPlanDispatch/updateDrivers"
        body = {
            "dispatchId": dispatch_id,
            "driverUserId": driver_id,
            "driverUserName": driver_name,
            "comment": "null"
        }

        response = self.tms_request.post(url=url, headers=central_header, body=body)
        if response['result']:
            log.info(f"派车单ID：{dispatch_id} 更新司机信息成功，司机信息为：{driver_name}-{driver_id}")
        else:
            log.error(
                f"派车单ID：{dispatch_id} 更新司机信息失败，接口信息为:{response}，请检查！\nError response:{response}")
        return response

    def sync_tasks(self, dispatch_id):
        """
        生成task任务
        @param dispatch_id: 派车单ID
        @return:
        """
        url = "/tms/dispatch/syncTasks"
        body = {
            "deliveryPlanId": "null",
            "dispatchId": dispatch_id,
            "invokeType": "sync"
        }

        response = self.tms_request.post(url=url, headers=central_header, body=body)
        if response['result']:
            log.info(f"派车单ID：{dispatch_id}生成新task任务成功！")
        else:
            log.error(f"派车单ID：{dispatch_id}生成新task任务失败，请检查！\nError response:{response}")
        return response

    def get_driver_info(self, driver_user_id):
        """
        获取司机信息
        @param driver_user_id: 司机ID
        @return:
        """
        url = f"/tms/driver/manage/queryDriversByDriverUserId/{driver_user_id}"
        response = self.tms_request.get(url=url, headers=central_header)
        if response['result']:
            log.info(f"获取司机：{driver_user_id}信息成功！")
        else:
            log.error(f"获取司机：{driver_user_id}信息失败，请检查！\nError response:{response}")
        return response

    def get_inventory_detail(self, task_id, load_type=None):
        """
        获取仓库详情
        @param task_id:
        @param load_type: 扫描方式 All,Apart
        @return:
        """
        url = f'/ec/tms/v1/task/inventory/detail?task_id={task_id}'
        if load_type:
            url = url + f'&load_type={load_type}'
        response = self.tms_request.get(url=url, headers=driver_header)
        if response['result']:
            log.info(f"获取仓库详情成功！")
        else:
            log.error(f"获取仓库详情失败，请检查！\nError response:{response}")
        return response

    def get_dispatch_detail(self):
        """
        获取司机派车单详情
        @return:
        """
        response = self.tms_request.get(url=f'/ec/tms/v1/dispatch/detail', headers=driver_header)
        if response['result']:
            log.info(f"获取司机派车单详情成功！")
        else:
            log.error(f"获取司机派车单详情失败，请检查！\nError response:{response}")
        return response

    def check_package_arrived(self, task_id, tracking_num, package_status, latitude, longitude, package_remarks=None):
        """
        校验包裹送达
        :param tracking_num: 包裹码
        :param task_id: 站点ID
        :param package_remarks: 包裹备注
        :param package_status: 包裹状态
        :param latitude: 纬度
        :param longitude: 经度
        :return:
        """
        body = {
            "latitude": latitude,
            "longitude": longitude,
            "package_remarks": package_remarks,
            "package_status": package_status,
            "task_id": task_id,
            "tracking_num": tracking_num,
            "update_dtm": utils.get_time()
        }
        response = self.tms_request.put(url=f'/ec/tms/v1/package/check_arrived', headers=driver_header, body=body)
        if response['result']:
            log.info(f"更新站点包裹:{tracking_num}状态成功！")
        else:
            log.error(f"更新站点包裹{tracking_num}状态失败，请检查！\nError response:{response}")
        return response

    def update_task(self, task_id, task_type, task_status, task_step, latitude, longitude, dispatch_id=None,
                    delivered_photo=None, note=None, failure_note=None, cancel_alcohol=None):
        """
        更新task状态
        :param task_id: task ID
        :param task_type: task类型,inventory,stop
        :param task_status:状态数值,30,60,70
        :param task_step: 状态描述 finish,failure,partially_finish
        :param latitude: 纬度
        :param longitude: 经度
        :param dispatch_id: 派车单ID
        :param delivered_photo: 配送照片
        :param note: 备注
        :param failure_note: 失败备注
        :param cancel_alcohol: 是否取消酒类 True/False
        :return:
        """
        body = {
            "dispatch_id": dispatch_id,
            "task_id": task_id,
            "task_type": task_type,
            "task_status": task_status,
            "task_step": task_step,
            "delivered_photo": delivered_photo,
            'cancel_alcohol': cancel_alcohol,
            'note': note,
            'failure_note': failure_note,
            "latitude": latitude,
            "longitude": longitude,
            "update_dtm": utils.get_time()
        }
        response = self.tms_request.put(url=f'/ec/tms/v1/task/update', headers=driver_header, body=body)
        if response['result']:
            log.info(f"更新{task_type},task_id:{task_id}状态为{task_step}成功！")
        else:
            log.error(f"更新{task_type},task_id:{task_id}状态为{task_step}失败，请检查！\nError response:{response}")
        return response

    def skip_package(self, task_id, storage_type, load_type, supervisor_pwd, latitude, longitude):
        """
        跳过包裹扫描
        :param task_id: Task ID
        :param storage_type: 包裹类型 F,D,Z
        :param load_type: 扫描类型,部分Apart,全部All
        :param supervisor_pwd: 超级密码
        :param latitude:
        :param longitude:
        :return:
        """
        body = {
            "task_id": task_id,
            "storage_type": storage_type,
            "load_type": load_type,
            "supervisor_pwd": supervisor_pwd,
            "latitude": latitude,
            "longitude": longitude
        }
        response = self.tms_request.put(url=f'/ec/tms/v1/package/skip', headers=driver_header, body=body)
        if response['result']:
            log.info(f"跳过{storage_type}类型包裹扫描成功！")
        else:
            log.error(f"跳过{storage_type}类型包裹扫描失败，请检查！\nError response:{response}")
        return response

    def load_package(self, task_id, scan_type, storage_type, latitude, longitude, is_fast_load_skip=True,
                     tracking_num_list=None):
        """
        扫描包裹
        :param task_id: Task ID
        :param scan_type: 扫描类型,1 fastload扫描,0逐个扫描
        :param storage_type: 包裹类型 F,D,Z
        :param is_fast_load_skip: 是否快速扫描
        :param tracking_num_list: 包裹码列表
        :param latitude:
        :param longitude:
        :return:
        """
        body = {
            "type": scan_type,
            "isFastLoadSkip": is_fast_load_skip,
            "requestStatus": "loaded",
            "task_id": task_id,
            "storage_type": storage_type,
            'trackingNumList': tracking_num_list,
            "latitude": latitude,
            "longitude": longitude
        }
        response = self.tms_request.put(url=f'/ec/tms/v1/package/load', headers=driver_header, body=body)
        if response['result']:
            log.info(f"扫描{storage_type}类型包裹成功！")
        else:
            log.error(f"扫描{storage_type}类型包裹失败，请检查！\nError response:{response}")
        return response

    def finish_dispatch(self, dispatch_id, dispatch_status):
        """
        完成派车单
        :param dispatch_id: 派车单ID
        :param dispatch_status: 派车单状态 30完成
        :return:
        """
        body = {
            "dispatch_id": dispatch_id,
            "dispatch_status": dispatch_status,
            "latitude": 34.1888894,
            "longitude": -108.8793095,
            "update_dtm": utils.get_time()
        }
        response = self.tms_request.put(url=f'/ec/tms/v1/dispatch/update', headers=driver_header, body=body)
        if response['result']:
            log.info(f"关闭派车单,派车单ID:{dispatch_id}配送完成！")
        else:
            log.error(f"派车单ID:{dispatch_id}完成失败，请检查！\nError response:{response}")
        return response

    def generate_delivery_point(self, delivery_ids: list):
        """
        生成派车计划
        @param delivery_ids: 发货批次列表
        @return:
        """
        body = {
            "deliveryIds": delivery_ids
        }
        response = self.tms_request.post(url='/tms/deliveryPointGenerate/generate', headers=central_header, body=body)
        if response['result']:
            log.info(f'发货批次:{delivery_ids}正在生成送货点...')
        else:
            log.error(f'发货批次:{delivery_ids}生成送货点失败！\nError response:{response}')
        return response

    def check_generate_point(self, delivery_ids: list):
        """
        校验生成派车计划状态
        @param delivery_ids: 发货批次列表
        @return:
        """
        body = {
            "deliveryIds": delivery_ids
        }
        response = self.tms_request.post(url='/tms/deliveryPointGenerate/checkStatus', headers=central_header,
                                         body=body)
        if response['result']:
            log.info(f'校验批次:{delivery_ids}生成派车计划状态成功')
            return response
        else:
            log.error(f'校验批次:{delivery_ids}生成派车计划状态失败！\nError response:{response}')
            return False

    def auto_generate_and_send(self, time_delivery):
        """
        校验生成派车计划状态
        @param time_delivery: cut delivery time
        @return:
        """
        response = self.tms_request.get(url=f'/wms-order/job/fpo/autoGenerateAndSend?time={time_delivery}',
                                        headers=central_header)
        if response['success']:
            log.info(f'校验批次:{time_delivery}生成派车计划状态成功')
            return response
        else:
            log.error(f'校验批次:{time_delivery}生成派车计划状态失败！\nError response:{response}')
            return False


if __name__ == '__main__':
    tms = TmsInterface()
    tms.login()
    tms.auto_generate_and_send(1725822000)
