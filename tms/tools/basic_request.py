# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py.py
@Description    :
@CreateTime     :  2023/9/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/7 13:35
"""

import requests as requests


class BasicRequest:

    def __init__(self):
        self.service = "http://api.tb1.sayweee.net"

    def get(self, url, headers):
        """
        获取请求
        :param url:
        :param headers:
        :return:
        """
        request_url = self.service + url
        response = requests.get(url=request_url, headers=headers)
        return response.json()

    def post(self, url, headers, body):
        """
        发送请求
        :param url:
        :param body:
        :param headers:
        :return:
        """

        request_url = self.service + url
        response = requests.post(url=request_url, headers=headers, json=body)
        return response.json()

    def put(self, url, headers, body):
        """
        PUT请求
        @param url: 请求路径
        @param body: 请求体
        @param headers: 请求体
        @return:
        """

        request_url = self.service + url
        response = requests.put(url=request_url, headers=headers, json=body)
        return response.json()

    def delete(self, url, headers):
        """
        Delete请求
        @param url: 请求路径
        @param headers: 请求体
        @return:
        """

        request_url = self.service + url
        response = requests.delete(url=request_url, headers=headers)
        return response.json()
