# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py.py
@Description    :
@CreateTime     :  2023/9/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/7 13:35
"""
from weeeTest.utils.logging import log

from tms.test_dir.api_case.tms.tms_sql import TmsDB
from tms.tools.driver_fee.driver_freight import DriverFee
from tms.tools.tms_request import TmsInterface


class Fee:
    def __init__(self):
        """
        初始化参数
        """
        self.con_db = TmsDB()
        self.con_ini = TmsInterface()

    def get_dispatch_fee(self, dispatch_id):
        """
        获取派车单费用
        :param dispatch_id:  派车单ID
        :return:
        """
        dispatch_info = self.con_db.get_dispatch_data(dispatch_id)
        free = DriverFee(delivery_plan_id=dispatch_info[0], group_point_id=dispatch_info[1], con_db=self.con_db)
        free.get_driver_free()

    def get_route_fee(self, plan_id, point_id):
        """
        获取路线费用
        :param plan_id: 派送计划ID
        :param point_id: 路线ID
        :return:
        """
        free = DriverFee(plan_id, point_id, self.con_db)
        free.get_driver_free()

    def get_all_routes_fee(self, plan_id):
        """
        获取所有路线费用
        :param plan_id: 派送计划ID
        :return:
        """

        result = self.con_db.get_delivery_num(plan_id)
        for seq in result:
            free = DriverFee(plan_id, seq, self.con_db)
            free.get_driver_free()

    def check_all_route_fee(self, plan_id, route_ids=None, check_fee=False):
        """
        校验司机运费
        @return:
        """
        if route_ids:
            result = list(range(*route_ids))
        else:
            result = self.con_db.get_delivery_num(plan_id)
        for group_point_id in result:
            free = DriverFee(plan_id, group_point_id, self.con_db)
            driver_fee = free.get_driver_free()
            driver_type = free.driver_type
            if check_fee:
                dispatch_id = self.con_db.dispatch_id(plan_id, group_point_id)
                dispatch_fee = self.con_db.get_dispatch_fee(dispatch_id)
                report_fee = self.con_db.get_report_fee(dispatch_id)
                log.info(f'\033[1;35m'
                         f'plan_id: {plan_id}\n'
                         f'group_point_id: {group_point_id}\n'
                         f'dispatch_id: {dispatch_id}\n'
                         f'driver_type: {driver_type}\n'
                         f'driver_fee: {driver_fee}\n'
                         f'dispatch_fee: {dispatch_fee}\n'
                         f'report_fee: {report_fee}\033[0m')


if __name__ == '__main__':
    fee = Fee()
    delivery_plan_id = 20338

    fee.get_dispatch_fee(dispatch_id=491628)  # 获取指定派车单运费明细
    # fee.get_dispatch_fee(dispatch_id=490156)
    # fee.get_route_fee(plan_id=delivery_plan_id, point_id=157)  # 获取指定路线的运费明细
    # for i in range(205, 209):
    #     fee.get_route_fee(plan_id=delivery_plan_id, point_id=i)  # 获取指定路线的运费明细
    # fee.get_all_routes_fee(delivery_plan_id)  # 获取所有路线运费明细
