# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  delivery_dispatch.py
@Description    :  
@CreateTime     :  2025/4/30 13:50
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/30 13:50
"""
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


def delivery_dispatch(dispatch_id=864916):
    """
    配送生鲜派车单
    """
    tms.login()
    # 获取司机ID
    driver_user_id = tms.tms_db.get_driver_user_id_by_dispatch(dispatch_id)
    # 获取司机类型
    driver_type = tms.tms_db.get_driver_account_info(user_id=driver_user_id, info='type')[0]
    # 获取路线Plan ID和subRegion ID
    dispatch_info = tms.tms_db.get_info_by_dispatch_id(dispatch_id, info='delivery_plan_id, sub_region_id, dispatch_type')[0]
    # # 获取subRegion_config配置信息, 包含: IC Compliance开关, Time Card开关, Vehicle Inspection开关, DVIR开关, Parking开关
    sub_region_config_info = tms.tms_db.get_sub_region_config_info(dispatch_info[1],
                                                                   info='support_ic_compliance,support_time_card,support_vehicle_inspection,support_vehicle_dvir,support_parking')

    #  获取路线类型
    flex_flag = tms.tms_db.get_info_by_dispatch_id(dispatch_id, info='flex_flag')[0]
    if flex_flag:
        #  修改Flex配置时间
        time_list = tms.util.calculate_time(time_interval=15)
        tms.tms_db.update_flex_config(release_time=time_list[1], pickup_time=time_list[2],
                                      sub_region_id=dispatch_info[1], rescue_time=time_list[3])
        tms.tms_db.update_dispatch_date_by_plan_id(plan_id=dispatch_info[0],
                                                   delivery_date=tms.util.us_current_date())
        if dispatch_info[2] == "G":
            sub_dispatch_id = tms.tms_db.get_sub_dispatch_id(group_dispatch_id=dispatch_id)[0][0]
            inventory_info = tms.tms_db.get_slot_inventory_info(dispatch_id=sub_dispatch_id)
        else:
            # 获取仓库经纬度
            inventory_info = tms.tms_db.get_slot_inventory_info(dispatch_id)
        #   Check In
        tms.driver.driver_delivery.flex_dispatch_check_in(dispatch_id=dispatch_id, latitude=inventory_info[0], longitude=inventory_info[1])
        #   accept路线
        tms.driver.driver_delivery
    # 走W2配送流程
    elif driver_type == 'F':
        #  是否支持Time Card
        if sub_region_config_info[1] == 1:
            pass
        #  是否支持Vehicle Inspection
        if sub_region_config_info[2] == 1:
            pass
        #  是否支持DVIR
        if sub_region_config_info[3] == 1:
            pass
        #  是否支持Parking
        if sub_region_config_info[4] == 1:
            pass
        #  仓库load包裹
        pass
        #   站点配送
        pass

    else:
        # 完成仓库任务
        tms.finish_inventory_task(dispatch_id)
        # 完成站点任务
        tms.finish_stop_task(dispatch_id)
    # 完成派车单
    tms.driver.driver_delivery.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30, check_result=True)


if __name__ == '__main__':
    delivery_dispatch()
