# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  delivery_method.py
@Description    :
@CreateTime     :  2023/10/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/10/7 13:35
"""
from tms.qa_config import global_data
from tms.test_dir.api_case.tms.tms_sql import TmsDB
from tms.tools.tms_request import TmsInterface
from tms.test_dir.api_case.tms import utils


class TMS:
    def __init__(self, driver_email=global_data.driver_email, driver_password=global_data.driver_password):
        self.tms_int = TmsInterface()
        self.tms_db = TmsDB()
        self.driver_email = driver_email
        self.tms_int.login(self.driver_email, driver_password)
        self.util = utils
        self.driver_user_id = self.tms_db.get_user_id(driver_email)

    def get_dispatch(self, username, delivery_date_old, delivery_type, status="load_finish"):
        """
        获取派车单
        @param username: 销售组织ID
        @param delivery_date_old: 配送日期
        @param delivery_type: 派车单类型
        @param status: 派车单状态
        @return:
        """

        delivery_date_new = self.util.us_current_date()
        account_id = self.tms_db.get_user_id(username)  # 获取账户ID
        sales_org_id = self.tms_db.get_driver_org_id(account_id)  # 获取销售组织ID
        inventory_id = self.tms_db.get_inventory_id(sales_org_id)  # 获取仓库ID
        delivery_plan_id = self.tms_db.get_delivery_plan_id(inventory_id=inventory_id, delivery_type=delivery_type,
                                                            delivery_date=delivery_date_old)  # 获取指定条件的派车计划
        dispatch_id = self.tms_db.get_dispatch_id(delivery_plan_id)  # 获取派车计划下的一条派车单ID
        self.tms_db.update_dispatch_status(dispatch_id=dispatch_id, delivery_date=delivery_date_new,
                                           status=status)  # 更新派车单状态
        self.tms_db.change_package_status(dispatch_id=dispatch_id)  # 修改包裹状态
        self.tms_db.delete_task(dispatch_id)  # 删除历史task数据
        self.tms_int.sync_tasks(dispatch_id)  # 生成task数据
        self.util.wait(3)  # 等待task数据生成
        return dispatch_id, account_id, username

    def get_driver_org_id(self, name="driver_test"):
        """
        获取司机信息
        @param name:
        @return:
        """
        user_id = self.tms_db.get_user_id(name)
        driver_sales_id = self.tms_db.get_driver_org_id(user_id)
        return driver_sales_id

    def reset_dispatch(self, dispatch_id, status="load_finish", delete_task=False, packed_status="packed"):
        """
        重置派车单数据
        @param packed_status: 包裹状态
        @param delete_task: 是否删除task
        @param status: 派车单状态，start、load_able、load_finish、finish
        @param dispatch_id: 派车单ID
        @return:
        """
        delivery_date_new = self.util.us_current_date()
        self.tms_db.update_dispatch_status(dispatch_id, delivery_date_new, status)
        self.tms_db.change_package_status(dispatch_id, packed_status)
        dispatch_data = self.tms_db.get_dispatch_data(dispatch_id)
        self.tms_db.del_delivered_photo(delivery_plan_id=dispatch_data[0], group_point_id=dispatch_data[1])
        driver_info = self.tms_db.get_delivery_type(delivery_plan_id=dispatch_data[0], group_point_id=dispatch_data[1])
        if driver_info[3] == 'F':
            self.tms_db.del_driver_time_clock_record(driver_user_id=driver_info[4])
            self.tms_db.del_driver_check_in_record(driver_user_id=driver_info[4])
        if delete_task:
            self.tms_db.delete_task(dispatch_id)
            self.tms_int.sync_tasks(dispatch_id)

    def update_dispatch_driver(self, dispatch_id, email):
        """
        更新派车单司机
        @param dispatch_id: 派车单ID
        @param email: 司机邮箱
        @return:
        """
        name = email.split('@sayweee.com')[0] if '@' in email else email
        driver_id = self.get_account_id(name)
        self.tms_int.update_drivers(dispatch_id, driver_id, name)

    def get_account_id(self, name):
        """
        获取账户ID
        @return:
        """
        account_id = self.tms_db.get_user_id(name)
        return account_id

    def update_driver(self, dispatch_id, email):
        """
        更新派车单配送日期及司机
        @return:
        """
        delivery_date_new = self.util.us_current_date()
        self.tms_db.update_dispatch_status(dispatch_id, delivery_date_new)
        self.update_dispatch_driver(dispatch_id, email)  # 更新派车单司机

    def get_packages(self, dispatch_id, package_type, stop_seq=None):
        """
        获取指定包裹类型的信息
        :param dispatch_id:
        :param package_type: F,Z,D
        :param stop_seq: 站点序号
        :return:
        """
        package_info = self.tms_db.get_package_by_type(dispatch_id=dispatch_id, package_type=package_type)
        stop_packages = []
        if stop_seq:
            for package in package_info:
                if package[0] == stop_seq:
                    stop_packages.append(package)
            return stop_packages
        else:
            return package_info

    def get_package_list(self, dispatch_id, package_type):
        """
        获取包裹码列表
        :param dispatch_id:
        :param package_type:
        :return:
        """
        package_info = self.tms_db.get_package_by_type(dispatch_id=dispatch_id, package_type=package_type)
        package_list = []
        for package in package_info:
            package_list.append(package[1])
        return package_list

    def get_dispatch_packages(self, dispatch_id):
        """
        根据站点编号返回包裹信息
        :param dispatch_id:
        :return:
        """
        package_info = self.tms_db.get_package_info(dispatch_id=dispatch_id)
        tracking_num_info = self.util.get_tracking_num_info(package_info)
        return tracking_num_info

    def delivery_dispatch(self, dispatch_id):
        """
        配送生鲜派车单
        :param dispatch_id: 派车单ID
        :return:
        """
        # 更新派车单配送日期及司机
        self.update_driver(dispatch_id, self.driver_email)
        # 获取仓库任务相关信息
        inventory_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                            task_type="inventory")
        package_types = self.tms_db.get_package_types(dispatch_id)
        # Travel操作
        self.tms_int.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                 task_step='travel', latitude=inventory_info[0][1], longitude=inventory_info[0][2])
        # 等待1秒
        self.util.wait(1)
        # Arrived操作
        self.tms_int.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=20,
                                 task_step='arrived', latitude=inventory_info[0][1], longitude=inventory_info[0][2])
        # 仓库扫码
        for package_type in package_types:
            package_list = self.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
            self.tms_int.load_package(task_id=inventory_info[0][0], scan_type=1, storage_type=package_type[0],
                                      latitude=inventory_info[0][1], longitude=inventory_info[0][2],
                                      is_fast_load_skip=True, tracking_num_list=package_list)
        # 完成仓库任务
        self.tms_int.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=30,
                                 task_step='finish', latitude=inventory_info[0][1],
                                 longitude=inventory_info[0][2])
        # 站点操作
        packages = self.get_dispatch_packages(dispatch_id)
        stop_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                       task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # Travel操作
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                     task_step='travel', latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                     task_step='arrived', latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                self.tms_int.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                   package_status='received',
                                                   latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 更新task任务
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                     task_step='finish', latitude=stop_info[seq][1], cancel_alcohol=False,
                                     longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                     delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')
        # 完成派车单
        self.tms_int.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30)

    def hot_delivery_dispatch(self, dispatch_id):
        """
        配送热送派车单
        :param dispatch_id: 派车单ID
        :return:
        """
        # 更新派车单配送日期及司机
        self.update_driver(dispatch_id, self.driver_email)
        # 获取仓库任务相关信息
        restaurant_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                             task_type="restaurant")
        package_types = self.tms_db.get_package_types(dispatch_id)
        # 循环执行仓库任务
        for seq in range(len(restaurant_info)):
            # Travel操作
            self.tms_int.update_task(task_id=restaurant_info[seq][0], task_type='restaurant', task_status=20,
                                     task_step='travel', latitude=restaurant_info[seq][1],
                                     longitude=restaurant_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.tms_int.update_task(task_id=restaurant_info[seq][0], task_type='restaurant', task_status=20,
                                     task_step='arrived', latitude=restaurant_info[seq][1],
                                     longitude=restaurant_info[seq][2])
            # 仓库扫码
            for package_type in package_types:
                package_list = self.get_package_list(dispatch_id=dispatch_id, package_type=package_type[0])
                self.tms_int.load_package(task_id=restaurant_info[seq][0], scan_type=1, storage_type=package_type[0],
                                          latitude=restaurant_info[seq][1], longitude=restaurant_info[seq][2],
                                          is_fast_load_skip=True, tracking_num_list=package_list)
            # 完成仓库任务
            self.tms_int.update_task(task_id=restaurant_info[seq][0], task_type='restaurant', task_status=30,
                                     task_step='finish', latitude=restaurant_info[seq][1],
                                     longitude=restaurant_info[seq][2])
        # 站点操作
        packages = self.get_dispatch_packages(dispatch_id)
        stop_info = self.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                       task_type="stop")
        # 循环执行站点任务
        for seq in range(len(stop_info)):
            # Travel操作
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                     task_step='travel', latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 等待1秒
            self.util.wait(1)
            # Arrived操作
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=20,
                                     task_step='arrived', latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 站点扫码
            for stop_packg in packages[seq]:
                self.tms_int.check_package_arrived(task_id=stop_info[seq][0], tracking_num=stop_packg[1],
                                                   package_status='received',
                                                   latitude=stop_info[seq][1], longitude=stop_info[seq][2])
            # 更新task任务
            self.tms_int.update_task(task_id=stop_info[seq][0], task_type='stop', task_status=30,
                                     task_step='finish', latitude=stop_info[seq][1], cancel_alcohol=False,
                                     longitude=stop_info[seq][2], dispatch_id=dispatch_id,
                                     delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg')
        # 完成派车单
        self.tms_int.finish_dispatch(dispatch_id=dispatch_id, dispatch_status=30)

    def generate_delivery(self, delivery_ids):
        """
        生成送货点
        :param delivery_ids:
        :return:
        """
        while True:
            delivery_res = self.tms_int.generate_delivery_point(delivery_ids=delivery_ids)
            delivery_res_info = self.util.get_response_values(delivery_res, 'message_id', 'object')
            if delivery_res_info[0] == '10000' and not delivery_res_info[1]:
                if not self.tms_int.check_generate_point(delivery_ids=delivery_ids):
                    return False
                else:
                    self.util.wait(1)
            elif delivery_res_info[0] == '110007':
                return False
            else:
                break
        delivery_plan_id = self.util.get_response_values(delivery_res, 'delivery_plan_id')[0]
        self.tms_db.get_delivery_plan_info(delivery_plan_id, info='rec_create_time')
        return delivery_plan_id

    def get_delivery_info(self, region_id=2):
        """
        获取批次数据
        :param region_id: 大区域ID
        :return:
        """
        return self.tms_db.get_delivery(region_id)

    def auto_delivery_dispatch(self, order_id=None, dispatch_id=None):
        """
        自动配送派车单(order_id和dispatch_id参数二选一)
        :param order_id: 订单ID
        :param dispatch_id: 派车单ID
        :return:
        """
        if order_id:
            dispatch_info = self.tms_db.get_dispatch_info_by_order_id(order_id=order_id)
            dispatch_id = dispatch_info[0]
        delivery_type = self.tms_db.get_delivery_type_by_dispatch_id(dispatch_id=dispatch_id)
        if delivery_type == 'delivery':
            self.delivery_dispatch(dispatch_id=dispatch_id)
        elif delivery_type == 'hot_delivery':
            self.hot_delivery_dispatch(dispatch_id=dispatch_id)
        else:
            raise Exception('类型不匹配,无法配送')
