# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  run.py
@Description    :
@CreateTime     :  2023/10/7 13:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/10/7 13:40
"""
from tms.tools.delivery_process.delivery_method import TMS

tms = TMS()


def auto_generate_delivery_plan(region_id):
    """
    自动生成某个region下的派车计划
    :param region_id:
    :return:
    """
    delivery_info_list = tms.get_delivery_info(region_id)
    delivery_info = []
    notes = []
    for info in delivery_info_list:
        note = info[4]
        if note not in notes:
            if tms.generate_delivery(delivery_ids=info[0]):
                notes.append(note)
                delivery_info.append(info)
    print(f'Notes:{notes}')
    print(f'DeliveryInfo:{delivery_info}')


if __name__ == '__main__':
    tms.reset_dispatch(dispatch_id=492054, delete_task=False, status='load_finish')  # 重置派车单数据
    # auto_generate_delivery_plan(region_id=1)  # 生成Region下的最近10个派车计划
    # tms.auto_delivery_dispatch(order_id=37711836) # 自动配送完成派车单,order_id与dispatch_id参数二选一
