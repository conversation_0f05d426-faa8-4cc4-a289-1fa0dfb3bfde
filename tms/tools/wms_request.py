# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  wms_request.py
@Description    :  
@CreateTime     :  2023/10/16 14:40
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/10/16 14:40
"""
from weeeTest import jmespath
from weeeTest.utils.logging import log
from tms.qa_config import global_data
from tms.tools import central_header, wms_header
from tms.tools.basic_request import BasicRequest


class WmsInterface:
    def __init__(self):
        self.wms_request = BasicRequest()

    def wms_login(self, user_id, password, login_platform = 'WMS'):
        """
        WMS平台登录
        @param login_platform: 平台
        @param user_id: 账户ID
        @param password: 密码
        @return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        body = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.anon_auth()
        response = self.wms_request.post(url='/hub/auth/user/login', headers=central_header, body=body)
        auth = jmespath(response, "object.token")
        username = jmespath(response, "object.user_name")
        if auth is not None and len(auth) > 0:
            log.info(f'WMS Central:{user_id}登录成功!')
            wms_header["authorization"] = 'Bearer ' + auth
            wms_header["weee_user"] = username
        else:
            raise Exception(f'WMS Central:{user_id}登录失败,msg:{jmespath(response, "message")}')
        return response

    def logout(self, warehouse_number, user_id, user_name):
        """
        退出WMS登陆
        :param warehouse_number:
        :param user_id:
        :param user_name:
        :return:
        """
        body = {
            "last_action_dtm": 1686046852520,
            "user_id": user_id,
            "user_name": user_name,
            "action": 1
        }
        self.wms_request.post(url=f'/wms/common/logout/{warehouse_number}', headers=wms_header, body=body)
        wms_header["authorization"] = ''

    def anon_auth(self):
        """
        获取匿名token
        @return:
        """
        response = self.wms_request.get(url='/ec/customer/login/token/generate', headers=central_header)
        auth = jmespath(response, "object.token")
        central_header["authorization"] = 'Bearer ' + auth
        return auth

    def central_login(self, user_id, password, login_platform = 'TMS'):
        """
        TMS Central登录
        @param login_platform: 平台
        @param user_id: 账户ID
        @param password: 密码
        @return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        body = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.anon_auth()
        response = self.wms_request.post(url='/hub/auth/user/login', headers=central_header, body=body)
        auth = jmespath(response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Central:{user_id}登录成功!')
            central_header["authorization"] = 'Bearer ' + auth
        else:
            raise Exception(f'Central:{user_id}登录失败,msg:{jmespath(response, "message")}')
        return response

    def login(self):
        """
        登录账户
        :return:
        """
        if not wms_header["authorization"]:
            self.wms_login(user_id=global_data.wms_user_id, password=global_data.wms_user_password)
        if not central_header["authorization"]:
            self.central_login(user_id=global_data.user_id, password=global_data.password)

    def pull_order_from_so(self):
        """
        从 so 拉取订单到 fpo
        :return:
        """
        response = self.wms_request.get(url='/wms-order/fpo/order/pullOrderFromSO', headers=wms_header)
        if response['result']:
            log.info(f"从 so 拉取订单到 fpo成功！")
        else:
            log.info(f"从 so 拉取订单到 fpo失败，请检查！！！")
        return response

    def get_fpo_info_by_warehouse(self, warehouse_number, delivery_date):
        """
        获取region和配送日信息
        :param warehouse_number:
        :param delivery_date:
        :return:
        """

        response = self.wms_request.get(
            url='/wms-order/fpo/region/byWarehouse/%s/%s' % (warehouse_number, delivery_date), headers=wms_header)
        if response['result']:
            log.info(f"获取region和配送日信息成功！")
        else:
            log.info(f"获取region和配送日信息失败，请检查！！！")
        return response

    def generate_invoice(self, delivery_date, region_ids, warehouse_number, user_id):
        """
        生成发货批次
        """

        body = {
            "delivery_date": delivery_date,
            "region_ids": region_ids,
            "warehouse_number": warehouse_number,
            "user_id": user_id
        }
        response = self.wms_request.post(url='/wms-order/fpo/invoice/generate', body=body, headers=wms_header)
        if response['result']:
            log.info(f"生成发货批次成功！")
        else:
            log.info(f"生成发货批次失败，请检查！！！")
        return response

    def get_delivery_list(self, region_id_list: list, delivery_start_date: str, delivery_end_date: str):
        """
        获取发货批次列表
        """
        body = {
            "regionIdList": region_id_list,
            "deliveryStartDate": delivery_start_date,
            "deliveryEndDate": delivery_end_date,
            "startColumn": 0,
            "pageSize": 10
        }
        response = self.wms_request.post(url='/wms-order/fpo/delivery/listDelivery', body=body, headers=wms_header)
        if response['result']:
            log.info(f"获取发货批次列表成功！")
        else:
            log.info(f"获取发货批次列表失败，请检查！！！")
        return response
