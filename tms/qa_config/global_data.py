# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  global_data.py
@Description    :  
@CreateTime     :  2023/8/23 16:46
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/23 16:46
"""
# ---------------------------------------------TMS参数---------------------------------------------
# 司机账户
driver_email = "<EMAIL>"
driver_account_name = "k001"
driver_password = "1234abcd"
driver_user_id = ********
driver_user_name = "k001 - BA-R1a"

#  CN-Test下的司机信息
ic_driver_user_id = 7642085
ic_driver_email = "<EMAIL>"
ic_driver_password = "1234abcd"

flex_driver_user_id = ********
flex_driver_email = "<EMAIL>"
flex_driver_password = "1234abcd"

w2_driver_user_id = ********
w2_driver_email = "<EMAIL>"
w2_driver_password = "1234abcd"
w2_region_id = 2
# central账户
user_id = 7642085
password = "chuan1992"
# 派车单
dispatch_id = 858958
# 课程title
course_title = "autoTest"
course_title_edite = "autoTestEdit"
fileLink = "https://video.weeecdn.com/tms/video/270/032/62BB2ADE2811D96E.mp4"
course_driver_user_id = ********
course_driver_email = "<EMAIL>"
course_driver_pwd = "1234abcd"
#用来修改密码的司机信息
pwd_driver_email = "<EMAIL>"
pwd_driver_password = "1234abcd"
#车辆类型Vehicle Type参数
vehicle_make = "Ford Test"

#是否是生成环境
is_prod = False
# ---------------------------------------------WMS参数---------------------------------------------
# WMS 账号设置
wms_user_id = "7251010"
wms_user_password = "112233"
user_name = "testuser22"
