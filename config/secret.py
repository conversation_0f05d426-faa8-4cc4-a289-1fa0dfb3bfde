# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  secret.py
@Description    :
@CreateTime     :  2023/8/16 10:22
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/16 10:22
"""
import json
import platform

import boto3
from botocore.exceptions import ClientError


def get_secret():
    secret_name = "qa/weee-test"
    region_name = "us-east-2"
    secret = {"db_erp_username": "weee_auto_test", "db_erp_password": "&!w1vgEJHW6fsTEb",
              "db_wms_username": "weee_auto_test", "db_wms_password": "&!w1vgEJHW6fsTEb"}
    # session = boto3.session.Session()
    # client = session.client(service_name='secretsmanager', region_name=region_name)
    # try:
    #     get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    # except ClientError as e:
    #     raise e
    # secret = json.loads(get_secret_value_response['SecretString'])
    return secret