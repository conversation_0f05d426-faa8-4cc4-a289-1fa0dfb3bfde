

import random
import string


class Utils:
    @staticmethod
    def random_word(num: int = 1):
        word = ''
        for _ in range(num):
            word += random.choice(string.ascii_lowercase)
        return word

    @staticmethod
    def random_num(min_num, max_num):
        """
        根据传入的数值生成一个随机数
        :param min_num: 最小值
        :param max_num: 最大值
        :return:
        """
        return random.randint(min_num, max_num)

    @staticmethod
    def random_title():
        random_string = f"QA_Test_{random.randint(10000, 99999)}"
        return random_string


if __name__ == '__main__':
    print(Utils.random_title())










