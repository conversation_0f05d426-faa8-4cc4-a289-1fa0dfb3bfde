# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  basic_db.py
@Description    :  
@CreateTime     :  2023/7/19 15:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/19 15:13
"""
import asyncio

from weeeTest.testdata.common.mysql.ext.mysqlUtil import MysqlUtil


class ConnectDatabase(object):
    def __init__(self, host, user, password, db_name, return_type=None):
        self.host = host
        self.user = user
        self.password = password
        self.db_name = db_name
        self.return_type = return_type

    def query_data(self, sql: str):
        """
        查询数据
        :param sql: 查询语句
        :return:
        """
        try:
            with MysqlUtil(host=self.host, user=self.user, password=self.password, db=self.db_name,
                           port=3306, return_type=self.return_type) as conn:
                db_data = asyncio.run(MysqlUtil.execute_query(conn, sql_str=sql))
            return db_data
        except Exception as e:
            raise Exception(f'错误sql为:{sql}, 异常为:{e}')

    def update_data(self, sql: str):
        """
        更新数据
        :param sql: 查询语句
        :return:
        """
        try:
            with MysqlUtil(host=self.host, user=self.user, password=self.password, db=self.db_name,
                           port=3306, return_type=self.return_type) as conn:
                db_data = asyncio.run(MysqlUtil.execute_update(conn, sql_str=sql))
            return db_data
        except Exception as e:
            raise Exception(f'错误sql为:{sql}, 异常为:{e}')
