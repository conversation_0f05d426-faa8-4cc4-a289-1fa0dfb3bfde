{"name": "test_order_pickdispose", "status": "failed", "statusDetails": {"message": "AssertionError", "trace": "self = <wms.test_dir.api_case.wms.scene.vendor_return.test_return_order.Testreturnorder object at 0x00000142E165BA50>\n\n    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')\n    def test_order_pickdispose(self):\n        '''\n        测试拣货的功能Weee! dispose\n        '''\n        self.rep_info['palletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(\n            warehouse=self.rep_info['warehouse_number'], location_type=38, flag=0,\n            info='location_no')\n        self.rep_info['vrppalletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(\n            warehouse=self.rep_info['warehouse_number'], location_type=80, flag=0,\n            info='location_no')\n        orderNOpickdispose = random.randint(1, 1000000)\n        # 登录WMS系统\n        user_id, user_name = wms.wms_login.common_login()\n    \n        # 获取return单数据\n        wms.return_order.createOrUpdate(orderNO=orderNOpickdispose, pickUpDate=self.rep_info['current_time'],deliveryType=2,itemNumber1=self.rep_info['itemNumber1'])\n        # 校验订单数据\n        return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)\n        orderitem = wms.wms_db.get_vendor_orderitem(orderNOpickdispose)\n        assert return_no['return_no'] == str(orderNOpickdispose)\n        assert orderitem[0]['item_number'] == self.rep_info['itemNumber1']\n    \n        #     # 开始预占订单\n        wms.return_order.startpreoccupy(orderNO=orderNOpickdispose)\n    \n        # 获取预占结果\n        preoccupyresult = wms.return_order.getpreoccupyresult(orderNO=orderNOpickdispose)\n        hastask = jmespath(preoccupyresult, \"hasTask\")\n        if hastask == True:\n            # check封装 校验预占生成任务\n            count = wms.wms_db.get_batch_preoccupancy_inventory(reference_no=orderNOpickdispose)\n            taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)\n            assert len(count) == len(taskdb)\n        else:\n            # 取消订单\n            wms.return_order.cancelorder(orderNO=orderNOpickdispose)\n            return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)\n            assert return_no['status'] == 100\n    \n        # 调整任务优先级\n        time.sleep(1)\n        wms.wms_db.update_vendor_taskpriority(orderNOpickdispose)\n        time.sleep(1)\n    \n        for i in range(len(taskdb)):\n            log.info(f\"taskdb: {taskdb}\")\n            workType = taskdb[i]['work_type']\n            itemLevel = taskdb[i]['item_level']\n            location = taskdb[i]['location_no']\n            recid = taskdb[i]['rec_id']\n            pickway = taskdb[i]['picking_way']\n            # 获取退货拣选任务列表，参数为1表示获取未扫pallet的任务列表\n>           gettasklist=wms.return_pick.gettasklist(action=1, workType=workType, warehouseNumber=self.rep_info['warehouse_number'],\n                                        userId=user_id)\n\nwms\\test_dir\\api_case\\wms\\scene\\vendor_return\\test_return_order.py:380: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <wms.test_dir.api.wms.vendor_return.returnpick_api.ReturnPickAPI object at 0x00000142E167A010>\naction = 1, workType = 3, warehouseNumber = '25', userId = '7251010'\n\n    def gettasklist(self,action,workType,warehouseNumber,userId):\n        #领取任务/查询任务/二次领取任务\n        url = '/wms/vendorReturnPick/getList'\n        body = {\n            \"workType\": workType,\n            \"action\": action,\n            \"warehouseNumber\": warehouseNumber,\n            \"storageType\": 2,\n            \"userId\": userId\n        }\n        self.post(url=url, headers=header, json=body)\n>       assert self.response['success'] == True\nE       AssertionError\n\nwms\\test_dir\\api\\wms\\vendor_return\\returnpick_api.py:31: AssertionError"}, "description": "\n        测试拣货的功能Weee! dispose\n        ", "start": 1734158407245, "stop": 1734158449551, "uuid": "baddfe0f-1674-42eb-9f29-2d94665c5d2e", "historyId": "d91cc24112e7e866c509d5e3ad341ee7", "testCaseId": "d91cc24112e7e866c509d5e3ad341ee7", "fullName": "wms.test_dir.api_case.wms.scene.vendor_return.test_return_order.Testreturnorder#test_order_pickdispose", "labels": [{"name": "tag", "value": "WMS-Regression"}, {"name": "tag", "value": "WMS-Smoke"}, {"name": "tag", "value": "WMS"}, {"name": "parentSuite", "value": "wms.test_dir.api_case.wms.scene.vendor_return"}, {"name": "suite", "value": "test_return_order"}, {"name": "subSuite", "value": "Testreturnorder"}, {"name": "host", "value": "XALAP10545"}, {"name": "thread", "value": "37244-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "wms.test_dir.api_case.wms.scene.vendor_return.test_return_order"}]}