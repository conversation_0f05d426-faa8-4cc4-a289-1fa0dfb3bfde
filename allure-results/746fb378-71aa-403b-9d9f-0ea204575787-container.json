{"uuid": "7380a027-44ed-43fc-ad69-857f0b2485f2", "children": ["2ff266c0-8921-4c43-8371-9cb7c10de63e"], "befores": [{"name": "get_token", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 103, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 902, in call_fixture_func\n    fixture_result = fixturefunc(**kwargs)\n                     ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\weeeTest\\pytest_plugin\\__init__.py\", line 591, in get_token\n    RequestHeader.ec_login_header = Header.login_header()\n                                    ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\weeeTest\\token\\get_token.py\", line 51, in login_header\n    login = EmailLogin().email_login(headers=Header.anony_header(), email=email, password=password)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\weeeTest\\token\\email_login.py\", line 61, in email_login\n    self.post(url='/ec/customer/login/email', headers=headers, json=data)\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\weeeTest\\testdata\\common\\request\\RequestUtil.py\", line 70, in wrapper\n    r = func(*args, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\weeeTest\\testdata\\common\\request\\RequestUtil.py\", line 150, in post\n    return requests.post(url, data=data, json=json, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\requests\\api.py\", line 115, in post\n    return request(\"post\", url, data=data, json=json, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\requests\\adapters.py\", line 486, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 715, in urlopen\n    httplib_response = self._make_request(\n                       ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 404, in _make_request\n    self._validate_conn(conn)\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 1058, in _validate_conn\n    conn.connect()\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\urllib3\\connection.py\", line 419, in connect\n    self.sock = ssl_wrap_socket(\n                ^^^^^^^^^^^^^^^^\n  File \"D:\\pythonwork\\qa-ec\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py\", line 402, in ssl_wrap_socket\n    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)\n"}, "start": 1734158528804, "stop": 1734158533454}], "start": 1734158528804, "stop": 1734158539879}