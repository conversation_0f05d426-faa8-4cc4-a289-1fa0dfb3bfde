{"name": "test_order_pickdispose", "status": "passed", "description": "\n        测试拣货的功能Weee! dispose\n        ", "start": 1734159815034, "stop": 1734160130480, "uuid": "a7a884fe-8416-4649-b1f4-62779107e87d", "historyId": "d91cc24112e7e866c509d5e3ad341ee7", "testCaseId": "d91cc24112e7e866c509d5e3ad341ee7", "fullName": "wms.test_dir.api_case.wms.scene.vendor_return.test_return_order.Testreturnorder#test_order_pickdispose", "labels": [{"name": "tag", "value": "WMS-Regression"}, {"name": "tag", "value": "WMS-Smoke"}, {"name": "tag", "value": "WMS"}, {"name": "parentSuite", "value": "wms.test_dir.api_case.wms.scene.vendor_return"}, {"name": "suite", "value": "test_return_order"}, {"name": "subSuite", "value": "Testreturnorder"}, {"name": "host", "value": "XALAP10545"}, {"name": "thread", "value": "28776-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "wms.test_dir.api_case.wms.scene.vendor_return.test_return_order"}]}