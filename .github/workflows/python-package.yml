# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: weeeTest run testing

on:
  push:
    branches: [ "dev", "master" ]
  pull_request:
    branches: [ "dev", "master" ]

jobs:
  tests:
    name: Run pytest
    runs-on: self-hosted

    strategy:
      fail-fast: false
      matrix:
        python-version: [ "3.11" ]
    container:
      image: python:${{ matrix.python-version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip config set global.index-url https://maven.sayweee.net/repository/pypi-public/simple/
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Run tests with weeeTest
        run: |
          python run.py


      - name: Get test results
        if: ${{ always() }}
        run: |
          cat tests/results.xml

      - name: Set pipeline status
        if: ${{ always() }}
        run: |
          if [ -e tests/results.xml ]; then
            if grep "errors=\"0\"" tests/results.xml; then
              echo "workflow_state=Success" >> $GITHUB_ENV
              exit 0
            else
              echo "workflow_state=Failed" >> $GITHUB_ENV
              exit 1
            fi
          fi