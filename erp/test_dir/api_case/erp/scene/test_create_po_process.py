
import weeeTest
import jsonpath
from erp.test_dir.api.erp.PO.api_po_order import PoRelateInterface
from weeeTest import log


class TestCreatePoProcess(weeeTest.TestCase):
    """PO的流程"""
    po_id = ""

    @weeeTest.mark.list('Regression', 'Smoke', 'ERP')
    def test_create_po(self):
        """po创建"""
        data = {}
        resp = PoRelateInterface().create_po_order(data)
        if jsonpath.jsonpath(resp, "$..order_id"):
            TestCreatePoProcess.po_id = resp["object"]["order_id"]

    @weeeTest.mark.list('Regression', 'Smoke', 'ERP')
    def test_update_po_lines(self):
        """po添加商品"""
        data = {
            "id": TestCreatePoProcess.po_id
        }
        PoRelateInterface().update_po_order_lines(data)

    @weeeTest.mark.list('Regression', 'Smoke', 'ERP')
    def test_protocol_po(self):
        """po protocol"""
        data = {
            "id": TestCreatePoProcess.po_id
        }
        PoRelateInterface().protocol_po_order(data)

    @weeeTest.mark.list('Regression', 'Smoke', 'ERP')
    def test_confirm_po(self):
        """confirm po"""
        data = {
            "id": TestCreatePoProcess.po_id
        }
        PoRelateInterface().confirm_po_order(data)

    @weeeTest.mark.list('Regression', 'Smoke', 'ERP')
    def test_delete_po(self):
        """po删除"""
        data = {
            "id": TestCreatePoProcess.po_id
        }
        PoRelateInterface().delete_po_order(data)

    # @weeeTest.mark.list('Smoke', 'ERP')
    # @weeeTest.mark.skip(reason="暂时跳过")
    # def test_pop_up_proposal_fail_message(self):
    #     """场景：生成proposal，校验一些常见的报错场景"""
    #     PoProposal().generate_proposal(order_id='265126')
    #     print("self.resp===>", self.response)
    #     log.info("self.resp===>", self.response)
    #     assert self.response['result'] is False
    #     assert "159 cannot be purchased by inbound inventory 7" in self.response["message"]
    #     bin_resp = PoProposal().generate_proposal(order_id='265207')
    #     assert bin_resp["result"] is False
    #     assert "You can click 'Cancel' to contact your demand planner to review the forecast" in bin_resp["message"]

    @weeeTest.mark.list('Smoke', 'ERP')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_cost_review_process(self):
        """场景：PO更改order line，触发成本审核校验，通过校验, 农产品不需要审核，直接可以改"""
        data = {
            "order_id": "265104",
            "sales_product_id_select[]": "10827",
            "purchase_product_id[]": "11844",
            "sales_product_id[]": "10827",
            "purchase_price[]": "50",
            "purchase_product_quantity[]": "1",
            "changed[]": "Y"
        }
        PoRelateInterface().update_po_order_lines(data)
        po_data = {
            "id": "265104"
        }
        PoRelateInterface().sync_po_price(po_data)
        resp = PoRelateInterface().cost_review_request()
        request_id = resp["data"][0]["id"]
        assert resp["recordsFiltered"] == 1
        approve_resp = PoRelateInterface().cost_review_approve(request_id)
        assert approve_resp["result"] is True
        query_resp = PoRelateInterface().cost_review_request()
        assert query_resp["recordsFiltered"] == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://tb1.sayweee.net')














