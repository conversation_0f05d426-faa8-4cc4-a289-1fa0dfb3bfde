import weeeTest
import re
from erp.test_dir.api.erp.product.product_cost_audit_api import ProductCostAuditInterface
from erp.test_dir.db_utils import DBConnect


class TestProductCostAuditModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="product_cost_audit_data.yaml", key="product_cost_audit_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_product_cost_audit_list(self, description, request):
        """商品成本审核列表查询"""
        print("执行接口描述：" + description)
        resp = ProductCostAuditInterface().product_cost_audit_list(request["params_data"])
        start_sql = "SELECT * FROM weee_merch.pi_purchase_cost_request ORDER BY id DESC limit 2;"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["data"][0]["id"] == str(res_db[0][0])

    @weeeTest.params.file(file_name="product_cost_audit_data.yaml", key="product_cost_audit_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_product_cost_audit_info(self, description, request):
        """商品成本审核详情页查询"""
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = ProductCostAuditInterface().product_cost_audit_info(data)
        matches = re.findall(r'<p class="form-control-static">(.*?)</p>', resp)
        start_sql = f"SELECT product_id FROM weee_merch.pi_purchase_cost_request WHERE id = {data}"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert matches[0] == str(data)
        assert matches[2] == str(resp_db[0][0])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
