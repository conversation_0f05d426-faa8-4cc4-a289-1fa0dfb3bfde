import weeeTest
import re
import random
from erp.test_dir.db_utils import db_query, db_update
from erp.test_dir.api.erp.PO.vendor_sponsored_promotion_api import VendorSponsoredPromotionInterface


class TestVendorSponsoredPromotionModule(weeeTest.TestCase):
    """
    验证供应商促销优惠相关单接口场景
    """

    @weeeTest.params.file("vendor_sponsored_promotion_data.yaml", key="vendor_sponsored_promotion_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_sponsored_promotion_list(self, description, request):
        """
        优惠促销活动列表查询
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = VendorSponsoredPromotionInterface().vendor_sponsored_promotion_list(data)
        if list(data.keys())[0] == "search[value][gb_product_vendor_promotion__vendor_id":
            inv_data = data["search[value][gb_product_vendor_promotion__vendor_id"]
            start_sql = f"SELECT id FROM weee_comm.gb_product_vendor_promotion WHERE vendor_id = {inv_data} AND status != 'X' ORDER BY id DESC;"
            db_resp = db_query(start_sql)
            assert resp["data"][0]["id"] == str(db_resp[0][0])
            assert resp["data"][0]["vendor_id"] == str(inv_data)
        if list(data.keys())[0] == "search[value][gb_product_vendor_promotion__product_id]":
            inv_data = data["search[value][gb_product_vendor_promotion__product_id]"]
            start_sql = f"SELECT id FROM weee_comm.gb_product_vendor_promotion WHERE product_id  = {inv_data} AND status != 'X' ORDER BY id DESC;"
            db_resp = db_query(start_sql)
            assert resp["data"][0]["id"] == str(db_resp[0][0])
            assert resp["data"][0]["product_id"] == str(inv_data)
        if list(data.keys())[0] == "search[value][gb_product_vendor_promotion__type]":
            inv_data = data["search[value][gb_product_vendor_promotion__type]"]
            start_sql = f"SELECT id FROM weee_comm.gb_product_vendor_promotion WHERE type = '{inv_data}' AND status != 'X' ORDER BY id DESC;"
            db_resp = db_query(start_sql)
            assert resp["data"][0]["id"] == str(db_resp[0][0])
            assert resp["data"][0]["type"] == inv_data
        if list(data.keys())[0] == "search[value][gb_product_vendor_promotion__po_id]":
            inv_data = data["search[value][gb_product_vendor_promotion__po_id]"]
            start_sql = f"SELECT x.* FROM weee_comm.gb_product_vendor_promotion x WHERE po_id = '{inv_data}' AND status != 'X' ORDER BY x.id DESC;"
            db_resp = db_query(start_sql)
            assert resp["data"][0]["id"] == str(db_resp[0][0])
            assert resp["data"][0]["po_id"] == str(inv_data)

    @weeeTest.params.file("vendor_sponsored_promotion_data.yaml", key="query_vendor_product_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_product(self, description, request):
        """
        支持供应商促销的下的商品查询
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = VendorSponsoredPromotionInterface().vendor_product_info(data)
        start_sql = f"SELECT product_id FROM weee_comm.gb_product_vendor WHERE vendor_id = {data} ORDER BY product_id;"
        db_resp = db_query(start_sql)
        assert resp["object"]["product_options"][1]["id"] == str(db_resp[0][0])
        assert resp["object"]["product_options"][-1]["id"] == str(db_resp[-1][0])

    @weeeTest.params.file("vendor_sponsored_promotion_data.yaml", key="vendor_sponsored_promotion_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_sponsored_promotion_info(self, request):
        """
        验证供应商促销优惠配置详情
        :param request:
        :return:
        """
        data = request["params_data"]
        resp = VendorSponsoredPromotionInterface().vendor_sponsored_promotion_info(data)
        start_sql = f"SELECT vendor_id ,`type` FROM weee_comm.gb_product_vendor_promotion WHERE id = {data};"
        db_resp = db_query(start_sql)
        matches = re.findall(r'selected="selected">(.*?) - (.*?)</option>', resp)
        assert matches[0][0] == str(db_resp[0][0])
        assert matches[1][0] == str(db_resp[0][1])

    @weeeTest.params.file("vendor_sponsored_promotion_data.yaml", key="create_vendor_sponsored_promotion")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_create_vendor_product_promotion(self, description, request):
        """
        新增商家促销配置
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        sql = f"SELECT product_id  FROM weee_comm.gb_product_vendor WHERE vendor_id = {data};"
        db_resp = db_query(sql)
        start_sql = f"SELECT count(*) FROM weee_comm.gb_product_vendor_promotion WHERE vendor_id = {data} ORDER BY id DESC;"
        db_start_resp = db_query(start_sql)
        choice_product = random.choice(db_resp)
        inv_data = {
            "vendor_id": data,
            "product_id": choice_product[0],
            "type": "F",
            "base_purchase_qty": 50,
            "base_gift_qty": 1
        }
        resp = VendorSponsoredPromotionInterface().add_vendor_product_promotion(inv_data)
        end_sql = f"SELECT count(*) FROM weee_comm.gb_product_vendor_promotion WHERE vendor_id = {data} ORDER BY id DESC;"
        db_end_resp = db_query(end_sql)
        assert resp["object"]["url"] == "https://tb1.sayweee.net/admin_po_promotion/product"
        assert db_start_resp[0][0] + 1 == db_end_resp[0][0]

    @weeeTest.params.file("vendor_sponsored_promotion_data.yaml", key="delete_vendor_sponsored_promotion")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_delete_vendor_product_promotion(self, description, request):
        """
        删除供应商促销优惠配置
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        inv_data = data["id"]
        sql = f"UPDATE weee_comm.gb_product_vendor_promotion SET status = 'O' WHERE id = {inv_data};"
        db_update(sql)
        resp = VendorSponsoredPromotionInterface().delete_vendor_product_promotion(data)
        start_sql = f"SELECT status FROM weee_comm.gb_product_vendor_promotion  WHERE id = {inv_data};"
        db_resp = db_query(start_sql)
        assert resp["message"] == f"success:ID-{inv_data}"
        assert db_resp[0][0] == "X"


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")
