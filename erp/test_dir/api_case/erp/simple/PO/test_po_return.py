import weeeTest
import re
from erp.test_dir.api.erp.PO.po_return_api import PoReturnInterface
from erp.test_dir.db_utils import DBConnect
from datetime import datetime, timedelta


class TestPoReturnModule(weeeTest.TestCase):
    """测试采购订单退货"""

    @weeeTest.params.file(file_name="po_return_data.yaml", key="po_return_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_po_return_list(self, description, request):
        """测试PO Return退货单列表查询"""
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = PoReturnInterface().po_return_list(data)
        inv_data = data["search[value][gb_po_return__inventory_id]"]
        start_sql = f"SELECT * FROM weee_comm.gb_po_return WHERE inventory_id = {inv_data} AND status != 'X' ORDER BY id DESC;"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["data"][0]["id"] == str(resp_db[0][0])

    @weeeTest.params.file(file_name="po_return_data.yaml", key="po_return_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_po_return_info(self, description, request):
        """Po Return退货单详情查询"""
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = PoReturnInterface().po_return_info(data)
        matches_1 = re.findall(r'<option value="credit" selected="selected">(.*?)</option>', resp)
        matches_2 = re.findall(r'target="_blank">(.*?)</a></td>', resp)
        start_sql = f"SELECT payment_method FROM weee_comm.gb_po_return WHERE id = {data};"
        end_sql = f"SELECT po_product_id FROM weee_comm.gb_po_return_line WHERE po_return_id = {data};"
        resp_db_1 = DBConnect().select_data_from_mysql(start_sql)
        resp_db_2 = DBConnect().select_data_from_mysql(end_sql)
        assert matches_1[0] == resp_db_1[0][0].capitalize()
        assert matches_2[0] == str(resp_db_2[0][0])

    @weeeTest.params.file(file_name="po_return_data.yaml", key="create_po_return_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_create_po_return_order(self, description, request):
        """
        创建PO Return订单
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = PoReturnInterface().create_po_return(data)
        if resp["result"] == True:
            vas = resp["object"]["url"].split("returns/", 1)[1]
        start_sql = "SELECT id FROM weee_comm.gb_po_return WHERE status = 'A' ORDER BY id DESC;"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert vas == str(resp_db[0][0])
        # 数据清理
        data = {"return_id": vas}
        PoReturnInterface().delete_po_return(data)

    @weeeTest.params.file(file_name="po_return_data.yaml", key="delete_po_return_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_delete_po_return_order(self, description, request):
        """
        验证删除PO Return订单
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        start_sql = f"SELECT status FROM weee_comm.gb_po_return WHERE id = {data['return_id']};"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        # 验证取出来的数据是否是已删除状态
        if resp_db[0][0] == "X":
            # 更新退货单的状态
            middle_sql = f"UPDATE weee_comm.gb_po_return SET status='A' WHERE id={data['return_id']};"
            DBConnect().update_data_from_mysql(middle_sql)
        # 调用删除接口
        resp = PoReturnInterface().delete_po_return(data)
        # 验证调用接口后的退货单的状态
        end_sql = f"SELECT status FROM weee_comm.gb_po_return WHERE id = {data['return_id']};"
        resp_db_end = DBConnect().select_data_from_mysql(end_sql)
        # 断言，对比接口返回的状态和数据库内的退货单状态
        assert resp["result"] == True
        assert resp_db_end[0][0] == "X"

    @weeeTest.params.file(file_name="po_return_data.yaml", key="update_po_return_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_update_po_return_order(self, description, request):
        """
        更新已创建的退货单的部分数据
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        # 只修改Estimated Return Pickup Date
        pick_date = (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d")
        return_date = {"return_date": pick_date}
        data.update(return_date)
        resp = PoReturnInterface().update_po_return(data)
        # 查询数据库内的return_date时间
        start_sql = f"SELECT return_date FROM weee_comm.gb_po_return WHERE id = {data['return_id']};"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        # 断言返回结果和数据库时间
        assert resp["result"] == True
        # 数据库查询出来的时间是<class 'datetime.date'>，需要转换成字符串格式
        assert resp_db[0][0].strftime('%Y-%m-%d') == pick_date


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")
