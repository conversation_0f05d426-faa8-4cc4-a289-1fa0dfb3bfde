import weeeTest
import datetime
from erp.test_dir.db_utils import db_query, db_update
from erp.test_dir.api.erp.PO.api_po_order import PoRelateInterface
from erp.test_dir.db_utils import DBConnect


class TestPurchaseOrderModule(weeeTest.TestCase):
    """
    验证采购订单模块的相关接口
    """

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_purchase_order_delivery(self):
        """创建配送方式为Delivery采购订单"""
        resp = PoRelateInterface().create_po_order_type(inventory_id='7',inbound_inventory_id='7',delivery_inventory_id='7',vendor_id='169',delivery_mode='Delivery')
        assert resp["result"] is True
        assert resp["object"]["order_id"] is not None

        # 获取出参中的po id，进行db断言
        order_id = resp["object"]["order_id"]
        sql_order_id = f'SELECT * FROM weee_comm.gb_purchase_order WHERE id = {order_id};'
        db_res = DBConnect().select_data_from_mysql(sql_order_id)
        assert int(resp['object']['order_id']) == db_res[0][0]
        assert 'Delivery' == db_res[0][5]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_purchase_order_pickup(self):
        """创建配送方式为Pickup采购订单"""
        resp = PoRelateInterface().create_po_order_type(inventory_id='7', inbound_inventory_id='7',delivery_inventory_id='7', vendor_id='169',delivery_mode='Pickup',
                                                        pickup_start_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                                        pickup_end_time=(datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S"))
        assert resp["result"] is True
        assert resp["object"]["order_id"] is not None

        # 获取出参中的po id，进行db断言
        order_id = resp["object"]["order_id"]
        sql_order_id = f'SELECT * FROM weee_comm.gb_purchase_order WHERE id = {order_id};'
        db_res = DBConnect().select_data_from_mysql(sql_order_id)
        assert int(resp['object']['order_id']) == db_res[0][0]
        assert 'Pickup' == db_res[0][5]
        # pickup_start_time不为空
        assert db_res[0][7] is not None

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_purchase_order_lines(self):
        """采购订单增加商品行"""
        # 查询自动化创建的测试PO
        sql_order_id = "SELECT * FROM weee_comm.gb_purchase_order WHERE comment = 'auto_create_po' order by id desc;"
        db_res = DBConnect().select_data_from_mysql(sql_order_id)
        # 从db查询结果中获取order_id
        order_id = db_res[0][0]
        # 增加一个商品
        resp = PoRelateInterface().adjust_po_order_line(order_id=order_id,product_id='757',purchase_product_id='91605',purchase_price='21',quantity='10')
        assert resp["result"] is True

        # 再次增加一个不同商品（第一次新增的商品被删除）
        resp2 = PoRelateInterface().adjust_po_order_line(order_id=order_id,product_id='760',purchase_product_id='65200',purchase_price='43',quantity='11')
        assert resp2["result"] is True

        # 商品行db校验
        sql_line = f'SELECT sales_product_id FROM weee_comm.gb_purchase_order_line WHERE purchase_order_id = {order_id};'
        db_res1 = DBConnect().select_data_from_mysql(sql_line)
        assert 760 == db_res1[0][0]


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")





















