import weeeTest
from erp.test_dir.api.cs.user.user_coupon import User<PERSON>oupon
from erp.test_dir.db_utils import DBConnect


class TestUserCoupon(weeeTest.TestCase):

    @weeeTest.params.file(file_name="user_coupon.yaml", key="select_user_coupon_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_select_coupon_list(self,  name, description, request, expected_result):
        """【100140】优惠券列表查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserCoupon().select_coupon_list(request["json_data"])
        assert resp["object"]["result"][0]["code"] == expected_result["code"]

    @weeeTest.params.file(file_name="user_coupon.yaml", key="disable_user_coupon")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_disable_user_coupon(self,  name, description, request, expected_result):
        """优惠券作废"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserCoupon().disable_user_coupon(request["json_data"])
        sql = "SELECT x.* FROM weee_promotion.coupon_code x WHERE code = 'XSIIPUEB';"
        res_db = DBConnect().select_data_from_mysql(sql)
        assert res_db[0][12] == "E"

    @weeeTest.params.file(file_name="user_coupon.yaml", key="coupon_log")
    @weeeTest.mark.list('Smoke', 'CS')
    def test_coupon_log(self,  name, description, request, expected_result):
        """优惠券日志记录"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserCoupon().coupon_log(request["json_data"])
        assert resp["object"][0]["userId"] == expected_result["user_id"]
        assert resp["object"][0]["code"] == expected_result["code"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')






