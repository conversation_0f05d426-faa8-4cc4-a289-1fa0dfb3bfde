import weeeTest
from erp.test_dir.api.cs.user.user_referral import UserReferral


class TestUserReferral(weeeTest.TestCase):

    @weeeTest.params.file(file_name="user_referral.yaml", key="user_referral")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_referral(self,  name, description, request, expected_result):
        """【10183】用户邀请关系"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserReferral().user_referral_list(request["json_data"])
        assert resp["object"]["data"][-1]["inviteeUserId"] == expected_result["inviteeUserId"]

    @weeeTest.params.file(file_name="user_referral.yaml", key="user_referral_count")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_referral_count(self,  name, description, request, expected_result):
        """用户邀请关系数量统计"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserReferral().user_referral_count(request["json_data"])
        assert resp["object"][0]["num"] == expected_result["weee_num"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')











