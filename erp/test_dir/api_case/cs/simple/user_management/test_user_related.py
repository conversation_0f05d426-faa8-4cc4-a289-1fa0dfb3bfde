import weeeTest
from erp.test_dir.api.cs.user.user_related import UserRelated


class TestUserAccountRelated(weeeTest.TestCase):

    @weeeTest.params.file(file_name="user_related.yaml", key="user_account_related_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_select_user_related_account(self, name, description, request, expected_result):
        """【101785】查询关联账号"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserRelated().user_related_account_list(request["json_data"])
        res_list = []
        if list(resp["object"]["data"]):
            for i in list(resp["object"]["data"]):
                res_list.append(i["userId"])
            if expected_result["userId"] in res_list:
                assert True
        else:
            print("该账号无关联账号")

    #@weeeTest.params.file(file_name="user_related.yaml", key="add_account_related")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_user_related_account(self, name, description, request, expected_result):
        """新增关联账号"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserRelated().add_user_related_account(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')






















