import weeeTest
from erp.test_dir.api.cs.user.user_select import UserSelect
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api_case import desk_header_huadan



class TestUserSelect(weeeTest.TestCase):

    @weeeTest.params.file(file_name="user_management.yaml", key="user_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_select(self, name, description, request, expected_result):
        """【7616787】 用户列表查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        body_data = request["json_data"]
        resp = UserSelect().user_list_select(body_data)
        expected_result_key = [key for key in body_data.keys()][0]
        assert resp["object"]["data"][0][expected_result_key] == body_data[expected_result_key]
        assert resp["object"]["data"][0]["globalUserId"] == 7616787
        assert resp["object"]["data"][0]["username"] == "huadan"
        assert resp["object"]["data"][0]["email"] == "<EMAIL>"

    @weeeTest.params.file(file_name="user_management.yaml", key="user_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_detail(self, name, description, request, expected_result):
        """【7616787】用户详情"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserSelect().user_detail(request["params_data"])
        start_sql = f'SELECT id FROM weee.`member` WHERE user_id = {request["params_data"]}'
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    #@weeeTest.params.file(file_name="user_management.yaml", key="check_user_delete")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    #def test_check_user_delete(self, name, description, request, expected_result):
        """查询用户是否账号被删除--已废弃"""
        #print("执行接口名称：" + name, "  接口描述：" + description)
        #resp = UserSelect().check_user_delete(request["params_data"])
        #start_sql = f"SELECT x.user_status FROM weee.user x WHERE Global_User_ID = {request['params_data']};"
        #db_res = DBConnect().select_data_from_mysql(start_sql)
        #if db_res[0][0] == 'X':
            #assert resp["object"] is True
        #else:
            #assert resp["object"] is False

    @weeeTest.params.file(file_name="user_management.yaml", key="address_book_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_select_user_address_book(self, name, description, request, expected_result):
        """【102483】 查询用户地址簿列表"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserSelect().user_address_list(request["json_data"])
        assert str(resp["object"]["data"][0]["userId"]) == request["json_data"]["userId"]

    @weeeTest.params.file(file_name="user_management.yaml", key="address_book_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_address_book_detail(self, name, description, request, expected_result):
        """查询用户地址簿详情"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserSelect().user_address_detail(request["data"])
        assert resp["object"]["id"] == expected_result["id"]

    @weeeTest.params.file(file_name="user_management.yaml", key="edit_address_book")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_edit_address_book(self, name, description, request, expected_result):
        """编辑用户地址簿"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        UserSelect().edit_user_address_book(request["json_data"])
        sql = "SELECT x.* FROM weee.user_address x WHERE id = 17895;"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][8] == expected_result["addrZipcode"]

    @weeeTest.params.file(file_name="user_management.yaml", key="user_related_account_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_related_account_list(self, name, description, request, expected_result):
        """【102482】相关账户"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserSelect().user_related_account_list(request["json_data"])
        assert resp["object"]["data"][0]["userId"] == 7616787
        assert resp["object"]["data"][0]["userName"] == "huadan"
        assert resp["object"]["data"][0]["userEmail"] == "<EMAIL>"

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')





