import weeeTest
from erp.test_dir.api.cs.user.user_point import UserPoints
from erp.test_dir.db_utils import DBConnect


class TestUserPoints(weeeTest.TestCase):

    @weeeTest.params.file(file_name="user_points.yaml", key="select_user_point_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_select_user_points(self,  name, description, request, expected_result):
        """查询用户积分summary"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserPoints().select_user_points_summary(request["data"])
        sql = f'SELECT x.* FROM weee_customer.user_points_summary x WHERE user_id = {request["data"]};'
        res_db = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["pointsTotal"] == int(res_db[0][2])

    @weeeTest.params.file(file_name="user_points.yaml", key="user_point_available_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_points_available_list(self,  name, description, request, expected_result):
        """【100129】查询用户积分Detail"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserPoints().user_points_available_list(request["json_data"])
        sql = f'SELECT x.* FROM weee_customer.user_points_record x WHERE user_id = {request["json_data"]["userId"]} AND active_status = 1 AND status = 0'
        res_db = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["recordList"][0]["recordId"] == int(res_db[0][0])

    @weeeTest.params.file(file_name="user_points.yaml", key="user_point_log")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_points_log(self,  name, description, request, expected_result):
        """查询用户积分log记录"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserPoints().user_points_log(request["json_data"])
        sql = f'SELECT x.* FROM weee_customer.user_points_transaction_detail x WHERE user_id = {request["json_data"]["userId"]} and transaction_id = 56366567'
        res_db = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["transactionList"][0]["transactionId"] == int(res_db[0][2])

    @weeeTest.params.file(file_name="user_points.yaml", key="transaction_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_user_points_transaction_detail(self,  name, description, request, expected_result):
        """查询用户积分transaction_detail记录"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = UserPoints().user_points_transaction_detail(request["json_data"])
        sql = f'SELECT x.* FROM weee_customer.user_points_transaction_detail x WHERE user_id = {request["json_data"]["userId"]}'
        res_db = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["transactionDetailList"][0]["transactionId"] == int(res_db[-1][2])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')













