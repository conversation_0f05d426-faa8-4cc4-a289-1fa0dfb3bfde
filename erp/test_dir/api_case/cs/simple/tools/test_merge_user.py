import weeeTest
from erp.test_dir.api.cs.tools.merge_user import MergeUser
class TestMergeUser(weeeTest.TestCase):
    @weeeTest.params.file(file_name="merge_user.yaml", key="target_user_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_target_user_info(self, name, description, request, expected_result):
        """【100147】获取目标用户合并信息"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = MergeUser().target_user_info(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"]["globalUserId"]) == expected_result["targetUserId"]
        assert resp["object"]["userStatus"] == "A"
        assert resp["object"]["userType"] == "SY_WE"
        assert resp["object"]["email"] == "<EMAIL>"
        assert resp["object"]["pointsTotal"] == 0
        assert resp["object"]["memberStatus"] == "E"
        assert resp["object"]["userStatusStr"] == "Normal"
        assert resp["object"]["memberId"] == "1499837"
        assert resp["object"]["memberStatusStr"] == "Expired"
        assert resp["object"]["googleId"] == "105264959593644553401"

    @weeeTest.params.file(file_name="merge_user.yaml", key="source_user_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_target_user_info(self, name, description, request, expected_result):
        """【100147】获取目标用户合并信息"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = MergeUser().target_user_info(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"]["globalUserId"]) == expected_result["sourceUserId"]
        assert resp["object"]["userStatus"] == "A"
        assert resp["object"]["userType"] == "SY_WE"
        assert resp["object"]["email"] == "<EMAIL>"
        assert resp["object"]["pointsTotal"] == 0
        assert resp["object"]["memberStatus"] == "E"
        assert resp["object"]["userStatusStr"] == "Normal"
        assert resp["object"]["memberId"] == "1858975"
        assert resp["object"]["memberStatusStr"] == "Expired"

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')