import weeeTest
from erp.test_dir.api.cs.tools.point_requisition import PointRequisition
from erp.test_dir.db_utils import DBConnect


class TestPointRequisition(weeeTest.TestCase):

    @weeeTest.params.file(file_name="point_requisition.yaml", key="point_requisition_list")
    @weeeTest.mark.list("Smoke", 'CS')
    def test_point_requisition_list(self, name, description, request, expected_result):
        """【100122】积分申请查询列表"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = PointRequisition().select_point_requisition_list(request["json_data"])
        start_sql = "SELECT COUNT(*) FROM weee_cs.cs_points_requisition;"
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"]["total"] == db_resp[0][0]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="point_requisition.yaml", key="add_point_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_add_point_requisition(self, name, description, request, expected_result):
        """【102115】新增积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = 'SELECT id FROM weee_cs.cs_points_requisition ORDER BY id DESC limit 1;'
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        PointRequisition().add_point_requisition(request["json_data"])
        end_sql = f'SELECT * FROM weee_cs.cs_points_requisition WHERE approver_id = {request["json_data"]["approverId"]} ORDER BY id DESC limit 1;'
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert db_start_resp[0][0] + 1 == db_end_resp[0][0]

    @weeeTest.params.file(file_name="point_requisition.yaml", key="edit_point_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_edit_point_requisition(self, name, description, request, expected_result):
        """编辑积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        PointRequisition().edit_point_requisition(request["json_data"])
        start_sql = f'SELECT * FROM weee_cs.cs_points_requisition WHERE id = {request["json_data"]["id"]};'
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        assert request["json_data"]["note"] == db_resp[0][6]

    #@weeeTest.params.file(file_name="point_requisition.yaml", key="approve_point_requisition")
    #@weeeTest.mark.list("Regression", 'CS')
    def test_approve_point_requisition(self, name, description, request, expected_result):
        """审批积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = 'SELECT * FROM weee_cs.cs_points_requisition WHERE status = 1 AND approver_id = 7169981 ORDER BY id DESC;'
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        json_data = {"id": db_resp[0][0]}
        PointRequisition().approve_point_requisition(json_data)
        end_sql = f"SELECT status FROM weee_cs.cs_points_requisition WHERE id = {db_resp[0][0]}"
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert db_end_resp[0][0] == 2

    #@weeeTest.params.file(file_name="point_requisition.yaml", key="batch_approve_point_requisition")
    #@weeeTest.mark.list("Regression", 'CS')
    def test_batch_approve_point_requisition(self, name, description, request, expected_result):
        """批量审批积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = 'SELECT * FROM weee_cs.cs_points_requisition WHERE status = 1 AND approver_id = 7169981 ORDER BY id DESC;'
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        if len(db_start_resp) < 1:
            json_data = {
                      "category":"Other",
                      "reasonArray":["Compensation", "Other"],
                      "note":"测试数据",
                      "pointsComment":"测试数据",
                      "approverId":"7169981",
                      "channel":"1",
                      "credits":"",
                      "userIds":"",
                      "creditsUserList":[
                          {
                              "userId":7164848,
                              "credits":10,
                              "userStr":"7164848 - weee.7164848"
                          }]
                  }
            PointRequisition().add_point_requisition(json_data)
        json_data = {"idList": [str(db_start_resp[0][0])]}
        PointRequisition().batch_approve_point_requisition(json_data)
        end_sql = f"SELECT status FROM weee_cs.cs_points_requisition WHERE id = {db_start_resp[0][0]}"
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert db_end_resp[0][0] == 2

    @weeeTest.params.file(file_name="point_requisition.yaml", key="void_point_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_void_point_requisition(self, name, description, request, expected_result):
        """void积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = 'SELECT id FROM weee_cs.cs_points_requisition WHERE status = 1;'
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        json_data = {"id": str(db_start_resp[0][0])}
        PointRequisition().void_point_requisition(json_data)
        end_sql = f"SELECT status FROM weee_cs.cs_points_requisition WHERE id = {db_start_resp[0][0]}"
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert db_end_resp[0][0] == 4

    #@weeeTest.params.file(file_name="point_requisition.yaml", key="decline_point_requisition")
    #@weeeTest.mark.list("Regression", 'CS')
    def test_decline_point_requisition(self, name, description, request, expected_result):
        """decline积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = f'SELECT id FROM weee_cs.cs_points_requisition WHERE status = 1 AND approver_id = 7169981;'
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        json_data = {"id": str(db_start_resp[0][0]), "memo": "测试Decline"}
        PointRequisition().decline_point_requisition(json_data)
        end_sql = f'SELECT status FROM weee_cs.cs_points_requisition WHERE id = {db_start_resp[0][0]};'
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert db_end_resp[0][0] == 3

    @weeeTest.params.file(file_name="point_requisition.yaml", key="batch_decline_point_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_batch_decline_point_requisition(self, name, description, request, expected_result):
        """【102506】batch decline积分申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = PointRequisition().batch_decline_point_requisition(request["json_data"])
        assert expected_result["result"] in resp["message"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')


