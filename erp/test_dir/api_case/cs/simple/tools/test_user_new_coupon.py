import weeeTest
from erp.test_dir.api.cs.tools.user_new_coupon import UserNewCoupon

class TestNewUserCoupon(weeeTest.TestCase):
    @weeeTest.params.file(file_name="user_new_coupon.yaml", key="user_new_coupon")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_new_user_coupon(self, name, description, request, expected_result):
        """【102517】查询没有新人优惠券"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = UserNewCoupon().user_new_coupon(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["userId"] == 10060559
        assert resp["object"][0]["userStatus"] == "A"

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
