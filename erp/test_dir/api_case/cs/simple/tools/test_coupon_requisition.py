import weeeTest
from erp.test_dir.api.cs.tools.coupon_requisition_api import CouponRequisition
from erp.test_dir.db_utils import DBConnect


class TestCouponRequisition(weeeTest.TestCase):

    @weeeTest.params.file(file_name="coupon_requisition.yaml", key="coupon_requisition_list")
    @weeeTest.mark.list("Regression", 'CS')
    def test_coupon_requisition_list(self, name, description, request):
        """【100140】优惠券申请查询列表"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        data = request["json_data"]
        resp = CouponRequisition().select_coupon_requisition_list(data)
        start_sql = f"SELECT count(*) FROM weee_cs.cs_coupon_requisition x WHERE user_ids REGEXP {data['userIds']};"
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["total"] == db_resp[0][0]

    @weeeTest.params.file(file_name="coupon_requisition.yaml", key="coupon_requisition_info")
    @weeeTest.mark.list("Regression", 'CS')
    def test_coupon_requisition_info(self, name, description, request):
        """优惠券申请详情查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        data = request["data"]
        resp = CouponRequisition().get_coupon_requisition_detail(data)
        start_sql = f"SELECT rec_creator_id FROM weee_cs.cs_coupon_requisition x WHERE id = {data};"
        db_resp = DBConnect().select_data_from_mysql(start_sql)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["couponType"] == "D"
        assert resp["object"]["status"] == 2
        assert resp["object"]["statusStr"] == "Approved"
        assert resp["object"]["bizType"] == "weee"
        assert resp["object"]["couponTypeStr"] == "Derate Coupons"
        assert resp["object"]["recCreatorId"] == db_resp[0][0]

    @weeeTest.params.file(file_name="coupon_requisition.yaml", key="add_coupon_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_add_coupon_requisition(self, name, description, request):
        """【102116】新增优惠券申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        data = request["json_data"]
        start_sql = f"SELECT x.* FROM weee_cs.cs_coupon_requisition x WHERE user_ids REGEXP {data['userIds']}"
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        CouponRequisition().add_coupon_requisition(request["json_data"])
        end_sql = f"SELECT x.* FROM weee_cs.cs_coupon_requisition x WHERE user_ids REGEXP {data['userIds']}"
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert len(db_start_resp) + 1 == len(db_end_resp)

    @weeeTest.params.file(file_name="coupon_requisition.yaml", key="approve_coupon_requisition")
    @weeeTest.mark.list("Regression", 'CS')
    def test_approve_coupon_requisition(self, name, description, request):
        """【102116】审批优惠券申请"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = "SELECT id FROM weee_cs.cs_coupon_requisition WHERE status = 1;"
        db_start_resp = DBConnect().select_data_from_mysql(start_sql)
        data = db_start_resp[0][0]
        resp = CouponRequisition().approve_coupon_requisition(data)
        end_sql = f"SELECT status FROM weee_cs.cs_coupon_requisition WHERE id in ({data});"
        db_end_resp = DBConnect().select_data_from_mysql(end_sql)
        assert resp["message_id"] == "10000"
        assert db_end_resp[0][0] == 2


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
