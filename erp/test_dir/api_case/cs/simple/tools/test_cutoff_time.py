import datetime
import time
import weeeTest
from erp.test_dir.api.cs.tools.cutoff_time import CutoffTime

class TestCutoffTime(weeeTest.TestCase):
    @weeeTest.params.file(file_name="cutoff_time.yaml", key="cutoff_time")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cutoff_time(self, name, description, request, expected_result):
        """【102522】查询截单时间"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = CutoffTime().cutoff_time(request["params_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert "2025-05-21" in resp["object"]["add_on_time"]
        assert "2025-05-21" in resp["object"]["cut_off_time"]

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
