import weeeTest
from erp.test_dir.api.cs.fp.order_comparison import OrderComparison


class TestOrderComparison(weeeTest.TestCase):
    @weeeTest.params.file(file_name="order_comparison.yaml", key="order_comparison")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_comparison(self, name, description, request, expected_result):
        """【102532】查询订单比较"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderComparison().order_comparison(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["userId"] == ********
        assert resp["object"][0]["userName"] == "xiao.miao6"
        assert resp["object"][0]["accountEmail"] == "<EMAIL>"
        assert resp["object"][0]["ip"] == "**************"
        assert resp["object"][0]["nickName"] == "xiao.miao6"
        assert resp["object"][0]["orderId"] == ********
        assert resp["object"][0]["shipEmail"] == "<EMAIL>"
        assert resp["object"][0]["shipAddress"] == "225 N Columbus Dr Ste 100, Chicago, Illinois, 60601, United States"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

