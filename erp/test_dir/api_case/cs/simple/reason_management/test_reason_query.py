import weeeTest
from erp.test_dir.api.cs.reason.reason_query import ReasonQuery


class TestReasonQuery(weeeTest.TestCase):
    @weeeTest.params.file(file_name="reason_query.yaml", key="list_page")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_list_page(self, name, description, request, expected_result):
        """【102563】reason query"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = ReasonQuery().list_page(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["data"][0]["id"] == 2
        assert resp["object"]["data"][0]["key"] == "shipment-issue"
        assert resp["object"]["data"][0]["valueEn"] == "Shipment issue"
        assert resp["object"]["data"][0]["valueZh"] == "物流问题"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')