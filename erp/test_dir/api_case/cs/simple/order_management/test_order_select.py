import weeeTest
from erp.test_dir.api.cs.order.order_select import OrderSelect
from erp.test_dir.db_utils import DBConnect


class TestOrderSelect(weeeTest.TestCase):

    @weeeTest.params.file(file_name="order_management.yaml", key="order_list")
    @weeeTest.mark.list('Smoke', 'CS','CSPrd')
    def test_order_select1(self, name, description, request, expected_result):
        """
        订单列表查询
        """
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_list_select(request["json"])
        expected_result_key = [key for key in expected_result.keys()][1]
        assert str(resp["object"]["data"][0][expected_result_key]) == expected_result[expected_result_key]
        assert resp["result"] is True
        assert resp["object"]["data"][0]["orderId"] == 42630198
        assert resp["object"]["data"][0]["userId"] == 10060559
        assert resp["object"]["data"][0]["orderType"] == "R-normal-0"
        assert resp["object"]["data"][0]["deliveryMode"] == "delivery"
        assert resp["object"]["data"][0]["status"] == "F"


    @weeeTest.params.file(file_name="order_management.yaml", key="order_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_detail(self, name, description, request, expected_result):
        """
        订单详情查询
        """
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_detail(request["data"])
        assert resp["object"]["id"] == expected_result["orderId"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_summary")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_summary(self, name, description, request, expected_result):
        """
        订单汇总信息查询
        """
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_summary(request["data"])
        assert resp["object"]["orderId"] == expected_result["orderId"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_amount")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_amount(self, name, description, request, expected_result):
        """
        订单支付金额信息查询
        """
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_amount(request["data"])
        assert resp["object"]["itemAmount"] == expected_result["itemAmount"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_payment_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_payment_info(self, name, description, request, expected_result):
        """
        订单支付金额信息查询
        """
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_payment_info(request["data"])
        assert resp["object"][0]["platform"] == expected_result["platform"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_return_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_return_list(self, name, description, request, expected_result):
        """订单退款信息查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_return_list(request["data"])
        assert resp["object"][0]["refundId"] == expected_result["refundId"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_activity_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_activity_list(self, name, description, request, expected_result):
        """订单退款信息查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_activity_list(request["data"])
        assert resp["object"][0]["orderId"] == expected_result["orderId"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_promotion_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_promotion_info(self, name, description, request, expected_result):
        """订单促销信息查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_promotion_info(request["data"])
        assert resp["object"][0]["couponCode"] == expected_result["couponCode"]

    @weeeTest.params.file(file_name="order_management.yaml", key="order_device_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_device_info(self, name, description, request, expected_result):
        """订单出校信息查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().order_device_info(request["data"])
        assert resp["object"]["deviceId"] == expected_result["deviceId"]


    # @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    # @weeeTest.params.file(file_name="order_management.yaml", key="order_stripe")
    # def test_order_stripe(self, name, description, request, expected_result):
    #     """订单出校信息查询"""
    #     print("执行接口名称：" + name, "  接口描述：" + description)
    #     resp = OrderSelect().order_stripe(request["data"])
    #     assert resp["message_id"] == expected_result["message_id"]

    # @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    # @weeeTest.params.file(file_name="order_management.yaml", key="query_order_note")
    # def test_order_order(self, name, description, request, expected_result):
    #     """订单备注信息查询"""
    #     print("执行接口名称：" + name, "  接口描述：" + description)
    #     resp = OrderSelect().query_order_note(request["data"])
    #     assert resp["message_id"] == expected_result["message_id"]
    #
    # @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    # @weeeTest.params.file(file_name="order_management.yaml", key="update_order_note")
    # def test_add_or_update_order_note(self, name, description, request, expected_result):
    #     """订单新增或更新备注信息"""
    #     print("执行接口名称：" + name, "  接口描述：" + description)
    #     resp = OrderSelect().add_or_update_order_note(request["json_data"])
    #     assert resp["message_id"] == expected_result["message_id"]

    @weeeTest.params.file(file_name="order_management.yaml", key="query_order_fp_note")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_fp_note(self, name, description, request, expected_result):
        """查询订单FP备注信息"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderSelect().query_order_fp_note(request["json_data"])
        assert resp["object"]["data"][0]["userId"] == expected_result["userId"]

    @weeeTest.params.file(file_name="order_management.yaml", key="add_order_fp_note")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_order_fp_note(self, name, description, request, expected_result):
        """新增订单FP备注信息"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = "SELECT * FROM weee_cs.cs_user_fp_note WHERE user_id = 7169981 AND delete_flag = 0"
        db_start = len(DBConnect().select_data_from_mysql(start_sql))
        OrderSelect().add_order_fp_note(request["json_data"])
        end_sql = "SELECT x.* FROM weee_cs.cs_user_fp_note x WHERE user_id = 7169981 AND delete_flag = 0"
        db_end = len(DBConnect().select_data_from_mysql(end_sql))
        sum_db = db_end-db_start
        assert sum_db == 1

    @weeeTest.params.file(file_name="order_management.yaml", key="delete_order_fp_note")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_delete_order_fp_note(self, name, description, request, expected_result):
        """删除订单FP备注信息"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        start_sql = "SELECT * FROM weee_cs.cs_user_fp_note WHERE user_id = 7169981 AND delete_flag = 0 ORDER BY id DESC;"
        db_data = DBConnect().select_data_from_mysql(start_sql)
        db_start = len(db_data)
        OrderSelect().delete_order_fp_note(db_data[0][0])
        end_sql = "SELECT x.* FROM weee_cs.cs_user_fp_note x WHERE user_id = 7169981 AND delete_flag = 0"
        db_end = len(DBConnect().select_data_from_mysql(end_sql))
        sum_db = db_start - db_end
        assert sum_db == 1


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')


















