import weeeTest
from erp.test_dir.api.cs.order.order_detail import OrderDetail
from erp.test_dir.db_utils import DBConnect


class TestOrderDetail(weeeTest.TestCase):
    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_list(self, name, description, request, expected_result):
        """【102459】根据用户ID查询"""
        print("执行接口名称："+name, "接口描述："+description)
        resp = OrderDetail().cs_order_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"]['data'][0]["userId"]) == expected_result["userId"]
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 20
        assert resp["object"]["pages"] == 0


    @weeeTest.params.file(file_name="order_detail.yaml", key="order_cancel_order")
    @weeeTest.mark.list('Smoke', 'CS')
    def test_order_cancel_order(self, name, description, request, expected_result):
        """【101742】取消订单接口"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = OrderDetail().order_cancel_order(request["json_data"])
        assert resp["result"] is True
        assert resp["message_id"] == "10000"
        assert resp["object"][0] == "42637182order payment amount all refunded, can't be cancelled."

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail(self, name, description, request, expected_result):
        """【102254】order detail"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"]["id"]) == expected_result["orderId"]
        assert resp["object"]["userId"] == 10060559
        assert resp["object"]["username"] == "xiao.ye888"
        assert resp["object"]["status"] == "F"
        assert resp["object"]["userStatus"] == "A"
        assert resp["object"]["userStatusStr"] == "Normal"
        assert resp["object"]["orderSource"] == "weee"
        assert resp["object"]["memberId"] == 1499837

    @weeeTest.params.file(file_name="order_detail.yaml", key="order_insights_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_order_insights_list(self, name, description, request, expected_result):
        """【102474】order_insights_list"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().order_insights_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 10

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_product_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_product_list(self, name, description, request, expected_result):
        """【102464】order detail 查询product list"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_product_list(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["inventoryId"] == 25
        assert resp["object"][0]["quantity"] == 1
        assert resp["object"][0]["refundedQty"] == 1
        assert resp["object"][0]["skuId"] == 68987
        assert resp["object"][0]["storage"] == "Normal"
        assert resp["object"][0]["title"] == "洽洽 香瓜子 五香味"
        assert resp["object"][0]["warehouse"] == "LA - La Mirada"
        assert resp["object"][1]["skuId"] == 87602
        assert resp["object"][1]["title"] == "百世兴 酒鬼花生 川式麻辣味 60 克"
        assert resp["object"][1]["warehouse"] == "LA - La Mirada"

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_refund_count")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_refund_count(self, name, description, request, expected_result):
        """【102466】order detail 查询refund count"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_refund_count(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_log")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_log(self, name, description, request, expected_result):
        """【102467】order detail 查询log"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_log(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"][0]["orderId"]) == expected_result["orderId"]
        assert resp["object"][0]["detectionType"] == 2
        assert resp["object"][0]["reasonType"] == 3
        assert resp["object"][0]["status"] == 1

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_promotion")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_promotion(self, name, description, request, expected_result):
        """【102468】查询order 促销信息"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_promotion(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["amount"] == 6.29
        assert resp["object"][0]["referId"] == "DYHVMXGN"
        assert resp["object"][0]["productName"] == "Beef Center Heel Muscle, Original Package, Frozen"
        assert resp["object"][0]["sku"] == 39334
        assert resp["object"][0]["type"] == "coupon"
        assert resp["object"][1]["amount"] == 0.56
        assert resp["object"][1]["referId"] == "DYHVMXGN"
        assert resp["object"][1]["productName"] == "Walovi Wanglaoji Herbal Beverage"
        assert resp["object"][1]["sku"] == 96024
        assert resp["object"][1]["type"] == "coupon"
        assert resp["object"][2]["amount"] == 3.15
        assert resp["object"][2]["referId"] == "DYHVMXGN"
        assert resp["object"][2]["productName"] == "Arctic Ocean Mandarin Flavored Soda"
        assert resp["object"][2]["sku"] == 95513
        assert resp["object"][2]["type"] == "coupon"

    @weeeTest.params.file(file_name="order_detail.yaml", key="query_device_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_query_device_info(self, name, description, request, expected_result):
        """【102469】查询设备信息"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().query_device_info(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["deviceId"] == "3682825"
        assert resp["object"]["ip"] == "**************"


    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_same_payment")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_same_payment(self, name, description, request, expected_result):
        """【102465】order detail 查询是否有相同支付订单"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_same_payment(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert str(resp["object"]["orderId"]) == expected_result["orderId"]
        assert resp["object"]["samePay"] == True
        assert resp["object"]["transactionId"] == "38kf4yd3"

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_request_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_request_list(self, name, description, request, expected_result):
        """【102470】order detail 查询相关售后申请"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_request_list(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["requestId"] == 4645
        assert resp["object"][0]["status"] == "Completed"
        assert "10060559-小.ye" in resp["object"][0]["updater"]
        assert "10060559-小.ye" in resp["object"][0]["creator"]
        assert resp["object"][0]["isMarket"] == 0

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_query_order_related_case")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_query_order_related_case(self, name, description, request, expected_result):
        """【102471】order detail 查询相关case"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_query_order_related_case(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["caseNumber"] == "100042416"
        assert resp["object"][0]["caseId"] == 42416
        assert resp["object"][0]["statusStr"] == "Closed"
        assert resp["object"][0]["subject"] == "Order #42618753 - 百世兴 酒鬼花生 川式麻辣味 60 克"
        assert resp["object"][1]["caseNumber"] == "100042387"
        assert resp["object"][1]["caseId"] == 42387
        assert resp["object"][1]["statusStr"] == "Closed"
        assert resp["object"][1]["subject"] == "Order #42618753 - 百世兴 酒鬼花生 川式麻辣味"


    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_get_user_tag")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_get_user_tag(self, name, description, request, expected_result):
        """order detail 获取用户标签"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_get_user_tag(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["id"] == 13
        assert resp["object"][0]["name"] == "CCPA"
        assert resp["object"][0]["type"] == "Manual"
        assert resp["object"][0]["bind"] == True

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_user_note")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_user_note(self, name, description, request, expected_result):
        """order detail 查询用户备注"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_user_note(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["total"] == 0

    @weeeTest.params.file(file_name="order_detail.yaml", key="cs_order_detail_blacklist_exisblack")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_order_detail_blacklist_exisblack(self, name, description, request, expected_result):
        """order detail 查询用户是否有被黑名单拦截"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = OrderDetail().cs_order_detail_blacklist_exisblack(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] == False


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

