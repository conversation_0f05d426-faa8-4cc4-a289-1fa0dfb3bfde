import weeeTest
from erp.test_dir.api.cs.login.cs_login_api import CSLoginApi


class TestLoginDemo(weeeTest.TestCase):

    @weeeTest.params.file(file_name="login.yaml", key="login")
    @weeeTest.mark.list('Smoke', 'CS')
    def test_login(self, name, description, request, expected_result):
        """登录"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = CSLoginApi().cs_login(request["json"])
        if resp["message_id"] == "10000":
            assert str(resp["object"]["realm_user_id"]) == request["json"]["account"]
        else:
            assert resp["message_id"] == expected_result["message_id"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')



