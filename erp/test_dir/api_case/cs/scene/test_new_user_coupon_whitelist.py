import datetime
import time
import weeeTest
from weeeTest import log
from erp.test_dir.api.cs.fp.new_user_coupon_whitelist import New<PERSON>ser<PERSON><PERSON>pon<PERSON><PERSON>elist
from erp.test_dir.db_utils import DBConnect


class TestNewUserCouponWhitelist(weeeTest.TestCase):
    id = ''

    @weeeTest.params.file(file_name="new_user_coupon_whitelist.yaml", key="new_user_coupon_whitelist_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_new_user_coupon_whitelist_create(self, name, description, request, expected_result):
        """【102549】新增新用户优惠券白名单"""
        print(description)
        resp = NewUserCouponWhitelist().new_user_coupon_whitelist_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] == "success"

    @weeeTest.params.file(file_name="new_user_coupon_whitelist.yaml", key="query_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_query_list(self, name, description, request, expected_result):
        """【102547】查询列表"""
        print(description)
        time.sleep(15)
        data = request["json_data"]
        data["applyTimeRange"] = [(datetime.datetime.now() + datetime.timedelta(days=-90)).strftime("%Y-%M-%d"), str(datetime.date.today())]
        resp = NewUserCouponWhitelist().query_list(data)
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["status"] == 0
        assert resp["object"]["data"][0]["userId"] == 5955706
        assert resp["object"]["data"][0]["userName"] == "weee.5955706"
        assert resp["object"]["data"][0]["recCreatorName"] == "qifang.ye"
        TestNewUserCouponWhitelist.id = resp["object"]["data"][0]["id"]
        if TestNewUserCouponWhitelist.id:
            sql = fr"DELETE FROM `weee_cs`.`cs_new_user_coupon_whitelist` WHERE `id` = {TestNewUserCouponWhitelist.id}"
            db_res = DBConnect().update_data_from_mysql(sql)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')