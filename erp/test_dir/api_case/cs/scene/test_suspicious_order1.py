import time
import weeeTest
from weeeTest import log
from erp.test_dir.api.cs.fp.suspicious_order1 import SuspiciousOrder1
from erp.test_dir.db_utils import DBConnect


class TestSuspiciousOrder1(weeeTest.TestCase):
    id = ""

    @weeeTest.params.file(file_name="suspicious_order1.yaml", key="add_suspicious_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_SuspiciousOrder1(self, name, description, request, expected_result):
        """【102528】新增可疑订单"""
        print(description)
        resp = SuspiciousOrder1().add_suspicious_order(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] is True

    @weeeTest.params.file(file_name="suspicious_order1.yaml", key="select_suspicious_order_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_SuspiciousOrder1(self, name, description, request, expected_result):
        """【102524】查询可疑订单"""
        print(description)
        resp = SuspiciousOrder1().select_suspicious_order_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["userId"] == 10060559
        assert resp["object"]["data"][0]["orderId"] == 42640674
        assert resp["object"]["data"][0]["typeStr"] == "Suspected Order"
        assert resp["object"]["data"][0]["orderStatus"] == "N"
        assert resp["object"]["data"][0]["orderAmount"] == 20.95
        assert resp["object"]["data"][0]["orderType"] == "Normal"
        assert resp["object"]["data"][0]["deliveryMode"] == "delivery"
        assert resp["object"]["data"][0]["paymentMethod"] == "CardBraintree"
        TestSuspiciousOrder1.id = resp["object"]["data"][0]["id"]

    @weeeTest.params.file(file_name="suspicious_order1.yaml", key="assign_suspicious_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_SuspiciousOrder1(self, name, description, request, expected_result):
        """【102526】可疑订单指派给"""
        print(description)
        resp = SuspiciousOrder1().assign_suspicious_order(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] is True

    @weeeTest.params.file(file_name="suspicious_order1.yaml", key="assign_to_me_suspicious_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_SuspiciousOrder1(self, name, description, request, expected_result):
        """【102527】可疑订单指派给某人"""
        print(description)
        resp = SuspiciousOrder1().assign_to_me_suspicious_order(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
