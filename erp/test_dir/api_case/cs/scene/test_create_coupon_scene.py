import weeeTest
from erp.test_dir.api.cs.tools.coupon_requisition_api import CouponRequisition
from erp.test_dir.db_utils import DBConnect


class TestCreateCouponRequisition(weeeTest.TestCase):
    """优惠券申请场景"""

    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_coupon_requisition(self):
        """【102116】创建优惠券申请审批流程"""
        data = {"userIds": "7164848"}
        CouponRequisition().add_coupon_requisition(data)
        sql = f"SELECT id FROM `weee_cs`.`cs_coupon_requisition` WHERE `user_ids` IN ({data['userIds']}) ORDER BY `id` DESC LIMIT 1;"
        db_res = DBConnect().select_data_from_mysql(sql)
        resp = CouponRequisition().approve_coupon_requisition(db_res[0][0])
        sql = f"SELECT status FROM `weee_cs`.`cs_coupon_requisition` WHERE id = {db_res[0][0]}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert db_res[0][0] == 2


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')














