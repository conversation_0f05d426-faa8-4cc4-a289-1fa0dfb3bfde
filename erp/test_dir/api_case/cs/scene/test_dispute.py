import time
import weeeTest
from weeeTest import log
from erp.test_dir.api.cs.fp.dispute import Dispute
from erp.test_dir.db_utils import DBConnect


class TestDispute(weeeTest.TestCase):
    id = ''

    @weeeTest.params.file(file_name="dispute.yaml", key="cs_dispute")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_dispute(self, name, description, request, expected_result):
        """【102550】新增dispute"""
        print(description)
        resp = Dispute().cs_dispute(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDispute.id = resp["object"]

    @weeeTest.params.file(file_name="dispute.yaml", key="query_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_query_list(self, name, description, request, expected_result):
        """【102551】查询列表"""
        print(description)
        resp = Dispute().query_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["paymentChannel"] == "Braintree"
        assert resp["object"]["data"][0]["disputeType"] == "Chargeback"
        assert resp["object"]["data"][0]["orderId"] == "42640605"
        assert resp["object"]["data"][0]["userId"] == 10060559
        assert resp["object"]["data"][0]["transactionId"] == "2s7zp8v4"
        assert resp["object"]["data"][0]["disputeAmount"] == "25.90"
        assert resp["object"]["data"][0]["disputeReason"] == "Fraud"
        assert resp["object"]["data"][0]["disputeStatus"] == "Open"
        assert resp["object"]["data"][0]["evidenceStatus"] == "Uploaded"
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 20
        TestDispute.id = resp["object"]["data"][0]["id"]

    @weeeTest.params.file(file_name="dispute.yaml", key="create_WatchList_FromDispute")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_WatchList_FromDispute(self, name, description, request, expected_result):
        """【102552】批量加黑名单"""
        print(description)
        request["json_data"]["id"] = TestDispute.id
        resp = Dispute().create_WatchList_FromDispute(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="dispute.yaml", key="query_list1")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_query_list1(self, name, description, request, expected_result):
        """【102551】查询列表"""
        print(description)
        resp = Dispute().query_list1(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["paymentChannel"] == "Braintree"
        assert resp["object"]["data"][0]["disputeType"] == "Chargeback"
        assert resp["object"]["data"][0]["orderId"] == "42640605"
        assert resp["object"]["data"][0]["userId"] == 10060559
        assert resp["object"]["data"][0]["transactionId"] == "2s7zp8v4"
        assert resp["object"]["data"][0]["disputeAmount"] == "25.90"
        assert resp["object"]["data"][0]["disputeReason"] == "Fraud"
        assert resp["object"]["data"][0]["disputeStatus"] == "Open"
        assert resp["object"]["data"][0]["evidenceStatus"] == "Uploaded"
        assert resp["object"]["data"][0]["watchListStatus"] == "Active"
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 20
        if TestDispute.id:
            sql = fr"DELETE FROM `weee_cs`.`cs_dispute` WHERE `id` = {TestDispute.id}"
            db_res = DBConnect().update_data_from_mysql(sql)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')