import weeeTest
from erp.test_dir.api.pps.event.event_detail import EventDetail
import datetime
from erp.test_dir.db_utils import DBConnect


class TestEventDetail(weeeTest.TestCase):

    @weeeTest.mark.list('SRM', 'PPS', 'Smoke')
    def test_add_single_exposure_product_fail(self):
        """新增单个曝光产品失败"""
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
               "product_id = 109460 and sales_org_id =2")
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = EventDetail().add_single_exposure_product(sku=109460, group_id='1', sales_org=[2], tag='featured',
                                                         start_date='2024-05-30', end_date='2024-06-30',
                                                         event_code='202505_AdsKU_1')
        assert resp["result"] is False
        assert resp["message"] == "Exposure product already exists for the Sales Org Id 2"
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_bulk_approve_reject_exposure_products(self):
        """批量Approve/Reject曝光产品"""
        approve_resp = EventDetail().bulk_approve_reject_exposure_products(event_method='202505_AdsKU_1/bulk_approve/',
                                                                           ids=[106356, 106352, 106355])
        assert approve_resp["result"] is True
        assert approve_resp["object"] == 'Approved successfully'
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
               "id in(106356, 106352, 106355)")
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][10] == 1
        reject_resp = EventDetail().bulk_approve_reject_exposure_products(event_method='202505_AdsKU_1/bulk_reject/',
                                                                          ids=[106356, 106352, 106355])
        assert reject_resp["result"] is True
        assert reject_resp["object"] == 'Rejected successfully'
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][10] == 0

    @weeeTest.mark.list('SRM', 'PPS', 'Smoke')
    def test_bulk_reject_exposure_products_fail(self):
        """批量Reject曝光产品失败"""
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and id in "
               "(106220, 106263)")
        db_res_before = DBConnect().select_data_from_mysql(sql)
        reject_resp = EventDetail().bulk_approve_reject_exposure_products(event_method='202505_AdsKU_1/bulk_reject/',
                                                                          ids=[106220, 106263])
        assert reject_resp["result"] is False
        assert reject_resp["message"] == 'Request Products were already rejected'
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_bulk_move_group_exposure_products_success(self):
        """批量移动曝光产品group"""
        resp = EventDetail().move_group_for_single_exposure_product(event_code='202501_1LNYC_8', group_id='3431',
                                                                    ids=[85296, 85373, 85450])
        assert resp["result"] is True
        assert resp["object"] == "Moved successfully to Group : meat recipes"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202501_1LNYC_8' and id in "
               "(85296, 85373, 85450)")
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][6] == 3431
        move_again = EventDetail().move_group_for_single_exposure_product(event_code='202501_1LNYC_8', group_id='3204',
                                                                    ids=[85296, 85373, 85450])
        assert move_again["result"] is True
        assert move_again["object"] == "Moved successfully to Group : fish collection"
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][6] == 3204
        move_same = EventDetail().move_group_for_single_exposure_product(event_code='202501_1LNYC_8', group_id='3204',
                                                                    ids=[85296, 85373, 85450])
        assert move_same["result"] is True
        assert move_same["object"] == "Moved successfully to Group : fish collection"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202501_1LNYC_8' and id in "
               "(85296, 85373, 85450)")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][6] == 3204

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_bulk_approve_reject_promotion_products(self):
        """批量Approve/Reject促销产品"""
        approve_resp = EventDetail().bulk_action_for_promotion_products(event_method='202504_AdsWi_1/bulk_approve/',
                                                                        ids=[76292321, 76292259])
        assert approve_resp["result"] is True
        assert approve_resp["object"] == 'Approved successfully'
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and id in "
               "(76292321, 76292259)")
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][16] == 1
        reject_resp = EventDetail().bulk_action_for_promotion_products(event_method='202504_AdsWi_1/bulk_reject/',
                                                                       ids=[76292321, 76292259])
        assert reject_resp["result"] is True
        assert reject_resp["object"] == 'Rejected successfully'
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and id in "
               "(76292321, 76292259)")
        db_res = DBConnect().select_data_from_mysql(sql)
        for x in range(len(db_res)):
            assert db_res[x][16] == 0

    @weeeTest.mark.list('SRM', 'PPS', 'Smoke')
    def test_bulk_reject_promotion_products_fail(self):
        """批量Reject促销产品失败"""
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and id in "
               "(76292321, 76292259)")
        db_res_before = DBConnect().select_data_from_mysql(sql)
        reject_resp = EventDetail().bulk_action_for_promotion_products(event_method='202504_AdsWi_1/bulk_reject/',
                                                                       ids=[76292321, 76292259])
        assert reject_resp["result"] is False
        assert reject_resp["message"] == 'Request Products were already rejected'
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after
        sql_2 = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and id in "
                 "(348, 350, 349)")
        db_res_before = DBConnect().select_data_from_mysql(sql_2)
        approve_resp = EventDetail().bulk_action_for_promotion_products(event_method='202504_AdsWi_1/bulk_approve/',
                                                                        ids=[348, 350, 349])
        assert approve_resp["result"] is False
        assert approve_resp["message"] == "Promotion Event Product Id 348 doesn't belong to the requested event"
        db_res_after = DBConnect().select_data_from_mysql(sql_2)
        assert db_res_before == db_res_after


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
