import weeeTest
from erp.test_dir.api.pps.event.event_list import EventList
import datetime
from erp.test_dir.db_utils import DBConnect


class TestEventList(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_get_event_list(self):
        """查询获取event list"""
        resp = EventList().get_event_list(start_date='2025-05-01', end_date='2025-05-10', event_title='Automation',
                                          event_owner='yi.shen', event_type=[1, 3])
        assert resp["result"] is True
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event WHERE title like '%Automation%' and owner_user_email like "
               "'%yi.shen%' and type_id in (1,3) and start_time <= '2025-05-10 23:59:59' and end_time >= '2025-05-01 "
               "00:00:00' ORDER BY start_time ASC, end_time ASC")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == len(resp["object"])
        for x in range(len(resp["object"])):
            assert resp["object"][x]["PromotionEventId"] in db_res[x]

    @weeeTest.params.file(file_name="event_list.yaml", key="update_event_success")
    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_update_event_success(self, request):
        """更新single event成功"""
        resp = EventList().update_event(request["json_data"], request["url"])
        assert resp["result"] is True
        assert resp["object"] == "Promotion Event updated successfully"
        sql_1 = 'SELECT * FROM weee_pricing.pps_promotion_event WHERE event_code ='
        sql_2 = request["url"][38:]
        sql = sql_1 + '\'' + sql_2 + '\''
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][4] == request["json_data"]["title"]
        assert request["json_data"]["start_date"] in str(db_res[0][8])
        assert request["json_data"]["end_date"] in str(db_res[0][9])
        assert db_res[0][16] == request["json_data"]["description"]

    @weeeTest.params.file(file_name="event_list.yaml", key="update_event_fail")
    @weeeTest.mark.list('SRM', 'PPS', 'Smoke')
    def test_update_event_fail(self, request):
        """更新single event失败"""
        sql_1 = 'SELECT * FROM weee_pricing.pps_promotion_event WHERE event_code ='
        sql_2 = request["url"][38:]
        sql = sql_1 + '\'' + sql_2 + '\''
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = EventList().update_event(request["json_data"], request["url"])
        assert resp["result"] is False
        assert resp["message"] == request["message"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_delete_event_fail(self):
        """删除不是未来的event"""
        resp = EventList().delete_event('202404_1AAPI_1')
        assert resp["result"] is False
        assert resp["message"] == "Promotion Event isn't eligible for delete"
        sql = 'SELECT * FROM weee_pricing.pps_promotion_event WHERE event_code = "202404_1AAPI_1"'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_get_store_keys(self):
        """查询获取event的storefront所有选项"""
        resp = EventList().get_store_front_keys()

        assert resp["result"] is True
        assert resp["object"] == [
        {
            "key": "lang_zh",
            "label": "Simplified Chinese"
        },
        {
            "key": "lang_zh-Hant",
            "label": "Traditional Chinese"
        },
        {
            "key": "lang_vi",
            "label": "Vietnamese"
        },
        {
            "key": "lang_es",
            "label": "Spanish"
        },
        {
            "key": "lang_ja",
            "label": "Japanese"
        },
        {
            "key": "lang_ko",
            "label": "Korean"
        },
        {
            "key": "store_cn",
            "label": "English-Chinese"
        },
        {
            "key": "store_ja",
            "label": "English-Japanese"
        },
        {
            "key": "store_ko",
            "label": "English-Korean"
        },
        {
            "key": "store_es",
            "label": "English-Mexican"
        },
        {
            "key": "store_ph",
            "label": "English-Filipino"
        },
        {
            "key": "store_vn",
            "label": "English-Vietnamese"
        },
        {
            "key": "store_us",
            "label": "English-American"
        },
        {
            "key": "store_in",
            "label": "English-Indian"
        }
    ]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_event_type_keys(self):
        """查询获取event的type所有选项"""
        resp = EventList().get_event_type_keys()
        assert resp["result"] is True
        assert resp["object"] == [
            {
                "key": "1",
                "label": "Crazy 8"
            },
            {
                "key": "2",
                "label": "Weekly Deal"
            },
            {
                "key": "3",
                "label": "Vendor Ads"
            },
            {
                "key": "5",
                "label": "Vendor Promotion"
            },
            {
                "key": "6",
                "label": "Weekly Collection"
            },
            {
                "key": "7",
                "label": "Hot Deal"
            }
        ]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_sales_org(self):
        """查询获取event进页面时的所有Sales org"""
        resp = EventList().get_event_sales_org()
        assert resp["result"] is True
        assert resp["object"] == [
            {
                "id": 1,
                "title": "SF Bay Area",
                "shortCode": "SF",
                "color": "magenta"
            },
            {
                "id": 2,
                "title": "LA - Greater",
                "shortCode": "LA",
                "color": "cyan"
            },
            {
                "id": 3,
                "title": "MAIL ORDER",
                "shortCode": "MOW",
                "color": "volcano"
            },
            {
                "id": 4,
                "title": "Seattle",
                "shortCode": "SEA",
                "color": "green"
            },
            {
                "id": 7,
                "title": "New York",
                "shortCode": "NY",
                "color": "red"
            },
            {
                "id": 10,
                "title": "Houston",
                "shortCode": "HOU",
                "color": "lime"
            },
            {
                "id": 15,
                "title": "Chicago",
                "shortCode": "CHI",
                "color": "gold"
            },
            {
                "id": 23,
                "title": "Tampa",
                "shortCode": "TAM",
                "color": "orange"
            },
            {
                "id": 24,
                "title": "MOF",
                "shortCode": "MOF-NJ",
                "color": "Peru"
            }
        ]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
