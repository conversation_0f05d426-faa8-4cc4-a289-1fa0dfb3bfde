import weeeTest
from erp.test_dir.api.pps.products_view.products_view import ProductsView
import datetime
from erp.test_dir.db_utils import DBConnect
import csv
from io import StringIO
import json


class TestProductView(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_delete_active_promotion(self):
        """删除已经结束的promotion"""
        resp = ProductsView().delete_promotion(pep_ids=[76089012], price_special_ids=[14544512])
        assert resp["result"] is False
        assert resp["message"] == "This Special price status is not Inactive."
        sql = 'SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_price_special_id=14544512'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_edit_active_promotion(self):
        """编辑已经结束的promotion"""
        resp = ProductsView().edit_single_promotion(pep_ids=[76089012], price_special_ids=[14544512],
                                                    price_priority='N', promotion_price='', discount='15',
                                                    lightning_flag='N', start_time='2025-03-01 00:00:00',
                                                    end_time='2025-03-01 02:09:22',
                                                    business_reason='Vendor Sponsored Cost Reduction',
                                                    reason_id='7', max_qty='15')
        assert resp["result"] is False
        assert resp["message"] == "Can not edit promotion ended already."
        sql = 'SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_price_special_id=14544512'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][23]) == '2025-02-12 17:57:09'

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_edit_inactive_promotion(self):
        """编辑未来的promotion"""
        resp = ProductsView().edit_single_promotion(pep_ids=[76200234], price_special_ids=[15931805],
                                                    price_priority='N', promotion_price='', discount='15',
                                                    lightning_flag='N',
                                                    start_time=(datetime.datetime.today() + datetime.timedelta(
                                                        days=120)).strftime("%Y-%m-%d 23:59:59"),
                                                    end_time=(datetime.datetime.today() + datetime.timedelta(
                                                        days=121)).strftime("%Y-%m-%d 23:59:59"),
                                                    business_reason='Vendor Sponsored Cost Reduction',
                                                    reason_id='7', max_qty='2')
        assert resp["result"] is True
        # assert resp["object"] == "success"
        sql = 'SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_price_special_id=15931805'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][6]) == (datetime.datetime.today() + datetime.timedelta(days=121)).strftime(
            "%Y-%m-%d 23:59:59")

    @weeeTest.params.file(file_name="product_list.yaml", key="link_event_fail")
    @weeeTest.mark.list('SRM', 'PPS', 'Smoke')
    def test_link_event_fail(self, request):
        """link event失败"""
        sql = f"SELECT * FROM weee_pricing.pps_promotion_event WHERE event_code = '{request['json_data']['eventCode'][0]}'"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = ProductsView().promotion_link_event(request["json_data"])
        assert resp["result"] is True
        assert request["message"] in resp["object"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_search_promotion_by_multi_filters(self):
        """多条件组合搜索promotion"""
        resp = ProductsView().get_promotion_list(
            sales_org='4,10,15,23', start_date=str(datetime.date(2024, 5, 3)), end_date=str(datetime.date(2024, 5, 3)),
            event_type='', event_code='',
            local_owner_id=3780320, sku='', promo_reason='2', promo_type='1',
            is_unlinked_only='yes', page_no=1, page_size=50, sort_by='product_id', sort_order='asc'
        )
        assert resp["result"] is True
        for x in range(resp["object"]["total"]):
            assert resp["object"]["data"][x]["ownerName"] == "Hang Yin"
            assert resp["object"]["data"][x]["isLightningDeal"] == "Y"
            assert resp["object"]["data"][x]["pepIds"] == []
            sql = f"SELECT * FROM weee_comm.gb_product_price_special WHERE id = {resp['object']['data'][x]['priceSpecialIds'][0]}"
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][1] == 'D'
        search_event_code = ProductsView().get_promotion_list(sales_org='4,10,15,23',
                                                              event_code='202504_INCra_14', page_no=1, page_size=50,
                                                              sort_by='product_id', sort_order='asc'
                                                              )
        assert search_event_code["result"] is True
        for x in range(search_event_code["object"]["total"]):
            assert search_event_code["object"]["data"][x]["eventDtos"][0]["eventCode"] == "202504_INCra_14"
            sql = ('SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ="202504_INCra_14" and '
                   'sales_org_id in (4,10,15,23)')
            db_res = DBConnect().select_data_from_mysql(sql)
            assert len(db_res) == search_event_code["object"]["total"]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_search_promotion_by_event_code(self):
        """搜索promotion by event code"""
        resp_no_result = ProductsView().get_promotion_list(
            sales_org='1,2,7,4,10,15,23', start_date='', end_date='', event_type='', event_code='202504_AdsHN_1',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp_no_result["result"] is True
        sql_no_result = 'SELECT * FROM weee_pricing.pps_promotion_event_product where event_code ="202504_AdsHN_1"'
        db_res_no_result = DBConnect().select_data_from_mysql(sql_no_result)
        assert len(db_res_no_result) == resp_no_result["object"]["total"]
        resp = ProductsView().get_promotion_list(
            sales_org='1,2,7,4,10,15,23', start_date='', end_date='', event_type='', event_code='202504_AdsEa_1',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_pricing.pps_promotion_event_product where event_code ="202504_AdsEa_1"'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == resp["object"]["total"]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_search_promotion_by_event_type(self):
        """搜索promotion by event type"""
        resp_no_result = ProductsView().get_promotion_list(
            sales_org='16', start_date=str(datetime.date(2024, 12, 17)),
            end_date=str(datetime.date(2025, 2, 15)), event_type=2, event_code='',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp_no_result["result"] is True
        sql_no = ('SELECT x.* FROM weee_pricing.pps_promotion_event_product x join weee_pricing.pps_promotion_event y '
                  'join weee_comm.gb_product_price_special z on x.event_code = y.event_code and '
                  'x.product_price_special_id =z.id WHERE y.type_id =2 and x.sales_org_id =16 and x.product_start_time '
                  '<"2024-12-18" and x.product_end_time >"2025-02-16" and z.status not in ("D","X")')
        db_res_no_result = DBConnect().select_data_from_mysql(sql_no)
        assert len(db_res_no_result) == resp_no_result["object"]["total"]
        resp = ProductsView().get_promotion_list(
            sales_org='1', start_date=str(datetime.date(2024, 12, 17)),
            end_date=str(datetime.date(2025, 2, 15)), event_type=5, event_code='',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp["result"] is True
        sql = ('SELECT x.* FROM weee_pricing.pps_promotion_event_product x join weee_pricing.pps_promotion_event y '
               'join weee_comm.gb_product_price_special z on x.event_code = y.event_code and '
               'x.product_price_special_id =z.id WHERE y.type_id = 5 and x.sales_org_id =1 and x.product_start_time '
               '<"2024-12-18" and x.product_end_time >"2025-02-16" and z.status not in ("D","X")')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == resp["object"]["total"]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_search_promotion_by_lightning(self):
        """搜索promotion by only lightning deals"""
        resp_no_result = ProductsView().get_promotion_list(
            sales_org='3,16,24,29', start_date=str(datetime.date(2024, 12, 17)),
            end_date=str(datetime.date(2025, 2, 15)), event_type='1', event_code='',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type=1,
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp_no_result["result"] is True
        sql_no = ('SELECT * FROM weee_pricing.pps_promotion_event_product WHERE sales_org_id in (3,16,24,'
                  '29) and product_start_time >"2024-12-18" and product_end_time <"2025-02-16" and is_lightning_deal '
                  '="Y"')
        db_res_no_result = DBConnect().select_data_from_mysql(sql_no)
        assert len(db_res_no_result) == resp_no_result["object"]["total"]
        resp = ProductsView().get_promotion_list(
            sales_org='4', start_date=str(datetime.date(2025, 3, 2)),
            end_date=str(datetime.date(2025, 3, 5)), event_type='7', event_code='',
            local_owner_id='', sku='', promo_reason='7', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp["result"] is True
        sql = ('SELECT * FROM weee_pricing.pps_promotion_event_product x join weee_pricing.pps_promotion_event y join '
               'weee_comm.gb_product_price_special z on x.event_code = y.event_code and x.product_price_special_id = '
               'z.id WHERE x.sales_org_id = 4 and y.type_id = 7 and z.reason = 7 and x.product_start_time >= '
               '"2025-02-28 00:00:00" and product_end_time <= "2025-03-08 23:59:59"')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == resp["object"]["total"]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_search_promotion_by_sku_level(self):
        """搜索promotion by sku id/title/department/local owner"""
        resp_no_result = ProductsView().get_promotion_list(
            sales_org='1', start_date=str(datetime.date(2024, 12, 20)),
            end_date=str(datetime.date(2025, 2, 18)), event_type='', event_code='',
            department='10', local_owner_id='', sku=105585, title='Medal Lechon Kawali', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp_no_result["result"] is True
        assert resp_no_result["object"]["total"] == 0
        resp = ProductsView().get_promotion_list(
            sales_org='1', start_date=str(datetime.date(2024, 12, 20)),
            end_date=str(datetime.date(2025, 2, 18)), event_type='', event_code='',
            department='08', local_owner_id='7788786', sku=105585, title='Medal Lechon Kawali', promo_reason='',
            promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        assert resp["result"] is True
        assert resp["object"]["data"][0]["productId"] == 105585
        assert 'Medal Lechon Kawali' in resp["object"]["data"][0]["productTitle"]
        sql = 'SELECT * FROM weee_comm.gb_product WHERE id =105585'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert '08' in db_res[0][23]
        sql_local_owner = ('SELECT x.product_id,x.weee_buyer_id,y.name FROM weee_comm.gb_product_sales x join '
                           'weee_comm.gb_weee_buyer y on x.weee_buyer_id = y.user_id WHERE x.product_id =105585 and '
                           'x.sales_org_id =1')
        db_res_local_owner = DBConnect().select_data_from_mysql(sql_local_owner)
        assert db_res_local_owner[0][2] == resp["object"]["data"][0]["ownerName"]

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_search_filter_source(self):
        """搜索1P/3P"""
        resp = ProductsView().get_promotion_list(
            sales_org='4', start_date=str(datetime.date(2025, 1, 21)),
            end_date=str(datetime.date(2025, 1, 21)), event_type='', event_code='',
            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc', source='3p'
        )
        assert resp["result"] is True
        for x in range(len(resp["object"]["data"])):
            assert resp["object"]["data"][x]["productId"] > 1000000

    @weeeTest.mark.list('SRM', 'PPS')
    def test_search_filter_source(self):
        """搜索3P特价"""
        resp = ProductsView().get_promotion_list(
            sales_org='1', start_date=str(datetime.date(2025, 5, 30)),
            end_date=str(datetime.date(2025, 7, 29)), event_type='', event_code='',
            department='', local_owner_id='', sku='2067528', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc')
        assert resp["result"] is True
        for x in range(len(resp["object"]["data"])):
            assert str(resp["object"]["data"][x]["productId"]) == '2067528'

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_product_view_export(self):
        """product view export"""
        resp = ProductsView().product_view_export(
            sales_org='2', start_date=str(datetime.date(2024, 9, 6)),
            end_date=str(datetime.date(2024, 9, 30)), event_type='', event_code='',
            local_owner_id='', sku='6335', title='', promo_reason='', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc'
        )
        # 创建一个类文件对象，用来读取这个文本数据
        f = StringIO(resp)
        # 使用 csv 模块读取文本数据
        reader = csv.DictReader(f)
        # 将读取的数据转换为列表
        data_list = list(reader)
        # 将数据转换为 JSON 格式
        json_data = json.dumps(data_list, indent=4)
        assert '6335' in json_data
        # 关闭文件对象
        f.close()
        resp_no_result = ProductsView().product_view_export(
            sales_org='4', start_date=str(datetime.date(2025, 1, 21)),
            end_date=str(datetime.date(2025, 1, 21)), event_type='', event_code='',
            local_owner_id='', sku='', title='app', promo_reason='6', promo_type='',
            is_unlinked_only='', page_no=1, page_size=50, sort_by='start_time', sort_order='desc')
        assert resp_no_result != []
        assert '4' not in resp_no_result

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_add_link_crazy_fail(self):
        """【103136】创建新的PS并且关联到crazy8数量超配额报错"""
        resp = ProductsView().add_single_promotion(sku=216, sales_org='1', price_priority='N',
                                                   promotion_price='12.69', discount=10,
                                                   lightning_flag='N',
                                                   start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                                   end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                                   business_reason='Expiration Control',
                                                   max_qty='2', limit_qty='6', event_code='202505_INCra_2',
                                                   event_group='1', feature_tag='featured')
        assert 'The maximum number of promotion price records allowed for this Crazy 8 Event has been reached' in resp[
            "message"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
