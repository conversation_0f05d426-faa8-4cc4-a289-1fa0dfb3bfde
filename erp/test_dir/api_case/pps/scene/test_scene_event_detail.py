import weeeTest
from erp.test_dir.api.pps.event.event_detail import EventDetail
from erp.test_dir.api.pps.products_view.products_view import ProductsView
import datetime
from erp.test_dir.db_utils import DBConnect


class TestSceneEventDetail(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_add_and_bulk_delete_exposure_product(self):
        """场景: 创建新的曝光产品然后批量删除"""
        resp = EventDetail().add_single_exposure_product(sku=81402, group_id='1', sales_org=[3, 4], tag='featured',
                                                         start_date='2025-06-22', end_date='2025-06-26',
                                                         event_code='202504_AdsWi_1')
        assert resp["result"] is True
        assert resp["object"] == "Exposure Products Saved Successfully"
        query_resp = EventDetail().get_exposure_products_list(ethnicity='japanese', category='0828', sales_org='3,4',
                                                              approve_status='pending', group_id='4232', tag='featured',
                                                              event_code='202504_AdsWi_1')
        assert '2025-06-22' in query_resp["object"][0]["startDate"]
        assert '2025-06-26' in query_resp["object"][0]["endDate"]
        id_mow = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["id"]
        id_sea = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][1]["id"]
        delete_resp = EventDetail().bulk_delete_exposure_products(event_code='202504_AdsWi_1', ids=[id_mow, id_sea])
        assert delete_resp["result"] is True
        assert delete_resp["object"] == 'Deleted successfully'
        query_again_resp = EventDetail().get_exposure_products_list(ethnicity='japanese', category='0828',
                                                                    sales_org='3,4',
                                                                    approve_status='pending', group_id='4232',
                                                                    product_owner='10937481,10676456', tag='featured',
                                                                    event_code='202504_AdsWi_1')

        assert query_again_resp["result"] is True
        assert query_again_resp["object"] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 81402")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_add_and_single_delete_exposure_product(self):
        """场景: 创建新的曝光产品然后单个删除"""
        resp = EventDetail().add_single_exposure_product(sku=96707, group_id='1', sales_org=[7], tag='',
                                                         start_date='2024-06-22', end_date='2024-06-26',
                                                         event_code='202504_AdsWi_1')
        assert resp["result"] is True
        assert resp["object"] == "Exposure Products Saved Successfully"
        query_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='7',
                                                              approve_status='pending', group_id='4232',
                                                              product_owner='9550812', tag='',
                                                              event_code='202504_AdsWi_1')
        assert query_resp["object"][0]["startDate"] == '2024-06-22'
        assert query_resp["object"][0]["endDate"] == '2024-06-26'
        id = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["id"]
        delete_resp = EventDetail().delete_exposure_product(event_code='202504_AdsWi_1', id=str(id))
        assert delete_resp["result"] is True
        assert delete_resp["object"] == 'Promotion Exposure Product Deleted successfully'
        query_again_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='7',
                                                                    approve_status='pending', group_id='4232',
                                                                    product_owner='9550812', tag='',
                                                                    event_code='202504_AdsWi_1')

        assert query_again_resp["result"] is True
        assert query_again_resp["object"] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 96707")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_approve_reject_single_exposure_product(self):
        """场景: Approve/Reject单个曝光产品 sku=96707"""
        query_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='29',
                                                              approve_status='no', group_id='5258',
                                                              product_owner='', tag='',
                                                              event_code='202505_AdsKU_1')
        id = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["id"]
        approve_resp = EventDetail().approve_reject_single_exposure_product(event_method='202505_AdsKU_1/approve/',
                                                                            id=str(id))
        assert approve_resp["result"] is True
        assert approve_resp["object"] == 'Exposure Product Approved successfully'
        query_status_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303',
                                                                     sales_org='29',
                                                                     approve_status='yes', group_id='5258',
                                                                     product_owner='', tag='',
                                                                     event_code='202505_AdsKU_1')
        assert query_status_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["isApproved"] is True
        assert query_status_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0][
                   "approverUser"] == "<EMAIL>"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
               "product_id = 96707")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][10] == 1
        reject_resp = EventDetail().approve_reject_single_exposure_product(event_method='202505_AdsKU_1/reject/',
                                                                           id=str(id))
        assert reject_resp["result"] is True
        assert reject_resp["object"] == 'Exposure Product Rejected successfully'
        query_status_again_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303',
                                                                           sales_org='29',
                                                                           approve_status='no', group_id='5258',
                                                                           product_owner='', tag='',
                                                                           event_code='202505_AdsKU_1')
        assert query_status_again_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0][
                   "isApproved"] is False
        assert query_status_again_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0][
                   "approverUser"] == "<EMAIL>"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
               "product_id = 96707")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][10] == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_feature_unfeature_exposure_product(self):
        """场景: feature/unfeature单个曝光产品 sku=96707"""
        query_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='29',
                                                              approve_status='no', group_id='5258',
                                                              product_owner='', tag='',
                                                              event_code='202505_AdsKU_1')
        tag_status = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["tag"]
        id = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["id"]
        start_date = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["startDate"]
        end_date = query_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["endDate"]
        if tag_status == 'featured':
            resp = EventDetail().feature_for_single_exposure_product(event_code='202505_AdsKU_1', tag='', id=id,
                                                                     start_date=start_date, end_date=end_date)
            assert resp["result"] is True
            assert resp["object"] == 'Promotion Exposure Product updated successfully'
            query_again_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='29',
                                                              approve_status='no', group_id='5258',
                                                              product_owner='', tag='',
                                                              event_code='202505_AdsKU_1')
            tag = query_again_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["tag"]
            assert tag == ''
            sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
                   "product_id = 96707")
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][13] == ''
        else:
            resp = EventDetail().feature_for_single_exposure_product(event_code='202505_AdsKU_1', tag='featured',
                                                                     id=id, start_date=start_date, end_date=end_date)
            assert resp["result"] is True
            assert resp["object"] == 'Promotion Exposure Product updated successfully'
            query_again_resp = EventDetail().get_exposure_products_list(ethnicity='chinese', category='0303', sales_org='29',
                                                              approve_status='no', group_id='5258',
                                                              product_owner='', tag='',
                                                              event_code='202505_AdsKU_1')
            tag = query_again_resp["object"][0]["promotionExposureProductSalesOrgResponseDtos"][0]["tag"]
            assert tag == 'featured'
            sql = ("SELECT * FROM weee_pricing.pps_promotion_exposure_product WHERE event_code ='202505_AdsKU_1' and "
                   "product_id = 96707")
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][13] == 'featured'

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_approve_reject_single_promotion_product(self):
        """场景: Approve/Reject单个促销产品 sku=74858"""
        query_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510', sales_org='7',
                                                               approve_status='', group_id='4232',
                                                               product_owner='9359639', tag='',
                                                               event_code='202504_AdsWi_1')
        id = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["id"]
        approve_resp = EventDetail().action_for_single_promotion_product(event_method='202504_AdsWi_1/approve/',
                                                                         id=str(id))
        assert approve_resp["result"] is True
        assert approve_resp["object"] == 'Promotion Event Product Approved successfully'
        query_status_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510',
                                                                      sales_org='7',
                                                                      approve_status='yes', group_id='4232',
                                                                      product_owner='9359639', tag='',
                                                                      event_code='202504_AdsWi_1')
        assert query_status_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["isApproved"] is True
        assert query_status_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0][
                   "approverUser"] == "<EMAIL>"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 74858")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][16] == 1
        reject_resp = EventDetail().action_for_single_promotion_product(event_method='202504_AdsWi_1/reject/',
                                                                        id=str(id))
        assert reject_resp["result"] is True
        assert reject_resp["object"] == 'Promotion Event Product Rejected successfully'
        query_status_again_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510',
                                                                            sales_org='7',
                                                                            approve_status='no', group_id='4232',
                                                                            product_owner='9359639', tag='',
                                                                            event_code='202504_AdsWi_1')
        assert query_status_again_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0][
                   "isApproved"] is False
        assert query_status_again_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0][
                   "approverUser"] == "<EMAIL>"
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 74858")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][16] == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_unlink_single_promotion_product(self):
        """场景: unlink单个促销产品"""
        add_resp = ProductsView().add_single_promotion(sku='78', sales_org='4', price_priority='N',
                                                       promotion_price='2.34', discount=10,
                                                       lightning_flag='N',
                                                       start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                                       end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                                       business_reason='Expiration Control',
                                                       max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                                       event_group='1', feature_tag='featured'),
        assert add_resp[0]['result'] is True
        assert 'Added Successfully' in add_resp[0]["object"]
        query_resp = EventDetail().get_promotion_products_list(ethnicity='chinese', category='0706', sales_org='4',
                                                               approve_status='', group_id='4232',
                                                               product_owner='10937481', tag='',
                                                               event_code='202504_AdsWi_1')
        id = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["id"]
        unlink_resp = EventDetail().action_for_single_promotion_product(event_method='202504_AdsWi_1/unlink/',
                                                                        id=str(id))
        assert unlink_resp["result"] is True
        assert unlink_resp["object"] == 'Promotion Event Product Deleted successfully'
        query_again_resp = EventDetail().get_promotion_products_list(ethnicity='chinese', category='0706',
                                                                     sales_org='4',
                                                                     approve_status='', group_id='4232',
                                                                     product_owner='10937481', tag='',
                                                                     event_code='202504_AdsWi_1')
        assert query_again_resp['result'] is True
        assert query_again_resp['object'] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 78")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_bulk_unlink_promotion_product(self):
        """场景: unlink多个促销产品"""
        ProductsView().add_single_promotion(sku='78', sales_org='4', price_priority='N',
                                            promotion_price='2.34', discount=10,
                                            lightning_flag='N',
                                            start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                            end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                            business_reason='Expiration Control',
                                            max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                            event_group='1', feature_tag='featured')
        ProductsView().add_single_promotion(sku='78', sales_org='2', price_priority='N',
                                            promotion_price='2.34', discount=10,
                                            lightning_flag='N',
                                            start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                            end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                            business_reason='Expiration Control',
                                            max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                            event_group='1', feature_tag='featured')
        query_resp = EventDetail().get_promotion_products_list(ethnicity='chinese', category='0706', sales_org='2,4',
                                                               approve_status='', group_id='4232',
                                                               product_owner='', tag='',
                                                               event_code='202504_AdsWi_1')
        first_id = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["id"]
        second_id = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][1]["id"]
        unlink_resp = EventDetail().bulk_action_for_promotion_products(event_method='202504_AdsWi_1/bulk_unlink/',
                                                                       ids=[first_id, second_id])
        assert unlink_resp["result"] is True
        assert unlink_resp["object"] == 'Unlinked successfully'
        query_again_resp = EventDetail().get_promotion_products_list(ethnicity='chinese', category='0706',
                                                                     sales_org='2,4',
                                                                     approve_status='', group_id='4232',
                                                                     product_owner='', tag='',
                                                                     event_code='202504_AdsWi_1')
        assert query_again_resp['result'] is True
        assert query_again_resp['object'] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
               "product_id = 78")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_feature_unfeature_promotion_product(self):
        """场景: feature/unfeature单个促销产品 sku =74858"""
        query_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510', sales_org='7',
                                                               approve_status='', group_id='4232',
                                                               product_owner='9359639', tag='',
                                                               event_code='202504_AdsWi_1')
        tag_status = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["tag"]
        id = query_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["id"]
        if tag_status == 'featured':
            resp = EventDetail().feature_for_single_promotion_product(event_code='202504_AdsWi_1', tag='', id=id)
            assert resp["result"] is True
            assert resp["object"] == 'Promotion Event Product updated successfully'
            query_again_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510', sales_org='7',
                                                               approve_status='', group_id='4232',
                                                               product_owner='9359639', tag='',
                                                               event_code='202504_AdsWi_1')
            tag = query_again_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["tag"]
            assert tag == ''
            sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
                   "product_id = 74858")
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][19] == ''
        else:
            resp = EventDetail().feature_for_single_promotion_product(event_code='202504_AdsWi_1', tag='featured',
                                                                      id=id)
            assert resp["result"] is True
            assert resp["object"] == 'Promotion Event Product updated successfully'
            query_again_resp = EventDetail().get_promotion_products_list(ethnicity='japanese', category='0510', sales_org='7',
                                                               approve_status='', group_id='4232',
                                                               product_owner='9359639', tag='',
                                                               event_code='202504_AdsWi_1')
            tag = query_again_resp["object"][0]["promotionEventProductSalesOrgResponseDtos"][0]["tag"]
            assert tag == 'featured'
            sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code ='202504_AdsWi_1' and "
                   "product_id = 74858")
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][19] == 'featured'

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_add_update_delete_event_group(self):
        """场景: event group的增删改查"""
        query_resp = EventDetail().get_event_group(event_code='202504_AdsWi_1')
        assert query_resp["result"] is True
        assert query_resp["object"][0]["groupId"] == 1
        add_resp = EventDetail().add_event_group(event_code='202504_AdsWi_1', title='group2', group=2,
                                                 start_time='2024-07-20 00:00:00', end_time='2024-08-31 23:59:00',
                                                 desc='Second group')
        assert add_resp["result"] is True
        assert add_resp["object"]["groupId"] == 2
        group_id = add_resp["object"]["id"]
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_group WHERE event_code ='202504_AdsWi_1' order by "
               "group_id desc")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == group_id
        assert db_res[0][2] == "group2"
        assert db_res[0][7] == 2
        assert str(db_res[0][8]) == "2024-07-20 00:00:00"
        assert str(db_res[0][9]) == "2024-08-31 23:59:00"
        assert str(db_res[0][10]) == "Second group"
        update_resp = EventDetail().update_event_group(group_id=group_id, event_code='202504_AdsWi_1',
                                                       title='group2_update', group=2,
                                                       start_time='2024-07-23 00:00:00', end_time='2024-08-13 23:59:00',
                                                       desc='Second group_update')
        assert update_resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == group_id
        assert db_res[0][2] == "group2_update"
        assert db_res[0][7] == 2
        assert str(db_res[0][8]) == "2024-07-23 00:00:00"
        assert str(db_res[0][9]) == "2024-08-13 23:59:00"
        assert str(db_res[0][10]) == "Second group_update"
        delete_resp = EventDetail().delete_event_group(group_id=group_id)
        assert delete_resp["result"] is True
        query_again_resp = EventDetail().get_event_group(event_code='202504_AdsWi_1')
        assert query_again_resp["result"] is True
        assert len(query_again_resp["object"]) == 1
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 1
        assert db_res[0][7] == 1


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
