import weeeTest
from erp.test_dir.api.pps.event.event_list import EventList
import datetime
from erp.test_dir.db_utils import DBConnect


class TestSceneEventList(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_add_update_delete_new_event(self):
        """场景: 创建新的event然后更新再删除"""
        add_resp = EventList().add_single_event(
            title='new event' + (datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d"),
            start_date=(datetime.datetime.today() + datetime.timedelta(days=2)).strftime("%m/%d/%y"),
            end_date=(datetime.datetime.today() + datetime.timedelta(days=30)).strftime(
                "%m/%d/%y"),
            storefronts=["lang_zh", "lang_zh-Hant"], event_type_id=1, description='test')
        assert add_resp["result"] is True
        assert add_resp["object"] == "Promotion Events saved successfully"
        query_resp = EventList().get_event_list(start_date=datetime.datetime.today().strftime("%Y-%m-%d"),
                                                end_date=(datetime.datetime.today() + datetime.timedelta(
                                                    days=2)).strftime(
                                                    "%Y-%m-%d"), event_title='new_event' + (
                    datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d"))
        assert_data = {
            "title": 'new event' + (datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d"),
            "startDate": (datetime.datetime.today() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
            "endDate": (datetime.datetime.today() + datetime.timedelta(days=30)).strftime("%Y-%m-%d"),
            "storefronts": ["lang_zh", "lang_zh-Hant"],
            "typeId": 1,
            "description": 'test'
        }
        self.assert_json(assert_data, query_resp["object"][0])
        event_code = query_resp["object"][0]["eventCode"]
        event_title = query_resp["object"][0]["title"]
        url = "/central/promotion_planning/v2/events/" + event_code
        update_date = {
            "title": 'new event' + (datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d") + 'update',
            "start_date": (datetime.datetime.today() + datetime.timedelta(days=3)).strftime("%Y-%m-%d"),
            "end_date": (datetime.datetime.today() + datetime.timedelta(days=28)).strftime("%Y-%m-%d"),
            "storefronts": ["store_cn", "lang_zh-Hant"],
            "description": 'test'
        }
        update_resp = EventList().update_event(update_date, url)
        assert update_resp["result"] is True
        assert update_resp["object"] == "Promotion Event updated successfully"
        query_again_resp = EventList().get_event_list(start_date=datetime.datetime.today().strftime("%Y-%m-%d"),
                                                      end_date=(datetime.datetime.today() + datetime.timedelta(
                                                          days=5)).strftime(
                                                          "%Y-%m-%d"),
                                                      event_title='new event' + (datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d"))
        assert_data = {
            "title": 'new event' + (datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d") + 'update',
            "startDate": (datetime.datetime.today() + datetime.timedelta(days=3)).strftime("%Y-%m-%d"),
            "endDate": (datetime.datetime.today() + datetime.timedelta(days=28)).strftime("%Y-%m-%d"),
            "storefronts": ["store_cn", "lang_zh-Hant"],
            "typeId": 1,
            "description": 'test'
        }
        self.assert_json(assert_data, query_again_resp["object"][0])
        delete_resp = EventList().delete_event(event_code)
        assert delete_resp["result"] is True
        assert delete_resp["object"] == "Promotion Event deleted successfully"
        query_resp_again = EventList().get_event_list(start_date=datetime.datetime.today().strftime("%Y-%m-%d"),
                                                      end_date=(datetime.datetime.today() + datetime.timedelta(
                                                          days=5)).strftime(
                                                          "%Y-%m-%d"), event_title='new event' + (
                    datetime.datetime.today() + datetime.timedelta(days=2)).strftime(
                "%Y-%m-%d"))
        assert query_resp_again["object"] == []
        sql = "SELECT * FROM weee_pricing.pps_promotion_event WHERE event_code =" + '\'' + event_code + '\''
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
