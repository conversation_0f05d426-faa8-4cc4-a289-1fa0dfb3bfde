import weeeTest
from erp.test_dir.api.pps.products_view.products_view import ProductsView
import datetime
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.pps.event.event_detail import EventDetail


class TestSceneProductsView(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_add_single_lightning(self):
        """场景: 创建新的LD然后删除，不涉及拆分"""
        add_resp = ProductsView().add_single_promotion(sku=216, sales_org='4', price_priority='Y',
                                                       promotion_price='12.69', discount=10,
                                                       lightning_flag='Y',
                                                       start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                                       end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                                       business_reason='Revenue Boost',
                                                       max_qty='2', limit_qty='6', event_code='202504_AdsWi_1'),
        assert add_resp[0]['result'] is True
        assert 'Added Successfully' in add_resp[0]["object"]
        query_resp = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 2, 23)),
                                                       end_date=str(datetime.date(2026, 2, 25)), sku=216, sales_org=4,
                                                       page_no=1, page_size=50,
                                                       sort_by='start_time', sort_order='desc')
        assert_data = {
            "productId": 216,
            "promoStartTime": str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
            "promoEndTime": str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
            "memberPrice": 12.69,
            "maxOrderQuantity": 2,
            "businessReasonId": 1,
            "pricePriority": "Y",
            "eventCodeSet": [],
            "isLightningDeal": "Y"
        }
        self.assert_json(assert_data, query_resp["object"]["data"][0])
        pep_ids = query_resp["object"]["data"][0]["pepIds"]
        price_special_ids = query_resp["object"]["data"][0]["priceSpecialIds"]
        delete_resp = ProductsView().delete_promotion(pep_ids, price_special_ids)
        assert delete_resp["result"] is True
        assert delete_resp["object"] == "Promotions Deleted Successfully"
        query_resp_again = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 2, 23)),
                                                             end_date=str(datetime.date(2026, 2, 25)), sku=216,
                                                             sales_org=4, page_no=1, page_size=50,
                                                             sort_by='start_time', sort_order='desc')
        assert query_resp_again["object"]["data"] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_id =216 and sales_org_id =4 and "
               "product_start_time > '2026-02-01'")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_edit_price_special(self):
        """场景: 创建新的PS,编辑更新,删除PS 不涉及拆分"""
        add_resp = ProductsView().add_single_promotion(sku=216, sales_org='4', price_priority='N',
                                                       promotion_price='12.69', discount=10,
                                                       lightning_flag='N',
                                                       start_time=str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
                                                       end_time=str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
                                                       business_reason='Expiration Control',
                                                       max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                                       event_group='1', feature_tag='featured'),
        assert add_resp[0]['result'] is True
        assert 'Added Successfully' in add_resp[0]["object"]
        query_resp = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 2, 23)),
                                                       end_date=str(datetime.date(2026, 2, 25)), sku=216, sales_org=4,
                                                       page_no=1, page_size=50,
                                                       sort_by='start_time', sort_order='desc')
        assert_data = {
            "productId": 216,
            "promoStartTime": str(datetime.datetime(2026, 2, 23, 00, 00, 00)),
            "promoEndTime": str(datetime.datetime(2026, 2, 23, 23, 59, 59)),
            "discountPercentage": 10,
            "maxOrderQuantity": 2,
            "businessReasonId": 10,
            "eventCodeSet": [
                "202504_AdsWi_1"
            ],
            "isLightningDeal": "N"
        }
        self.assert_json(assert_data, query_resp["object"]["data"][0])
        pep_ids = query_resp["object"]["data"][0]["pepIds"]
        price_special_ids = query_resp["object"]["data"][0]["priceSpecialIds"]
        edit_resp = ProductsView().edit_single_promotion(pep_ids=pep_ids, price_special_ids=price_special_ids,
                                                         price_priority='N', promotion_price='', discount='15',
                                                         lightning_flag='N', start_time='2026-02-18 00:30:00',
                                                         end_time='2026-02-25 22:59:00',
                                                         business_reason='Vendor Sponsored Cost Reduction',
                                                         reason_id='7', max_qty='5')
        assert edit_resp["result"] is True
        query_edit_resp = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 2, 23)),
                                                            end_date=str(datetime.date(2026, 2, 25)), sku=216,
                                                            sales_org=4,
                                                            page_no=1, page_size=50,
                                                            sort_by='start_time', sort_order='desc')
        assert_edit_data = {
            "productId": 216,
            "promoStartTime": str(datetime.datetime(2026, 2, 18, 00, 30, 00)),
            "promoEndTime": str(datetime.datetime(2026, 2, 25, 22, 59, 00)),
            "discountPercentage": 15,
            "maxOrderQuantity": 5,
            "businessReasonId": 7,
            "eventCodeSet": [],
            "isLightningDeal": "N"
        }
        self.assert_json(assert_edit_data, query_edit_resp["object"]["data"][0])
        delete_resp = ProductsView().delete_promotion(pep_ids, price_special_ids)
        assert delete_resp["result"] is True
        assert delete_resp["object"] == "Promotions Deleted Successfully"
        query_resp_again = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 2, 23)),
                                                             end_date=str(datetime.date(2026, 2, 25)), sku=216,
                                                             sales_org=4, page_no=1, page_size=50,
                                                             sort_by='start_time', sort_order='desc')
        assert query_resp_again["object"]["data"] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_id =216 and sales_org_id =4 and "
               "product_start_time > '2026-02-01'")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0

    @weeeTest.params.file(file_name="product_list.yaml", key="link_event_success")
    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_link_event_success(self, request):
        """场景：link event成功，然后从Event移除，unlink 过去和进行中的特价/秒杀"""
        event_code = request["json_data"]["eventCode"][0]
        sql = f"SELECT * FROM weee_pricing.pps_promotion_event_product WHERE event_code = '{event_code}' AND product_id IN (105760,54845) ORDER BY product_price_special_id DESC"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = ProductsView().promotion_link_event(request["json_data"])
        assert resp["result"] is True
        assert resp["object"] == request["message"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert len(db_res_before) == len(db_res_after) - 2
        special_ids = []
        for x in range(len(request["json_data"]["promotionDtos"])):
            assert tuple(request["json_data"]["promotionDtos"][x]["priceSpecialIds"])[0] == db_res_after[x][4]
            special_ids.append(db_res_after[x][0])
        event_code = request["json_data"]["eventCode"][0] + '/bulk_unlink'
        unlink_resp = EventDetail().bulk_action_for_promotion_products(event_method=event_code, ids=special_ids)
        assert unlink_resp["result"] is True
        assert unlink_resp["object"] == "Unlinked successfully"

    @weeeTest.mark.list('Regression', 'SRM', 'PPS', 'Smoke')
    def test_scene_bulk_delete_price_specials(self):
        """场景: 创建新的PS,批量删除PS 不涉及拆分"""
        add_resp_1 = ProductsView().add_single_promotion(sku=16459, sales_org=3, price_priority='N',
                                                         promotion_price='12.69', discount=10,
                                                         lightning_flag='N',
                                                         start_time=str(datetime.datetime(2026, 4, 23, 00, 00, 00)),
                                                         end_time=str(datetime.datetime(2026, 4, 23, 23, 59, 59)),
                                                         business_reason='Expiration Control',
                                                         max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                                         event_group='1', feature_tag='featured')
        assert add_resp_1['result'] is True
        assert 'Added Successfully' in add_resp_1["object"]
        add_resp_2 = ProductsView().add_single_promotion(sku=16459, sales_org=16, price_priority='N',
                                                         promotion_price='12.69', discount=10,
                                                         lightning_flag='N',
                                                         start_time=str(datetime.datetime(2026, 4, 23, 00, 00, 00)),
                                                         end_time=str(datetime.datetime(2026, 4, 23, 23, 59, 59)),
                                                         business_reason='Expiration Control',
                                                         max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                                         event_group='1', feature_tag='featured')
        assert add_resp_2['result'] is True
        assert 'Added Successfully' in add_resp_2["object"]
        add_resp_3 = ProductsView().add_single_promotion(sku=16459, sales_org=24, price_priority='N',
                                                         promotion_price='12.69', discount=10,
                                                         lightning_flag='N',
                                                         start_time=str(datetime.datetime(2026, 4, 23, 00, 00, 00)),
                                                         end_time=str(datetime.datetime(2026, 4, 23, 23, 59, 59)),
                                                         business_reason='Expiration Control',
                                                         max_qty='2', limit_qty='6', event_code='202504_AdsWi_1',
                                                         event_group='1', feature_tag='featured')
        assert add_resp_3['result'] is True
        assert 'Added Successfully' in add_resp_3["object"]
        query_resp = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 4, 23)),
                                                       end_date=str(datetime.date(2026, 4, 25)), sku=16459,
                                                       sales_org='3,16,24',
                                                       page_no=1, page_size=50,
                                                       sort_by='start_time', sort_order='desc')
        assert_data = {
            "productId": 16459,
            "promoStartTime": str(datetime.datetime(2026, 4, 23, 00, 00, 00)),
            "promoEndTime": str(datetime.datetime(2026, 4, 23, 23, 59, 59)),
            "discountPercentage": 10,
            "maxOrderQuantity": 2,
            "businessReasonId": 10,
            "eventCodeSet": [
                "202504_AdsWi_1"
            ],
            "isLightningDeal": "N"
        }
        self.assert_json(assert_data, query_resp["object"]["data"][0])
        for x in range(query_resp["object"]["total"]):
            delete_resp = ProductsView().delete_promotion(query_resp["object"]["data"][x]["pepIds"],
                                                          query_resp["object"]["data"][x]["priceSpecialIds"])
            assert delete_resp["result"] is True
            assert delete_resp["object"] == "Promotions Deleted Successfully"
        query_resp_again = ProductsView().get_promotion_list(start_date=str(datetime.date(2026, 4, 23)),
                                                             end_date=str(datetime.date(2026, 4, 25)), sku=16459,
                                                             sales_org='3,16,24', page_no=1, page_size=50,
                                                             sort_by='start_time', sort_order='desc')
        assert query_resp_again["object"]["data"] == []
        sql = ("SELECT * FROM weee_pricing.pps_promotion_event_product WHERE product_id =16459 and sales_org_id in (3,"
               "16,24) and product_start_time > '2026-02-01'")
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
