import time
import weeeTest
from erp.test_dir.api.task.task_common_api import TaskCommonApi
from erp.test_dir.api.merch.product.product_warehouse_api import ProductWarehouseInterface
from erp.test_dir.api.task.purchase_status_quest import PurchaseStatusManagement


class TestStatusRequest(weeeTest.TestCase):
    product = ''
    warehouse_id = ''
    instance_url = ''
    ticket_instance_id = ''
    task_id = ''
    flow_name = ''
    deadline = ''
    assignee_name = ''

    def test_create_request(self):
        """新增工单"""
        resp = ProductWarehouseInterface().product_warehouse_filter_group(purchaseStatus='O', purchaseSubStatus='P',
                                                                          assortmentRole='Testing',
                                                                          physicalInventoryId='25')
        assert resp["result"] is True
        TestStatusRequest.product = resp["object"]["list"][0]["productId"]
        TestStatusRequest.warehouse_id = resp["object"]["list"][0]["id"]
        resp_create = PurchaseStatusManagement().create_request_from_warehouse_list(product=TestStatusRequest.product,
                                                                                    inventory=25, value_to=["A", "R"])

        assert resp_create["result"] is True
        assert resp_create["message"] == "request created"
        time.sleep(2)
        warehouse_detail_resp = ProductWarehouseInterface().product_warehouse_detail(TestStatusRequest.warehouse_id)
        TestStatusRequest.task_id = warehouse_detail_resp["object"]["productWarehouse"]["business_id"]

    def test_verify_product_info(self):
        """获取中间产品模块信息并校验内容"""
        task_detail_url = f'businessKey={TestStatusRequest.task_id}'
        resp = TaskCommonApi().get_request_detail_content(task_detail_url)
        assert str(resp["object"]["productId"]) == str(TestStatusRequest.product)
        assert resp["object"]["inventoryTitle"] == "25 - LA - La Mirada"
        assert resp["object"]["currentData"] == "Testing-DNR-A-Bad Performance"
        assert resp["object"]["requestData"] == "Live-Regular"
        assert resp["object"]["reason"] == "test"

    def test_verify_node_info(self):
        """获取顶部节点模块信息并校验内容"""
        time.sleep(3)
        TestStatusRequest.instance_url = f'businessKey={TestStatusRequest.task_id}&zoneId=Asia/Shanghai'
        resp = TaskCommonApi().get_request_detail(TestStatusRequest.instance_url)
        assert resp["object"]["ticketNumber"] == str(TestStatusRequest.task_id)
        assert resp["object"]["creator"] == "<EMAIL>"
        TestStatusRequest.ticket_instance_id = resp["object"]["processInstanceId"]
        TestStatusRequest.task_id = resp["object"]["taskId"]
        assert resp["object"]["flowName"] == "Merch Leadership"
        deadline = TaskCommonApi().get_next_business_day(2)
        assert deadline in resp["object"]["deadline"]
        assert resp["object"]["assigneeName"] == "<EMAIL>"

    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cancel_ticket(self):
        """创建purchase status工单并且取消 [102642, 102649]"""
        TestStatusRequest.test_create_request(self)
        TestStatusRequest.test_verify_product_info(self)
        TestStatusRequest.test_verify_node_info(self)
        cancel_url = f'process_instance_id={TestStatusRequest.ticket_instance_id}&task_id={TestStatusRequest.task_id}&type=purchase_status_approval'
        cancel_resp = TaskCommonApi().cancel_request(cancel_url)
        assert cancel_resp["message"] == "Task canceled."
        resp = TaskCommonApi().get_request_detail(TestStatusRequest.instance_url)
        assert resp["object"]["flowName"] == "End"
        assert resp["object"]["flowStatus"] == 3

    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_create_ticket_fail(self):
        """发起失败 [102655]"""
        resp_access = PurchaseStatusManagement().create_request_from_warehouse_list(product=107403, inventory=25,
                                                                                    value_to=["A", "R"])
        assert resp_access["result"] is False
        assert resp_access["message"] == "You can modify it directly."
        resp_duplicate = PurchaseStatusManagement().create_request_from_warehouse_list(product=105967, inventory=29,
                                                                                       value_to=["A", "R"])
        assert resp_duplicate["result"] is False
        assert "Duplicate ticket existed:" in resp_duplicate["message"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
