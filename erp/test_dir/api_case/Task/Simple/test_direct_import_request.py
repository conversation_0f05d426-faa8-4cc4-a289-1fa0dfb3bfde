import time
import weeeTest
from erp.test_dir.api.merch.product.product_srm import ProductInfo
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.task.task_common_api import TaskCommonApi


def get_ticket_id_from_product():
    resp = ProductInfo().product_detail(107131)
    ticket_id = resp["object"]["warehouse_info"]["business_id"]
    return ticket_id


def get_purchase_method_from_product():
    resp = ProductInfo().product_detail(107131)
    purchase_method = []
    for i in range(len(resp["object"]["warehouse_info"]["warehouse_product_infos"])):
        purchase_method.append(resp["object"]["warehouse_info"]["warehouse_product_infos"][i]["purchase_method"])
    return purchase_method


class TestDIRequest(weeeTest.TestCase):
    ticket_id = ''
    instance_url = ''
    ticket_instance_id = ''
    task_id = ''
    flow_name = ''
    deadline = ''
    assignee_name = ''

    @weeeTest.params.file(file_name="task_data.yaml", key="DI_request_create")
    def test_create_request(self, request):
        """新增工单"""
        ticket_id = get_ticket_id_from_product()
        if ticket_id == '':
            resp_ticket_detail = TaskCommonApi().create_request(request["json_data"])
            assert resp_ticket_detail["result"] is True
        ticket_id_updated = get_ticket_id_from_product()
        TestDIRequest.ticket_id = ticket_id_updated
        time.sleep(3)

    @weeeTest.params.file(file_name="task_data.yaml", key="DI_request_create")
    def test_verify_product_info(self, request):
        """获取中间产品模块信息并校验内容"""
        task_detail_url = f'businessKey={TestDIRequest.ticket_id}'
        resp_ticket_detail = TaskCommonApi().get_request_detail_content(task_detail_url)
        assert resp_ticket_detail["object"]["subject"] == "Direct Import Request"
        assert resp_ticket_detail["object"]["productId"] == request["json_data"]["product_id"]
        assert resp_ticket_detail["object"]["indirectSupplierName"] == request["json_data"]["indirect_supplier_name"]
        assert resp_ticket_detail["object"]["indirectSupplierStatus"] == request["json_data"][
            "indirect_supplier_status"]
        assert resp_ticket_detail["object"]["landingPrice"] == request["json_data"]["landing_price"]
        assert resp_ticket_detail["object"]["reason"] == request["json_data"]["reason"]

    def test_verify_node_info(self):
        """获取顶部节点模块信息并校验内容"""
        time.sleep(3)
        TestDIRequest.instance_url = f'businessKey={TestDIRequest.ticket_id}&zoneId=Asia/Shanghai'
        resp = TaskCommonApi().get_request_detail(TestDIRequest.instance_url)
        assert resp["object"]["ticketNumber"] == str(TestDIRequest.ticket_id)
        assert resp["object"]["creator"] == "<EMAIL>"
        TestDIRequest.ticket_instance_id = resp["object"]["processInstanceId"]
        TestDIRequest.task_id = resp["object"]["taskId"]
        TestDIRequest.flow_name = resp["object"]["flowName"]
        if "deadline" in resp["object"]:
            TestDIRequest.deadline = resp["object"]["deadline"]
        TestDIRequest.assignee_name = resp["object"]["assigneeName"]

    @weeeTest.params.file(file_name="task_data.yaml", key="DI_request_create")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cancel_ticket(self, request):
        """创建工单并且取消工单"""
        TestDIRequest.test_create_request(self, request)
        TestDIRequest.test_verify_product_info(self, request)
        TestDIRequest.test_verify_node_info(self)
        cancel_url = f'process_instance_id={TestDIRequest.ticket_instance_id}&task_id={TestDIRequest.ticket_id}&type=sku_direct_import'
        cancel_resp = TaskCommonApi().cancel_request(cancel_url)
        assert cancel_resp["message"] == "Task canceled."
        resp = TaskCommonApi().get_request_detail(TestDIRequest.instance_url)
        assert resp["object"]["flowName"] == "End"
        assert resp["object"]["flowStatus"] == 3

    @weeeTest.params.file(file_name="task_data.yaml", key="DI_request_create")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_all_approve_to_end(self, request):
        """All approve工单"""

        # 创建工单
        TestDIRequest.test_create_request(self, request)
        TestDIRequest.test_verify_product_info(self, request)
        TestDIRequest.test_verify_node_info(self)
        assert TestDIRequest.flow_name == "Demand Planner"
        assert TestDIRequest.assignee_name == "<EMAIL>"
        deadline = TaskCommonApi().get_next_business_day(8)
        assert deadline in TestDIRequest.deadline

        # demand planner提交
        resp = TaskCommonApi().submit_request(processInstanceId=TestDIRequest.ticket_instance_id,
                                                       taskId=TestDIRequest.task_id)
        assert resp["result"] is True
        time.sleep(5)
        TestDIRequest.test_verify_node_info(self)
        assert TestDIRequest.flow_name == "Merch Strategy"
        assert TestDIRequest.assignee_name == "<EMAIL>"
        deadline = TaskCommonApi().get_next_business_day(3)
        assert deadline in TestDIRequest.deadline

        # merch strategy approve
        resp = TaskCommonApi().submit_request(processInstanceId=TestDIRequest.ticket_instance_id,
                                                       taskId=TestDIRequest.task_id, solution='approve')
        assert resp["result"] is True
        time.sleep(5)
        TestDIRequest.test_verify_node_info(self)
        assert TestDIRequest.flow_name == "Food Safety"
        assert TestDIRequest.assignee_name == "Merch Food Safety"
        resp_deadline = TaskCommonApi().get_request_detail(TestDIRequest.instance_url)
        assert "deadline" not in resp_deadline["object"]

        # food safety approve
        resp = TaskCommonApi().submit_request(processInstanceId=TestDIRequest.ticket_instance_id,
                                                       taskId=TestDIRequest.task_id, solution='approve')
        assert resp["result"] is True
        time.sleep(5)
        TestDIRequest.test_verify_node_info(self)
        assert TestDIRequest.flow_name == "Merch Operation"
        assert TestDIRequest.assignee_name == "<EMAIL>"
        deadline = TaskCommonApi().get_next_business_day(3)
        assert deadline in TestDIRequest.deadline

        # merch Operation提交
        resp = TaskCommonApi().submit_request(processInstanceId=TestDIRequest.ticket_instance_id,
                                                       taskId=TestDIRequest.task_id)
        assert resp["result"] is True
        time.sleep(5)
        TestDIRequest.test_verify_node_info(self)
        assert TestDIRequest.flow_name == "End"

        # 检查产品purchase method更新
        purchase_method = get_purchase_method_from_product()
        count_direct_import = purchase_method.count('I')
        assert count_direct_import > 10

        # 产品开关还原
        sql = 'UPDATE weee_comm.gb_product SET product_feature = 0 WHERE id = 107131'
        DBConnect().update_data_from_mysql(sql)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
