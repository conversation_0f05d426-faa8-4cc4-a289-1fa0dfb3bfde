import weeeTest
from erp.test_dir.api.merch.Vendor.srm_review_vendor_api import SrmReviewVendorInterface
from erp.test_dir.db_utils import DBConnect


class TestVendorReviewModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="vendor_review_module.yaml", key="vendor_review_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_review_list(self, name, description, request):
        """供应商列表查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = SrmReviewVendorInterface().select_review_vendor_list(request["params_data"])
        start_sql = "SELECT id FROM weee_comm.gb_vender where type != 'M' ORDER BY id DESC;"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"]["list"][0]["id"] == str(res_db[0][0])

    @weeeTest.params.file(file_name="vendor_review_module.yaml", key="vendor_review_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_review_info(self, name, description, request):
        """供应商详情查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = SrmReviewVendorInterface().select_review_vendor_info(request["params_data"])
        assert resp["object"]["basic_info"]["id"] == str(request["params_data"])

    @weeeTest.params.file(file_name="vendor_review_module.yaml", key="vendor_review_info_update")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_review_info_update(self, name, request):
        """供应商详情查询"""
        print("执行接口名称：" + name)
        data = request["json_data"]
        resp = SrmReviewVendorInterface().update_review_vendor_info(data)
        if resp["message"] == "Operation successful":
            assert resp["object"] == str(data["id"])


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

