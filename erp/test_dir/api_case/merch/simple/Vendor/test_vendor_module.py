import weeeTest
from erp.test_dir.api.merch.Vendor.srm_vendor_api import SrmVendorInterface
from erp.test_dir.db_utils import DBConnect


class TestVendorModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_list(self, name, description, request):
        """供应商列表查询"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = SrmVendorInterface().select_vendor_list(request["params_data"])
        start_sql = "SELECT id FROM weee_comm.gb_vender where status = 'A' ORDER BY id DESC;"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"]["vendors"][0]["id"] == str(res_db[0][0])

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_info(self, name, description, request):
        """供应商详情查询"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = SrmVendorInterface().select_vendor_info(request["params_data"])
        assert resp["object"]["vender"]["id"] == str(request["params_data"])

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_info_update")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_info_update(self, name, request):
        """供应商详情查询"""
        print("执行接口名称：" + name)
        data = request["json_data"]
        resp = SrmVendorInterface().update_vendor_detail(data)
        assert resp["result"] == str(data["id"])

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_finance_update")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_finance_update(self, name, description, request):
        """供应商详情页财务更新"""
        print("执行接口名称：" + name, "接口描述：" + description)
        data = request["json_data"]
        resp = SrmVendorInterface().update_vendor_finance(data)
        start_sql = f"SELECT quickbook_id FROM weee_comm.gb_vender WHERE id = {data['id']};"
        if resp["result"] == "true":
            res_db = DBConnect().select_data_from_mysql(start_sql)
            assert res_db[0][0] == data["quickbook_id"]

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_finance_approve")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_finance_approve(self, name, request):
        """供应商详情页财务审批"""
        print("执行接口名称：" + name)
        data = request["json_data"]
        resp = SrmVendorInterface().approve_vendor_finance(data)
        start_sql = f"SELECT quickbook_id,status FROM weee_comm.gb_vender WHERE id = {data['id']};"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        if resp["result"] == "false":
            assert res_db[0][0] is None
        if resp["result"] == "true":
            assert res_db[0][1] == "A"

    @weeeTest.params.file(file_name="vendor_module.yaml", key="vendor_inactive")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_inactive(self, name, request):
        """供应商 inactive"""
        print("执行接口名称：" + name )
        data = request["json_data"]
        resp = SrmVendorInterface().approve_vendor_finance(data)
        start_sql = f"SELECT count(*) FROM weee_comm.gb_product_sales WHERE primary_vendor = {data['id']};"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        if resp["result"] == "false":
            assert res_db[0][0] > 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

