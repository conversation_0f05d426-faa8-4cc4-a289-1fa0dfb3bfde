import weeeTest
from erp.test_dir.api.merch.transfer.transfer_order import TransferOrder
from erp.test_dir.db_utils import DBConnect


class TestTransferOrder(weeeTest.TestCase):
    internal_order_id = ''
    invoice_id =''

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_list(self):
        """调拨订单列表查询"""
        resp = TransferOrder().transfer_order_list(order_id=42597188)
        assert resp["result"] is True
        assert resp["object"]["orders"][0]["status"] == 'C'
        sql = 'SELECT * FROM weee_comm.gb_invoice_internal WHERE order_id =42597188'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == resp["object"]["orders"][0]["id"]
        assert str(db_res[0][5]) == resp["object"]["orders"][0]["invoice_id"]

    @weeeTest.params.file(file_name="transfer_order.yaml", key="transfer_order_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_detail(self, request):
        """调拨订单详情查询"""
        resp = TransferOrder().transfer_order_detail(request["params_data"])
        assert resp["result"] is True
        assert resp["object"]["order"]["id"] == "3262"
        sql = 'SELECT * FROM weee_comm.gb_invoice_internal WHERE id =3262'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][1]) == resp["object"]["order"]["order_id"]
        assert str(db_res[0][5]) == resp["object"]["order"]["invoice_id"]

    @weeeTest.params.file(file_name="transfer_order.yaml", key="transfer_order_itinerary")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_transfer_order_itinerary(self, name, description, request, expected_result):
        """调拨单线路查询"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = TransferOrder().transfer_order_itinerary(request["params_data"])
        assert resp["result"] == expected_result["result"]
        assert resp["message_id"] == expected_result["message_id"]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_transfer_order_inventory_list(self):
        """调拨单可用仓库列表查询"""
        resp = TransferOrder().transfer_order_inventory_list()
        assert resp["result"] is True
        assert resp["object"]["inventory_list"] == [
            {
                "id": "14",
                "text": "SF - Volpey"
            },
            {
                "id": "20",
                "text": "TX - Houston"
            },
            {
                "id": "23",
                "text": "LA - Riverside - Inactive"
            },
            {
                "id": "25",
                "text": "LA - La Mirada"
            },
            {
                "id": "30",
                "text": "NJ - Edison - Dry"
            },
            {
                "id": "8",
                "text": "WA - Seattle"
            },
            {
                "id": "29",
                "text": "IL - Chicago"
            },
            {
                "id": "31",
                "text": "SF - Whipple"
            },
            {
                "id": "32",
                "text": "GA - Atlanta"
            },
            {
                "id": "33",
                "text": "NJ - Clifton"
            },
            {
                "id": "35",
                "text": "FL - Tampa"
            },
            {
                "id": "37",
                "text": "SF - Milpitas"
            },
            {
                "id": "101",
                "text": "Weee-Reserve-32 Cold"
            },
            {
                "id": "102",
                "text": "Weee-Reserve-United cold"
            },
            {
                "id": "103",
                "text": "Weee-Reserve-FBM"
            },
            {
                "id": "104",
                "text": "Weee-Reserve-Ready Trucking INC"
            },
            {
                "id": "105",
                "text": "Weee-Reserve-Lineage"
            },
            {
                "id": "40",
                "text": "LA - DC - Santa Fe Springs"
            },
            {
                "id": "41",
                "text": "NJ - Belleville"
            },
            {
                "id": "106",
                "text": "Weee-Reserve-Americold"
            },
            {
                "id": "48",
                "text": "CN - Test"
            },
            {
                "id": "107",
                "text": "MGX"
            }
        ]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_transfer_order(self):
        """新建调拨单"""
        resp = TransferOrder().create_transfer_order(outbound_inventory=25, inbound_inventory=29,
                                                     email='<EMAIL>', pickup_date='2024-07-20',
                                                     eta_date='2024-07-20', comment='test add')
        assert resp["result"] is True
        assert "id" in resp["object"]
        sql = ('SELECT * FROM weee_comm.gb_invoice_internal WHERE warehouse_num =25 and target_warehouse_num =29 and '
               'markout_date ="2024-07-20" order by id desc')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == resp["object"]["id"]
        assert db_res[0][1] == resp["object"]["order_id"]
        assert db_res[0][5] == resp["object"]["invoice_id"]
        TestTransferOrder.internal_order_id = resp["object"]["id"]
        TestTransferOrder.invoice_id = resp["object"]["invoice_id"]
        get_detail_resp = TransferOrder().transfer_order_detail({"id": str(TestTransferOrder.internal_order_id)})
        assert get_detail_resp["object"]["order"]["order_id"] == str(resp["object"]["order_id"])
        assert get_detail_resp["object"]["order"]["inbound_inventory_id"] == "29"
        assert get_detail_resp["object"]["order"]["physical_inventory_id"] == "25"
        assert get_detail_resp["object"]["order"]["markout_date"] == "2024-07-20"
        assert get_detail_resp["object"]["order"]["eta_date"] == "2024-07-20"
        assert get_detail_resp["object"]["order"]["comment"] == "test add"

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_transfer_order(self):
        """更新调拨单"""
        resp = TransferOrder().create_transfer_order(order_id=2351, outbound_inventory=25, inbound_inventory=29,
                                                     email='<EMAIL>', pickup_date='2024-09-20',
                                                     eta_date='2024-09-20', comment='test update')
        assert resp["result"] is True
        assert "id" in resp["object"]
        sql = 'SELECT * FROM weee_comm.gb_invoice_internal WHERE id =2351 order by id desc'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == 41144234
        assert str(db_res[0][2]) == '2024-09-20'
        assert str(db_res[0][3]) == '2024-09-20'

    @weeeTest.params.file(file_name="transfer_order.yaml", key="external_create_transfer_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_external_create_transfer_order(self, request):
        """外部接口创建调拨单"""
        sql = ('SELECT * FROM weee_comm.gb_invoice_internal WHERE warehouse_num =29 and target_warehouse_num =20 and '
               'markout_date ="2023-09-01"')
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = TransferOrder().external_create_transfer_order(request["json_data"])
        assert resp["result"] is True
        assert "Line 1, SKU [42]:  warehouse status is not live." in resp["object"]["msg"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_cancel_transfer_order(self):
        """取消调拨单"""
        sql_fail = 'SELECT * FROM weee_comm.gb_invoice WHERE id =39468070'
        db_res_before = DBConnect().select_data_from_mysql(sql_fail)
        resp = TransferOrder().transfer_order_cancel(3279)
        assert resp["result"] is True
        assert resp["object"]["msg"] == "Only orders with status Create or Printed can be cancelled"
        db_res_after = DBConnect().select_data_from_mysql(sql_fail)
        assert db_res_before == db_res_after
        print(TestTransferOrder.internal_order_id)
        TransferOrder().transfer_order_cancel(TestTransferOrder.internal_order_id)
        sql_success = f'SELECT * FROM weee_comm.gb_invoice WHERE id= {TestTransferOrder.invoice_id}'
        db_res = DBConnect().select_data_from_mysql(sql_success)
        assert db_res[0][7] == 'X'

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_add_product_line(self):
        """调拨单添加商品"""
        resp = TransferOrder().transfer_order_add_product_line(order_id=2378, product_id=170, quantity='10')
        assert resp["result"] is True
        assert resp["object"]["success"] == 1
        sql = 'SELECT * FROM weee_comm.gb_invoice_line WHERE invoice_id =39427878'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][10] == 10

    @weeeTest.params.file(file_name="transfer_order.yaml", key="add_attachment")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_transfer_order_add_attachment(self, request):
        """调拨单附件新增"""
        resp = TransferOrder().transfer_order_add_attachment(request["json_data"])
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.internal_order_attachment WHERE invoice_internal_id =1968 ORDER BY id DESC'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][2] == '2022112501.pdf'
        assert db_res[0][6] == 'A'

    @weeeTest.params.file(file_name="transfer_order.yaml", key="delete_attachment")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_transfer_order_delete_attachment(self, request):
        """调拨单附件删除"""
        query_resp = TransferOrder().transfer_order_detail(request["params_data"])
        assert query_resp["result"] is True
        assert query_resp["object"]["order"]["id"] == '1968'
        attach_id = query_resp["object"]["order"]["attachments"][0]["id"]
        resp = TransferOrder().transfer_order_delete_attachment(id=attach_id)
        assert resp["result"] is True
        sql_1 = 'SELECT * FROM weee_merch.internal_order_attachment WHERE id ='
        sql = sql_1 + attach_id
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][6] == 'X'

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_send_wms(self):
        """调拨单下发失败"""
        sql = 'SELECT * FROM weee_comm.gb_invoice WHERE id = 31455757'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = TransferOrder().transfer_order_send_wms(order_id=2062)
        assert resp["result"] is True
        assert resp["object"]["success"] == 0
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="transfer_order.yaml", key="transfer_order_mark_out")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_mark_out(self, request):
        """调拨单出库失败"""
        resp = TransferOrder().transfer_order_mark_out(request["json_data"])
        assert resp["result"] is True
        assert resp["object"]["msg"] == "Warehouses that support wms for internal orders are not allowed to be mark out of ERP"
        query_resp = TransferOrder().transfer_order_list(invoice_id=39465117)
        assert query_resp["object"]["orders"][0]["status"] == 'C'
        sql = 'SELECT * FROM weee_comm.gb_invoice WHERE id =48667366'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][7] == 'C'

    @weeeTest.params.file(file_name="transfer_order.yaml", key="to_send_email")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_transfer_order_send_email(self, name, description, request, expected_result):
        """调拨单-给联系人发送邮件"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = TransferOrder().transfer_order_send_email(request["json_data"])
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["success"] == expected_result["success"]

    @weeeTest.params.file(file_name="transfer_order.yaml", key="transfer_order_change_quantity")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_transfer_order_change_quantity(self, name, description, request, expected_result):
        """调拨单-编辑商品的数量失败"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        sql = 'SELECT * FROM weee_comm.gb_invoice_line WHERE invoice_id = 39464604'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = TransferOrder().transfer_order_change_quantity(request["json_data"])
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["msg"] == expected_result["msg"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="transfer_order.yaml", key="transfer_order_change_per_unit")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_transfer_order_change_quantity_per_unit(self, name, description, request, expected_result):
        """调拨单-编辑商品的箱规"""
        print("执行接口名称：" + name, "  接口描述：" + description)
        resp = TransferOrder().transfer_order_change_quantity_per_unit(request["json_data"])
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["success"] == expected_result["success"]
        sql = 'SELECT * FROM weee_merch.internal_invoice_line_extra WHERE invoice_id =31456596'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][3] == 4


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
