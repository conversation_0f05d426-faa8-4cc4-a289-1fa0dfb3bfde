import weeeTest
from erp.test_dir.db_utils import db_query
from erp.test_dir.api.merch.product.internal_work_order_api import InternalWorkOrderInterface


class TestWorkOrderModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="work_order_data.yaml", key="work_order_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_work_order_list(self, description, request):
        """
        work order列表查询
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = InternalWorkOrderInterface().internal_work_order_list(data)
        if list(data.keys())[0] == "filter[target_product_id]":
            inv_data = data["filter[target_product_id]"]
            start_sql = f"SELECT * FROM weee_merch.io_product_bundle_order WHERE product_id = {inv_data} AND status != 'X' ORDER BY id DESC;"
            resp_db = db_query(start_sql)
            assert resp["object"]["totalCount"] == str(len(resp_db))
            assert resp["object"]["list"][0]["target_product_id"] == str(inv_data)
        if list(data.keys())[0] == "filter[keyword]":
            inv_data = data["filter[keyword]"]
            start_sql = f"SELECT * FROM weee_merch.io_product_bundle_order WHERE id = {inv_data};"
            resp_db = db_query(start_sql)
            assert resp["object"]["list"][0]["id"] == str(inv_data)
            assert resp_db[0][0] == inv_data
        if list(data.keys())[0] == "filter[status]":
            inv_data = data["filter[status]"]
            start_sql = f"SELECT * FROM weee_merch.io_product_bundle_order WHERE status = '{inv_data}' ORDER BY id DESC;"
            resp_db = db_query(start_sql)
            assert resp["object"]["totalCount"] == str(len(resp_db))
            assert resp["object"]["list"][0]["status"] == inv_data
        if list(data.keys())[0] == "filter[inventory_id]":
            inv_data = data["filter[inventory_id]"]
            start_sql = f"SELECT * FROM weee_merch.io_product_bundle_order WHERE inventory_id  = '{inv_data}' AND status != 'X' ORDER BY id DESC;"
            resp_db = db_query(start_sql)
            assert resp["object"]["totalCount"] == str(len(resp_db))
            assert resp["object"]["list"][0]["inventory_id"] == str(inv_data)
        if list(data.keys())[0] == "filter[bundle_id]":
            inv_data = data["filter[bundle_id]"]
            start_sql = f"SELECT * FROM weee_merch.io_product_bundle_order WHERE bundle_id = '{inv_data}' AND status != 'X' ORDER BY id DESC;"
            resp_db = db_query(start_sql)
            assert resp["object"]["totalCount"] == str(len(resp_db))
            assert resp["object"]["list"][0]["bundle_id"] == str(inv_data)

    @weeeTest.params.file(file_name="work_order_data.yaml", key="work_order_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_work_order_info(self, description, request):
        """
        验证work order订单详情
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = InternalWorkOrderInterface().internal_work_order_info(data)
        start_sql = f"SELECT product_id ,status FROM weee_merch.io_product_bundle_order WHERE id = {data};"
        resp_db = db_query(start_sql)
        assert resp["object"]["workOrder"]["target_product_id"] == str(resp_db[0][0])
        assert resp["object"]["workOrder"]["status"] == resp_db[0][1]
        assert resp["object"]["workOrder"]["id"] == str(data)

    @weeeTest.params.file(file_name="work_order_data.yaml", key="create_work_order")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_create_work_order(self, description, request):
        """
        验证新增Work Order订单
        :param description:
        :param request:
        :return:
        """
        print("执行接口描述：" + description)
        data = request["params_data"]
        resp = InternalWorkOrderInterface().create_internal_work_order(data)
        start_sql = f"SELECT id FROM weee_merch.io_product_bundle_order ORDER BY id DESC;"
        resp_db_start = db_query(start_sql)
        assert resp["message"] == "success"
        assert resp["object"]["id"] == resp_db_start[0][0]

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_delete_work_order(self):
        """
        验证删除work order订单
        :return:
        """
        start_sql = f"SELECT id FROM weee_merch.io_product_bundle_order WHERE status = 'A';"
        resp_db_start = db_query(start_sql)
        # 判断数据库是否存在符合的数据，不存在则新建
        if not resp_db_start:
            data = {
                "inventory_id": "25",
                "bundle_id": "53",
                "qty": "1"}
            resp_ = InternalWorkOrderInterface().create_internal_work_order(data)
            wo_id = resp_["object"]["id"]
            resp = InternalWorkOrderInterface().delete_internal_work_order(wo_id)
            end_sql = f"SELECT status FROM weee_merch.io_product_bundle_order WHERE id = {wo_id};"
            resp_db_end = db_query(end_sql)
            assert resp["message"] == "success"
            assert resp_db_end[0][0] == "X"
        if resp_db_start:
            wo_id = resp_db_start[0][0]
            resp = InternalWorkOrderInterface().delete_internal_work_order(wo_id)
            end_sql = f"SELECT status FROM weee_merch.io_product_bundle_order WHERE id = {wo_id};"
            resp_db_end = db_query(end_sql)
            assert resp["message"] == "success"
            assert resp_db_end[0][0] == "X"

    @weeeTest.mark.list('Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_send_work_order_to_wms(self):
        """
        验证下发work order订单到WMS系统
        :return:
        """
        data = {
            "inventory_id": "25",
            "bundle_id": "53",
            "qty": "1"}
        # 新创建一条work order订单
        resp_ = InternalWorkOrderInterface().create_internal_work_order(data)
        wo_id = resp_["object"]["id"]
        # 下发work order订单到WMS
        resp = InternalWorkOrderInterface().send_work_order_to_wms(wo_id)
        end_sql = f"SELECT status FROM weee_merch.io_product_bundle_order WHERE id = {wo_id};"
        resp_db_end = db_query(end_sql)
        assert resp["message"] == "success"
        assert resp_db_end[0][0] == "P"

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_query_bundle_config(self):
        """
        验证查询出所以已配置并在Active状态的盲盒
        :return:
        """
        start_sql = f"SELECT id FROM weee_merch.pi_product_bundle WHERE status = 'A' ORDER BY id DESC;"
        resp_db_start = db_query(start_sql)
        resp = InternalWorkOrderInterface().query_bundle_config_list()
        assert resp["object"]["list"][0]["id"] == str(resp_db_start[0][0])
        assert resp["object"]["list"][-1]["id"] == str(resp_db_start[-1][0])


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")
