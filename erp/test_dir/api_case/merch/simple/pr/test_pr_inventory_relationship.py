import weeeTest
from erp.test_dir.api.merch.pr_management.pr_inventory_relationship import PrInventoryRelationship
from erp.test_dir.db_utils import DBConnect


class TestPrInventoryRelationship(weeeTest.TestCase):

    @weeeTest.params.file(file_name="inventory_relationship.yaml", key="pr_inventory_relationship_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_inventory_relationship_list(self, request):
        """inventory relationship 列表查询"""
        resp = PrInventoryRelationship().inventory_relationship_list(request["params_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert len(db_res) == len(resp["object"]["list"])
        for x in range(len(resp["object"]["list"])):
            assert int(resp["object"]["list"][x]["id"]) == db_res[x][0]
            assert int(resp["object"]["list"][x]["inventory_id"]) in db_res[x]
            assert int(resp["object"]["list"][x]["vendor_id"]) in db_res[x]
            assert resp["object"]["list"][x]["storage_type"] in db_res[x]
            if resp["object"]["list"][x]["connection_inventory_id"] == '':
                assert db_res[x][5] is None
            else:
                assert int(resp["object"]["list"][x]["connection_inventory_id"]) == db_res[x][5]
            if resp["object"]["list"][x]["connection_inventory_type"] == '':
                assert db_res[x][6] is None
            else:
                assert int(resp["object"]["list"][x]["connection_inventory_type"]) == db_res[x][6]

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_add_inventory_relationship(self):
        """新增inventory relationship失败"""
        sql = 'SELECT * FROM weee_merch.pr_config_inventory_relation WHERE vendor_id =1 and inventory_id =8'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp_direct = PrInventoryRelationship().add_inventory_relationship(vendor_id='1', inventory_id='8',
                                                                           storage_type='N',
                                                                           supply_mode='1')
        resp_cross = PrInventoryRelationship().add_inventory_relationship(vendor_id='1', inventory_id='8',
                                                                          storage_type='F',
                                                                          supply_mode='2',
                                                                          connection_inventory_type='1',
                                                                          connection_inventory_id='34')
        resp_blank = PrInventoryRelationship().add_inventory_relationship()
        assert resp_direct["result"] is False
        assert resp_direct["message"] == 'Duplicate supplier-inventory configuration, stop adding; '
        assert resp_cross["result"] is False
        assert resp_cross["message"] == 'Duplicate supplier-inventory configuration, stop adding; '
        assert resp_blank["result"] is False
        assert resp_blank["message"] == 'The Vendor field is required. The Inventory field is required. The Supply Mode field is required. '
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="inventory_relationship.yaml", key="pr_update_inventory_relationship")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_inventory_relationship(self, request):
        """inventory relationship 更新"""
        resp = PrInventoryRelationship().update_inventory_relationship(request["json_data"])
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_inventory_relation WHERE id=1234'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == 288
        assert db_res[0][2] == 25
        assert db_res[0][3] == "R"

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_inventory_relationship(self):
        """inventory relationship 删除"""
        resp = PrInventoryRelationship().delete_inventory_relationship(ids='6427')
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_inventory_relation WHERE id=6427'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res == []


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
