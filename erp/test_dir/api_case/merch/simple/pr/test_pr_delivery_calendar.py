import weeeTest
from erp.test_dir.api.merch.pr_management.pr_delivery_calendar import PrDeliveryCalendar
from erp.test_dir.db_utils import DBConnect


class TestPrDeliveryCalendar(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delivery_calendar_list(self):
        """PR 送货日历列表查询"""
        resp = PrDeliveryCalendar().delivery_calendar_list(vendor_id=38, inventory_id=20)
        assert resp["result"] is True
        assert resp["object"]["list"][0]["vendor_id"] == "38"
        assert resp["object"]["list"][0]["inventory_name"] == "TX - Houston"
        sql = ('SELECT * FROM weee_merch.pr_config_delivery_calendar WHERE vendor_id =38 and inventory_id =20 ORDER BY '
               'id DESC')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == resp["object"]["list"][0]["id"]

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_add_delivery_calendar(self):
        """PR 送货日历新增失败"""
        sql = 'SELECT * FROM weee_merch.pr_config_delivery_calendar WHERE vendor_id =1244 and inventory_id =20'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrDeliveryCalendar().add_delivery_calendar(vendor_id='1244', inventory_id='20',
                                                          storage_type='F', order_weekday='2', frequency='1',
                                                          lead_time='16')
        assert resp["result"] is False
        assert resp["message"] == 'The Lead Time To Connecting Warehouse field is required. '
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="pr_delivery_calendar_data.yaml", key="update_delivery_calendar")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_delivery_calendar(self, request):
        """PR 送货日历更新"""
        resp = PrDeliveryCalendar().update_delivery_calendar(request["json_data"])
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_delivery_calendar WHERE id =8949'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][4] == 4

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_delivery_calendar(self):
        """PR 送货日历删除"""
        sql_update = 'UPDATE weee_merch.pr_config_delivery_calendar SET status = "A" where id =5884'
        DBConnect().update_data_from_mysql(sql_update)
        resp = PrDeliveryCalendar().delete_delivery_calendar(ids=5884)
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_delivery_calendar WHERE id =5884'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][13] == 'X'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
