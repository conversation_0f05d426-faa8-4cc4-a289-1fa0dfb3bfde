import weeeTest
from erp.test_dir.api.merch.pr_management.pr_purchase_requirement import PrPurchaseRequirement
from erp.test_dir.db_utils import DBConnect


class TestPurchaseRequirement(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_purchase_requirement_list(self):
        """PR列表查询"""
        resp = PrPurchaseRequirement().purchase_requirement_list(status='P', product_id=76702, inventory_id=25)
        assert resp["result"] is True
        assert resp["object"]["orders"][0]["inventory_title"] == "LA - La Mirada"
        assert resp["object"]["orders"][0]["vendor_title"] == "Test Vender 123321"
        assert resp["object"]["orders"][0]["status"] == "P"
        sql = 'SELECT * FROM weee_merch.pr_purchase_request WHERE status ="P" and product_id =76702'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == resp["object"]["orders"][0]["id"]

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_add_purchase_requirement(self):
        """PR新增失败"""
        resp = PrPurchaseRequirement().add_purchase_requirement()
        assert resp["result"] is False
        assert resp[
                    "message"] == 'The Vendor field is required. The Inventroy field is required. The SKU field is required. The QTY field is required. The ETA field is required. '

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_purchase_requirement(self):
        """PR删除"""
        sql = 'SELECT * FROM weee_merch.pr_purchase_request WHERE id =174678'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrPurchaseRequirement().delete_purchase_requirement(ids=174678)
        assert resp["result"] is False
        assert resp["message"] == 'Cannot delete since PR[174678] is already generated PO.'
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
