import weeeTest
from erp.test_dir.api.merch.pr_management.pr_additional_delivery import PrAdditionalDelivery
from erp.test_dir.db_utils import DBConnect


class TestPrAdditionalDelivery(weeeTest.TestCase):
    additional_id = []

    @weeeTest.params.file(file_name="pr_additional_delivery_data.yaml", key="query_additional_delivery_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_additional_delivery_list(self, request):
        """additional 列表查询"""
        resp = PrAdditionalDelivery().additional_delivery_list(request["params_data"])
        assert resp["result"] is True
        sql_1 = "SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE vendor_id ="
        sql_2 = str(request["params_data"]["filter[vendor_id]"])
        sql_3 = " and inventory_id ="
        sql_4 = str(request["params_data"]["filter[inventory_id]"])
        sql_5 = " and status ='A' ORDER BY id desc"
        sql = sql_1 + sql_2 + sql_3 + sql_4 + sql_5
        print(sql)
        db_res = DBConnect().select_data_from_mysql(sql)
        print(db_res)
        if len(resp["object"]["list"]) > 0:
            assert len(db_res) == len(resp["object"]["list"])
            for x in range(len(resp["object"]["list"])):
                assert int(resp["object"]["list"][x]["id"]) == db_res[x][0]
                assert resp["object"]["list"][x]["storage_type"] == db_res[x][3]
        else:
            assert len(db_res) == len(resp["object"]["list"])

    @weeeTest.params.file(file_name="pr_additional_delivery_data.yaml", key="pr_add_additional_delivery_fail")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_add_additional_delivery_fail(self, request):
        """additional新增失败，重复添加"""
        sql = ("SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE vendor_id =1 and inventory_id =37 and "
               "status ='A' and storage_type ='N'")
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrAdditionalDelivery().add_additional_delivery(request["json_data"])
        assert resp["result"] is False
        assert resp["message"] == 'Duplicate vendor - delivery date configuration, stop adding'
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="pr_additional_delivery_data.yaml", key="pr_add_additional_delivery_success")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_add_additional_delivery_success(self, request):
        """additional新增成功"""
        vendor_id = request["json_data"]["vendor_id"]
        inventory_id = request["json_data"]["inventory_id"]
        sql = f"SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE vendor_id = {vendor_id} and inventory_id = {inventory_id} and status ='A' ORDER BY id desc"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrAdditionalDelivery().add_additional_delivery(request["json_data"])
        assert resp["result"] is True
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_after[0][1] == request["json_data"]["vendor_id"]
        assert db_res_after[0][2] == request["json_data"]["inventory_id"]
        assert db_res_after[0][3] == request["json_data"]["storage_type"]
        assert str(db_res_after[0][6]) == request["json_data"]["order_date"]
        if request["json_data"]["eta_connection_date"] is None:
            assert db_res_after[0][7] is None
        else:
            assert str(db_res_after[0][7]) == request["json_data"]["eta_connection_date"]
        assert str(db_res_after[0][8]) == request["json_data"]["eta_final_date"]
        assert len(db_res_after) == len(db_res_before) + 1
        TestPrAdditionalDelivery.additional_id.append(db_res_after[0][0])
        print(TestPrAdditionalDelivery.additional_id)

    @weeeTest.params.file(file_name="pr_additional_delivery_data.yaml", key="pr_update_additional_delivery_success")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_additional_delivery_list_success(self, request):
        """additional 更新成功"""
        resp = PrAdditionalDelivery().update_additional_delivery(request["json_data"])
        assert resp["result"] is True
        id = request["json_data"]["id"]
        sql = f"SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE id = {id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == request["json_data"]["vendor_id"]
        assert db_res[0][2] == request["json_data"]["inventory_id"]
        assert db_res[0][3] == request["json_data"]["storage_type"]
        assert str(db_res[0][6]) == request["json_data"]["order_date"]
        if request["json_data"]["eta_connection_date"] is None:
            assert db_res[0][7] is None
        else:
            assert str(db_res[0][7]) == request["json_data"]["eta_connection_date"]
        assert str(db_res[0][8]) == request["json_data"]["eta_final_date"]

    @weeeTest.params.file(file_name="pr_additional_delivery_data.yaml", key="pr_update_additional_delivery_fail")
    @weeeTest.mark.list('Smoke', 'SRM')
    def test_update_additional_delivery_list_fail(self, request):
        """additional 更新失败"""
        id = request["json_data"]["id"]
        sql = f"SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE id = {id}"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrAdditionalDelivery().update_additional_delivery(request["json_data"])
        assert resp["result"] is False
        assert resp["message"] == request["message"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_additional_delivery_list(self):
        """additional 删除"""
        for x in range(len(TestPrAdditionalDelivery.additional_id)):
            resp = PrAdditionalDelivery().delete_additional_delivery(TestPrAdditionalDelivery.additional_id[x])
            assert resp["result"] is True
            id = TestPrAdditionalDelivery.additional_id[x]
            sql = f"SELECT * FROM weee_merch.pr_config_delivery_date_special WHERE id = {id}"
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][9] == 'X'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
