import weeeTest
from erp.test_dir.api.merch.pr_management.pr_dedicated_relationship import PrDedicatedRelationship
from erp.test_dir.db_utils import DBConnect


class TestPrDedicatedRelationship(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_dedicated_relationship_list(self):
        """dedicated_relationship 列表查询"""
        resp = PrDedicatedRelationship().dedicated_relationship_list(vendor_id=38, warehouse_id=41)
        assert resp["result"] is True
        assert resp["object"]["list"][0]["vendor_name"] == "BIG GREEN USA INC."
        assert resp["object"]["list"][0]["warehouse_id"] == "41"
        sql = 'SELECT * FROM weee_merch.pr_config_dedicated_relation WHERE vendor_id =38 and warehouse_id =41'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == resp["object"]["list"][0]["id"]

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_add_dedicated_relationship(self):
        """dedicated_relationship 新增失败"""
        sql = 'SELECT * FROM weee_merch.pr_config_dedicated_relation WHERE vendor_id =1 and warehouse_id =8'
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = PrDedicatedRelationship().add_dedicated_relationship(vendor_id='1', warehouse_id='8',
                                                                           dedicated_user_id='7660033',
                                                                           storage_type='N',
                                                                           ethnicity=['chinese', 'korean'],
                                                                           timezone='America/New_York')
        resp_blank = PrDedicatedRelationship().add_dedicated_relationship()
        assert resp["result"] is False
        assert resp["message"] == 'Duplicate dedicated catman - vendor - warehouse - storage type -ethnicity configuration, stop adding,please modify; '
        assert resp_blank["result"] is False
        assert resp_blank["message"] == 'The Vendor field is required. The Dedicated Catman field is required. The Warehouse field is required. The Timezone field is required. The Storage Type field is required. The Ethnicity field is required. '
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert db_res_before == db_res_after

    @weeeTest.params.file(file_name="pr_dedicated_relationship_data.yaml", key="update_dedicated_relationship")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_update_dedicated_relationship(self, request):
        """dedicated_relationship 编辑"""
        resp = PrDedicatedRelationship().update_dedicated_relationship(request["json_data"])
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_dedicated_relation WHERE id=2878'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == 1
        assert db_res[0][2] == 7169981
        assert db_res[0][3] == "R"
        assert db_res[0][6] == "chinese,korean"

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_dedicated_relationship(self):
        """dedicated_relationship 删除"""
        resp = PrDedicatedRelationship().delete_dedicated_relationship(ids='2289')
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.pr_config_dedicated_relation WHERE id =2289'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res == []


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
