import weeeTest
import re
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.merch.task.task_api import TaskInterface


class TestTask(weeeTest.TestCase):
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_dashboard_list(self):
        """查询Task Dashboard列表"""
        # 获取当前登录账号权限下的userid
        resp = TaskInterface().task_dashboard_org_tree()
        assert resp["result"] is True
        user_id = int(resp["object"]["user_id"])

        # 根据userid查询dashboard列表数据
        resp1 = TaskInterface().task_dashboard_list(user_id)
        assert resp1["result"] is True
        assert resp1["object"] is not None

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_dashboard_check(self):
        """Dashboard和Task list数据对比"""
        # 根据自动化登录账号10937481,对比报表数据和task list数据
        resp = TaskInterface().task_dashboard_list('10937481')
        assert resp["result"] is True
        # 获取不同task类型dashboard中Pending总数
        cs_to_catman_p = int(resp["object"][0]["cs_to_catman"]["pending"])
        # sku_direct_import_p = int(resp["object"][0]["sku_direct_import"]["pending"])
        document_review_p = int(resp["object"][0]["document_review"]["pending"])
        po_payment_review_p = int(resp["object"][0]["po_payment_review"]["pending"])
        po_receipt_p = int(resp["object"][0]["po_receipt"]["pending"])

        # 数据对比
        resp_cs_to_catman_p = TaskInterface().task_management_filter(type='cs_to_catman', status='1')
        assert resp_cs_to_catman_p["result"] is True
        assert resp_cs_to_catman_p["object"]["totalCount"] == cs_to_catman_p

        # resp_sku_direct_import_p = TaskInterface().task_management_filter(type='sku_direct_import', status='1')
        # assert resp_sku_direct_import_p["object"]["totalCount"] == sku_direct_import_p

        resp_document_review_p = TaskInterface().task_management_filter(type='document_review', status='1')
        assert resp_document_review_p["object"]["totalCount"] == document_review_p

        resp_po_payment_review_p = TaskInterface().task_management_filter(type='po_payment_review', status='1')
        assert resp_po_payment_review_p["object"]["totalCount"] == po_payment_review_p

        resp_po_receipt_p = TaskInterface().task_management_filter(type='po_receipt', status='1')
        assert resp_po_receipt_p["object"]["totalCount"] == po_receipt_p

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_home_todo_list(self):
        """查询Home页Todos列表"""
        resp = TaskInterface().home_todo_option()
        assert resp["result"] is True
        # 目前支持13种业务类型卡片看板
        assert len(resp["object"]) >= 13
        # 查询当前账号Direct Import设置
        has_di = resp["object"][12]["disabled"]

        # 查询当前账号设置的todo
        resp1 = TaskInterface().home_todo_list()
        assert resp1["result"] is True
        has_data = len(resp1["object"]["todoData"])
        # 判断当前账号是否配置todo卡片,且包含DI配置
        if has_data > 0 and has_di == "true":
            assert re.search("Direct Import Request", str(resp1["object"]["todoData"]))

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_task_list_direct_import(self):
        """查询task列表"""
        resp_DI = TaskInterface().task_management_filter(type='sku_direct_import', keyword='106347',
                                                         creator='10937481')
        assert resp_DI["result"] is True
        count = resp_DI["object"]["totalCount"]
        sql = 'SELECT * FROM weee_bpm.bpm_direct_import_ticket WHERE product_id =106347'
        db_res = DBConnect().select_data_from_mysql(sql)
        if count == 0:
            assert len(db_res) == 0
        else:
            for x in range(count):
                assert resp_DI["object"]["list"][x]["business_data"]["subject"] == "Direct Import Request"
                assert resp_DI["object"]["list"][x]["type_name"] == "Direct Import Request"
                assert resp_DI["object"]["list"][x]["rec_creator_name"] == "yi.shen"
                assert "106347" in resp_DI["object"]["list"][x]["business_id"]




if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")
