import weeeTest
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.merch.product.product_bundle_config_api import ProductBundleConfigInterface


class TestBundleConfigModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="product_bundle_config_data.yaml", key="product_bundle_config_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_bundle_config_list(self, description, request):
        """测试盲盒商品配置列表查询"""
        data = request["params_data"]
        resp = ProductBundleConfigInterface().product_bundle_config_list(data)
        if list(data.keys())[0] == "filter[product_id]":
            inv_data = data["filter[product_id]"]
            start_sql = f"SELECT id FROM weee_merch.pi_product_bundle WHERE product_id = {inv_data} ORDER BY id DESC;"
            resp_db = DBConnect().select_data_from_mysql(start_sql)
            assert resp["object"]["totalCount"] == str(len(resp_db))
            assert resp["object"]["list"][0]["id"] == str(resp_db[0][0])
        if list(data.keys())[0] == "filter[keyword]":
            inv_data = data["filter[keyword]"]
            start_sql = f"SELECT product_id FROM weee_merch.pi_product_bundle WHERE id = {inv_data};"
            resp_db = DBConnect().select_data_from_mysql(start_sql)
            assert resp["object"]["list"][0]["product_id"] == str(resp_db[0][0])
        if list(data.keys())[0] == "filter[status]":
            inv_data = data["filter[status]"]
            start_sql = f"SELECT product_id FROM weee_merch.pi_product_bundle WHERE `status` = '{inv_data}' ORDER BY id DESC;"
            resp_db = DBConnect().select_data_from_mysql(start_sql)
            assert resp["object"]["list"][0]["product_id"] == str(resp_db[0][0])

    @weeeTest.params.file(file_name="product_bundle_config_data.yaml", key="product_bundle_config_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_bundle_config_info(self, description, request):
        """测试盲盒商品配置详情查询"""
        data = request["params_data"]
        resp = ProductBundleConfigInterface().product_bundle_config_info(data)
        inv_data = data["id"]
        start_sql = f"SELECT product_id FROM weee_merch.pi_product_bundle WHERE id = {inv_data};"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"]["productBundle"]["id"] == str(inv_data)
        assert resp["object"]["productBundle"]["product_id"] == str(resp_db[0][0])

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_query_bundle_product(self):
        """测试盲盒商品配置支持的商品查询"""
        resp = ProductBundleConfigInterface().query_bundle_product()
        start_sql = "SELECT id FROM weee_comm.gb_product WHERE product_type = 'box' AND  status != 'X' AND web_status != 'X';"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"][0]["id"] == str(resp_db[0][0])

    @weeeTest.params.file(file_name="product_bundle_config_data.yaml", key="product_bundle_config_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_bundle_detail(self, description, request):
        """盲盒商品的商品详情查询"""
        data = request["params_data"]
        resp = ProductBundleConfigInterface().query_product_info(data)
        inv_data = data["productId"]
        start_sql = f"SELECT storage_type FROM weee_comm.gb_product WHERE product_type = 'box' AND id = {inv_data}"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["message"] == "success"
        assert resp["object"]["storage_type"] == resp_db[0][0]

    @weeeTest.params.file(file_name="product_bundle_config_data.yaml", key="create_product_bundle_config")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_product_bundle_config(self, description, request):
        """创建商品盲盒配置"""
        data = request["data"]
        resp = ProductBundleConfigInterface().create_product_bundle_config(data)
        start_sql = "SELECT id FROM weee_merch.pi_product_bundle ORDER BY id DESC;"
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["message"] == "success"
        assert resp["object"]["id"] == resp_db[0][0]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_delete_product_bundle_config(self):
        """删除未下盲盒订单的盲盒商品配置"""
        start_sql = """SELECT ppb.id FROM weee_merch.pi_product_bundle ppb
        LEFT JOIN weee_merch.io_product_bundle_order ipbo on ppb.id = ipbo.bundle_id
        WHERE ipbo.bundle_id IS NULL AND ppb.status = 'A' ORDER BY ppb.id DESC;"""
        resp_db = DBConnect().select_data_from_mysql(start_sql)
        inv_data = resp_db[0][0]
        data = {"id": inv_data}
        resp = ProductBundleConfigInterface().delete_product_bundle_config(data)
        end_sql = f"SELECT status FROM weee_merch.pi_product_bundle WHERE id = {inv_data}"
        resp_end_db = DBConnect().select_data_from_mysql(end_sql)
        assert resp["message"] == "success"
        assert resp_end_db[0][0] == "X"


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")
