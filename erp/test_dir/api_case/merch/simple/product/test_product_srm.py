import time

import weeeTest
import datetime
from erp.test_dir.api.merch.product.product_srm import ProductInfo
from erp.test_dir.db_utils import DBConnect
import random

sku_ID = 106822
inactive_sku = 1
pack_SKU = 106551


class TestProductSRM(weeeTest.TestCase):
    # 定义全局产品id，用于后续添加tag、发起DI task，减少创建产品测试数据
    global_product_id = ''

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_add_product_sync_pricing(self):
        """新增、编辑商品同步产品平台新表"""
        upc_random = random.randint(1000, 9999) + 2000000170000
        resp = ProductInfo().add_product_tag(title='auto-sku-sync', short_title_en='auto-sku',
                                             short_title='auto-random',
                                             weee_buyer_id='13347083',
                                             ethnicity='chinese', storage_temperate_zone='N1',
                                             division_num='04',
                                             catalogue_num='1201', sub_catalogue_num='120101',
                                             product_id='', upc_code=str(upc_random), pi_tags=[])
        # 接口出参断言
        assert resp["result"] is True
        assert resp["object"]["product_id"] is not None
        # 获取出参中的产品id，进行db断言
        product_id = resp["object"]["product_id"]

        # 全局product id赋值
        TestProductSRM.global_product_id = product_id

        sql_product_id = f'SELECT * FROM weee_comm.gb_product WHERE id = {product_id};'
        db_res = DBConnect().select_data_from_mysql(sql_product_id)
        assert int(resp['object']['product_id']) == db_res[0][0]
        # 两次调用产品detail接口增加休眠
        time.sleep(1)

        # 修改产品title、short_title、short_title_en、division_num、catalogue_num、sub_catalogue_num
        resp1 = ProductInfo().add_product_tag(product_id=product_id, title='auto-sku-sync' + str(datetime.date.today()),
                                              short_title_en='auto-short_title_en' + str(datetime.date.today()),
                                              short_title='auto-short_title' + str(datetime.date.today()),
                                              weee_buyer_id='13347083',
                                              ethnicity='chinese', storage_temperate_zone='N1',
                                              division_num='05',
                                              catalogue_num='0501', sub_catalogue_num='050101',
                                              upc_code=str(upc_random), pi_tags=[])
        # 产品修改成功
        assert resp1["result"] is True

        # 检查产品老表数据weee_comm.gb_product
        db_res_old = DBConnect().select_data_from_mysql(sql_product_id)
        assert 'auto-sku-sync' + str(datetime.date.today()) == db_res_old[0][3]
        assert 'auto-short_title' + str(datetime.date.today()) == db_res_old[0][5]
        assert 'auto-short_title_en' + str(datetime.date.today()) == db_res_old[0][6]
        assert '05' == db_res_old[0][22]
        assert '0501' == db_res_old[0][23]
        assert '050101' == db_res_old[0][24]

        # # 同步数据走的MQ，增加休眠，避免数据未消费导致校验失败
        # time.sleep(3)

        # # 再进行一次修改
        # resp2 = ProductInfo().add_product_tag(product_id=product_id, title='auto-sku-sync' + str(datetime.date.today()),
        #                                       short_title_en='auto-short_title_en' + str(datetime.date.today()),
        #                                       short_title='auto-short_title' + str(datetime.date.today()),
        #                                       weee_buyer_id='13347083',
        #                                       ethnicity='chinese', storage_temperate_zone='N1',
        #                                       division_num='05',
        #                                       catalogue_num='0501', sub_catalogue_num='050101',
        #                                       upc_code=str(upc_random), pi_tags=[])
        # assert resp2["result"] is True
        #
        # # 检查产品新表数据weee_pricing.prc_product
        # sql_pricing_product_id = f'SELECT * FROM weee_pricing.prc_product WHERE product_id = {product_id};'
        # db_res_new = DBConnect().select_data_from_mysql(sql_pricing_product_id)
        # assert 'auto-sku-sync' + str(datetime.date.today()) == db_res_new[0][2]
        # assert 'auto-short_title' + str(datetime.date.today()) == db_res_new[0][3]
        # assert 'auto-short_title_en' + str(datetime.date.today()) == db_res_new[0][4]
        # assert '05' == db_res_new[0][8]
        # assert '0501' == db_res_new[0][9]
        # assert '050101' == db_res_new[0][10]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_list(self):
        """商品列表查询"""
        resp = ProductInfo().product_list(10937481)
        assert resp["result"] is True
        assert resp["object"]["list"] is not None
        sql_new_product = f"SELECT * FROM weee_comm.gb_product WHERE id = {resp['object']['list'][0]['id']}"
        db_res = DBConnect().select_data_from_mysql(sql_new_product)
        assert str(db_res[0][0]) == resp['object']['list'][0]['id']

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_detail(self):
        """商品详情"""
        resp = ProductInfo().product_detail(sku_ID)
        if resp["result"] is True:
            product_title = str(resp["object"]["basic_info"]["title"])
            sql_product = f"SELECT * FROM weee_comm.gb_product WHERE id = {sku_ID}"
            db_res = DBConnect().select_data_from_mysql(sql_product)
            assert sku_ID == db_res[0][0]  # ID
            assert product_title == db_res[0][3]  # 标题

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_add_product(self):
        """新增商品"""
        categories = {
            'R1': {
                '06': {
                    '0101': ['010101', '010106'],
                    '0205': ['020508', '020509']
                },
                '02': {
                    '1301': ['130101', '130102'],
                    '1302': ['130201', '130202']
                }
            },
            'N1': {
                '04': {
                    '1201': ['120101', '120102'],
                    '1601': ['160101', '160102']
                },
                '05': {
                    '0501': ['050101', '050102']
                }
            },
            'F1': {
                '01': {
                    '0301': ['030101', '030102'],
                    '0401': ['040101', '040102']
                },
                '03': {
                    '0801': ['080101', '080102']
                }
            }
        }
        # 随机选择第一层类别
        random_first_category = random.choice(list(categories.keys()))
        # 随机选择第二层子类别
        random_second_category = random.choice(list(categories[random_first_category].keys()))
        # 随机选择第三层子类别
        random_third_category = random.choice(list(categories[random_first_category][random_second_category].keys()))
        # 随机选择第四层子子类别
        random_fourth_category = random.choice(
            categories[random_first_category][random_second_category][random_third_category])
        upc_random = random.randint(1000, 9999) + 2000000180000
        resp = ProductInfo().add_product(short_title_en='auto-sku', short_title='auto-random',
                                         weee_buyer_id='10937481',
                                         ethnicity='chinese', storage_temperate_zone=random_first_category,
                                         division_num=random_second_category,
                                         catalogue_num=random_third_category, sub_catalogue_num=random_fourth_category,
                                         product_id='', upc_code=str(upc_random))
        assert resp["result"] is True
        assert resp["object"]["product_id"] is not None
        product_id = resp["object"]["product_id"]
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {product_id};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert int(resp['object']['product_id']) == db_res[0][0]
        # delete_product = f'DELETE FROM weee_comm.gb_product WHERE id = {product_id};'
        # delete_warehouse = f'DELETE FROM weee_merch.im_warehouse_product WHERE product_id = {product_id};'
        # DBConnect().update_data_from_mysql(delete_product)
        # DBConnect().update_data_from_mysql(delete_warehouse)
        # db_res_query_product = DBConnect().select_data_from_mysql(sql)
        # assert db_res_query_product == []
        # sql_warehouse = f'SELECT * FROM weee_merch.im_warehouse_product WHERE product_id = {product_id};'
        # db_res_query_warehouse = DBConnect().select_data_from_mysql(sql_warehouse)
        # assert db_res_query_warehouse == []

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_edit_product_detail(self):
        """编辑商品详情"""
        ethnicity = 'chinese'
        resp = ProductInfo().add_product(short_title_en='auto-peach', short_title='auto-peach',
                                         weee_buyer_id='10937481',
                                         ethnicity=ethnicity, storage_temperate_zone='R1', division_num='02',
                                         catalogue_num='1302', sub_catalogue_num='130204', product_id=sku_ID)
        assert resp["result"] is True
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {sku_ID};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp['object']['product_id'] == db_res[0][0]
        assert ethnicity == db_res[0][27]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_inactive_product(self):
        """inactive商品"""
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {inactive_sku};'
        db_res = DBConnect().select_data_from_mysql(sql)
        if db_res[0][9] == 'A':
            json = {
                "operate": "down"
            }
            resp = ProductInfo().put_on_and_take_off_product(sku_id=inactive_sku, json_data=json)
            assert resp["result"] is True
            print('下架', resp, inactive_sku, db_res[0][9])
        else:
            json1 = {
                "operate": "up"
            }
            resp = ProductInfo().put_on_and_take_off_product(sku_id=inactive_sku, json_data=json1)
            assert resp["result"] is True
            print('上架', resp, inactive_sku, db_res[0][9])

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_add_special_product(self):
        """新增包材商品"""
        data = {
            "vendor_product_title": "A-包材",
            "short_title_en": "A-包材",
            "title_en": "A-包材",
            "inventory_image_url": "https://img06.test.weeecdn.com/product/image/534/825/58590D6099CEAA74.jpeg",
            "vendor_id": "7239",
            "storage_temperate_zone": "N1",
            "division_num": "09",
            "catalogue_num": "9801",
            "purchase_price": 11,
            "purchase_unit": "unit",
            "quantity_per_unit": 1,
            "weee_buyer_id": "10937481",
            "volume": 90
        }
        resp = ProductInfo().add_edit_special_product(data=data)
        assert resp["result"] is True
        pack_SKU = resp["object"]["product_id"]
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {pack_SKU};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert int(resp["object"]["product_id"]) == db_res[0][0]
        delete_product = f'DELETE FROM weee_comm.gb_product WHERE id = {pack_SKU};'
        delete_warehouse = f'DELETE FROM weee_merch.im_warehouse_product WHERE product_id = {pack_SKU};'
        DBConnect().update_data_from_mysql(delete_product)
        DBConnect().update_data_from_mysql(delete_warehouse)
        db_res_query_product = DBConnect().select_data_from_mysql(sql)
        assert db_res_query_product == []
        sql_warehouse = f'SELECT * FROM weee_merch.im_warehouse_product WHERE product_id = {pack_SKU};'
        db_res_query_warehouse = DBConnect().select_data_from_mysql(sql_warehouse)
        assert db_res_query_warehouse == []

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_special_product_detail(self):
        """包材商品详情"""
        resp = ProductInfo().special_product_detail(pack_SKU)
        assert resp["result"] is True
        short_title_en = resp["object"]["short_title_en"]
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {pack_SKU};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert short_title_en == db_res[0][6]  # short_title_en
        # print('包材商品详情', resp, db_res[0][6])

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_edit_special_product(self):
        """编辑包材商品"""

        title_en = "包材Update"
        data = {
            "product_id": pack_SKU,
            "vendor_product_title": "包材",
            "short_title_en": title_en,
            "title_en": "包材Update",
            "inventory_image_url": "https://img06.test.weeecdn.com/product/image/534/825/58590D6099CEAA74.jpeg",
            "vendor_id": "7239",
            "storage_temperate_zone": "N1",
            "division_num": "09",
            "catalogue_num": "9801",
            "purchase_price": "11.00",
            "purchase_unit": "unit",
            "quantity_per_unit": "1.00",
            "weee_buyer_id": "10937481",
            "volume": 91
        }
        resp = ProductInfo().add_edit_special_product(data=data)
        assert resp["result"] is True
        sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {pack_SKU};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert int(pack_SKU) == db_res[0][0]  # ID
        assert title_en == db_res[0][6]  # short_title_en

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_addtag(self):
        """编辑商品并添加标签"""
        # 根据全局产品id查询现有数据，用于后续增加产品标签
        if TestProductSRM.global_product_id != '':
            sql_product = f'SELECT * FROM weee_comm.gb_product WHERE id = {TestProductSRM.global_product_id};'
            db_res = DBConnect().select_data_from_mysql(sql_product)

            # 根据db中数据提取后续增加tag接口入参
            product_id = db_res[0][0]
            title = db_res[0][3]
            short_title = db_res[0][5]
            short_title_en = db_res[0][6]
            weee_buyer_id = db_res[0][19]
            division_num = db_res[0][22]
            catalogue_num = db_res[0][23]
            sub_catalogue_num = db_res[0][24]
            upc_code = db_res[0][16]

            # 修改产品，增加product tag
            resp = ProductInfo().add_product_tag(product_id=product_id, title=title, short_title_en=short_title_en,
                                                 short_title=short_title,
                                                 weee_buyer_id=weee_buyer_id, ethnicity="chinese",
                                                 storage_temperate_zone="N1",
                                                 division_num=division_num, catalogue_num=catalogue_num,
                                                 sub_catalogue_num=sub_catalogue_num,
                                                 upc_code=upc_code, pi_tags=['auto', 'ZhangjwTag'])
            # 产品修改成功
            assert resp["result"] is True

            # product tag db断言
            sql_product_tag = f'SELECT * FROM weee_merch.pi_tag WHERE target_id = {product_id};'
            db_res1 = DBConnect().select_data_from_mysql(sql_product_tag)
            assert "auto" == db_res1[0][3]
            # 新增产品成功，校验db中产品数量大于2
            assert len(db_res1) == 2

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_product_direct_import(self):
        """产品发起Direct Import Task"""
        # 创建DI Task
        resp1 = ProductInfo().add_product_direct_import(product_id=TestProductSRM.global_product_id,
                                                        indirect_supplier_name='product_DI_auto',
                                                        indirect_supplier_status='N', landing_price='20.99')
        assert resp1["result"] is True

        # 调用产品详情接口
        resp_detail = ProductInfo().product_detail(TestProductSRM.global_product_id)
        # response中获取DI Task Ticket Id
        DI_ticket_id = resp_detail["object"]["warehouse_info"]["business_id"]
        assert resp_detail["result"] is True
        assert DI_ticket_id is not None

        # ticket id数据库落表检查
        sql_ticket = f'SELECT * FROM weee_merch.pi_product_task WHERE business_id = {DI_ticket_id};'
        db_res1 = DBConnect().select_data_from_mysql(sql_ticket)
        assert DI_ticket_id == db_res1[0][1]
        # 校验Task表中的产品id
        assert int(TestProductSRM.global_product_id) == db_res1[0][2]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_sku_performance_info(self):
        """【102839】sku_performance页面数据校验"""
        # basic info
        resp = ProductInfo().sku_performance_basic(42)
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_comm.gb_product WHERE id = 42'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][19] == resp["object"]["weee_buyer_id"]
        assert db_res[0][6] == resp["object"]["short_title_en"]
        assert db_res[0][23] == resp["object"]["catalogue_num"]
        assert db_res[0][24] == resp["object"]["sub_catalogue_num"]
        assert db_res[0][27] == resp["object"]["ethnicity"]
        sql_tier = 'SELECT * FROM weee_merch.pi_product_tier WHERE product_id =42 and ethnicity ="chinese"'
        db_res = DBConnect().select_data_from_mysql(sql_tier)
        assert str(db_res[0][3]) == resp["object"]["tier"]




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
