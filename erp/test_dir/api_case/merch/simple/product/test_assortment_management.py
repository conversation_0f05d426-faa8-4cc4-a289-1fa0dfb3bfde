import weeeTest
from erp.test_dir.api.merch.product.assortment_management import AssortmentManagement
from erp.test_dir.db_utils import DBConnect


class TestMetrics(weeeTest.TestCase):
    @weeeTest.params.file(file_name="assortment_management.yaml", key="get_assortment_list")
    @weeeTest.mark.list('Regression', 'SRM', 'Smoke')
    def test_get_assortment_list(self, request):
        """查询获取 assortment list """
        resp = AssortmentManagement().get_assortment_list(request["params_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        if len(db_res) != 0:
            for x in range(min(len(resp["object"]["list"]), 5)):
                assert resp["object"]["list"][x]["id"] == str(db_res[x][0])
                assert resp["object"]["list"][x]["max_capacity"] == str(db_res[x][10])
                assert resp["object"]["list"][x]["active_capacity"] == str(db_res[x][11])
        assert len(db_res) == resp["object"]["totalCount"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')