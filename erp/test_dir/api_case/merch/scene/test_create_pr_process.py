import datetime
import weeeTest
from erp.test_dir.api.merch.pr_management.pr_inventory_relationship import PrInventoryRelationship
from erp.test_dir.api.merch.pr_management.pr_dedicated_relationship import PrDedicatedRelationship
from erp.test_dir.api.merch.pr_management.pr_delivery_calendar import PrDeliveryCalendar
from erp.test_dir.api.merch.pr_management.pr_purchase_requirement import PrPurchaseRequirement
from erp.test_dir.api.merch.pr_management.pr_common_methods import PrCommonMethods
from erp.test_dir.db_utils import DBConnect


class TestCreatePRProcess(weeeTest.TestCase):
    """PR的流程"""
    vendor_id = '24'
    manual_vendor = '2'
    vendor_name = "长城海鲜食品公司 Great wall seafood LA"
    inventory_id = '14'
    inventory_name = "SF - Volpey"
    storage_type = 'F'
    user_id = '7673902'
    today = datetime.datetime.today().strftime("%Y-%m-%d")
    tomorrow = (datetime.datetime.today() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    eta_date = (datetime.datetime.today() + datetime.timedelta(days=2)).strftime("%Y-%m-%d")
    product_id = '216'

    @weeeTest.params.file(file_name="inventory_relationship.yaml", key="scene_add_direct_inventory_relationship")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_scene_add_direct_shipping_inventory_relationship(self, request):
        """场景: 创建新的direct_shipping inventory relationship"""
        resp = PrInventoryRelationship().inventory_relationship_list(request["params_data"])
        if resp["object"]["totalCount"] == 0:
            PrInventoryRelationship().add_inventory_relationship(vendor_id=TestCreatePRProcess.vendor_id,
                                                                 inventory_id=TestCreatePRProcess.inventory_id,
                                                                 storage_type=TestCreatePRProcess.storage_type,
                                                                 supply_mode='1')
            new_resp = PrInventoryRelationship().inventory_relationship_list(request["params_data"])
            assert_data = {
                "connection_inventory_name": "",
                "connection_inventory_type": "",
                "inventory_name": TestCreatePRProcess.inventory_name,
                "storage_type_map": [
                    "Frozen"
                ],
                "supply_mode_text": "Direct shipping",
                "vendor_name": TestCreatePRProcess.vendor_name
            }
            PrCommonMethods().assert_api_data(assert_data, new_resp["object"]["list"][0])
        sql = 'SELECT * FROM weee_merch.pr_config_inventory_relation WHERE vendor_id =24 and inventory_id =14'
        db_res = DBConnect().select_data_from_mysql(sql)
        query_again = PrInventoryRelationship().inventory_relationship_list(request["params_data"])
        assert str(db_res[0][0]) == query_again["object"]["list"][0]["id"]
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_scene_add_delivery_calendar(self):
        """场景: 创建新的delivery calendar"""
        resp = PrDeliveryCalendar().delivery_calendar_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                           inventory_id=TestCreatePRProcess.inventory_id,
                                                           storage_type=TestCreatePRProcess.storage_type)
        if resp["object"]["totalCount"] != 0:
            relationship_id = PrCommonMethods().get_required_id(resp)
            PrDeliveryCalendar().delete_delivery_calendar(ids=relationship_id)
        PrDeliveryCalendar().add_delivery_calendar(vendor_id=TestCreatePRProcess.vendor_id,
                                                   inventory_id=TestCreatePRProcess.inventory_id,
                                                   storage_type=TestCreatePRProcess.storage_type,
                                                   order_weekday=datetime.datetime.today().weekday() + 1,
                                                   frequency='1',
                                                   lead_time='1')
        new_resp = PrDeliveryCalendar().delivery_calendar_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                               inventory_id=TestCreatePRProcess.inventory_id,
                                                               storage_type=TestCreatePRProcess.storage_type)
        assert_data = {
            "storage_type_map": [
                "Frozen"
            ],
            "order_cycle": "Every week",
            "vendor_name": TestCreatePRProcess.vendor_name,
            "inventory_name": TestCreatePRProcess.inventory_name,
            "status": "A"
        }
        PrCommonMethods().assert_api_data(assert_data, new_resp["object"]["list"][0])
        sql = ('SELECT * FROM weee_merch.pr_config_delivery_calendar WHERE vendor_id =24 and inventory_id =14 and '
               'status = "A" and lead_time =1')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == new_resp["object"]["list"][0]["id"]
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_scene_add_dedicated_relationship(self):
        """场景: 创建新的dedicated relationship"""
        resp = PrDedicatedRelationship().dedicated_relationship_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                     warehouse_id=TestCreatePRProcess.inventory_id,
                                                                     storage_type=TestCreatePRProcess.storage_type)
        if resp["object"]["totalCount"] != 0:
            relationship_id = PrCommonMethods().get_required_id(resp)
            PrDedicatedRelationship().delete_dedicated_relationship(ids=relationship_id)
        PrDedicatedRelationship().add_dedicated_relationship(vendor_id=TestCreatePRProcess.vendor_id,
                                                             warehouse_id=TestCreatePRProcess.inventory_id,
                                                             dedicated_user_id=TestCreatePRProcess.user_id,
                                                             storage_type=TestCreatePRProcess.storage_type,
                                                             ethnicity=['chinese', 'korean'],
                                                             timezone='America/Los_Angeles')
        new_resp = PrDedicatedRelationship().dedicated_relationship_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                         warehouse_id=TestCreatePRProcess.inventory_id,
                                                                         user_id=TestCreatePRProcess.user_id)
        assert_data = {
            "storage_type_map": [
                "Frozen"
            ],
            "timezone": "America/Los_Angeles",
            "vendor_name": TestCreatePRProcess.vendor_name,
            "warehouse_id": "14",
            "ethnicity": "chinese,korean"
        }
        PrCommonMethods().assert_api_data(assert_data, new_resp["object"]["list"][0])
        sql = 'SELECT * FROM weee_merch.pr_config_dedicated_relation WHERE vendor_id =24 and warehouse_id =14'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][0]) == new_resp["object"]["list"][0]["id"]
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_scene_add_pr(self):
        """场景: 创建新的pr"""
        resp = PrPurchaseRequirement().purchase_requirement_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                 inventory_id=TestCreatePRProcess.inventory_id,
                                                                 status='C', product_id=TestCreatePRProcess.product_id)
        if resp["object"]["totalCount"] != 0:
            relationship_id = PrCommonMethods().get_required_pr_id(resp)
            PrPurchaseRequirement().delete_purchase_requirement(ids=relationship_id)
        PrPurchaseRequirement().add_purchase_requirement(vendor_id=TestCreatePRProcess.vendor_id,
                                                         inventory_id=TestCreatePRProcess.inventory_id,
                                                         product_id=TestCreatePRProcess.product_id,
                                                         quantity='4', eta_final_date=(
                    datetime.datetime.today() + datetime.timedelta(days=1)).strftime("%Y-%m-%d"))
        new_resp = PrPurchaseRequirement().purchase_requirement_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                     inventory_id=TestCreatePRProcess.inventory_id,
                                                                     status='C',
                                                                     product_id=TestCreatePRProcess.product_id)
        assert_data = {
            "storage_type": TestCreatePRProcess.storage_type,
            "qty": "4",
            "vendor_title": TestCreatePRProcess.vendor_name,
            "product_title": "Frozen Seasoned Seaweed Salad 4.4lbs",
            "inventory_title": TestCreatePRProcess.inventory_name,
            "status": "C",
            "source": "U",
            "quantity_per_unit": "4.00",
            # "eta_final_date": (datetime.datetime.today() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        }
        PrCommonMethods().assert_api_data(assert_data, new_resp["object"]["orders"][0])
        sql = ('SELECT * FROM weee_merch.pr_purchase_request WHERE vendor_id =24 and inventory_id =14 and '
               'status = "C" and qty =4 ORDER BY id DESC')
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == new_resp["object"]["orders"][0]["id"]
        assert len(db_res) >= 1  # 有可能有哪天的数据没有自动convert

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_scene_auto_generate_po(self):
        """场景: auto job, pr自动生成PO"""
        # --- 需要下午4点以后order day = today ---
        query_resp = PrPurchaseRequirement().purchase_requirement_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                       inventory_id=TestCreatePRProcess.inventory_id,
                                                                       status='C',
                                                                       product_id=TestCreatePRProcess.product_id)

        pr_id = query_resp["object"]["orders"][0]["id"]
        PrPurchaseRequirement().pr_auto_generate_po()
        pr_resp = PrPurchaseRequirement().purchase_requirement_list(vendor_id=TestCreatePRProcess.vendor_id,
                                                                    inventory_id=TestCreatePRProcess.inventory_id,
                                                                    status='P',
                                                                    product_id=TestCreatePRProcess.product_id)
        # assert pr_resp["object"]["orders"][0]["id"] == pr_id
        # assert pr_resp["object"]["orders"][0]["po_status"] == 'P'

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_scene_add_pr_consolidate(self):
        """场景: PR consolidate, pr生成PO"""
        # --- 创建新的pr consolidate ---
        resp = PrPurchaseRequirement().get_consolidation_pr_list(today_toggle='0',
                                                                 order_date=datetime.datetime.today().strftime(
                                                                     "%Y-%m-%d"),
                                                                 product_id=TestCreatePRProcess.product_id)
        pr_id = resp["object"]["orders"][0]["id"]
        consolidate_resp = PrPurchaseRequirement().pr_consolidate_to_po(pr_id=pr_id,
                                                                        inventory_name=TestCreatePRProcess.inventory_name,
                                                                        eta_final_date=TestCreatePRProcess.tomorrow)
        assert consolidate_resp["result"] is True
        pr_resp = PrPurchaseRequirement().purchase_requirement_list(vendor_id=TestCreatePRProcess.manual_vendor,
                                                                    inventory_id=TestCreatePRProcess.inventory_id,
                                                                    status='P',
                                                                    product_id=TestCreatePRProcess.product_id)
        assert pr_resp["object"]["orders"][0]["id"] == pr_id
        assert pr_resp["object"]["orders"][0]["po_status"] == 'A'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
