
import weeeTest
import jsonpath
from erp.test_dir.api.merch.Vendor.srm_vendor_api import SrmVendorInterface
from config.utils import Utils


class TestSrmVendor(weeeTest.TestCase):
    """
    场景测试：创建供应商并审核
    """
    id = ""

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_create_vendor(self):
        """创建供应商"""
        data = {}
        resp = SrmVendorInterface().create_vendor(data)
        resp.status_code = 200
        if jsonpath.jsonpath(resp, "$..result"):
            TestSrmVendor.id = resp["result"]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_finance_update_vendor(self):
        """财务更新供应商信息"""
        data = {
            "id": TestSrmVendor.id,
            "primary_vender_id": TestSrmVendor.id,
            "quickbook_id": Utils.random_num(100000000000, 999999999999)
        }
        resp = SrmVendorInterface().update_finance(data)
        try:
            assert resp["result"] is True
        except AssertionError as error:
            raise error

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_finance_update_vendor(self):
        """财务审批生效供应商"""
        data = {
            "id": TestSrmVendor.id,
        }
        resp = SrmVendorInterface().approve_finance(data)
        try:
            assert resp["result"] is True
        except AssertionError as error:
            raise error

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_finance_inactive_vendor(self):
        """财务审批inactive供应商"""
        data = {
            "id": TestSrmVendor.id,
        }
        resp = SrmVendorInterface().inactive_vendor(data)
        try:
            assert resp["result"] is True
        except AssertionError as error:
            raise error


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')







