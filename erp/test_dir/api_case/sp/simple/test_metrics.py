import weeeTest
from erp.test_dir.api.sp.metrics import MetricsManagement
import datetime
from erp.test_dir.db_utils import DBConnect


class TestMetrics(weeeTest.TestCase):
    @weeeTest.params.file(file_name="metrics.yaml", key="get_metrics_list")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_get_metrics_list(self, request):
        """查询获取 metrics list """
        resp = MetricsManagement().get_metrics_list(request["params_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        if len(db_res) != 0:
            for x in range(len(resp["object"]["data"])):
                assert resp["object"]["data"][x]["id"] in db_res[x]
                assert resp["object"]["data"][x]["metricsName"] in db_res[x]
        assert len(db_res) == len(resp["object"]["data"])

    @weeeTest.params.file(file_name="metrics.yaml", key="get_single_metrics_detail")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_get_single_metrics_detail(self, request):
        """查询获取单个 metrics 详情"""
        resp = MetricsManagement().get_single_metrics_detail(request["metrics_id"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert all(resp["object"][key] == db_res[0][index + 1] for index, key in enumerate(
            ["status", "metricsName", "metricsType", "skuScope", "salesScope", "isGoalSet", "ownerUserId"]))

    @weeeTest.params.file(file_name="metrics.yaml", key="add_metrics_success")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_add_metrics_success(self, request):
        """【102258，103299，102174，102173】新增 metrics 成功"""
        resp = MetricsManagement().add_update_metrics(request["json_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        json_data = request["json_data"]
        assert all(db_res[0][i + 1] == json_data[key] for i, key in enumerate(
            ["status", "metricsName", "metricsType", "skuScope", "salesScope", "isGoalSet", "ownerUserId"]))
        metrics_id = db_res[0][0]
        sql = f'SELECT * FROM weee_merch.sp_metrics_viewer WHERE metrics_id = {metrics_id}'
        db_res_viewer = DBConnect().select_data_from_mysql(sql)
        if not json_data["viewerIds"]:
            assert not db_res_viewer
        else:
            assert all(json_data["viewerIds"][x] == db_res_viewer[x][2] for x in range(len(json_data["viewerIds"])))

    @weeeTest.params.file(file_name="metrics.yaml", key="add_metrics_fail")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_add_metrics_fail(self, request):
        """新增 metrics 失败"""
        resp = MetricsManagement().add_update_metrics(request["json_data"])
        assert resp["result"] is False
        assert resp["message"] == 'The owner user cannot be included in the list of shared viewers.'
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert len(db_res) == 0

    @weeeTest.params.file(file_name="metrics.yaml", key="update_metrics_success")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_update_metrics_success(self, request):
        """更新 metrics 成功"""
        resp = MetricsManagement().add_update_metrics(request["json_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        json_data = request["json_data"]
        assert all(db_res[0][i + 1] == json_data[key] for i, key in enumerate(
            ["status", "metricsName", "metricsType", "skuScope", "salesScope", "isGoalSet", "ownerUserId"]))
        metrics_id = db_res[0][0]
        sql = f'SELECT * FROM weee_merch.sp_metrics_viewer WHERE metrics_id = {metrics_id}'
        db_res_viewer = DBConnect().select_data_from_mysql(sql)
        if not json_data["viewerIds"]:
            assert not db_res_viewer
        else:
            assert all(json_data["viewerIds"][x] == db_res_viewer[x][2] for x in range(len(json_data["viewerIds"])))

    @weeeTest.params.file(file_name="metrics.yaml", key="update_metrics_fail")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_update_metrics_fail(self, request):
        """ 更新metrics失败 """
        db_res_before = DBConnect().select_data_from_mysql(request["sql"])
        resp = MetricsManagement().add_update_metrics(request["json_data"])
        assert resp["result"] is False
        assert resp["message"] == request["message"]
        db_res_after = DBConnect().select_data_from_mysql(request["sql"])
        assert db_res_before == db_res_after

    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_update_metrics_status(self):
        """ 更新metrics状态active/inactive """
        resp = MetricsManagement().update_metrics_status(metrics_id="169", status="I")
        assert resp["result"] is True
        sql = 'SELECT * FROM weee_merch.sp_metrics WHERE id =169'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == "I"
        active_resp = MetricsManagement().update_metrics_status(metrics_id="169", status="A")
        assert active_resp["result"] is True
        sql = 'SELECT * FROM weee_merch.sp_metrics WHERE id =169'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][1] == "A"

    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_delete_metrics(self):
        """ 删除metrics -- SQL清理数据 """
        sql = 'DELETE FROM weee_merch.sp_metrics WHERE title like "%01_test%"'
        db_res = DBConnect().update_data_from_mysql(sql)
        sql_after = 'SELECT * FROM weee_merch.sp_metrics WHERE title like "%01_test%"'
        db_res_after = DBConnect().select_data_from_mysql(sql_after)
        assert len(db_res_after) == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
