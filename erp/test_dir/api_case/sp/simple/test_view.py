import weeeTest
from erp.test_dir.api.sp.view import ViewManagement
from erp.test_dir.db_utils import DBConnect


class TestView(weeeTest.TestCase):
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_tree(self):
        """查询获取org view的组织tree """
        resp = ViewManagement().get_org_view_tree()
        assert resp["result"] is True
        assert resp["object"][0]["key"] == 23907
        assert resp["object"][0]["disabled"] is False
        assert resp["object"][1]["key"] == 7339926
        assert resp["object"][1]["disabled"] is True
        sql = "SELECT * FROM weee_merch.sp_merch_org_employee_planning WHERE planning_id =58 and user_id =23907"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][6] == 'Julie'

    @weeeTest.params.file(file_name="sp_view.yaml", key="get_view_plan_list")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_get_org_view_plan_list(self, request):
        """查询获取用户在org view可查看的plan list """
        resp = ViewManagement().get_org_view_plan_list(request["params_data"])
        assert resp["result"] is True
        plan_id = request["params_data"]["planningId"]
        user_id = request["params_data"]["viewUserId"]
        sql_1 = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE owner_user_id={user_id} and planning_id={plan_id} order by metrics_type asc, id desc"
        db_res = DBConnect().select_data_from_mysql(sql_1)
        assert len(resp["object"]["metricsList"]) == len(db_res)
        for x in range(len(db_res)):
            assert resp["object"]["metricsList"][x]["name"] == db_res[x][5]
            assert resp["object"]["metricsList"][x]["planningMetricsId"] == db_res[x][0]
            assert resp["object"]["metricsList"][x]["type"] == db_res[x][3]
            assert resp["object"]["metricsList"][x]["goal"] == db_res[x][9]
        sql_2 = (f"SELECT DISTINCT(x.planning_id), y.plan_name FROM weee_merch.sp_planning_metrics x join "
                 f"weee_merch.sp_planning y on x.planning_id = y.id WHERE owner_user_id = {user_id} and y.status!='D' "
                 f"order by x.planning_id desc")
        db_res_2 = DBConnect().select_data_from_mysql(sql_2)
        assert len(resp["object"]["planningList"]) == len(db_res_2)
        for x in range(len(db_res_2)):
            assert resp["object"]["planningList"][x]["value"] == str(db_res_2[x][0])
            assert resp["object"]["planningList"][x]["label"] == db_res_2[x][1]
        sql_3 = f"SELECT * FROM weee_merch.sp_planning WHERE id={plan_id}"
        db_res_3 = DBConnect().select_data_from_mysql(sql_3)
        assert resp["object"]["planningName"] == db_res_3[0][1]
        assert resp["object"]["status"] == db_res_3[0][2]
        assert str(db_res_3[0][3]) in resp["object"]["startDate"]
        assert str(db_res_3[0][4]) in resp["object"]["endDate"]

    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_config(self):
        """查询获取org view的配置信息 """
        resp = ViewManagement().get_org_view_config()
        assert resp["result"] is True
        assert resp["object"]["chartConfig"][0]["legend"]["data"]["default"] == ["Goal", "Begin Forecast Snapshot",
                                                                                 "Adjusted Forecast Daily", "Actual",
                                                                                 "Auxiliary"]
        assert resp["object"]["chartConfig"][1]["legend"]["data"]["1"] == ["Chinese", "Japanese", "Korean", "Vietnam",
                                                                           "Mainstream", "Filipino", "Indian"
                                                                           ]
        assert resp["object"]["chartConfig"][1]["legend"]["data"]["2"] == ["Tier 0", "Tier 1", "Tier 2", "Tier 99"]

    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_options(self):
        """查询获取org view的下拉选项 """
        resp = ViewManagement().get_org_view_options()
        assert resp["result"] is True
        sql = ("SELECT * FROM weee_merch.sp_metrics_series_options WHERE planning_id =58 and planning_metrics_id =618 "
               "order by id asc")
        db_res = DBConnect().select_data_from_mysql(sql)
        for i in range(3):
            assert db_res[i][4] == resp["object"]["salesOrgId"][i]["value"]
        assert db_res[3][4] == resp["object"]["category"][1]["value"]
        assert db_res[7][4] == resp["object"]["category"][1]["options"][0]["value"]
        assert db_res[8][4] == resp["object"]["category"][1]["options"][1]["value"]
        assert db_res[4][4] == resp["object"]["category"][2]["value"]
        assert db_res[5][4] == resp["object"]["category"][3]["value"]

    @weeeTest.params.file(file_name="sp_view.yaml", key="update_review_content")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_update_org_view_plan_review(self, request):
        """查询更新用户在org view的review内容 """
        resp = ViewManagement().update_review_content(request["json_data"], request["data"])
        assert resp["result"] is True
        plan_id = request["data"]
        sql = f"SELECT * FROM weee_merch.sp_planning_review WHERE planning_id={plan_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["comment"] == db_res[0][3]
        assert resp["object"]["userId"] == db_res[0][2]

    @weeeTest.params.file(file_name="sp_view.yaml", key="get_chart_data")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_chart_data(self, request):
        """查询获取chart图表数据"""
        resp = ViewManagement().get_org_view_chart_data(request["data"], request["params_data"])
        assert resp["result"] is True
        plan_id = request["data"][2:4]
        metrics_id = request["data"][5:]
        sql = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id={plan_id} and id={metrics_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        metrics_type = db_res[0][3]
        if metrics_type == 1:
            sql_actual = f"SELECT SUM(revenue) FROM weee_merch.sp_metrics_series_revenue WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`>='2024-06-15' and `date`<='2024-06-19'"
            db_res_actual = DBConnect().select_data_from_mysql(sql_actual)
            assert resp["object"]["seriesData"]["revenueActual"][4] == db_res_actual[0][0]
            assert resp["object"]["seriesData"]["revenueForecast"][4] == resp["object"]["seriesData"]["revenueActual"][4]
            sql_goal = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id={plan_id} and id={metrics_id}"
            db_res_goal = DBConnect().select_data_from_mysql(sql_goal)
            assert resp["object"]["seriesData"]["revenueGoal"][4] == db_res_goal[0][9]
            assert resp["object"]["seriesData"]["revenueAuxiliary"][4] == round((int(resp["object"]["seriesData"]["revenueGoal"][4]) / 11 * 5),2)
            sql_plan = f"SELECT SUM(revenue) FROM weee_merch.sp_metrics_series_revenue WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=3 and sales_org_id=0 and category='all' and sub_category='all' and `date`>='2024-06-15' and `date`<='2024-06-17'"
            db_res_plan = DBConnect().select_data_from_mysql(sql_plan)
            assert resp["object"]["seriesData"]["revenuePlan"][2] == db_res_plan[0][0]
        elif metrics_type == 2:
            sql_goal = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id={plan_id} and id={metrics_id}"
            db_res_goal = DBConnect().select_data_from_mysql(sql_goal)
            assert int(resp["object"]["seriesData"]["marginGoal"][1]) == db_res_goal[0][9]
            sql_actual = f"SELECT (SUM(revenue)-SUM(cogs))/SUM(revenue) FROM weee_merch.sp_metrics_series_margin WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`='2024-06-15'"
            db_res_actual = DBConnect().select_data_from_mysql(sql_actual)
            number = db_res_actual[0][0]
            assert str(resp["object"]["seriesData"]["marginActual"][0]) == "{:.2f}%".format(number * 100)[:-1]
        else:
            sql_goal = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id={plan_id} and id={metrics_id}"
            db_res_goal = DBConnect().select_data_from_mysql(sql_goal)
            assert int(resp["object"]["seriesData"]["availablityGoal"][1]) == db_res_goal[0][9]
            sql_actual = f"SELECT COALESCE(ROUND((SUM(cogs)) / NULLIF(SUM(revenue), 0) * 100, 2), 0) FROM weee_merch.sp_metrics_series_availability WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`='2024-06-16'"
            db_res_actual = DBConnect().select_data_from_mysql(sql_actual)
            assert resp["object"]["seriesData"]["availablityActual"][1] == db_res_actual[0][0]

    @weeeTest.params.file(file_name="sp_view.yaml", key="get_bar_ethnicity_data")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_bar_ethnicity_data(self, request):
        """查询获取bar ethnicity 图表数据"""
        resp = ViewManagement().get_org_view_chart_data(request["data"], request["params_data"])
        assert resp["result"] is True
        plan_id = request["data"][2:4]
        metrics_id = request["data"][5:]
        sql = f"SELECT SUM(CASE WHEN customer_ethnicity = 'Chinese' THEN revenue ELSE 0 END) AS chinese,SUM(CASE WHEN customer_ethnicity = 'Japanese' THEN revenue ELSE 0 END) AS japanese,SUM(CASE WHEN customer_ethnicity = 'Korean' THEN revenue ELSE 0 END) AS korean,SUM(CASE WHEN customer_ethnicity = 'Vietnamese' THEN revenue ELSE 0 END) AS vietnam,SUM(CASE WHEN customer_ethnicity = 'Other American' OR customer_ethnicity = 'Mainstream' THEN revenue ELSE 0 END) AS mainstream,SUM(CASE WHEN customer_ethnicity = 'Filipino' THEN revenue ELSE 0 END) AS filipino,SUM(CASE WHEN customer_ethnicity = 'Indian' THEN revenue ELSE 0 END) AS indian FROM weee_merch.sp_metrics_series_revenue WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`='2024-06-16' group by date"
        db_res = DBConnect().select_data_from_mysql(sql)
        keys = [
            "revenueChinese",
            "revenueJapanese",
            "revenueKorean",
            "revenueVietnam",
            "revenueMainstream",
            "revenueFilipino",
            "revenueIndian"
        ]
        for i, key in enumerate(keys):
            assert resp["object"]["seriesData"][key][1] == db_res[0][i]

    @weeeTest.params.file(file_name="sp_view.yaml", key="get_bar_tier_data")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_org_view_bar_tier_data(self, request):
        """查询获取bar tier 图表数据"""
        resp = ViewManagement().get_org_view_chart_data(request["data"], request["params_data"])
        assert resp["result"] is True
        plan_id = request["data"][2:4]
        metrics_id = request["data"][5:]
        sql = f"SELECT tier, SUM(revenue) FROM weee_merch.sp_metrics_series_revenue WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`='2024-06-15' group by tier"
        db_res = DBConnect().select_data_from_mysql(sql)
        keys = [
            "revenueTier0",
            "revenueTier1",
            "revenueTier2",
            "revenueTier99"
        ]
        for i, key in enumerate(keys):
            assert resp["object"]["seriesData"][key][0] == db_res[i][1]
        sql_1 = f"SELECT tier, SUM(revenue) FROM weee_merch.sp_metrics_series_revenue WHERE planning_id={plan_id} and planning_metrics_id={metrics_id} and `type`=1 and sales_org_id=0 and category='all' and sub_category='all' and `date`='2024-06-16' group by tier"
        db_res_1 = DBConnect().select_data_from_mysql(sql_1)
        for i in range(len(db_res_1)):
            test = keys[i]
            print(resp["object"]["seriesData"][test][1])
            assert db_res_1[i][1] == resp["object"]["seriesData"][test][1]

    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_my_view_share_data(self):
        """查询my view下面的share数据"""
        resp = ViewManagement().get_my_view_share_report_data('share')
        assert resp["result"] is True
        for i in range(len(resp["object"])):
            metrics_id = resp["object"][i]["planningMetricsId"]
            metrics_type = resp["object"][i]["metricsType"]
            sql_owner = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id=58 and id={metrics_id}"
            db_res_owner = DBConnect().select_data_from_mysql(sql_owner)
            assert resp["object"][i]["ownerUserId"] == db_res_owner[0][8]
            if metrics_type == 1:
                sql_current = f"SELECT SUM(revenue) FROM weee_merch.sp_metrics_series_revenue WHERE planning_id=58 and planning_metrics_id={metrics_id} and type=1 and sales_org_id=0 and category='all' and sub_category='all' and date>='2024-06-15' and date<='2024-06-25'"
                db_res_current = DBConnect().select_data_from_mysql(sql_current)
                assert int(resp["object"][i]["currentProgress"]) == int(db_res_current[0][0])
            else:
                sql = f"SELECT round((sum(revenue)-sum(cogs))/sum(revenue)*100,2) FROM weee_merch.sp_metrics_series_margin WHERE planning_id=58 and planning_metrics_id={metrics_id} and type=1 and sales_org_id=0 and category='all' and sub_category='all' and date>='2024-06-15' and date<='2024-06-25'"
                db_res = DBConnect().select_data_from_mysql(sql)
                assert resp["object"][i]["currentProgress"] == db_res[0][0]

    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_get_my_view_report_data(self):
        """查询my view下面的report数据"""
        resp = ViewManagement().get_my_view_share_report_data('report')
        assert resp["result"] is True
        for i in range(len(resp["object"])):
            user_id = resp["object"][i]["ownerUserId"]
            sql_owner = f"SELECT * FROM weee_merch.sp_merch_org_employee_planning WHERE planning_id=58 and user_id={user_id}"
            db_res_owner = DBConnect().select_data_from_mysql(sql_owner)
            assert db_res_owner[0][4] == 10937481


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
