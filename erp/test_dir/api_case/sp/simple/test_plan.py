import weeeTest
from erp.test_dir.api.sp.plan import PlanManagement
import datetime
from erp.test_dir.db_utils import DBConnect


class TestPlan(weeeTest.TestCase):
    @weeeTest.params.file(file_name="plan.yaml", key="get_plan_list")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_get_plan_list(self, request):
        """查询获取 plan list """
        resp = PlanManagement().get_plan_list(request["params_data"])
        assert resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        if len(db_res) != 0:
            for x in range(len(resp["object"]["data"])):
                assert resp["object"]["data"][x]["id"] in db_res[x]
                assert resp["object"]["data"][x]["planName"] in db_res[x]
        assert len(db_res) == len(resp["object"]["data"])

    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def verify_single_plan_detail(self, plan_id):
        """查询获取单个 plan 详情"""
        resp = PlanManagement().get_single_plan_detail(plan_id=plan_id)
        assert resp["result"] is True
        sql = f"SELECT * FROM weee_merch.sp_planning WHERE id ={plan_id}"
        print(sql)
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["planName"] == db_res[0][1]
        assert resp["object"]["status"] == db_res[0][2]
        assert str(db_res[0][3]) in resp["object"]["startDate"]
        assert str(db_res[0][4]) in resp["object"]["endDate"]
        sql_2 = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id ={plan_id} order by id asc"
        print(sql_2)
        db_res_2 = DBConnect().select_data_from_mysql(sql_2)
        assert len(db_res_2) == len(resp["object"]["metrics"])
        for x in range(len(db_res_2)):
            assert resp["object"]["metrics"][x]["id"] in db_res_2[x]
            assert resp["object"]["metrics"][x]["metricsId"] in db_res_2[x]
            assert resp["object"]["metrics"][x]["metricsName"] in db_res_2[x]

    @weeeTest.params.file(file_name="plan.yaml", key="add_plan_success")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_add_plan_success(self, request):
        """新增 plan 成功"""
        resp = PlanManagement().import_plan_metrics()
        assert resp["result"] is True
        metrics_list = resp["object"]
        add_resp = PlanManagement().add_update_plan(request["json_data"], metrics_list=metrics_list)
        assert add_resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert request["json_data"]["planName"] == db_res[0][1]
        assert str(db_res[0][3]) == request["json_data"]["startDate"]
        assert str(db_res[0][4]) == request["json_data"]["endDate"]
        assert db_res[0][2] == "I"
        planning_id = db_res[0][0]
        sql_1 = f"SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id = {planning_id} ORDER BY id ASC"
        db_res_2 = DBConnect().select_data_from_mysql(sql_1)
        assert len(db_res_2) == len(resp["object"])
        for x in range(len(db_res_2)):
            assert resp["object"][x]["metricsId"] in db_res_2[x]
            assert resp["object"][x]["metricsName"] in db_res_2[x]
        self.verify_single_plan_detail(plan_id=str(planning_id))

    @weeeTest.params.file(file_name="plan.yaml", key="add_plan_fail")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_add_plan_fail(self, request):
        """新增 plan 失败"""
        metrics_list = []
        add_resp = PlanManagement().add_update_plan(request["json_data"], metrics_list=metrics_list)
        assert add_resp["result"] is False
        assert add_resp["message"] == 'Metrics cannot be empty. Please import first.'
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert len(db_res) == 0

    @weeeTest.params.file(file_name="plan.yaml", key="update_plan_name_date_success")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_update_plan_name_date_success(self, request):
        """更新 plan name和date 成功"""
        add_resp = PlanManagement().add_update_plan(request["json_data"], metrics_list=request["json_data"]["metrics"])
        assert add_resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert request["json_data"]["planName"] == db_res[0][1]
        assert db_res[0][2] == "I"
        assert str(db_res[0][3]) == request["json_data"]["startDate"]
        assert str(db_res[0][4]) == request["json_data"]["endDate"]
        self.verify_single_plan_detail(plan_id=request["json_data"]["id"])

    @weeeTest.params.file(file_name="plan.yaml", key="update_plan_goal_amount_success")
    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_update_plan_goal_amount_success(self, request):
        """更新 plan goal amount 成功"""
        add_resp = PlanManagement().add_update_plan(request["json_data"], metrics_list=request["json_data"]["metrics"])
        assert add_resp["result"] is True
        db_res = DBConnect().select_data_from_mysql(request["sql"])
        for x in range(len(db_res)):
            assert db_res[x][9] == request["json_data"]["metrics"][x]["goal"]
        self.verify_single_plan_detail(plan_id=request["json_data"]["id"])

    @weeeTest.params.file(file_name="plan.yaml", key="update_plan_fail")
    @weeeTest.mark.list('SRM', 'SP', 'Smoke')
    def test_update_plan_fail(self, request):
        """ 更新plan失败 """
        before_db_res = DBConnect().select_data_from_mysql(request["sql"])
        resp = PlanManagement().add_update_plan(request["json_data"], metrics_list=request["json_data"]["metrics"])
        assert resp["result"] is False
        after_db_res = DBConnect().select_data_from_mysql(request["sql"])
        assert before_db_res == after_db_res

    @weeeTest.mark.list('Regression', 'SRM', 'SP', 'Smoke')
    def test_delete_plan(self):
        """ 删除plan -- SQL清理数据 """
        sql = 'DELETE FROM weee_merch.sp_planning WHERE plan_name="01_test_plan"'
        DBConnect().update_data_from_mysql(sql)
        sql_after = 'SELECT * FROM weee_merch.sp_planning WHERE plan_name="01_test_plan"'
        db_res_after = DBConnect().select_data_from_mysql(sql_after)
        assert len(db_res_after) == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
