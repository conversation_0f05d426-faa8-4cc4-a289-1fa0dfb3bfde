
import weeeTest
from erp.test_dir.api.desk.rma.rma_return import Rma<PERSON>eturn
from erp.test_dir.db_utils import DBConnect


class TestRmaReturn(weeeTest.TestCase):

    @weeeTest.params.file(file_name="rma_returns.yaml", key="select_return_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'Transfer', 'CS', )
    def test_rma_return(self, name, description, request, expected_result):
        """退款列表查询"""
        resp = RmaReturn().select_rma_return_list(request["json_data"])
        assert resp["result"] is True
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["object"]["data"][0]["userId"] == 7660033
        assert resp["object"]["data"][0]["orderId"] == 42602036
        assert resp["object"]["data"][0]["roId"] == "693"
        assert resp["object"]["pages"] == 1
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 20
        assert resp["object"]["total"] == 6
        sql = fr"SELECT * FROM `weee_comm`.`gb_return_order_line` WHERE `return_order_id` = '693' ORDER BY `id` DESC LIMIT 0,100"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert len(db_res) == len(resp["object"]["data"])
        id_in_db = [item[0] for item in db_res]
        for x in range(len(resp["object"]["data"])):
            assert resp["object"]["data"][x]["id"] in id_in_db




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')







