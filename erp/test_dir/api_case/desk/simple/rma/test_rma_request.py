
import weeeTest
from erp.test_dir.api.desk.rma.rma_request import RmaRequest


class TestRmaRequest(weeeTest.TestCase):

    @weeeTest.params.file(file_name="rma_requests.yaml", key="select_request_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_rma_request(self, name, description, request, expected_result):
        """request列表查询"""
        resp = RmaRequest().select_rma_request_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 20
        assert resp["object"]["pages"] == 5

    @weeeTest.params.file(file_name="rma_requests.yaml", key="create_request_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_rma_request(self, name, description, request, expected_result):
        """创建request"""
        resp = RmaRequest().create_rma_request(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="rma_requests.yaml", key="rma_request_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_rma_request_detail(self, name, description, request, expected_result):
        """request详情查询"""
        resp = RmaRequest().rma_request_detail(request["params_date"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["id"] == 1126
        assert resp["object"]["requestType"] == 1
        assert resp["object"]["requestTypeStr"] == "Refund Only"
        assert resp["object"]["orderStatus"] == "Finished"
        assert resp["object"]["status"] == "Completed"
        assert resp["object"]["orderId"] == 37706323
        assert resp["object"]["category"] == "Product quality"
        assert resp["object"]["totalAmount"] == 5.85

    @weeeTest.params.file(file_name="rma_requests.yaml", key="void_rma_request")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_void_rma_request(self, name, description, request, expected_result):
        """作废request"""
        resp = RmaRequest().void_rma_request(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')











