
import weeeTest
from erp.test_dir.api.desk.rma.rma_refund import RmaRefund


class TestRmaRefund(weeeTest.TestCase):

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="select_refund_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_rma_refund(self, name, description, request, expected_result):
        """退货列表查询"""
        resp = RmaRefund().select_rma_refund_list(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="create_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_rma_refund(self, name, description, request, expected_result):
        """创建退款申请"""
        resp = RmaRefund().create_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="edit_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_edit_rma_refund(self, name, description, request, expected_result):
        """编辑退款申请"""
        resp = RmaRefund().edit_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="approve_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_approve_rma_refund(self, name, description, request, expected_result):
        """approve退款申请"""
        resp = RmaRefund().action_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="batch_approve_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_batch_approve_rma_refund(self, name, description, request, expected_result):
        """批量approve退款申请"""
        resp = RmaRefund().batch_approve_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="decline_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_decline_rma_refund(self, name, description, request, expected_result):
        """decline退款申请"""
        resp = RmaRefund().action_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="delete_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_delete_rma_refund(self, name, description, request, expected_result):
        """delete退款申请"""
        resp = RmaRefund().action_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="modify_rma_refund")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_modify_rma_refund(self, name, description, request, expected_result):
        """modify退款申请"""
        resp = RmaRefund().action_rma_refund(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="rma_refund_log")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_rma_refund_log(self, name, description, request, expected_result):
        """退款申请log记录"""
        resp = RmaRefund().select_rma_refund_log(request["params_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    @weeeTest.params.file(file_name="rma_refunds.yaml", key="rma_refund_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_rma_refund_detail(self, name, description, request, expected_result):
        """退款申请详情查询"""
        resp = RmaRefund().select_rma_refund_detail(request["params_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')



















