import weeeTest
from erp.test_dir.api.desk.qctask.task_management import TaskManagement


class TestTaskManagement(weeeTest.TestCase):

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_page")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_task_page(self, name, description, request, expected_result):
        """【102621】qc task list"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_page(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["pages"] == 2

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_assign_agent")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_task_assign_agent(self, name, description, request, expected_result):
        """【102621】qc task assign agent"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_assign_agent(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_add_tag")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_qc_task_add_tag(self, name, description, request, expected_result):
        """【102621】qc task add tag"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_add_tag(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_remove_tag")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_qc_task_add_tag(self, name, description, request, expected_result):
        """【102621】qc task remove tag"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_remove_tag(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_score_all_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_score_all_view(self, name, description, request, expected_result):
        """【102622】qc score all view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_score_all_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_score_case_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_score_case_view(self, name, description, request, expected_result):
        """【102622】qc score case view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_score_case_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_score_call_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_score_call_view(self, name, description, request, expected_result):
        """【102622】qc score call view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_score_call_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_score_my_score_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_score_call_view(self, name, description, request, expected_result):
        """【102622】qc score my score view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_score_my_score_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_new_case_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_task_new_case_view(self, name, description, request, expected_result):
        """【102677】my qc task new case data view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_new_case_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_new_call_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_task_new_call_view(self, name, description, request, expected_result):
        """【102677】my qc task new call data view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_new_call_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="task_management.yaml", key="qc_task_complete_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_qc_task_complete_view(self, name, description, request, expected_result):
        """【102677】my qc task complete data view"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = TaskManagement().qc_task_complete_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')