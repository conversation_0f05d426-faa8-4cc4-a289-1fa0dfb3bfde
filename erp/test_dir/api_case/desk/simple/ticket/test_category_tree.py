import weeeTest
from erp.test_dir.api.desk.tickets.category_tree import DeskCategoryTree


class TestCategoryTree(weeeTest.TestCase):

    @weeeTest.params.file(file_name="desk_category_tree.yaml", key="ticket_category_tree")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_category_tree_info(self, name, description, request, expected_result):
        """获取ticket的category_tree"""
        print(description)
        resp = DeskCategoryTree().ticket_category_tree_info()
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="desk_category_tree.yaml", key="ticket_category_tree_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_category_tree_detail(self, name, description, request, expected_result):
        """获取具体的category的详情"""
        print(description)
        resp = DeskCategoryTree().ticket_category_tree_detail(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    #@weeeTest.params.file(file_name="desk_category_tree.yaml", key="ticket_add_category_tree")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_add_category_tree(self, name, description, request, expected_result):
        """新增 category tree"""
        print(description)
        resp = DeskCategoryTree().ticket_add_category_tree(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="desk_category_tree.yaml", key="ticket_rename_category_tree")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_rename_category_tree(self, name, description, request, expected_result):
        """重命名category tree"""
        print(description)
        resp = DeskCategoryTree().ticket_rename_category_tree(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="desk_category_tree.yaml", key="ticket_delete_category_tree")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_delete_category_tree(self, name, description, request, expected_result):
        """删除category tree"""
        print(description)
        resp = DeskCategoryTree().ticket_delete_category_tree(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')


