
import weeeTest
from erp.test_dir.api.desk.tickets.customer_service_ticket import CustomerServiceTickets


class TestTicket(weeeTest.TestCase):

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_menu")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_menu(self, name, description, request, expected_result):
        """ticket菜单"""
        print(description)
        resp = CustomerServiceTickets().ticket_menu_list()
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_list(self, name, description, request, expected_result):
        """ticket列表"""
        print(description)
        resp = CustomerServiceTickets().ticket_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    #@weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_validate")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_validate_ticket(self, name, description, request, expected_result):
        """ticket的新增"""
        print(description)
        resp = CustomerServiceTickets().validate_ticket(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_detail(self, name, description, request, expected_result):
        """ticket的详情"""
        print(description)
        resp = CustomerServiceTickets().ticket_detail(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_follow")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_follow(self, name, description, request, expected_result):
        """对ticket做follow和unfollow"""
        print(description)
        resp = CustomerServiceTickets().ticket_follow(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_flow_operation")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_flow_operation(self, name, description, request, expected_result):
        """ticket流程节点"""
        print(description)
        resp = CustomerServiceTickets().ticket_flow_operation(request["params_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_add_chat_interaction")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_add_chat_interaction(self, name, description, request, expected_result):
        """ticket 新增信息回复"""
        print(description)
        resp = CustomerServiceTickets().add_ticket_chat_interaction(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["success"] == expected_result["success"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_chat_log")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_chat_log(self, name, description, request, expected_result):
        """ticket chat_log记录"""
        print(description)
        resp = CustomerServiceTickets().ticket_chat_log(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["success"] == expected_result["success"]

    @weeeTest.params.file(file_name="desk_ticket.yaml", key="ticket_submit_task")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_submit_task(self, name, description, request, expected_result):
        """submit_task"""
        print(description)
        resp = CustomerServiceTickets().ticket_submit_task(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')



















