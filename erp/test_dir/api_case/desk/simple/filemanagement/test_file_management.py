import weeeTest
from erp.test_dir.api.desk.filemanagement.file_management import DeskFileManagement

class TestDeskFileManagement(weeeTest.TestCase):
    @weeeTest.params.file(file_name="file_management.yaml", key="owned_by_me")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_owned_by_me(self, name, description, request, expected_result):
        """【102412】owned by me"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().owned_by_me(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

    @weeeTest.params.file(file_name="file_management.yaml", key="shared_with_me")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_shared_with_me(self, name, description, request, expected_result):
        """【102412】shared with_me"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().shared_with_me(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

    @weeeTest.params.file(file_name="file_management.yaml", key="recently_viewed")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_recently_viewed(self, name, description, request, expected_result):
        """【102412】recently viewed"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().recently_viewed(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

    @weeeTest.params.file(file_name="file_management.yaml", key="following_viewed")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_following_viewed(self, name, description, request, expected_result):
        """【102412】following viewed"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().following_viewed(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

    @weeeTest.params.file(file_name="file_management.yaml", key="cs_viewed")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cs_viewed(self, name, description, request, expected_result):
        """cs viewed"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().cs_viewed(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

    @weeeTest.params.file(file_name="file_management.yaml", key="seller_viewed")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_seller_viewed(self, name, description, request, expected_result):
        """seller viewed"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskFileManagement().seller_viewed(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["pageNum"] == 1
        assert resp["object"]["pageSize"] == 50

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')


