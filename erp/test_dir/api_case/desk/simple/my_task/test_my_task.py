import weeeTest
from erp.test_dir.api.desk.mytask.my_task import DeskMyTask


class TestDeskMyTask(weeeTest.TestCase):
    @weeeTest.params.file(file_name="my_task.yaml", key="my_task_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_my_task(self, name, description, request, expected_result):
        """页面查询my task 视图"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskMyTask().my_task_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["name"] == "Recently Viewed"
        assert resp["object"][0]["count"] == 200


class TestDeskMyTask(weeeTest.TestCase):
    @weeeTest.params.file(file_name="my_task.yaml", key="my_task_recently")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_my_task(self, name, description, request, expected_result):
        """【102269】页面查询my task recently list"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskMyTask().my_task_recently(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["key"] == "2024090900007"
        assert resp["object"]["data"][0]["subject"] == "test catman 流程"
        assert resp["object"]["data"][0]["type"] == "ticket"


class TestDeskMyTask(weeeTest.TestCase):
    @weeeTest.params.file(file_name="my_task.yaml", key="my_task_ticket_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_my_task(self, name, description, request, expected_result):
        """【102270】my task ticket detail"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskMyTask().my_task_ticket_detail(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["categoryName"] == "Catman - Packaging issue"
        assert resp["object"]["orderId"] == "42616032"
        assert resp["object"]["packingArea"] == "NJ - New York[2]"
        assert resp["object"]["deliveryDate"] == "2024-08-01"
        assert resp["object"]["productNameEn"] == "Yoshinoya Cooked Beef w/ Onion"
        assert resp["object"]["relateCases"][0]["caseNumber"] == "100043803"
        assert resp["object"]["sku"] == "2478"
        assert resp["object"]["statusStr"] == "Pending"
        assert resp["object"]["subject"] == "test catman 流程"
        assert resp["object"]["typeStr"] == "Co-operate"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')