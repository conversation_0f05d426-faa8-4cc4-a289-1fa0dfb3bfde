
import weeeTest
from erp.test_dir.api.desk.cases.ec_no_order_case import EcNoOrderCase


class TestNoOrderCase(weeeTest.TestCase):

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="get_user_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_user_info(self, name, description, request, expected_result):
        """获取用户详情"""
        print(description)
        resp = EcNoOrderCase().get_ec_user_info()
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="no_order_case_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_ec_no_order_case_list(self, name, description, request, expected_result):
        """获取no order case list"""
        print(description)
        resp = EcNoOrderCase().get_ec_no_order_case_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="no_order_case_category")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_case_category(self, name, description, request, expected_result):
        """获取用户二级分类树"""
        print(description)
        resp = EcNoOrderCase().get_ec_no_order_case_category(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="create_no_order_case")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_no_order_case(self, name, description, request, expected_result):
        """【102106】创建no order case"""
        print(description)
        resp = EcNoOrderCase().create_ec_no_order_case(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="no_order_case_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_no_order_case_detail(self, name, description, request, expected_result):
        """on order case详情"""
        print(description)
        resp = EcNoOrderCase().get_ec_no_order_case_detail(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="cancel_no_order_case")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_cancel_no_order_case(self, name, description, request, expected_result):
        """撤销on order case"""
        print(description)
        resp = EcNoOrderCase().cancel_ec_no_order_case(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="create_no_order_case_message")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_create_no_order_case_message(self, name, description, request, expected_result):
        """回复no order case消息"""
        print(description)
        resp = EcNoOrderCase().create_ec_no_order_case_message(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="ec_no_order_case.yaml", key="update_no_order_case_message_status")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_update_no_order_case_message_status(self, name, description, request, expected_result):
        """更新no order Case消息已读"""
        print(description)
        resp = EcNoOrderCase().update_ec_no_order_case_message_status(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')



























