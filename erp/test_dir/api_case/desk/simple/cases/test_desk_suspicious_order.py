import weeeTest
from erp.test_dir.api.desk.cases.desk_suspicious_order import DeskSuspiciousOrder
class TestDeskSuspiciousOrder(weeeTest.TestCase):
    @weeeTest.params.file(file_name="desk_suspicious_order.yaml", key="suspicious_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_suspicious_order(self, name, description, request, expected_result):
        """【102390】查询可疑订单review information"""
        print("执行接口名称：" + name, "接口描述：" + description)
        resp = DeskSuspiciousOrder().suspicious_order(request["data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"][0]["id"] == 30143
        assert resp["object"][0]["userId"] == 13347454
        assert resp["object"][0]["orderStatus"] == "N"
        assert resp["object"][0]["orderAmount"] == 530.92
        assert resp["object"][0]["paymentMethod"] == "CardBraintree"
        assert resp["object"][0]["orderId"] == 42634004

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
