import logging
import time

import weeeTest
from weeeTest import log
from erp.test_dir.api.desk.announcement.desk_announcement_create import DeskAnnouncementCreate
from erp.test_dir.db_utils import DBConnect


class TestDeskAnnouncementCreate(weeeTest.TestCase):
    id = ''

    @weeeTest.params.file(file_name="desk_announcement_create.yaml", key="desk_announcement_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_announcement_create(self, name, description, request, expected_result):
        """【102165】 desk_announcement_create接口"""
        print(description)
        resp = DeskAnnouncementCreate().desk_announcement_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_announcement_create.yaml", key="page_query_data_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_page_query_data_view(self, name, description, request, expected_result):
        """页面查询my announcement"""
        print(description)
        time.sleep(15)
        resp = DeskAnnouncementCreate().page_query_data_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["list"][0]["subject"] == "SF R1b R1a地区业务调整"
        assert resp["object"]["list"][0]["status"] == 1
        TestDeskAnnouncementCreate.id = resp["object"]["list"][0]["id"]

    @weeeTest.params.file(file_name="desk_announcement_create.yaml", key="desk_announcement_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_announcement_detail(self, name, description, request, expected_result):
        """【102383】announcement detail"""
        print(description)
        request["json_data"]["id"] = TestDeskAnnouncementCreate.id
        resp = DeskAnnouncementCreate().desk_announcement_detail(request["json_data"]["id"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["subject"] == "SF R1b R1a地区业务调整"
        assert resp["object"]["status"] == 1
        assert resp["object"]["recCreatorName"] == "xiao.ye"
        assert resp["object"]["recUpdateName"] == "xiao.ye"

    @weeeTest.params.file(file_name="desk_announcement_create.yaml", key="desk_announcement_edit")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_announcement_edit(self, name, description, request, expected_result):
        """【102378】 announcement edit"""
        print(description)
        request["json_data"]["id"] = TestDeskAnnouncementCreate.id
        resp = DeskAnnouncementCreate().desk_announcement_edit(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_announcement_create.yaml", key="page_query_data_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_page_query_data_view1(self, name, description, request, expected_result):
        """页面查询my announcement status和校验数据库desk_announcement status是否一致"""
        print(description)
        request["json_data"]["id"] = TestDeskAnnouncementCreate.id
        time.sleep(10)
        resp = DeskAnnouncementCreate().page_query_data_view(request["json_data"])
        announcement = [item for item in resp["object"]["list"] if item["id"] == TestDeskAnnouncementCreate.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["list"][0]["subject"] == "SF R1b R1a地区业务调整edit功能"
        assert resp["object"]["list"][0]["status"] == 1
        assert announcement[0][
                   "status"] == 1, f"announcement={announcement}, resp={resp},TestDeskAnnouncementCreate.id={TestDeskAnnouncementCreate.id}"
        sql = fr"SELECT status From `weee_cs`.`desk_announcement` WHERE id={TestDeskAnnouncementCreate.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == announcement[0]["status"] == 1


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
