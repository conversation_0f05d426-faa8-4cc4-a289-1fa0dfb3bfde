import time

import weeeTest
from erp.test_dir.api.desk.cases.desk_seller_support_case import DeskSellerSupportCase
from erp.test_dir.db_utils import DBConnect


class TestDeskSellerSupportCase(weeeTest.TestCase):
    case_Id = ''
    quickTextContent = ''
    caseNumber = ''

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="get_user_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_user_info(self, name, description, request, expected_result):
        """获取用户信息接口"""
        print(description)
        resp = DeskSellerSupportCase().get_user_info()
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["status"] == 1
        assert resp["object"]["type"] == 1
        assert resp["object"]["status"] == 1
        assert resp["object"]["type"] == 1
        assert 'userName' in resp['object'].keys()
        assert 'weeeUserId' in resp['object'].keys()

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="desk_seller_support_case_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_seller_support_case_create(self, name, description, request, expected_result):
        """【102105】desk_seller_support_case_create接口"""
        print(description)
        resp = DeskSellerSupportCase().desk_seller_support_case_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDeskSellerSupportCase.case_Id = resp["object"]["caseId"]
        TestDeskSellerSupportCase.caseNumber = resp["object"]["caseNumber"]

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="case_interaction_List")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_case_interaction_List(self, name, description, request, expected_result):
        """case_interaction_List接口"""
        print(description)
        request["json_data"]["caseId"] = TestDeskSellerSupportCase.case_Id
        resp = DeskSellerSupportCase().case_interaction_List(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="get_Quick_Text_Content")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_Quick_Text_Content(self, name, description, request, expected_result):
        """获取快速回复文本接口"""
        print(description)
        request["json_data"]["caseId"] = TestDeskSellerSupportCase.case_Id
        resp = DeskSellerSupportCase().get_Quick_Text_Content(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDeskSellerSupportCase.quickTextContent = resp["object"]["quickTextContent"]

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="send_EmailAndResolve")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_send_EmailAndResolve(self, name, description, request, expected_result):
        """回复客人并处理完case"""
        print(description)
        request["json_data"]["caseId"] = TestDeskSellerSupportCase.case_Id
        request["json_data"]["quickTextContent"] = TestDeskSellerSupportCase.quickTextContent
        resp = DeskSellerSupportCase().send_EmailAndResolve(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_seller_support_case.yaml", key="search_case_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_search_case_list(self, name, description, request, expected_result):
        """【102645】页面查询case list"""
        print(description)
        request["json_data"]["compositeInfo"] = TestDeskSellerSupportCase.caseNumber
        time.sleep(15)
        resp = DeskSellerSupportCase().get_search_case_list(request["json_data"])
        print(resp)
        assert resp["result"] is True
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        if resp["object"]["list"][0]['status'] == 6:
            DeskSellerSupportCase().get_search_case_list(request["json_data"])
        sql = f"SELECT status FROM `weee_cs`.`desk_case` WHERE id={TestDeskSellerSupportCase.case_Id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == 7
        resp_again = DeskSellerSupportCase().get_search_case_list(request["json_data"])
        assert resp_again["object"]["list"][0]['status'] == 7


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
