import time

import weeeTest
from weeeTest import log

from erp.test_dir.api.desk.cases.desk_phone_case import DeskPhoneCase
from erp.test_dir.db_utils import DBConnect

class TestDeskPhoneCase(weeeTest.TestCase):
    case_Id = ''
    id = ''

    @weeeTest.params.file(file_name="desk_phone_case.yaml", key="desk_call_record_create_case")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS', 'abc')
    def test_desk_call_record_create_case(self, name, description, request, expected_result):
        """【102107】desk创建phone case"""
        print(description)
        resp = DeskPhoneCase().desk_call_record_create_case(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDeskPhoneCase.case_Id = resp["object"]["caseId"]
        TestDeskPhoneCase.id = resp["object"]["caseId"]

    @weeeTest.params.file(file_name="desk_phone_case.yaml", key="add_Relate_Data")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS', 'abc')
    def test_add_Relate_Data(self, name, description, request, expected_result):
        """绑定相关订单"""
        print(description)
        request["json_data"]["caseId"] = TestDeskPhoneCase.case_Id
        resp = DeskPhoneCase().add_Relate_Data(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_phone_case.yaml", key="update_Description")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS', 'abc')
    def test_update_Description(self, name, description, request, expected_result):
        """更新描述和category"""
        print(description)
        request["json_data"]["caseId"] = TestDeskPhoneCase.case_Id
        resp = DeskPhoneCase().update_Description(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_phone_case.yaml", key="send_email_and_resolve")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS', 'abc')
    def test_send_email_and_resolve(self, name, description, request, expected_result):
        """发送邮件回复客人并解决case"""
        print(description)
        request["json_data"]["caseId"] = TestDeskPhoneCase.case_Id
        resp = DeskPhoneCase().send_email_and_resolve(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_phone_case.yaml", key="pageQuery_DataView")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS', 'abc')
    def test_pageQuery_DataView(self, name, description, request, expected_result):
        """【102265】页面查询case view all"""
        print(description)
        request["json_data"]["caseId"] = TestDeskPhoneCase.id
        time.sleep(15)
        resp = DeskPhoneCase().pageQuery_DataView(request["json_data"])
        case = [item for item in resp['object']['list'] if item['id'] == TestDeskPhoneCase.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["list"][0]["accountName"] == "********-qifang.ye"
        assert resp["object"]["list"][0]["assigneeName"] == "xiao.ye"
        assert resp["object"]["list"][0]["caseCategoryFirstName"] == "Product Related"
        assert case[0]['status'] == 7, f"case={case}, resp={resp},TestDeskPhoneCase.id={TestDeskPhoneCase.id}"
        sql = fr"SELECT status FROM `weee_cs`.`desk_case` WHERE id={TestDeskPhoneCase.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == case[0]['status'] == 7

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
