import time
import weeeTest

from erp.test_dir.api.cs.order.order_select import OrderSelect
from erp.test_dir.api.desk.rma.rma_mkpl_order_case_approve import RmaMkplOrderCaseApprove
from erp.test_dir.api_case import desk_header_huadan


class TestRmaMkplOrderCaseApprove(weeeTest.TestCase):
    case_id = ''
    id = ''
    request_id=''
    product = {}

    @weeeTest.params.file(file_name="rma_mkpl_order_case_approve.yaml", key="ec_mkpl_order_case_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ec_mkpl_order_case_create(self, name, description, request, expected_result):
        """ec_mkpl_order_case_create接口"""
        product_list_default = OrderSelect().order_product_list(data='42657790')
        order_product_list = product_list_default.get('object')
        assert order_product_list, f"no product in current order! order_product_list={order_product_list}"
        available_product_list = [item for item in order_product_list if item.get("returnFlag")]
        assert available_product_list, f"no refund product in current order, available_product_list={available_product_list}"
        req_data = request["json_data"]
        req_data["products"][0]['product_id'] = available_product_list[0].get("orderProductInfoDtoList")[0].get("product_id")
        req_data["products"][0]['order_product_id']=available_product_list[0].get("orderProductInfoDtoList")[0].get("order_product_id")
        req_data["products"][0]['amount']= available_product_list[0].get("orderProductInfoDtoList")[0].get("price")
        req_data["products"][0]['product_quantity']= available_product_list[0].get("orderProductInfoDtoList")[0].get("quantity")



        TestRmaMkplOrderCaseApprove.product=available_product_list[0].get("orderProductInfoDtoList")[0]
        print(description)
        resp = RmaMkplOrderCaseApprove().ec_mkpl_order_case_create(req_data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        TestRmaMkplOrderCaseApprove.case_id = resp["object"]["case_id"]

    @weeeTest.params.file(file_name="rma_mkpl_order_case_approve.yaml", key="get_Rma_Detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_Rma_Detail(self, name, description, request, expected_result):
        """获取rma detail"""
        print(description)
        TestRmaMkplOrderCaseApprove.id = TestRmaMkplOrderCaseApprove.case_id
        request["json_data"]["id"] = TestRmaMkplOrderCaseApprove.id
        resp = RmaMkplOrderCaseApprove().get_Rma_Detail({"req_id": TestRmaMkplOrderCaseApprove.id})
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        TestRmaMkplOrderCaseApprove.id = resp["object"]["id"]
        print(1)

    @weeeTest.params.file(file_name="rma_mkpl_order_case_approve.yaml", key="rma_Request_approve")
    @weeeTest.mark.list('Regression', 'Smoke', 'Transfer', 'CS')
    def test_rma_Request_approve(self, name, description, request, expected_result):
        """审核request"""
        # get order product_list
        time.sleep(30)
        request["json_data"]["id"] = TestRmaMkplOrderCaseApprove.id
        req_data = {
            "id": 8484,
            "deskCaseId": None,
            "requestType": 1,
            "requestTypeStr": "Refund Only",
            "requestTime": "2025-06-09T03:07:51.000+00:00",
            "confirmedTime": None,
            "orderStatus": "Shipped",
            "status": "New",
            "orderId": 42657790,
            "category": "Product quality",
            "salesOrg": None,
            "totalAmount": TestRmaMkplOrderCaseApprove.product.get("price"),
            "customerInfo": {
                "userId": 7616787,
                "username": "suspicious/地址",
                "phoneNum": "071***3698",
                "encryptPhoneNum": "M3ehaI9jr48kypJ8QD9bmcD/Q==",
                "address": "47467 Fremont Blvd, Fremont, California, 94538, United States",
                "comment": "test mkpl order case",
                "email": "<EMAIL>",
                "imageList": [
                    "https://img06.test.weeecdn.com/cs/image/766/684/4340F6700C77AADB.png"
                ],
                "tags": [],
                "userTag": [
                    {
                        "id": None,
                        "name": "Loyal customers",
                        "type": None,
                        "bind": None
                    },
                    {
                        "id": None,
                        "name": "High Value",
                        "type": None,
                        "bind": None
                    },
                    {
                        "id": 5,
                        "name": "WatchList",
                        "type": "Rule",
                        "bind": True
                    },
                    {
                        "id": 13,
                        "name": "CCPA",
                        "type": "Manual",
                        "bind": True
                    }
                ]
            },
            "steps": [
                "New",
                "Refunding",
                "Completed"
            ],
            "nextStep": "Confirm",
            "sellerId": 9740,
            "sellerName": "WOO CHINA",
            "isMarket": True,
            "deadline": 48,
            "paymentMode": "online",
            "userId": None,
            "username": None,
            "canEditAmount": True,
            "productList": [
                {
                    "id": None,
                    "status": None,
                    "requestId": "",
                    "oosStatus": None,
                    "rmaProductId": None,
                    "productId": TestRmaMkplOrderCaseApprove.product.get("product_id"),
                    "orderProductId": TestRmaMkplOrderCaseApprove.product.get("order_product_id"),
                    "productTitle": "[Nezha*2]  refrigerator sticker",
                    "requestQty": 1,
                    "requestAmount": TestRmaMkplOrderCaseApprove.product.get("price"),
                    "rmaRefundId": None,
                    "storageType": None,
                    "percentage": 100,
                    "refundPercentage": 100,
                    "returnReasonOption": None,
                    "maxRefundAmount":TestRmaMkplOrderCaseApprove.product.get("price"),
                    "tax": 0,
                    "unitPrice": 24,
                    "shippedQty": 1,
                    "refundQty": 1,
                    "refundProduct": 24,
                    "refundTax": 0,
                    "refundStatus": None,
                    "maximumQty": 1,
                    "returnType": None,
                    "internalReason": None,
                    "subTotal": TestRmaMkplOrderCaseApprove.product.get("price"),
                    "taxRate": 0,
                    "discount": None,
                    "refundAmount": TestRmaMkplOrderCaseApprove.product.get("price"),
                    "needReturn": "N",
                    "replaceProductId": None,
                    "replaceProductTitle": None,
                    "replaceQty": None,
                    "imageUrl": "https://img06.weeecdn.com/item/image/407/188/20E208337F6986C4.jpeg",
                    "replaceImageUrl": None,
                    "category": "Product quality",
                    "sellerSku": None,
                    "replaceSellerSku": None,
                    "receivedQty": None,
                    "refundedQty": None,
                    "replacedQty": None,
                    "canRefund": None,
                    "sellerId": None,
                    "sellerName": None,
                    "riskLevel": None,
                    "statusType": None,
                    "taxCanRefund": 0,
                    "crvOrderProductId": None,
                    "type": "normal",
                    "targetOrderProductId": None
                }
            ],
            "shipmentInfo": [
                {
                    "id": None,
                    "type": 1,
                    "carrierId": 13,
                    "trackingUrl": "https://track.amazon.com/tracking/3333333333",
                    "requestId": None,
                    "status": None,
                    "shipmentValue": "Original Order",
                    "carrier": "Amazon",
                    "trackingNum": "3333333333",
                    "pickupDate": None,
                    "carrierName": None,
                    "attachments": None
                }
            ],
            "operationLogInfo": [
                {
                    "id": 3012,
                    "type": "Create",
                    "operator": "dan.hua",
                    "operatorId": 7616787,
                    "operateTime": "2024-05-06T03:07:51.000+00:00",
                    "comment": "save request",
                    "rejectReason": None,
                    "operatorType": "Customer "
                }
            ],
            "nextAction": [
                {
                    "buttonValue": "Confirm",
                    "isPrimary": True
                },
                {
                    "buttonValue": "Reject",
                    "isPrimary": False
                }
            ],
            "selectedKeys": None,
            "comment": "test mkpl order case",
            "orderType": "S-normal-0",
            "subOrderType": "seller_mail",
            "deliveryMode": "shipping",
            "relatedOrders": [
                {
                    "id": None,
                    "type": "Case#",
                    "linkUrl": "/cases/case-profile/25670/100025670",
                    "createTime": "2024-04-30T03:07:52.000+00:00",
                    "updateTime": "2024-04-30T03:07:51.000+00:00",
                    "status": "Unassigned",
                    "operatorId": None,
                    "operatorName": None,
                    "approveId": None,
                    "approveName": None
                }
            ],
            "isOnDemandOrder": False,
            "returnMethod": None,
            "canEditRefundMethod": True,
            "canEditReturnMethod": False,
            "pickupDate": None,
            "carrierId": None,
            "trackingNumber": None,
            "returnAddress": None,
            "refundReason": None,
            "refundReasonOption": None,
            "source": "desk",
            "autoRefund": False
        }

        req_data['id'] = TestRmaMkplOrderCaseApprove.id
        resp = RmaMkplOrderCaseApprove().rma_Request_approve(data=req_data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True

    @weeeTest.params.file(file_name="rma_mkpl_order_case_approve.yaml", key="ec_cs_case_cancel_order")
    @weeeTest.mark.list('Regression', 'Smoke', 'Transfer', 'CS')
    def test_ec_cs_case_cancel_order(self, name, description, request, expected_result):
        """ec取消mkpl order"""
        print(description)
        time.sleep(30)
        request["json_data"]["case_id"] = str(TestRmaMkplOrderCaseApprove.case_id)
        resp = RmaMkplOrderCaseApprove().ec_cs_case_cancel_order_new(data=request["json_data"], header=desk_header_huadan)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
