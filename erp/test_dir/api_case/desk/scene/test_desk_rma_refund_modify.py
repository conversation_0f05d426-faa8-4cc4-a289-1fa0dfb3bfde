import datetime
import time
import weeeTest
from erp.test_dir.api.desk.rma.desk_rma_refund_modify import DeskRmaRefundModify
from erp.test_dir.db_utils import DBConnect

class TestDeskRmaRefundModify(weeeTest.TestCase):
    productId = ''
    orderProductId = ''
    product = None
    order_id = 42643909
    id = ''
    request_id = ''
    """DESK发起售后申请modify场景"""
    @weeeTest.params.file(file_name="desk_rma_refund_modify.yaml", key="desk_get_valid_product_info")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_get_valid_product_info(self, name, description, request, expected_result):
        """获取有效产品信息"""
        print(description)
        resp = DeskRmaRefundModify().desk_get_valid_product_info(data=request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        valid_product_list = [i for i in resp["object"]["productList"] if i["statusType"] == 1]
        if not valid_product_list:
            weeeTest.mark.skip(reason='当前没有sku可退')
        TestDeskRmaRefundModify.productId = valid_product_list[0]["productId"]
        TestDeskRmaRefundModify.orderProductId = valid_product_list[0]["orderProductId"]
        TestDeskRmaRefundModify.product = valid_product_list[0]

    @weeeTest.params.file(file_name="desk_rma_refund_modify.yaml", key="desk_rma_create_request")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_rma_create_request(self,name, description, request, expected_result):
        """【101922】 desk创建request"""
        request['json_data']['productList'][0] = TestDeskRmaRefundModify.product
        request['json_data']['productList'][0]['orderProductId'] = TestDeskRmaRefundModify.orderProductId
        data = request['json_data']
        resp = DeskRmaRefundModify().desk_rma_create_request(data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True

    @weeeTest.params.file(file_name="desk_rma_refund_modify.yaml", key="desk_rma_refund_do_action")
    @weeeTest.mark.list('Regression','CS')
    def test_desk_rma_refund_do_action(self, name, description, request, expected_result):
        """【102366】 modify refund"""
        sql = "SELECT * FROM `weee_cs`.`cs_rma_refund` WHERE `user_id` = '10060559' ORDER BY `id` DESC LIMIT 1;"
        db_res = DBConnect().select_data_from_mysql(sql)[0][0]
        assert db_res, f"db_res is incorrect,dbres={db_res}"
        TestDeskRmaRefundModify.id = db_res
        request['json_data']['id'] = db_res
        data = request['json_data']
        resp = DeskRmaRefundModify().desk_rma_refund_do_action(data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True

    @weeeTest.params.file(file_name="desk_rma_refund_modify.yaml", key="desk_rma_refund_void_request")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_rma_refund_void_request(self, name, description, request, expected_result):
        """【102436】 void request"""
        sql = "SELECT * FROM `weee_cs`.`cs_rma_refund` WHERE `user_id` = '10060559' ORDER BY `request_id` DESC LIMIT 1;"
        db_res = DBConnect().select_data_from_mysql(sql)[0][-1]
        assert db_res, f"db_res is incorrect, dbres={db_res}"
        TestDeskRmaRefundModify.request_id = db_res
        request['json_data']['requestId'] = db_res
        data = request['json_data']
        resp = DeskRmaRefundModify().desk_rma_refund_void_request(data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True

    @weeeTest.params.file(file_name="desk_rma_refund_modify.yaml", key="desk_rma_refund_find_list")
    @weeeTest.mark.list('Regression','CS')
    def test_desk_rma_refund_find_list(self, name, description, request, expected_result):
        """【102586】查询refund list"""
        print(description)
        time.sleep(30)
        data = request['json_data']
        data['applyTimeRange'] = [(datetime.datetime.now() + datetime.timedelta(days=-90)).strftime("%Y-%m-%d"),str(datetime.date.today())]
        resp = DeskRmaRefundModify().desk_rma_refund_find_list(data)
        refund = [item for item in resp['object']['data'] if item['id'] == TestDeskRmaRefundModify.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] is True
        assert resp["object"]["data"][0]["userId"] == 10060559
        sql = fr"SELECT id, status FROM `weee_cs`.`cs_rma_refund` WHERE id={TestDeskRmaRefundModify.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == refund[0]['id']
        assert refund[0]['status'] == 'X'

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')