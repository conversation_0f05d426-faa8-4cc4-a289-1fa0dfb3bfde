import weeeTest
from weeeTest import log

from erp.test_dir.api.desk.emailcenter.email_center import DeskEmailCenter


class TestDeskEmailCenter(weeeTest.TestCase):
    id = ''
    @weeeTest.params.file(file_name="email_center.yaml", key="email_center_view")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_email_center_view(self, name, description, request, expected_result):
        """ 【102290】email center all view"""
        print(description)
        resp = DeskEmailCenter().email_center_view(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["total"] == 10000
        TestDeskEmailCenter.id = resp["object"]["list"][0]["id"]

    @weeeTest.params.file(file_name="email_center.yaml", key="email_center_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_email_center_detail(self, name, description, request, expected_result):
        """ 【102429】email center detail"""
        print(description)
        request["json_data"]["id"] = TestDeskEmailCenter.id
        resp = DeskEmailCenter().email_center_detail(request["json_data"]["id"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["email"]["id"] == TestDeskEmailCenter.id
        assert resp["object"]["email"]["status"] == 0


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')