import weeeTest
import datetime
from erp.test_dir.api.desk.cases.ec_order_case import EcOrderCase
from erp.test_dir.db_utils import DBConnect


class TestEcOrderCase(weeeTest.TestCase):
    id = ''
    case_id = ''
    orderCaseId = ''
    caseNumber = ''
    caseId = ''
    today = (datetime.datetime.today()).strftime("%Y-%m-%d")

    """测试ec order case申请售后流程"""

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="ec_order_case_create")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ec_order_case_create(self, name, description, request, expected_result):
        """EC前端创建order case"""
        sql = rf"""
        SELECT
	d.* ,e.*,o.*,g.*
FROM
	weee_comm.gb_order o
	JOIN weee_comm.gb_order_ext e ON e.order_id = o.id
	JOIN weee_comm.gb_order_product d ON d.order_id = o.id and d.original_delivery_id=d.delivery_id
	left JOIN weee_comm.gb_order_case g ON g.order_id = o.id and g.product_id=d.product_id
WHERE
	o.STATUS = 'F' 
	AND o.invoice_id IS NOT NULL 
	AND o.rec_create_time >= '{TestEcOrderCase.today}'
	AND o.rec_creator_id = '10060559'
	GROUP BY d.id
HAVING
	NOW()< DATE_ADD( e.sort_date, INTERVAL 12 HOUR ) 
	AND o.close_time < e.sort_date 
	AND d.delivery_id IS NOT NULL 
	and (g.id is null or g.`status` in ('R','X'))
ORDER BY
	d.id DESC;
            """
        db_res = DBConnect().select_data_from_mysql(sql)
        request['json_data']['order_id'] = db_res[1][1]
        request['json_data']['order_product_id'] = db_res[1][0]
        request['json_data']['product_id'] = db_res[1][2]
        request['json_data']['products'][0]['product_id'] = db_res[1][2]
        request['json_data']['products'][0]['order_product_id'] = db_res[1][0]

        data = request['json_data']
        resp = EcOrderCase().ec_order_case_create(data)
        try:
            assert resp["message_id"] == "10000"
        except AssertionError as error:
            raise error
        TestEcOrderCase.case_id = resp["object"]["case_id"]

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="page_query_data_view")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_page_query_data_view(self, name, description, request, expected_result):
        """页面查询data view"""
        resp = EcOrderCase().page_query_data_view(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error
        TestEcOrderCase.case_id = resp["object"]["list"][0]["orderCaseId"]
        TestEcOrderCase.caseNumber = resp["object"]["list"][0]["caseNumber"]
        TestEcOrderCase.id = resp["object"]["list"][0]["id"]

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="assigned_to_me")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_assigned_to_me(self, name, description, request, expected_result):
        """指给某人处理"""
        print(description)
        request["json_data"]["caseNumber"] = TestEcOrderCase.caseNumber
        resp = EcOrderCase().assigned_to_me(request["json_data"])
        try:
            assert resp["message_id"] == "10000"
        except AssertionError as error:
            raise error

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="tree_ByCase_Type")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_tree_ByCase_Type(self, name, description, request, expected_result):
        """填写category"""
        print(description)
        request["json_data"]["caseNumber"] = TestEcOrderCase.caseNumber
        resp = EcOrderCase().tree_ByCase_Type(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="update_Description")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_update_Description(self, name, description, request, expected_result):
        """更新回复描述"""
        print(description)
        request["json_data"]["caseId"] = TestEcOrderCase.id
        resp = EcOrderCase().update_Description(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

    #@weeeTest.params.file(file_name="ec_order_case.yaml", key="reply_Message_And_Resolve")
    #@weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_reply_Message_And_Resolve(self, name, description, request, expected_result):
        """回复客人并解决case"""
        print(description)
        request["json_data"]["caseId"] = TestEcOrderCase.id
        resp = EcOrderCase().reply_Message_And_Resolve(request["json_data"])
        try:
            assert resp["message_id"] == expected_result["message_id"]
            assert resp["result"] == expected_result["result"]
        except AssertionError as error:
            raise error

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
