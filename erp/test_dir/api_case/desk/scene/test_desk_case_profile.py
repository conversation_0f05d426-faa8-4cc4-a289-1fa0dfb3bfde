import weeeTest
from erp.test_dir.api.desk.cases.desk_case_profile import DeskCaseProfile
from erp.test_dir.db_utils import DBConnect
import time


class TestDeskCaseProfile(weeeTest.TestCase):
    id = ''
    caseNumber = ''
    case = None

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="desk_case_profile_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_case_profile_create(self, name, description, request, expected_result):
        """desk_case_profile_create接口"""
        sql = "SELECT * FROM weee_cs.desk_case WHERE email ='<EMAIL>' ORDER BY id DESC"
        db_res_before = DBConnect().select_data_from_mysql(sql)
        resp = DeskCaseProfile().desk_case_profile_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        db_res_after = DBConnect().select_data_from_mysql(sql)
        assert len(db_res_after) == len(db_res_before) + 1
        TestDeskCaseProfile.caseNumber = db_res_after[0][1]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="pageQuery_Data_View")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_pageQuery_Data_View(self, name, description, request, expected_result):
        """【102265】 页面查询all view"""
        time.sleep(30)
        resp = DeskCaseProfile().pageQuery_Data_View(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] is True
        case_list = resp["object"]["list"]
        assert case_list, f"当前case列表为空"
        for index, item in enumerate(case_list):
            if item.get("accountId") == 2 and index in [0, 1, 2, 3, 4]:
                self.case = item
                break
        assert self.case, f"没找到当前创建的case"
        assert self.case["accountId"] == 2
        assert self.case["accountName"] == "********-qifang.ye"
        assert self.case["customerDescription"] == "接口测试case profile"
        assert self.case["assignee"] == 3
        assert self.case["assigneeName"] == "Tier 1"
        assert self.case["language"] == "zh"
        assert self.case["importance"] == 2
        TestDeskCaseProfile.caseNumber = resp["object"]["list"][0]["caseNumber"]
        TestDeskCaseProfile.id = resp["object"]["list"][0]["id"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="search_case_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_search_case_list(self, name, description, request, expected_result):
        """【102645】 页面搜索查询case"""
        request["json_data"]["compositeInfo"] = TestDeskCaseProfile.caseNumber
        resp = DeskCaseProfile().get_search_case_list(request["json_data"])
        assert resp["result"] is True
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["object"]["total"] == 1
        TestDeskCaseProfile.caseNumber = resp["object"]["list"][0]["caseNumber"]
        TestDeskCaseProfile.id = resp["object"]["list"][0]["id"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="enter_CaseDetail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_enter_CaseDetail(self, name, description, request, expected_result):
        """【102267】 进入case详情页"""
        print(description)
        request["json_data"]["id"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().enter_CaseDetail(request["json_data"]['id'])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="assigned_ToMe")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_assigned_ToMe(self, name, description, request, expected_result):
        """【102646】 指给业务处理"""
        print(description)
        request["json_data"]["caseNumber"] = TestDeskCaseProfile.caseNumber
        resp = DeskCaseProfile().assigned_ToMe(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="update_WeeeUserId")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_update_WeeeUserId(self, name, description, request, expected_result):
        """【102647】 绑定另一个customer"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().update_WeeeUserId(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="unbind_Order")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_unbind_Order(self, name, description, request, expected_result):
        """【102648】 取消绑定某个订单"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().unbind_Order(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="upload_Image")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_upload_Image(self, name, description, request, expected_result):
        """【102649】 上传图片"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().upload_Image(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="upload_File")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_upload_File(self, name, description, request, expected_result):
        """【102650】 上传文件"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().upload_File(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="add_All")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_All(self, name, description, request, expected_result):
        """【102651】 新增tag"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().add_All(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="pageQuery_Data_View")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_pageQuery_Data_View1(self, name, description, request, expected_result):
        """【102265】 页面查询case view all"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        time.sleep(15)
        resp = DeskCaseProfile().pageQuery_Data_View(request["json_data"])
        case = [item for item in resp['object']['list'] if item['id'] == TestDeskCaseProfile.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert case[0]['status'] == 6, f"case={case}, resp={resp},TestDeskCaseProfile.id={TestDeskCaseProfile.id}"
        sql = fr"SELECT status FROM `weee_cs`.`desk_case` WHERE id={TestDeskCaseProfile.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == case[0]['status'] == 6

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="validate_Ticket")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_validate_Ticket(self, name, description, request, expected_result):
        """【103023650】 新建编辑关联ticket"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().validate_Ticket(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="add_Ticket")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_Ticket(self, name, description, request, expected_result):
        """【103023650】 新增编辑关联ticket成功"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().add_Ticket(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_case_profile.yaml", key="add_Related_Date")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_Related_Date(self, name, description, request, expected_result):
        """【3023737】 新增相关联数据"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCaseProfile.id
        resp = DeskCaseProfile().add_Related_Date(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
