import weeeTest
from erp.test_dir.api.desk.cases.claim_3pl import Claim3pl

class TestClaim3pl(weeeTest.TestCase):
    id = ''
    invoiceId = ''
    @weeeTest.params.file(file_name="claim_3pl.yaml",key="desk_claim_3pl_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_claim_3pl_create(self,name, description, request, expected_result):
        """【102419】 desk claim 3pl 创建接口"""
        resp = Claim3pl().desk_claim_3pl_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"] == "success"
    @weeeTest.params.file(file_name="claim_3pl.yaml",key="claim_3pl_find_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_claim_3pl_find_list(self,name, description, request, expected_result):
        """desk claim 3pl查询列表接口"""
        resp = Claim3pl().claim_3pl_find_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["recCreatorId"] == 10060559
        assert resp["object"]["data"][0]["amount"] == 80.07
        assert resp["object"]["data"][0]["amountReceived"] == 70.26
        assert resp["object"]["data"][0]["carrier"] == "GLS US"
        TestClaim3pl.id = resp["object"]["data"][0]["id"]
        TestClaim3pl.invoiceId = resp["object"]["data"][0]["invoiceId"]

    @weeeTest.params.file(file_name="claim_3pl.yaml", key="claim_3pl_edit")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_claim_3pl_edit(self, name, description, request, expected_result):
        """【102420】 desk claim 3pl编辑接口"""
        request["json_data"]["id"] = TestClaim3pl.id
        resp = Claim3pl().claim_3pl_edit(request["json_data"]["id"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["deliveryDate"] == "2022-12-14"
        assert resp["object"]["status"] == "Confirmed"
        assert resp["object"]["invoiceId"] == 30691632
        assert resp["object"]["salesOrg"] == "Seattle"
        assert resp["object"]["seller"] == "Weee!"
        assert resp["object"]["responsible"] == "GLS US"
        assert resp["object"]["submitter"] == "10060559 - xiao.ye888"
        assert resp["object"]["amountReceived"] == 70.26

    @weeeTest.params.file(file_name="claim_3pl.yaml", key="claim_3pl_modify_status")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_claim_3pl_modify_status(self, name, description, request, expected_result):
        """desk claim 3pl修改状态"""
        request["json_data"]["claimId"] = TestClaim3pl.id
        resp = Claim3pl().claim_3pl_modify_status(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="claim_3pl.yaml", key="claim_3pl_pending")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_claim_3pl_pending(self, name, description, request, expected_result):
        """【102425】 desk claim 3pl pending状态"""
        resp = Claim3pl().claim_3pl_pending(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="claim_3pl.yaml", key="claim_3pl_approved")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_claim_3pl_approved(self, name, description, request, expected_result):
        """【102424】 desk claim 3pl approved状态"""
        resp = Claim3pl().claim_3pl_approved(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
