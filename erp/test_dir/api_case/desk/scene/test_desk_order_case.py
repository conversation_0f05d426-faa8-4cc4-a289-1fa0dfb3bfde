import time

import weeeTest
from weeeTest import log

from erp.test_dir.api.desk.cases.desk_order_case import DeskOrderCase
from erp.test_dir.db_utils import DBConnect


class TestDeskOrderCase(weeeTest.TestCase):
    case_Id = ''
    id = ''

    @weeeTest.params.file(file_name="desk_order_case.yaml", key="get_user_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_user_info(self, name, description, request, expected_result):
        """获取用户信息接口"""
        print(description)
        resp = DeskOrderCase().get_user_info()
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["status"] == 1
        assert resp["object"]["type"] == 1
        assert resp["object"]["status"] == 1
        assert resp["object"]["type"] == 1
        assert 'userName' in resp['object'].keys()
        assert 'weeeUserId' in resp['object'].keys()

    @weeeTest.params.file(file_name="desk_order_case.yaml", key="desk_order_case_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_order_case_create(self, name, description, request, expected_result):
        """【102105】desk_order_create接口"""
        print(description)
        resp = DeskOrderCase().desk_order_case_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDeskOrderCase.case_Id = resp["object"]["caseId"]
        TestDeskOrderCase.id = resp["object"]["caseId"]

    @weeeTest.params.file(file_name="desk_order_case.yaml", key="case_interaction_List")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_case_interaction_List(self, name, description, request, expected_result):
        """case_interaction_List接口"""
        print(description)
        request["json_data"]["caseId"] = TestDeskOrderCase.case_Id
        resp = DeskOrderCase().case_interaction_List(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_order_case.yaml", key="send_EmailAndResolve")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_send_EmailAndResolve(self, name, description, request, expected_result):
        """回复客人并处理完case"""
        print(description)
        request["json_data"]["caseId"] = TestDeskOrderCase.case_Id
        resp = DeskOrderCase().send_EmailAndResolve(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_no_order_case.yaml", key="pageQuery_DataView")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_pageQuery_DataView(self, name, description, request, expected_result):
        """【102265】页面查询case view all"""
        print(description)
        request["json_data"]["caseId"] = TestDeskOrderCase.id
        time.sleep(15)
        resp = DeskOrderCase().pageQuery_DataView(request["json_data"])
        case = [item for item in resp['object']['list'] if item['id'] == TestDeskOrderCase.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        log.info("case===>", case)
        assert case[0]['status'] == 7, f"case={case}, resp={resp},TestDeskOrderCase.id={TestDeskOrderCase.id}"
        sql = fr"SELECT status FROM `weee_cs`.`desk_case` WHERE id={TestDeskOrderCase.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == case[0]['status'] == 7

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
