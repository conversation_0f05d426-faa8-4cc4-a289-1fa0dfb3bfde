import time

import weeeTest
from weeeTest import log
from erp.test_dir.api.desk.tickets.desk_ticket_agree import DeskTicketAgree
from erp.test_dir.db_utils import DBConnect


class TestDeskTicketAgree(weeeTest.TestCase):
    taskId = ""
    ticketNumber = ""
    processDefinitionId = ""
    processInstanceId = ""
    taskKey = ""
    ticketId = ""

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="add_ticket")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_ticket(self, name, description, request, expected_result):
        """【102272】新增ticket"""
        print(description)
        resp = DeskTicketAgree().add_ticket(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestDeskTicketAgree.ticketNumber = resp["object"]["ticketNumber"]

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="list_page")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_list_page(self, name, description, request, expected_result):
        """【102271】列表页"""
        print(description)
        time.sleep(20)
        resp = DeskTicketAgree().list_page(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["subject"] == "test ticket agree"
        assert resp["object"]["data"][0]["categoryName"] == "CRM - Coupon Issues - Others"
        assert resp["object"]["data"][0]["statusStr"] == "Pending"
        assert resp["object"]["data"][0]["typeStr"] == "Co-operate"
        assert resp["object"]["data"][0]["status"] == 7
        TestDeskTicketAgree.processDefinitionId = resp["object"]["data"][0]["processDefinitionId"]
        TestDeskTicketAgree.processInstanceId = resp["object"]["data"][0]["processInstanceId"]
        TestDeskTicketAgree.taskId = resp["object"]["data"][0]["taskId"]
        TestDeskTicketAgree.taskKey = resp["object"]["data"][0]["taskKey"]
        TestDeskTicketAgree.ticketNumber = resp["object"]["data"][0]["ticketNumber"]
        TestDeskTicketAgree.ticketId = resp["object"]["data"][0]["ticketId"]

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="ticket_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_detail(self, name, description, request, expected_result):
        """ticket详情页"""
        print(description)
        request["json_data"]["processDefinitionId"] = TestDeskTicketAgree.processDefinitionId
        request["json_data"]["processInstanceId"] = TestDeskTicketAgree.processInstanceId
        request["json_data"]["taskId"] = TestDeskTicketAgree.taskId
        request["json_data"]["taskKey"] = TestDeskTicketAgree.taskKey
        request["json_data"]["ticketNumber"] = TestDeskTicketAgree.ticketNumber
        resp = DeskTicketAgree().ticket_detail(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["typeStr"] == "Co-operate"
        assert resp["object"]["status"] == 7
        assert resp["object"]["statusStr"] == "Pending"
        assert resp["object"]["categoryName"] == "CRM - Coupon Issues - Others"
        assert resp["object"]["subject"] == "test ticket agree"
        assert resp["object"]["userId"] == "10060559"
        assert resp["object"]["userEmail"] == "<EMAIL>"
        assert resp["object"]["relateCases"][0]["caseNumber"] == "103012122"
        assert resp["object"]["relateCases"][0]["caseId"] == 3012122
        assert resp["object"]["recCreatorName"] == "xiao.ye"
        assert resp["object"]["processor"] == "Engineering"
        TestDeskTicketAgree.ticketId = resp["object"]["ticketId"]

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="review_ticket")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_review_ticket(self, name, description, request, expected_result):
        """review ticket"""
        print(description)
        request["json_data"]["ticketNumber"] = TestDeskTicketAgree.ticketNumber
        resp = DeskTicketAgree().review_ticket(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="submit_task")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_submit_task(self, name, description, request, expected_result):
        """submit ticket"""
        print(description)
        request["json_data"]["ticketNumber"] = TestDeskTicketAgree.ticketNumber
        request["json_data"]["processInstanceId"] = TestDeskTicketAgree.processInstanceId
        request["json_data"]["taskId"] = TestDeskTicketAgree.taskId
        resp = DeskTicketAgree().submit_task(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_ticket_agree.yaml", key="ticket_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_ticket_list(self, name, description, request, expected_result):
        """ 【102271】all ticket list"""
        print(description)
        request["json_data"]["ticketNumber"] = TestDeskTicketAgree.ticketNumber
        resp = DeskTicketAgree().ticket_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["data"][0]["subject"] == "test ticket agree"
        assert resp["object"]["data"][0]["categoryName"] == "CRM - Coupon Issues - Others"
        assert resp["object"]["data"][0]["status"] == "Pending"
        assert resp["object"]["data"][0]["typeName"] == "Co-operate"
        assert resp["object"]["data"][0]["creator"] == "<EMAIL>"
        if TestDeskTicketAgree.ticketId:
            sql = fr"DELETE FROM `weee_cs`.`desk_ticket` WHERE `id`= {TestDeskTicketAgree.ticketId}"
            db_res = DBConnect().update_data_from_mysql(sql)

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')