import time

import weeeTest
from weeeTest import log
from erp.test_dir.api.desk.space.space import Space
from erp.test_dir.db_utils import DBConnect



class TestSpace(weeeTest.TestCase):
    spaceId = ""

    @weeeTest.params.file(file_name="space.yaml", key="desk_kb_space_create")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_kb_space_create(self, name, description, request, expected_result):
        """ 【102442】desk创建space"""
        print(description)
        resp = Space().desk_kb_space_create(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="space.yaml", key="space_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_space_list(self, name, description, request, expected_result):
        """ 【102443】space list"""
        print(description)
        resp = Space().space_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestSpace.spaceId = resp["object"]["data"][0]["id"]

    @weeeTest.params.file(file_name="space.yaml", key="add_article")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_add_article(self, name, description, request, expected_result):
        """【102450】add article"""
        print(description)
        resp = Space().add_article(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="space.yaml", key="edit_article_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_edit_article_detail(self, name, description, request, expected_result):
        """【102370】article edit"""
        print(description)
        resp = Space().edit_article_detail(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="space.yaml", key="space_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_space_list1(self, name, description, request, expected_result):
        """ 【102443】space list"""
        print(description)
        request["json_data"]["id"] = TestSpace.spaceId
        time.sleep(15)
        resp = Space().space_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        TestSpace.spaceId = resp["object"]["data"][0]["id"]
        if TestSpace.spaceId:
            sql = fr"DELETE FROM `weee_cs`.`desk_kb_space` WHERE `id` = {TestSpace.spaceId}"
            db_res = DBConnect().update_data_from_mysql(sql)

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')