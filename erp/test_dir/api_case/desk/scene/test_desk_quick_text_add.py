import logging
import time


import weeeTest
from weeeTest import log
from erp.test_dir.api.desk.quicktext.desk_quick_text_add import DeskQuickTextAdd
from erp.test_dir.db_utils import DBConnect

class TestDeskQuickTextAdd(weeeTest.TestCase):
    id = ''

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="desk_quick_text_add")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_desk_quick_text_add(self, name, description, request, expected_result):
        """【102255】desk_quick_text_add接口"""
        print(description)
        resp = DeskQuickTextAdd().desk_quick_text_add(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="quick_text_page_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_quick_text_page_list(self, name, description, request, expected_result):
        """【102400】quick text list"""
        print(description)
        time.sleep(30)
        resp = DeskQuickTextAdd().quick_text_page_list(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["list"][0]["name"] == "1"
        assert resp["object"]["list"][0]["content"] == "test"
        assert resp["object"]["list"][0]["description"] == "test"
        assert resp["object"]["list"][0]["language"] == "zh"
        assert resp["object"]["list"][0]["recCreatorName"] == "xiao.ye"
        TestDeskQuickTextAdd.id = resp["object"]["list"][0]["id"]

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="quick_text_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_quick_text_detail(self, name, description, request, expected_result):
        """【102401】quick text detail"""
        print(description)
        request["json_data"]["id"] = TestDeskQuickTextAdd.id
        resp = DeskQuickTextAdd().quick_text_detail(request["json_data"]["id"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]
        assert resp["object"]["name"] == "1"
        assert resp["object"]["content"] == "test"
        assert resp["object"]["description"] == "test"
        assert resp["object"]["language"] == "zh"
        assert resp["object"]["label"] == "11111111"
        assert resp["object"]["recUpdateName"] == "xiao.ye"

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="quick_text_edit")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_quick_text_edit(self, name, description, request, expected_result):
        """【102402】quick text edit"""
        print(description)
        request["json_data"]["id"] = TestDeskQuickTextAdd.id
        resp = DeskQuickTextAdd().quick_text_edit(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="quick_text_delete")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_quick_text_delete(self, name, description, request, expected_result):
        """【102403】quick text delete"""
        print(description)
        request["json_data"]["id"] = TestDeskQuickTextAdd.id
        resp = DeskQuickTextAdd().quick_text_delete(request["json_data"]["id"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="desk_quick_text_add.yaml", key="quick_text_page_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_quick_text_page_list1(self, name, description, request, expected_result):
        """页面再次查询quick text page list 数据是否删除成功"""
        print(description)
        request["json_data"]["id"] = TestDeskQuickTextAdd.id
        time.sleep(30)
        resp = DeskQuickTextAdd().quick_text_page_list(request["json_data"])
        quicktext = [item for item in resp["object"]["list"] if item["id"] == TestDeskQuickTextAdd.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')