
import weeeTest
from erp.test_dir.api.vendor.vendor_order_api import VendorOrderInterfaces
# from test_dir.api.vendor.vendor_login_api import VendorLoginInterfaces


class TestVendorDeliveryAppointment(weeeTest.TestCase):

    def test_login(self):
        pass

    def test_order_info(self):
        pass

    def test_order_add_appointment(self):
        pass

    def test_order_edit_appointment(self):
        pass

    def test_cancel_appointment(self):
        pass



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')







































