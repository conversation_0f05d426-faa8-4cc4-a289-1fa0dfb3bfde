import weeeTest
from erp.test_dir.api.vendor.vendor_order_api import VendorOrderInterfaces
from erp.test_dir.db_utils import DBConnect


class TestVendorOrderCenter(weeeTest.TestCase):
    """
    供应商平台的订单中心模块
    """
    @weeeTest.params.file(file_name="vendor_order_data.yaml", key="vendor_order_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_order_list(self, title, request, expected_result):
        """供应商平台采购订单模块列表查询"""
        header_type = request["header"]["vendor_id"]
        resp = VendorOrderInterfaces().vendor_order_list(request["params_data"], header_type)
        assert resp["result"] == expected_result["result"], f"{resp}接口返回失败"
        assert expected_result["object"] in resp["object"]
        assert resp["object"] is not None

        if "object" in resp and "orders" in resp["object"] and resp["object"]["orders"]:
            po_id = resp["object"]["orders"][0]["id"]
            sql = f'SELECT * FROM weee_comm.gb_purchase_order WHERE id = {po_id};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(po_id)

    @weeeTest.params.file(file_name="vendor_order_data.yaml", key="vendor_order_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_order_info(self, title, request, expected_result):
        """供应商平台采购订单详情"""
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        header_type = request["header"]["vendor_id"]
        resp = VendorOrderInterfaces().vendor_order_info(request["data"], header_type)
        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        po_id = resp["object"]["id"]
        sql = f'SELECT * FROM weee_comm.gb_purchase_order WHERE id = {po_id};'
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == int(po_id)
        # print('详情1', po_id, db_res[0][0])

    @weeeTest.params.file(file_name="vendor_delivery_appointment.yaml", key="vendor_appointment_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_delivery_appointment_list(self, title, request, expected_result):
        """
        供应商平台预约送货列表查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["params_data"]
        header_type = request["header"]["vendor_id"]
        resp = VendorOrderInterfaces().vendor_delivery_appointment_list(body, header_type)
        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "list" in resp["object"] and resp["object"]["list"]:
            app_id = resp["object"]["list"][0]["id"]
            sql = f'SELECT * FROM weee_merch.po_inventory_calendar_event_item WHERE id = {app_id};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(app_id)
            # print('预约11', db_res[0][0])

    @weeeTest.params.file(file_name="vendor_delivery_appointment.yaml", key="vendor_order_detail")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_delivery_order_detail(self, title, request, expected_result):
        """
        供应商平台订单的详情查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["data"]

        header_type = request["header"]["vendor_id"]
        resp = VendorOrderInterfaces().vendor_get_po_info(body, header_type)

        if not resp['result']:
            print('resp is false')
            assert expected_result["result"] != resp["result"]
            assert resp[
                       "message"] == ("This PO has an existing appointment scheduled by Test Vender 123321 on "
                                      "2023-12-26 13:00 - 14:00 PST")

    @weeeTest.params.file(file_name="vendor_delivery_appointment.yaml", key="vendor_order_active_time")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_delivery_active_date(self, title, request, expected_result):
        """供应商平台订单的可配送日期查询"""
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["json_data"]
        header_type = request["header"]
        resp = VendorOrderInterfaces().vendor_get_active_time(body, header_type)
        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]
        assert resp['message'] == 'request ok.'
        # print('可配送', resp['message'])

    @weeeTest.params.file(file_name="vendor_delivery_appointment.yaml", key="vendor_order_appointment_time")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_add_delivery_appointment(self, title, request, expected_result):
        """供应商平台订单预约时间新增、编辑"""
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["json_data"]
        header_type = request["header"]
        resp = VendorOrderInterfaces().vendor_add_delivery_appointment(body, header_type)

        assert resp['result'] == True
        assert resp["result"] == expected_result["result"]
        assert expected_result["message"] == resp["message"]

    @weeeTest.params.file(file_name="vendor_delivery_appointment.yaml", key="vendor_order_cancel_appointment_time")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_delivery_cancel_appointment(self, title, request, expected_result):
        """供应商平台订单预约时间取消"""
        body = request["json_data"]
        header_type = request["header"]
        resp = VendorOrderInterfaces().vendor_cancel_delivery_appointment(body, header_type)

        assert resp['message'] is not None
        assert resp["result"] == expected_result["result"]
        assert expected_result["message"] == resp["message"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
