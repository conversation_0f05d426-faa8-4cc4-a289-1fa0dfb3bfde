import weeeTest
from erp.test_dir.api.auth.vendor_login import VendorLoginInterfaces


class TestVendorLogin(weeeTest.TestCase):
    """
    供应商平台的登录模块
    """

    @weeeTest.params.file(file_name="vendor_login_data.yaml", key="vendor_login")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_vendor_login(self, title, request, expected_result):
        """
        供应商平台账号登录
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["json_data"]
        resp = VendorLoginInterfaces().vendor_login(body)
        assert resp["result"] == expected_result["result"]
        assert resp["message"] == expected_result["message"]
        if "object" in resp and "token" in resp["object"] and resp["object"]["token"]:
            assert resp["object"]["token"] is not None


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
