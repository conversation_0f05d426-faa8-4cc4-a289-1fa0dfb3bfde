import weeeTest
from erp.test_dir.api.vendor.vendor_product_api import VendorProductInterfaces
from erp.test_dir.db_utils import DBConnect


class TestVendorProductCenter(weeeTest.TestCase):

    @weeeTest.params.file(file_name="vendor_active_product_data.yaml", key="vendor_active_product_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_active_product_list(self, title, request, expected_result):
        """
        供应商平台商品中心的Active Product列表查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["params_data"]
        resp = VendorProductInterfaces().vendor_active_product_list(body)

        if "object" in resp and "products" in resp["object"] and resp["object"]["products"]:
            sku_id = resp["object"]["products"][0]["id"]
            sql = f'SELECT * FROM weee_comm.gb_product WHERE id = {str(sku_id)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(sku_id)

    @weeeTest.params.file(file_name="vendor_active_product_data.yaml", key="vendor_update_code")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_update_vendor_code(self, title, request, expected_result):
        """
        供应商平台商品中心的更新供应商code
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        body = request["json_data"]
        resp = VendorProductInterfaces().vendor_update_item_code(body)

        assert resp["result"] == expected_result["result"]

        sql = f'SELECT * FROM weee_comm.gb_product_vendor WHERE id = {115992};'
        db_res = DBConnect().select_data_from_mysql(sql)

        if db_res[0][0] is not None:
            assert db_res[0][0] == int(115992)
            assert db_res[0][4] == "update1"

    @weeeTest.params.file(file_name="vendor_product_proposal_data.yaml", key="vendor_review_product_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_product_review_list(self, title, request, expected_result):
        """
        供应商平台Review商品列表查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        resp = VendorProductInterfaces().vendor_review_product_list(request["params_data"])

        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "list" in resp["object"] and resp["object"]["list"]:
            review_Product = resp["object"]["list"][0]["id"]

            sql = f'SELECT * FROM weee_comm.gb_product_audit WHERE id = {str(review_Product)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(review_Product)

    @weeeTest.params.file(file_name="vendor_product_proposal_data.yaml", key="vendor_review_product_info")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_review_product_info(self, title, request, expected_result):
        """
        供应商平台Review商品详情
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        resp = VendorProductInterfaces().vendor_review_product_info(request["data"])

        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "id" in resp["object"] and resp["object"]["id"]:
            review_Product = resp["object"]["id"]

            sql = f'SELECT * FROM weee_comm.gb_product_audit WHERE id = {str(review_Product)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(review_Product)
            assert db_res[0][2] == "金莎巧克力24粒装"

    @weeeTest.params.file(file_name="vendor_product_proposal_data.yaml", key="vendor_create_review_product")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_create_review_product(self, title, request, expected_result):
        """
        供应商平台创建商品Review
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        header_type = request["header"]
        resp = VendorProductInterfaces().vendor_create_review_product(request["json_data"], header_type)
        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "id" in resp["object"] and resp["object"]["id"]:
            new_Review = resp["object"]["id"]

            sql = f'SELECT * FROM weee_merch.vm_vendor_product_proposal WHERE id = {str(new_Review)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][0] == int(new_Review)

    @weeeTest.params.file(file_name="vendor_product_update_data.yaml", key="vendor_product_request_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_product_request_list(self, title, request, expected_result):
        """
        供应商平台商品申请列表查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        header_type = request["header"]
        resp = VendorProductInterfaces().vendor_request_product_list(request["params_data"], header_type)

        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "list" in resp["object"] and resp["object"]["list"]:
            sku_id = resp["object"]["list"][0]["product_id"]

            sql = f'SELECT * FROM weee_merch.pi_vendor_request WHERE product_id = {str(sku_id)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][1] == int(sku_id)

    @weeeTest.params.file(file_name="vendor_product_update_data.yaml", key="vendor_product_update_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    # @weeeTest.mark.skip(reason="暂时跳过")
    def test_vendor_product_update_list(self, title, request, expected_result):
        """
        供应商平台商品更新列表查询
        :param title:
        :param request:
        :param expected_result:
        :return:
        """
        print(f"接口名称: {title['name']}，接口场景：{title['description']}")
        resp = VendorProductInterfaces().vendor_product_update_list(request["params_data"])

        assert resp["result"] == expected_result["result"]
        assert expected_result["object"] in resp["object"]

        if "object" in resp and "products" in resp["object"] and resp["object"]["products"]:
            sku_id = resp["object"]["products"][0]["product_id"]

            sql = f'SELECT * FROM weee_comm.gb_product_vendor WHERE product_id = {str(sku_id)};'
            db_res = DBConnect().select_data_from_mysql(sql)
            assert db_res[0][1] == int(sku_id)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
