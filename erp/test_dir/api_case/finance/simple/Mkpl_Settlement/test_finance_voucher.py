import weeeTest
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.finance.Mkpl_Settlement.mkpl_business_voucher import FinanceVoucherListQuery

class TestFinanceVoucher(weeeTest.TestCase):

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_mkpl_list(self):
        """MKPL Finance Vocuher tab查询;  [MS:100303] """
        resp = FinanceVoucherListQuery().financeMkpl_list(business_type="MKPL")
        mkpl_id = resp["object"]["data"][0]["id"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher WHERE id = {mkpl_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["orderId"] == db_res[0][2]
        assert resp["object"]["data"][0]["voucherId"] == db_res[0][3]

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_mkpl_Renvenue_list(self):
        """MKPL Revenue tab查询  [MS:100309] """
        resp = FinanceVoucherListQuery().revenue_list(business_type='MKPL')
        voucher_id = resp["object"]["data"][0]["voucherId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_revenue WHERE voucher_id = {voucher_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["orderId"] == db_res[0][3]
        assert resp["object"]["data"][0]["revenueType"] == db_res[0][8]

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_fbw_list(self):
        """FBW tab查询;  [MS:100311] """
        resp = FinanceVoucherListQuery().financeMkpl_list(business_type="FBW")
        list_id = resp["object"]["data"][0]["id"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_fbw WHERE id = {list_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["orderId"] == db_res[0][2]
        assert resp["object"]["data"][0]["voucherId"] == db_res[0][3]

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_fbw_Revenue_list(self):
        """FBW Renvenue tab查询  [MS:100312] """
        resp = FinanceVoucherListQuery().revenue_list(business_type='FBW')
        voucherId = resp["object"]["data"][0]["voucherId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_revenue_fbw WHERE voucher_id = {voucherId}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["orderId"] == db_res[0][3]
        assert resp["object"]["data"][0]["revenueType"] == db_res[0][8]

    @weeeTest.mark.list('Regression', 'Smoke', 'Finance')
    def test_finance_mfbw_list(self):
        """MFBW tab查询; [MS:100318] """
        resp = FinanceVoucherListQuery().financeMkpl_list(business_type="MFBW")
        list_id = resp["object"]["data"][0]["id"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_mfbw WHERE id = {list_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["orderId"] == db_res[0][2]
        assert resp["object"]["data"][0]["voucherId"] == db_res[0][3]

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_ro_return_list(self):
        """RO_RETURN tab查询  [MS:100616] """
        resp = FinanceVoucherListQuery().finance_ro_return_list()
        voucherId = resp["object"]["data"][0]["voucherId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_ar WHERE voucher_id = {voucherId}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][2]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][3]

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_tms_approval_list(self):
        """TMS Approval Voucher tab查询  [MS:100405] """
        resp = FinanceVoucherListQuery().finance_tms_approval_list()
        voucher_id = resp["object"]["data"][0]["voucherId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_payment_tms WHERE voucher_id = {voucher_id}"
        db_res = DBConnect().select_data_from_mysql(sql)

        dispatch_float = float(db_res[0][3])
        assert resp["object"]["data"][0]["dispatchFee"] == dispatch_float

        adjustment_float = float(db_res[0][4])
        assert resp["object"]["data"][0]["adjustmentFee"] == adjustment_float

        tip_float = float(db_res[0][5])
        assert resp["object"]["data"][0]["tip"] == tip_float

    @weeeTest.mark.list('Regression','Smoke', 'Finance')
    def test_finance_tms_sap_list(self):
        """TMS SAP Voucher tab查询  [MS:100372] """
        resp = FinanceVoucherListQuery().finance_tms_sap_list()
        voucher_id = resp["object"]["data"][0]["voucher_id"]
        sql = f"SELECT * FROM weee_finance.fs_wss_finance_voucher_payment_sap WHERE voucher_id = {voucher_id}"

        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["subject"] == db_res[0][12]

        floatValue = float(db_res[0][13])
        assert resp["object"]["data"][0]["amount"] == floatValue


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')