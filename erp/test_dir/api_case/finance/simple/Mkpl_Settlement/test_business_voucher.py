import weeeTest
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api.finance.Mkpl_Settlement.mkpl_business_voucher import MkplListQuery

class TestMkplSettlement(weeeTest.TestCase):

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_mkpl_settlement_list(self):
        """MKPL tab查询"""

        resp = MkplListQuery().mkpl_settlement_list(biz_type="MKPL")
        order_id = resp["object"]["data"][0]["orderId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_voucher_mkpl WHERE order_id = {order_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][7]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][8]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_fbw_settlement_list(self):
        """FBW tab查询"""

        resp = MkplListQuery().mkpl_settlement_list(biz_type="FBW")
        order_id = resp["object"]["data"][0]["orderId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_voucher_fbw WHERE order_id = {order_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][7]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][8]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_mfbw_settlement_list(self):
        """MFBW tab查询"""

        resp = MkplListQuery().mkpl_settlement_list(biz_type="MFBW")
        order_id = resp["object"]["data"][0]["orderId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_voucher_mfbw WHERE order_id = {order_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][7]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][8]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_storageFee_settlement_list(self):
        """Storage Fee tab查询"""

        resp = MkplListQuery().storageFee_list()
        seller_id = resp["object"]["data"][0]["sellerId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_storage WHERE seller_id = {seller_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][1]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][2]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_vas_settlement_list(self):
        """VAS tab查询"""

        resp = MkplListQuery().vas_list()
        seller_id = resp["object"]["data"][0]["sellerId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_vas WHERE seller_id = {seller_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][1]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][2]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_csbw_settlement_list(self):
        """CSBW tab查询"""

        resp = MkplListQuery().csbw_list()
        seller_id = resp["object"]["data"][0]["sellerId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_indirect WHERE seller_id = {seller_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][1]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][2]

    @weeeTest.mark.list('Regression', 'SRM', 'Smoke', 'Finance')
    def test_roReturn_settlement_list(self):
        """RO Return tab查询"""

        resp = MkplListQuery().roReturn_list()
        seller_id = resp["object"]["data"][0]["sellerId"]
        sql = f"SELECT * FROM weee_finance.fs_wss_business_vas WHERE seller_id = {seller_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert resp["object"]["data"][0]["sellerId"] == db_res[0][1]
        assert resp["object"]["data"][0]["sellerTitle"] == db_res[0][2]



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
