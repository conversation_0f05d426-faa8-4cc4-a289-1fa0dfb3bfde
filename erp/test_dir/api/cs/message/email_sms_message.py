
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header



class EmailSmsMessage(weeeTest.TestCase):

    def email_sms_message_template_list(self, data):
        self.post(url="/central/cs/mail/queryList", json=data, headers=header)
        return self.response

    def select_email_message_list(self, data):
        self.post(url="/central/cs/message/mailList", json=data, headers=header)
        return self.response

    def select_sms_message_list(self, data):
        self.post(url="/central/cs/message/smsList", json=data, headers=header)
        return self.response

    def send_email_message(self, data):
        self.post(url="/central/cs/mail/sendEmail", json=data, headers=header)
        return self.response

    def send_sms_message(self, data):
        self.post(url="/central/cs/message/sendMessage", json=data, headers=header)
        return self.response

    def add_email_message_template(self, data):
        self.post(url="/central/cs/mail/addMialTemplate", json=data, headers=header)
        return self.response

    def update_email_sms_message_template(self, data):
        self.post(url="/central/cs/mail/updateMialTemplate", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'























