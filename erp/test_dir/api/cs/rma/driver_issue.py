import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class DriverIssue(weeeTest.TestCase):

    def select_driver_issue_list(self, data):
        self.post(url="/central/cs/claim/queryList", json=data, headers=header)
        return self.response

    def add_driver_issue(self, data):
        self.post(url="/central/cs/claim/save", json=data, headers=header)
        return self.response

    def edit_driver_issue(self, data):
        self.post(url="/central/cs/claim/edit", json=data, headers=header)
        return self.response

    def driver_issue_detail(self, data):
        self.get(url="/central/cs/claim/detail", params=data, headers=header)
        return self.response

    def approve_driver_issue(self, data):
        self.post(url="/central/cs/claim/approvalClaim", json=data, headers=header)
        return self.response

    def decline_driver_issue(self, data):
        self.post(url="/central/cs/claim/edit", json=data, headers=header)
        return self.response

    def recall_driver_issue(self, data):
        self.get(url="/central/cs/claim/recallClaim", params=data, headers=header)
        return self.response

    def void_driver_issue(self, data):
        self.get(url="/central/cs/claim/voidClaim", params=data, headers=header)
        return self.response

    def driver_issue_log(self, data):
        self.get(url="/central/cs/claim/log", params=data, headers=header)
        return self.response

    def get_invoice_drivers_issue(self, data):
        self.get(url="/central/cs/claim/invoiceDetail", params=data, headers=header)
        return self.response

    def get_drivers_info(self):
        self.get(url="/central/cs/claim/drivers", headers=header)
        return self.response

    def get_sale_org_list(self):
        self.get(url="/central/cs/addressBlackList/selectSaleOrgList", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'












