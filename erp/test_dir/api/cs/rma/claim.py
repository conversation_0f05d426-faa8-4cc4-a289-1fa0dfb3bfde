import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class ClaimThirdParty(weeeTest.TestCase):

    def select_claim_third_list(self, data):
        self.post(url="/central/cs/claim/tp/findList", json=data, headers=header)
        return self.response

    def create_claim_third(self, data):
        self.post(url="/central/cs/claim/tp/create", json=data, headers=header)
        return self.response

    def edit_claim_third(self, data):
        self.post(url="/central/cs/claim/tp/edit", json=data, headers=header)
        return self.response

    def modify_claim_third(self, data):
        self.post(url="/central/cs/claim/tp/modifyStatus", json=data, headers=header)
        return self.response

    def assign_claim_third_list(self, data):
        self.post(url="/central/cs/claim/tp/updateOperatorId", json=data, headers=header)
        return self.response

    def batch_edit_status_claim_third(self, data):
        self.post(url="/central/cs/claim/tp/batchEditStatus", json=data, headers=header)
        return self.response

    def claim_third_detail(self, data):
        self.get(url=f"/central/cs/claim/tp/edit/{data}", headers=header)
        return self.response

    def get_note_claim_third(self, data):
        self.get(url=f"/central/cs/claim/tp/getNote/{data}", headers=header)
        return self.response

    def edit_note_claim_third(self, data):
        self.post(url=f"/central/cs/claim/tp/addNote/", json=data, headers=header)
        return self.response

    def get_file_claim_third(self, data):
        self.get(url=f"/central/cs/claim/tp/edit/file/{data}", headers=header)
        return self.response

    def get_items_claim_third(self, data):
        self.get(url=f"/central/cs/claim/tp/queryClaimItems/{data}", headers=header)
        return self.response

    def num_count_claim_third_status(self, data):
        self.post(url="/central/cs/claim/tp/count", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'









