
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class UserPoints(weeeTest.TestCase):

    def select_user_points_summary(self, data):
        self.get(url=f"/central/cs/userPoints/summary/{data}", headers=header)
        return self.response

    def user_points_available_list(self, data):
        self.post(url="/central/cs/userPoints/availableList", json=data, headers=header)
        return self.response

    def user_points_log(self, data):
        self.post(url="/central/cs/userPoints/pointsLog", json=data, headers=header)
        return self.response

    def user_points_transaction_detail(self, data):
        self.post(url="/central/cs/userPoints/detailList", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = UserPoints().select_user_points_summary(data="")
    print(resp)















