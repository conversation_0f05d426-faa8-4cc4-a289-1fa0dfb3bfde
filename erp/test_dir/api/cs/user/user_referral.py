
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class UserReferral(weeeTest.TestCase):

    def user_referral_list(self, data):
        self.post(url="/central/cs/userReferral/queryUserInviteRelationPage", json=data, headers=header)
        return self.response

    def user_referral_count(self, data):
        self.post(url="/central/cs/userReferral/countUserReferral", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'













