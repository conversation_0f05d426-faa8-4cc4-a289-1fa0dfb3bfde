
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class UserCoupon(weeeTest.TestCase):

    def select_coupon_list(self, data):
        self.post(url="/central/cs/userCoupon/queryUserCouponPage", json=data, headers=header)
        return self.response

    def disable_user_coupon(self, data):
        self.post(url="/central/cs/userCoupon/cancelCoupon", json=data, headers=header)
        return self.response

    def coupon_log(self, data):
        self.post(url="/central/cs/userCoupon/couponLog", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = UserCoupon().select_coupon_list(data={"pageNum":1,"pageSize":20,"user_id":"7169981"})
    print(resp)









