
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class UserSelect(weeeTest.TestCase):

    def user_list_select(self, data):
        self.post(url=f"/central/cs/user/queryUserList", json=data, headers=header)
        return self.response

    def user_detail(self, data):
        self.get(url=f"/central/cs/user/getUserDetail/{data}", headers=header)
        return self.response

    def check_user_delete(self, data):
        self.get(url=f"/central/cs/user/checkUserDelete/{data}", headers=header)
        return self.response

    def user_points_summary(self, data):
        self.get(url=f"/central/cs/userPoints/summary/{data}", headers=header)
        return self.response

    def user_related_count(self, data):
        self.get(url=f"/central/cs/user/queryRelatedCount/{data}", headers=header)
        return self.response

    def user_tag(self, data):
        self.get(url=f"/central/cs/user/getUserTag/{data}", headers=header)
        return self.response

    def add_user_switch_tag(self, data):
        self.post(url="/central/cs/userTag/switchTag", headers=header)
        return self.response

    def user_summary(self, data):
        self.get(url="/central/cs/user/getUserSummary", params=data, headers=header)
        return self.response

    def user_stolen_list(self, data):
        self.post(url="/central/cs/user/queryStolenList", json=data, headers=header)
        return self.response

    def user_log_list(self, data):
        self.post(url="/central/cs/userLog/queryLogList", json=data, headers=header)
        return self.response

    def user_note_list(self, data):
        self.post(url="/central/cs/userLog/queryLogList", json=data, headers=header)
        return self.response

    def user_related_account_list(self, data):
        self.post(url=f"/central/cs/user/queryRelatedAccountList", json=data, headers=header)
        return self.response

    def add_user_related_account(self, data):
        self.post(url="/central/cs/user/addRelatedUser", json=data, headers=header)
        return self.response

    def user_address_list(self, data):
        self.post(url="/central/cs/address/queryAddressList", json=data, headers=header)
        return self.response

    def user_address_detail(self, data):
        self.get(url=f"/central/cs/address/queryAddressDetail/{data}", headers=header)
        return self.response

    def edit_user_address_book(self, data):
        self.post(url="/central/cs/address/editUserAddress", json=data, headers=header)
        return self.response

    def user_order_summary(self, data):
        self.get(url=f"/central/cs/memberSummary/querySummary/{data}", headers=header)
        return self.response

    def user_risk_list(self, data):
        self.get(url=f"/central/cs/user/queryUserRiskList/{data}", headers=header)
        return self.response

    def user_note(self, data):
        self.post(url="/central/cs/user/note", json=data, headers=header)
        return self.response

    def save_user_comment(self, data):
        self.post(url="/central/cs/user/saveUserComment", json=data, headers=header)
        return self.response

    def user_fp_note(self, data):
        self.post(url="/central/cs/userNote/queryList", json=data, headers=header)
        return self.response

    def add_user_fp_note(self, data):
        self.post(url="/central/cs/userNote/add", json=data, headers=header)
        return self.response

    def delete_user_fp_note(self, data):
        self.get(url=f"/central/cs/userNote/delete/{data}", headers=header)
        return self.response

    def edit_phone_num(self, data):
        self.post(url="/central/cs/user/editPhoneNum", json=data, headers=header)
        return self.response

    def cancel_phone_num(self, data):
        self.post(url="/central/cs/user/cancelPhoneNum", json=data, headers=header)
        return self.response

    def user_unbind_email(self, data):
        self.post(url="/central/cs/user/unbindEmail/", json=data, headers=header)
        return self.response

    def user_edit_email(self, data):
        self.post(url="/central/cs/user/editEmail", json=data, headers=header)
        return self.response

    def cancel_user(self, data):
        self.post(url="/central/cs/user/cancelUser", json=data, headers=header)
        return self.response

    def batch_cancel_user(self, data):
        self.post(url="/central/cs/user/batchCancelUser", json=data, headers=header)
        return self.response

    def recover_user(self, data):
        self.post(url="/central/cs/user/recoverUser", json=data, headers=header)
        return self.response

    def block_user(self, data):
        self.get(url="/central/cs/user/block/{data}", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = UserSelect().user_log_list(data={"pageNum":1,"pageSize":20,"userId":"7169981"})
    print(resp)





