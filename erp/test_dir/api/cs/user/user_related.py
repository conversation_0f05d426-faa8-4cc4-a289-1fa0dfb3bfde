import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class UserRelated(weeeTest.TestCase):

    def user_related_account_list(self, data):
        self.post(url="/central/cs/relatedUser/queryRelatedAccountList", json=data, headers=header)
        return self.response

    def add_user_related_account(self, data):
        self.post(url="/central/cs/relatedUser/addRelatedUser", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    print(UserRelated().user_related_account_list(data={"pageNum": 1, "pageSize": 10, "userId": "7169981"}))








