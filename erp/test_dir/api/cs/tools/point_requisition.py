import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header



class PointRequisition(weeeTest.TestCase):

    def select_point_requisition_list(self, data):
        self.post(url="/central/cs/pointsRequisition/queryPointsRequisitionPage", json=data, headers=header)
        return self.response

    def get_point_requisition_detail(self, data):
        self.get(url=f"/central/cs/pointsRequisition/getPointsRequisitionDetail/{data}", headers=header)
        return self.response

    def add_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/addPointsRequisition", json=data, headers=header)
        return self.response

    def edit_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/editPointsRequisition", json=data, headers=header)
        return self.response

    def approve_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/approvePointsRequisition", json=data, headers=header)
        return self.response

    def batch_approve_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/batchApprovePointsRequisition", json=data, headers=header)
        return self.response

    def void_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/voidPointsRequisition", json=data, headers=header)
        return self.response

    def decline_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/declinePointsRequisition", json=data, headers=header)
        return self.response

    def batch_decline_point_requisition(self, data):
        self.post(url="/central/cs/pointsRequisition/batchDeclinePointsRequisition", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # resp = PointRequisition().select_point_requisition_list(data={"pageNum":1, "pageSize":10})
    resp = PointRequisition().get_point_requisition_detail(data=1936)
    print(resp)
