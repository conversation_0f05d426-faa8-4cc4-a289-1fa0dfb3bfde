
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header



class EmailSubscription(weeeTest.TestCase):

    def select_email_subscription_detail(self, data):
        self.post(url="/central/cs/user/queryMailSub", json=data, headers=header)
        return self.response

    def cancel_email_subscription(self, data):
        self.post(url="/central/cs/user/cancelMailSub", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'








