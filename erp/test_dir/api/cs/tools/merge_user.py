import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class MergeUser(weeeTest.TestCase):
    def target_user_info(self,data):
        """获取目标用户合并信息"""
        self.get(url=f"/central/cs/userMerge/getInfo/{data}", headers=header)
        return self.response

    def source_user_info(self, data):
        """获取目标用户合并信息"""
        self.get(url=f"/central/cs/userMerge/getInfo/{data}", headers=header)
        return self.response
if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'