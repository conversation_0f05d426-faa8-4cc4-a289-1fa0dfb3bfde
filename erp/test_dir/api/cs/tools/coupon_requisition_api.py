
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header

import datetime


class CouponRequisition(weeeTest.TestCase):

    def select_coupon_requisition_list(self, data):
        """
        优惠券申请列表查询
        :param data:
        :return:
        """
        self.post(url="/central/cs/couponRequisition/queryPage", json=data, headers=header)
        return self.response

    def get_coupon_requisition_detail(self, data):
        """
        优惠券申请详情页查询
        :param data:
        :return:
        """
        self.get(url=f"/central/cs/couponRequisition/getDetail/{data}", headers=header)
        return self.response

    def add_coupon_requisition(self, data):
        """
        新增优惠券申请
        :param data:
        :return:
        """
        start_time = datetime.datetime.today().strftime("%Y-%m-%d %H:%M:%S")
        end_time = (datetime.datetime.today() + datetime.timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
        json_data = {
            "couponType": "D",
            "bizTypeList": [
                "weee"
            ],
            "amountLimit": 35,
            "discount": 5,
            "titleDescrip": "买满$35立减$5\n買滿$35立減$5\n$5 off for purchase over $35\n$5 de descuento en $35\n$35 이상 주문 시 $5 할인\n35ドル以上の購入で5ドルオフ",
            "timeRange": [
                start_time,
                end_time
            ],
            "userIds": "7164848",
            "applyReasonCode": "14",
            "applyReason": "QA Reason",
            "channel": "1"
        }
        json_data.update(data)
        self.post(url="/central/cs/couponRequisitionManage/add", json=json_data, headers=header)
        return self.response

    def edit_coupon_requisition(self, data):
        """
        编辑优惠券申请
        :param data:
        :return:
        """
        json_data = {
            "couponType": "D",
            "bizTypeList": [
                "weee"
            ],
            "channel": "1",
            "caseNumber": None,
            "amountLimit": 35,
            "discount": 5,
            "timeRange": [
                "2024-08-15 08:00:00",
                "2024-08-30 07:59:59"
            ],
            "userIds": "7164848",
            "applyReasonCode": "14",
            "applyReason": "QA Reason",
            "titleDescrip": "买满$35立减$5\n買滿$35立減$5\n$5 off for purchase over $35\n$5 de descuento en $35\n$35 이상 주문 시 $5 할인\n35ドル以上の購入で5ドルオフ",
            "title": None,
            "isOwner": 1,
            "id": "17026"
        }
        json_data.update(data)
        self.post(url="/central/cs/couponRequisitionManage/edit", json=json_data, headers=header)
        return self.response

    def approve_coupon_requisition(self, data):
        """
        审批优惠券申请
        :param data: 优惠券申请ID
        :return:
        """
        self.get(url=f"/central/cs/couponRequisitionManage/approve/{data}", headers=header)
        return self.response

    def batch_approve_coupon_requisition(self, data):
        """
        批量审批优惠券申请
        :param data:
        :return:
        """
        re_data = {
            "idList": [
                17025
            ]
        }
        re_data.update(data)
        self.post(url="/central/cs/couponRequisitionManage/batchApprove", json=re_data, headers=header)
        return self.response

    def void_coupon_requisition(self, data):
        """
        Void优惠券申请
        :param data:
        :return:
        """
        re_data = {
            "id": None
        }
        re_data.update(data)
        self.post(url="/central/cs/couponRequisitionManage/void", json=re_data, headers=header)
        return self.response

    def decline_coupon_requisition(self, data):
        """
        Decline优惠券申请
        :param data:
        :return:
        """
        re_data = {
            "id": None,
            "memo": "Decline"
        }
        re_data.update(data)
        self.post(url="/central/cs/couponRequisitionManage/decline", json=re_data, headers=header)
        return self.response

    def batch_decline_coupon_requisition(self, data):
        """
        批量Decline优惠券申请
        :param data:
        :return:
        """
        re_data = {
            "idList": [
                17015
            ],
            "memo": "QA"
        }
        self.post(url="/central/cs/couponRequisitionManage/batchDecline", json=re_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = CouponRequisition().add_coupon_requisition(data={})









