
import os
import yaml


class ReadYamlUtils:


    def get_obj_path(self):
        """
        获取项目路径所在的根目录
        :return:
        """
        return os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

    def read_yaml(self, yaml_path, key):
        """
        读取yaml文件内的数据
        :param yaml_path: yaml文件的路径
        :key: yaml文件的值
        :return:
        """
        with open(self.get_obj_path() + yaml_path, mode="r", encoding="utf-8") as f:
            value = yaml.load(stream=f, Loader=yaml.FullLoader)
            return value[key]

    def write_yaml(self, yaml_path, write_data):
        """
        数据写入yaml文件中
        :param yaml_path: yaml文件路径
        :param write_data: 写入数据
        :return:
        """
        with open(self.get_obj_path() + yaml_path, encoding="utf-8", mode="a+") as f:
            yaml.dump(write_data, stream=f, allow_unicode=True)

    def clear_yaml(self, yaml_path):
        """
        清空yaml文件中的数据
        :param yaml_path: yaml文件路径
        :return:
        """
        with open(self.get_obj_path() + yaml_path, encoding="utf-8", mode="w") as f:
            f.truncate()


if __name__ == '__main__':
    # print(get_obj_path())
    print(ReadYamlUtils().read_yaml(r"\test_data\cs\scene\login\login.yaml", "login"))
    # print(ReadYamlUtils().read_yaml(r"/test_data/cs/scene/access_token.yaml"))


















