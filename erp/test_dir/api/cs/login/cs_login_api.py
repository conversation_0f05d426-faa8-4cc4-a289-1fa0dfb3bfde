import weeeTest
from weeeTest import weeeConfig


class CSLoginApi(weeeTest.TestCase):

    def cs_login(self, data):
        """CS账号登录"""
        json_data = {
                        "account": "",
                        "password": "",
                        "realm_key": "ecommerce"
                    }
        json_data.update(data)
        self.post(url="/hub/auth/user/login", json=json_data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
