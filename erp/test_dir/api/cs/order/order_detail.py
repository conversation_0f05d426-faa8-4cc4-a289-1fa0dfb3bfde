import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class OrderDetail(weeeTest.TestCase):
    def cs_order_list(self, data):
        """根据用户ID查询"""
        self.post(url="/central/cs/salesOrder/queryList", json=data, headers=desk_header)
        return self.response

    def order_cancel_order(self, data):
        """取消订单接口"""
        self.post(url=f"/central/cs/orderManage/batchCancelOrder", json=data, headers=desk_header)
        return self.response

    def cs_order_detail(self, data):
        """order detail"""
        self.get(url=f"/central/cs/salesOrder/queryDetailInfo/{data}", headers=desk_header)
        return self.response

    def order_insights_list(self, data):
        """order insights list"""
        self.post(url=f"/central/cs/orderInsights/list", json=data, headers=desk_header)
        return self.response

    def cs_order_detail_query_product_list(self, data):
        """order detail 查询product list"""
        self.get(url=f"/central/cs/salesOrder/queryProductList/{data}/default", headers=desk_header)
        return self.response

    def cs_order_detail_query_refund_count(self, data):
        """order detail 查询refund count"""
        self.get(url=f"/central/cs/salesOrder/queryRefundCount/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_query_log(self, data):
        """order detail 查询log"""
        self.get(url=f"/central/cs/suspiciousOrder/queryLog/{data}", headers=desk_header)
        return self.response

    def cs_order_promotion(self, data):
        """查询order 促销信息"""
        self.get(url=f"/central/cs/orderPromotion/queryInfo/{data}", headers=desk_header)
        return self.response

    def query_device_info(self, data):
        """查询设备信息"""
        self.get(url=f"/central/cs/salesOrder/queryDeviceInfo/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_query_same_payment(self, data):
        """order detail 查询same payment"""
        self.get(url=f"/central/cs/salesOrder/querySamePayment/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_query_request_list(self, data):
        """order detail 查询相关售后申请"""
        self.get(url=f"/central/cs/salesOrder/queryRequestList/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_query_order_related_case(self, data):
        """order detail 查询相关case"""
        self.get(url=f"/central/cs/salesOrder/orderRelatedCaseList/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_get_user_tag(self, data):
        """order detail 获取用户标签"""
        self.get(url=f"/central/cs/userTag/getUserTag/{data}", headers=desk_header)
        return self.response

    def cs_order_detail_user_note(self, data):
        """order detail 查询用户备注"""
        self.post(url="/central/cs/userNote/queryList", json=data, headers=desk_header)
        return self.response

    def cs_order_detail_blacklist_exisblack(self, data):
        """order detail 查询用户是否有被黑名单拦截"""
        self.post(url="/central/cs/blackList/existBlack", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'