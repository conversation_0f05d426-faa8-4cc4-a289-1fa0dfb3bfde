import weeeTest
from weeeTest import weeeConfig
# from test_dir.api.auth import header
from erp.test_dir.api_case import desk_header


class OrderSelect(weeeTest.TestCase):

    def order_list_select(self, data):
        """查询订单的列表信息"""
        self.post(url=f"/central/cs/salesOrder/queryList", json=data, headers=desk_header)
        return self.response


    def order_detail(self, data):
        """查询订单的详情信息"""
        self.get(url=f"/central/cs/salesOrder/queryDetailInfo/{data}", headers=desk_header)
        return self.response

    def order_summary(self, data):
        """查询订单的汇总信息"""
        self.get(url=f"/central/cs/salesOrder/querySummary/{data}", headers=desk_header)
        return self.response

    def order_amount(self, data):
        """查询订单的Amount信息"""
        self.get(url=f"/central/cs/salesOrder/queryOrderAmount/{data}", headers=desk_header)
        return self.response

    def order_payment_info(self, data):
        """查询订单的支付信息"""
        self.get(url=f"/central/cs/salesOrder/queryPaymentInfo/{data}", headers=desk_header)
        return self.response

    def order_return_list(self, data):
        """查询订单关联的退款信息"""
        self.get(url=f"/central/cs/salesOrder/queryRefundList/{data}", headers=desk_header)
        return self.response

    def order_product_list(self, data):
        """查询订单关联的商品列表"""
        self.get(url=f"/central/cs/salesOrder/queryProductList/{data}/default", headers=desk_header)
        return self.response

    def order_activity_list(self, data):
        """查询订单的日志信息"""
        self.get(url=f"/central/cs/salesOrder/queryOrderActivityList/{data}", headers=desk_header)
        return self.response

    def order_promotion_info(self, data):
        """查询订单关联的优惠信息"""
        self.get(url=f"/central/cs/orderPromotion/queryInfo/{data}", headers=desk_header)
        return self.response

    def order_device_info(self, data):
        """查询订单关联的设备及IP"""
        self.get(url=f"/central/cs/salesOrder/queryDeviceInfo/{data}", headers=desk_header)
        return self.response

    def order_stripe(self, data):
        self.get(url=f"/central/cs/salesOrder/queryStripe/{data}", headers=desk_header)
        return self.response

    def query_order_note(self, data):
        """查询订单关联的备注信息"""
        self.get(url=f"/central/cs/salesOrder/queryOrderNote/{data}", headers=desk_header)
        return self.response

    def add_or_update_order_note(self, json_data):
        """新增或更新订单关联的备注信息"""
        self.post(url="/central/cs/salesOrder/updeteOrAddNote", json=json_data, headers=desk_header)
        return self.response

    def query_order_fp_note(self, json_data):
        """查询订单关联的fp备注信息"""
        self.post(url="/central/cs/userNote/queryList", json=json_data, headers=desk_header)
        return self.response

    def add_order_fp_note(self, json_data):
        """新增订单关联的fp备注信息"""
        self.post(url="/central/cs/userNote/add", json=json_data, headers=desk_header)
        return self.response

    def delete_order_fp_note(self, note_id):
        """删除订单关联的fp备注信息"""
        self.get(url=f"/central/cs/userNote/delete/{note_id}", headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = OrderSelect().order_payment_info(data=37706148)
