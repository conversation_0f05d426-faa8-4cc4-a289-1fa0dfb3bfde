import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class Dispute(weeeTest.TestCase):
    def cs_dispute(self,data):
        """新增dispute"""
        self.post(url=f"/central/cs/dispute", json=data, headers=desk_header)
        return self.response

    def query_list(self,data):
        """查询列表"""
        self.post(url=f"/central/cs/dispute/queryList", json=data, headers=desk_header)
        return self.response

    def create_WatchList_FromDispute(self,data):
        """批量加黑名单"""
        self.post(url=f"/central/cs/dispute/createWatchListFromDispute", json=data, headers=desk_header)
        return self.response

    def query_list1(self,data):
        """查询列表"""
        self.post(url=f"/central/cs/dispute/queryList", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'