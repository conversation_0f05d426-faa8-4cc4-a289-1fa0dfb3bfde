
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header



class SuspiciousOrder1(weeeTest.TestCase):

    def add_suspicious_order(self, data):
        self.post(url="/central/cs/suspiciousOrder/addSuspectedOrder", json=data, headers=desk_header)
        return self.response

    def select_suspicious_order_list(self, data):
        self.post(url="/central/cs/suspiciousOrder/queryList", json=data, headers=desk_header)
        return self.response

    def assign_suspicious_order(self, data):
        self.post(url="/central/cs/suspiciousOrder/updateOperatorId", json=data, headers=desk_header)
        return self.response

    def assign_to_me_suspicious_order(self, data):
        self.post(url="/central/cs/suspiciousOrder/updateOperatorId", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'













