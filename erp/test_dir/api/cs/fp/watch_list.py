
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class WatchList(weeeTest.TestCase):

    def select_watch_list_list(self, data):
        self.post(url="/central/cs/blackList/queryBlackListPage", json=data, headers=header)
        return self.response

    def add_watch_list(self, data):
        self.post(url="/central/cs/blackList/batchAddBlackList", json=data, headers=header)
        return self.response

    def edit_watch_list(self, data):
        self.post(url="/central/cs/blackList/batchUpdateBlackList", json=data, headers=header)
        return self.response

    def toggle_status_watch_list(self, data):
        self.post(url="/central/cs/blackList/toggleStatus", json=data, headers=header)
        return self.response

    def select_watch_list_log(self, data):
        self.post(url="/central/cs/blackList/queryBlackListMemoList", json=data, headers=header)
        return self.response

    def add_comment_watch_list(self, data):
        self.post(url="/central/cs/blackList/addMemo", json=data, headers=header)
        return self.response

    def update_file_watch_list(self, data):
        self.post(url="/central/cs/blackList/updateFileList", json=data, headers=header)
        return self.response

    def select_cancel_reason_watch_list(self):
        self.get(url="/central/cs/salesOrder/getCancelReason?orderType", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'


