import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class NewUserCouponWhitelist(weeeTest.TestCase):
    def new_user_coupon_whitelist_create(self,data):
        """新增新用户优惠券白名单"""
        self.post(url=f"/central/cs/newUserCouponWhitelist/create", json=data, headers=desk_header)
        return self.response

    def query_list(self,data):
        """查询列表"""
        self.post(url=f"/central/cs/newUserCouponWhitelist/queryList", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'