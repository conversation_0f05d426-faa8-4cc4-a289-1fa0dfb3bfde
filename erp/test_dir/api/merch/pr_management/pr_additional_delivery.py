import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PrAdditionalDelivery(weeeTest.TestCase):
    """相关配置接口"""

    def additional_delivery_list(self, data):
        """交货日历配置列表查询"""
        params_data = {
            "filter[vendor_id]": '',
            "filter[inventory_id]": '',
        }
        params_data.update(data)
        self.get(url="/central/merch/v1/config/pr/delivery/special", params=params_data, headers=header)
        return self.response

    def add_additional_delivery(self, json_data):
        """交货日历配置列表查询"""
        self.put(url="/central/merch/v1/config/pr/delivery/special", json=json_data, headers=header)
        return self.response

    def update_additional_delivery(self, json_data):
        """交货日历配置列表查询"""
        self.put(url="/central/merch/v1/config/pr/delivery/special", json=json_data, headers=header)
        return self.response

    def delete_additional_delivery(self, ids):
        """交货日历配置列表查询"""
        json_data = {
            "ids": ids,
            "config_type": "pr_special"
        }
        self.post(url="/central/merch/v1/config/pr", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
