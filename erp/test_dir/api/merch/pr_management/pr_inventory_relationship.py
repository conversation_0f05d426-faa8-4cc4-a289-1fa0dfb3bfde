import weeeTest
from weeeTest import weeeConfig, log, jmespath
from erp.test_dir.api_case import header


class PrInventoryRelationship(weeeTest.TestCase):
    """相关配置接口"""

    def inventory_relationship_list(self, data):
        """交货日历配置列表查询"""
        params_data = {
            "filter[vendor_id][0]": ''
        }
        params_data.update(data)
        self.get(url="/central/merch/v1/config/pr/relation/inventory", params=params_data, headers=header)
        return self.response

    def add_inventory_relationship(self, vendor_id='', inventory_id='', storage_type='', supply_mode='',
                                   connection_inventory_type='', connection_inventory_id=''):
        """交货日历配置列表查询"""
        json_data = {
            "vendor_id": vendor_id,
            "inventory_id": inventory_id,
            "storage_type": storage_type,
            "supply_mode": supply_mode,
            "connection_inventory_type": connection_inventory_type,
            "connection_inventory_id": connection_inventory_id
        }
        self.put(url="/central/merch/v1/config/pr/relation/inventory", json=json_data, headers=header)
        return self.response

    def update_inventory_relationship(self, json_data):
        """交货日历配置列表查询"""
        self.put(url="/central/merch/v1/config/pr/relation/inventory", json=json_data, headers=header)
        return self.response

    def delete_inventory_relationship(self, ids):
        """交货日历配置列表查询"""
        json_data = {
            "ids": ids,
            "config_type": "pr_inventory"
        }
        self.post(url="/central/merch/v1/config/pr", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
