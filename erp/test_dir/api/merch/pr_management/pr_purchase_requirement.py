import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PrPurchaseRequirement(weeeTest.TestCase):
    """预约采购模块相关接口"""

    def purchase_requirement_list(self, vendor_id=None, inventory_id=None, status=None, product_id=None):
        """PR列表查询"""
        params_data = {
            "filter[vendor_id]": vendor_id,
            "filter[inventory_id]": inventory_id,
            "filter[status]": status,
            "filter[product_id]": product_id
        }
        self.get(url="/central/merch/v1/pr/order", params=params_data, headers=header)
        return self.response

    def get_product_list(self, params_data):
        """根据供应商id获取商品列表"""
        # eg_data = {"vendor_id": "1", "inventory_id": "8",}
        self.get(url="/central/merch/v1/Pr/order/getProductList", params=params_data, headers=header)
        return self.response

    def get_config_eta_list(self, vendor_id, inventory_id, storage_type):
        """获取eta"""
        # eg_data = {"vendor_id": "1", "inventory_id": "8","type": "R"}
        self.get(url=f"/central/merch/v1/config/pr/eta/{vendor_id}/{inventory_id}/{storage_type}", headers=header)
        return self.response

    def add_purchase_requirement(self, vendor_id='', inventory_id='', product_id='', quantity='', eta_final_date=''):
        """新增或编辑采购预约"""
        json_data = {
            "vendor_id": vendor_id,
            "inventory_id": inventory_id,
            "product_id": product_id,
            "quantity": quantity,
            "eta_final_date": eta_final_date
        }
        self.put(url="/central/merch/v1/Pr/order/apiUpdateOrder", json=json_data, headers=header)
        return self.response

    def delete_purchase_requirement(self, ids):
        """删除采购预约"""
        json_data = {
            "id": ids
        }
        self.put(url="/central/merch/v1/Pr/order/apiBatchDeleteOrder", json=json_data, headers=header)
        return self.response

    def get_consolidation_pr_list(self, today_toggle=None, order_date=None, product_id=None):
        """PR consolidate列表查询"""
        params_data = {
            "filter[is_show_today_order]": today_toggle,
            "filter[order_date]": order_date,
            "filter[product_id]": product_id
        }
        self.get(url="/central/merch/v1/pr/order/getConsolidationPrList", params=params_data, headers=header)
        return self.response

    def pr_consolidate_to_po(self, pr_id=None, vendor_name='', inventory_name='', eta_final_date=''):
        """PR转PO"""
        json_data = {
            "ids": pr_id,
            "is_all": False,
            "vendor_title": vendor_name,
            "inventory_title": inventory_name,
            "eta_final_date": eta_final_date,
            "direct_import": False,
            "shipping": "0.00",
            "return_process_fee": "0.00",
            "container_offload_fee": "0.00",
            "internal_comment": "",
            "comment": "",
            "local_timezone": "Asia/Shanghai"
        }
        self.put(url="/central/merch/v1/pr/order/apiCreatePO", json=json_data, headers=header)
        return self.response

    def pr_auto_generate_po(self):
        """PR自动转PO"""
        self.put(url="/central/merch/v1/pr/order/autoCreatePOByPr/1", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # PrPurchaseRequirement().purchase_requirement_list({})
