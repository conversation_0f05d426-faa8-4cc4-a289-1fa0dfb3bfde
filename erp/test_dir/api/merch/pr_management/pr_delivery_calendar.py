import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PrDeliveryCalendar(weeeTest.TestCase):
    """预约采购交货日历相关配置接口"""

    def delivery_calendar_list(self, vendor_id=None, inventory_id=None, storage_type=None):
        """交货日历配置列表查询"""
        params_data = {
            "filter[vendor_id][0]": vendor_id,
            "filter[inventory_id][0]": inventory_id,
            "filter[storage_type][0]": storage_type
        }
        self.get(url="/central/merch/v1/config/pr/delivery/calendar", params=params_data, headers=header)
        return self.response

    def add_delivery_calendar(self, vendor_id='', inventory_id='', storage_type='', order_weekday='', frequency='',
                              lead_time=''):
        """新增交货日历配置"""
        json_data = {
            "vendor_id": vendor_id,
            "inventory_id": inventory_id,
            "storage_type": storage_type,
            "order_weekday": order_weekday,
            "frequency": frequency,
            "lead_time": lead_time
        }
        self.put(url="/central/merch/v1/config/pr/delivery/calendar", json=json_data, headers=header)
        return self.response

    def update_delivery_calendar(self, json_data):
        """更新交货日历配置"""
        self.put(url="/central/merch/v1/config/pr/delivery/calendar", json=json_data, headers=header)
        return self.response

    def delete_delivery_calendar(self, ids):
        """删除交货日历配置"""
        json_data = {
            "ids": ids,
            "config_type": "pr_calendar"
        }
        self.post(url="/central/merch/v1/config/pr", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
