import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PrCommonMethods(weeeTest.TestCase):
    """相关公共方法"""

    def assert_api_data(self, assert_data, resp):
        """公共方法场景: 结果校验"""
        try:
            return self.assert_json(assert_data, resp)
        except AssertionError as error:
            raise error

    def get_required_id(self, resp):
        """公共方法场景: 获取id for inventory/dedicated/calendar"""
        relationship_id = resp["object"]['list'][0]['id']
        return relationship_id

    def get_required_pr_id(self, resp):
        """公共方法场景: 获取id for purchase requirement"""
        relationship_id = resp["object"]['orders'][0]['id']
        return relationship_id


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
