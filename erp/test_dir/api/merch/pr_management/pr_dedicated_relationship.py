import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PrDedicatedRelationship(weeeTest.TestCase):
    """相关配置接口"""

    def dedicated_relationship_list(self, vendor_id=None, warehouse_id=None, user_id=None, storage_type=None,
                                    ethnicity=None):
        """交货日历配置列表查询"""
        params_data = {
            "filter[vendor_id][0]": vendor_id,
            "filter[warehouse_id][0]": warehouse_id,
            "filter[dedicated_user_id][0]": user_id,
            "filter[storage_type][0]": storage_type,
            "filter[ethnicity][0]": ethnicity
        }
        self.get(url="/central/merch/v1/config/pr/relation/dedicated", params=params_data, headers=header)
        return self.response

    def add_dedicated_relationship(self, vendor_id='', warehouse_id='', dedicated_user_id='', storage_type='',
                                   ethnicity=None, timezone=''):
        """交货日历配置列表新增"""
        if ethnicity is None:
            ethnicity = []
        json_data = {
            "vendor_id": vendor_id,
            "warehouse_id": warehouse_id,
            "dedicated_user_id": dedicated_user_id,
            "storage_type": storage_type,
            "ethnicity": ethnicity,
            "timezone": timezone
        }
        self.put(url="/central/merch/v1/config/pr/relation/dedicated", json=json_data, headers=header)
        return self.response

    def update_dedicated_relationship(self, json_data):
        """交货日历配置列表更新"""
        self.put(url="/central/merch/v1/config/pr/relation/dedicated", json=json_data, headers=header)
        return self.response

    def delete_dedicated_relationship(self, ids):
        """交货日历配置列表删除"""
        json_data = {
            "ids": ids,
            "config_type": "pr_dedicated"
        }
        self.post(url="/central/merch/v1/config/pr", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
