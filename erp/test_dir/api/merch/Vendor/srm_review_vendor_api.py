import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class SrmReviewVendorInterface(weeeTest.TestCase):
    """
    供应商review模块相关接口
    """

    def select_review_vendor_list(self, vendor_data):
        """
        待审核review供应商查询列表
        :param vendor_data: 查询数据
        :return:
        """
        data = {
            "offset": 0,
            "limit": 2,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[title]": None,
            "filter[rec_creator_id]": None,
            "filter[status]": None
        }
        data.update(vendor_data)
        self.get(url="/central/merch/v1/vendor/review", headers=header, params=data)
        return self.response

    def select_review_vendor_info(self, apply_id):
        """
        待审核供应商详情
        :param apply_id: 审核id,既是供应商ID
        :return:
        """
        self.get(url=f"/central/merch/v1/vendor/review/detail/{apply_id}", headers=header)
        return self.response

    def update_review_vendor_info(self, vendor_data):
        """
        编辑待审核的供应商内容
        :param vendor_data:
        :return:
        """
        data = {
            "id":"320",
            "sales_org_id":"1",
            "title":"test--2023-10-13 14:14:27.629493",
            "legal_name":"Vendor Legal Name (DBA)",
            "vendor_location":"",
            "payment_term":"30",
            "actual_term":"30",
            "billing_country":"1",
            "billing_street":"Billing street",
            "billing_city":"Billing city",
            "billing_state":"3",
            "billing_zipcode":"400000",
            "billing_address":"1207 S. Rice",
            "pickup_address":"",
            "quickbook_id": None,
            "phone_number":"400001",
            "vendor_group":"",
            "image_url":"",
            "description_html":"Description---1",
            "remark":"",
            "max_inventory_volume":"",
            "ethnicity": [],
            "is_restaurant":"N",
            "payment_method":"check",
            "owner_id":"",
            "secondary_owner_id":"",
            "restaurant_logo_url":"",
            "restaurant_short_desc":"",
            "restaurant_status":"",
            "restaurant_user_id":"0",
            "restaurant_pos":"0",
            "emails":[
                {
                    "id":"1789",
                    "apply_id":"320",
                    "email":"<EMAIL>",
                    "name":"huan",
                    "rec_create_time":"2023-10-13 06:17:06",
                    "is_remittance":0,
                    "is_edit":"false"
                }
            ],
            "catman_contact": [],
            "direct_email":"<EMAIL>",
            "account_type":"",
            "currency":"",
            "account_name":"",
            "address":"",
            "routing_number":"",
            "account_number":"",
            "iban":"",
            "swift":"",
            "source":"weee",
            "status":"D",
            "comment":"",
            "comment_type":"weee"
        }
        data.update(vendor_data)
        self.put(url="/central/vendor/v1/review/apply/updateVendorApply", json=data, headers=header)
        return self.response

    def apply_review_vendor(self, apple_data, apply_id):
        """
        Review 供应商申请(拒绝或同意)
        :param apple_data:
        :return:
        """
        data = {
            "status": None,
            "comment": None
        }
        data.update(apple_data)
        self.put(url=f"/central/vendor/v1/review/apply/{apply_id}", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'










