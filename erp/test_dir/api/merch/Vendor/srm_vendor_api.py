import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
import datetime


class SrmVendorInterface(weeeTest.TestCase):
    """
    供应商管理模块相关接口
    """

    def select_vendor_list(self, vendor_data):
        """
        查询供应商列表接口
        :param vendor_data:
        :return:
        """
        # 默认需要传参的字段
        data = {
            "offset": 0,
            "limit": 2,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[id]": "",
            "filter[title]": "",
            "filter[sales_org_id]": "",
            "filter[status]": "A",
            "filter[type]": "",
            "filter[owner_id]": "",
            "filter[secondary_owner_id]": ""
        }
        data.update(vendor_data)
        self.get(url="/central/vendor/v1/manager/vendor", headers=header, params=data)
        return self.response

    def select_vendor_info(self, vendor_id):
        """
        查询供应商详情展示
        :param vendor_id: 供应商ID
        :return:
        """
        self.get(url=f"/central/vendor/v1/manager/vendor/detail/{vendor_id}", headers=header)
        return self.response

    def create_vendor(self, vendor_data):
        """
        创建供应商接口
        :param vendor_data:
        :return:
        """
        # 默认需要传参的字段
        data = {
            "source": "weee",
            "type": "G",
            "id": "",
            "sales_org_id": "1",
            "title": "QA  Grocery_Test001",
            "legal_name": "QA  Grocery_Test001",
            "vendor_location": "SF",
            "payment_term": "30",
            "actual_term": "30",
            "billing_country": "2",
            "billing_street": "1207 S. Rice",
            "billing_city": "Hamilton",
            "billing_state": "41",
            "billing_zipcode": "76531",
            "pickup_address": "",
            "phone_number": "5876543267",
            "vendor_group": "",
            "image_url": "",
            "description_html": "Description",
            "remark": "",
            "max_inventory_volume": "",
            "ethnicity": [

            ],
            "is_restaurant": "N",
            "restaurant_logo_url": "",
            "restaurant_short_desc": "",
            "restaurant_status": "",
            "restaurant_user_id": "",
            "restaurant_pos": "",
            "emails": [
                {
                    "id": "01697161286061",
                    "index": 0,
                    "name": "baolin",
                    "email": "<EMAIL>",
                    "is_remittance": 0
                }
            ],
            "reason": "",
            "direct_email": ""
        }
        data.update(vendor_data)
        self.post(url="/central/vendor/v1/manager/vendor/save", headers=header, json=data)
        return self.response

    def update_vendor_detail(self, vendor_data):
        """
        更新供应上详情接口
        :param vendor_data:
        :return:
        """
        data = {
            "source": "weee",
            "type": "G",
            "id": "7958",
            "sales_org_id": "1",
            "title": "QA  Grocery_Test001",
            "legal_name": "QA  Grocery_Test001",
            "vendor_location": "SF",
            "payment_term": "30",
            "actual_term": "30",
            "billing_country": "2",
            "billing_street": "1207 S. Rice",
            "billing_city": "Hamilton",
            "billing_state": "41",
            "billing_zipcode": "76531",
            "billing_address": "1207 S. Rice, Hamilton, California, 76531, United States",
            "pickup_address": "",
            "phone_number": "5876543267",
            "vendor_group": None,
            "image_url": None,
            "description_html": "Description",
            "remark": "",
            "max_inventory_volume": "",
            "ethnicity": [],
            "is_restaurant": "N",
            "owner_id": 0,
            "restaurant_logo_url": None,
            "restaurant_short_desc": None,
            "restaurant_status": None,
            "restaurant_user_id": None,
            "restaurant_pos": "0",
            "inventory": [
            ],
            "emails": [
                {
                    "id": "22806",
                    "vendor_id": "7958",
                    "email": "<EMAIL>",
                    "name": "baolin",
                    "rec_create_time": "2023-10-13 01:47:21",
                    "is_remittance": "0",
                    "status": "",
                    "invite_date": "",
                    "creator_name": ""
                }
            ]
        }
        data.update(vendor_data)
        self.post(url="/central/vendor/v1/manager/vendor/save", headers=header, json=data)
        return self.response

    def update_vendor_finance(self, vendor_data):
        """
        财务更新基础数据
        :param vendor_data:
        :return:
        """
        data = {
            "id": "",
            "legal_name": "QA  Grocery_Test001",
            "primary_vender_id": "",
            "billing_country": "2",
            "billing_street": "1207 S. Rice",
            "billing_city": "Hamilton",
            "billing_state": "41",
            "billing_zipcode": "76531",
            "quickbook_id": ""
        }
        data.update(vendor_data)
        self.post(url="/central/vendor/v1/manager/vendor/finance_update", headers=header, json=data)
        return self.response

    def approve_vendor_finance(self, vendor_data):
        """
        财务审批生效供应商
        :param vendor_data:
        :return:
        """
        data = {
            "id": ""
        }
        data.update(vendor_data)
        self.post(url="/central/vendor/v1/manager/vendor/approve", headers=header, json=data)
        return self.response

    def inactive_vendor(self, vendor_data):
        """
        把供应商状态由active更改为inactive
        :param vendor_data:
        :return:
        """
        data = {
            "id": ""
        }
        data.update(vendor_data)
        self.post(url="t/central/vendor/v1/manager/vendor/inActiveVendor", headers=header, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    SrmVendorInterface().select_vendor_list(vendor_data={})
