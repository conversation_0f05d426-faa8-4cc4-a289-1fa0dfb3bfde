import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class TaskInterface(weeeTest.TestCase):

    def task_dashboard_org_tree(self):
        """Task Dashboard组织架构树"""
        # 未指定分页参数，默认查询第1页，200条
        self.get(url="/central/merch/v1/task/getOrgTree", headers=header)
        return self.response

    def task_dashboard_list(self, user_id):
        """Task Dashboard列表"""
        self.get(url=f"/central/merch/v1/task/managerList/{user_id}", headers=header)
        return self.response

    def task_management_filter(self, id='', type='', location='', keyword='', due_before='', status='', creator=''):
        """Task Dashboard列表筛选"""
        params_data = {
            "filter[id]": id,
            "filter[type][0]": type,
            "filter[location]": location,
            "filter[keyword]": keyword,
            "filter[due_before]": due_before,
            "filter[status]": status,
            "filter[rec_creator_name]": creator
        }
        self.get(url="/central/merch/v1/task/list", params=params_data, headers=header)
        return self.response

    def home_todo_option(self):
        """Todos Option"""
        self.get(url="/central/merch/v1/home/<USER>", headers=header)
        return self.response

    def home_todo_list(self):
        """Todos List"""
        self.get(url="/central/merch/v1/home/<USER>/list", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
