import weeeTest
import datetime
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class TransferOrder(weeeTest.TestCase):
    """内部订单模块相关接口"""

    def transfer_order_list(self, outbound_inventory='', inbound_inventory='', creator='', status='', sku='', order_id='', invoice_id=''):
        """调拨单列表查询"""
        params_data = {
            "filter[physical_inventory_title]": outbound_inventory,
            "filter[physical_inbound_inventory_title]": inbound_inventory,
            "filter[rec_creator_id]": creator,
            "filter[status]": status,
            "filter[sku]": sku,
            "filter[order_id]": order_id,
            "filter[invoice_id]": invoice_id,
            "order_by[column]": "id",
            "order_by[rule]": "desc"
        }
        self.get(url="/central/merch/v1/internal/internalOrder", params=params_data, headers=header)
        return self.response

    def transfer_order_detail(self, params_data):
        """调拨单详情"""
        # eg_data = {"id": 2130}
        self.get(url="/central/merch/v1/internal/internalOrder/getOrderDetail/", params=params_data, headers=header)
        return self.response

    def transfer_order_vendor_inventory_options(self, params_data):
        """调拨单供应商、仓库选项关系"""
        self.get(url="/central/merch/v1/config/pr/delivery/vendorInventoryOptions/relation", headers=header)
        return self.response

    def transfer_order_itinerary(self, params_data):
        """调拨单可用线路查询"""
        # eg_data = {
        #             "originReferenceId": 25,    # 出库仓
        #             "destinationType": "3PL",    # DC仓库需要填写
        #             "destinationReferenceId": 20    # 入库仓
        #             }
        self.get(url="/scm/lhms/api/v1/itinerary/reference", params=params_data, headers=header)
        return self.response

    def transfer_order_inventory_list(self):
        """调拨单可用仓库列表"""
        self.get(url="/central/merch/v1/internal/internalOrder/getInternalOrderInventoryList", headers=header)
        return self.response

    def transfer_order_creator_list(self):
        """调拨单创建人员列表"""
        self.get(url="/central/merch/v1/internal/internalOrder/getRecreatorList", headers=header)
        return self.response

    def create_transfer_order(self, order_id='', outbound_inventory='', inbound_inventory='', email='', pickup_date='',
                              eta_date='', comment=''):
        """创建/编辑调拨单"""
        # ed_data = {
        #             "physical_inventory_id": "",  # 出库仓
        #             "physical_inbound_inventory_id": "",  # 入库仓
        #             "email": "",     # 邮箱
        #             "delivery_date": "2023-08-10",      # 拣货日期
        #             "eta_date": "2023-08-11",       # 预计到达日期
        #             "comment": "QA 测试数据"
        #              "id": "1968",    # 调拨单id，更新时使用
        #            }
        json_data = {
            "id": order_id,
            "physical_inventory_id": outbound_inventory,
            "physical_inbound_inventory_id": inbound_inventory,
            "email": email,
            "delivery_date": pickup_date,
            "eta_date": eta_date,
            "comment": comment
        }
        self.put(url="/central/merch/v1/internal/internalOrder/apiOrder", json=json_data, headers=header)
        return self.response

    def external_create_transfer_order(self, json_data):
        """外部创建调拨单"""
        self.put(url="/central/merch/v1/internal/internalOrder/apiBulkInsertOrderAndLines", json=json_data,
                 headers=header)
        return self.response

    def transfer_order_add_product_line(self, order_id='', product_id='', quantity=''):
        """调拨单添加商品line"""
        json_data = {
            "order_id": order_id,
            "product": [
                {
                    "product_id": product_id,
                    "quantity": quantity,
                }
            ]
        }
        self.put(url="/central/merch/v1/internal/internalOrder/apiOrderLine", json=json_data, headers=header)
        return self.response

    def get_transfer_order_line(self, params_data):
        """获取调拨单的商品line"""
        self.get(url="/central/merch/v1/internal/internalOrder/getOrderLine", params=params_data, headers=header)
        return self.response

    def transfer_order_product_list(self, params_data):
        """调拨单添加商品查询"""
        # eg_data = {"id": 2074,      # 调拨单ID
        #            "keyword": 5472  # 商品SKU ID
        #            }
        self.get(url="/central/merch/v1/internal/internalOrder/getOrderProductList", params=params_data, headers=header)
        return self.response

    def transfer_order_send_email(self, json_data):
        """发送调拨单详情邮件"""
        # eg_data = {
        #             "id": 333   # 调拨单id
        #           }
        self.put(url="/central/merch/v1/internal/internalOrder/apiSendInvoiceToEmail", json=json_data, headers=header)
        return self.response

    def transfer_order_cancel(self, data):
        """取消调拨单"""
        json_data = {
            "id": data
        }
        self.put(url="/central/merch/v1/internal/internalOrder/apicancelorder", json=json_data, headers=header)
        return self.response

    def transfer_order_send_wms(self, order_id):
        """提交调拨单"""
        json_data = {
            "id": order_id
        }
        self.post(url="/central/merch/v1/internal/internalOrder/submitOrder", json=json_data, headers=header)
        return self.response

    def transfer_order_mark_out(self, json_data):
        """调拨单出库"""
        # eg_data = {
        #             "id": 333   # 调拨单id
        #           }
        self.post(url="/central/merch/v1/internal/internalOrder/markOut", json=json_data, headers=header)
        return self.response

    def transfer_order_change_quantity(self, json_data):
        """调拨单修改商品数量"""
        # eg_data = {
        #     "quantity": 2,
        #     "product_id": "90191",
        #     "id": "433"
        # }
        self.put(url="/central/merch/v1/internal/internalOrder/apiChangeQuantity", json=json_data, headers=header)
        return self.response

    def transfer_order_change_quantity_per_unit(self, json_data):
        """调拨单修改商品箱规"""
        # eg_data = {
        #     "id": "2074",
        #     "product_id": "42",
        #     "quantity_per_unit": 40
        # }
        self.post(url="/central/merch/v1/internal/internalOrder/apiChangeQuantityPerUnit", json=json_data,
                  headers=header)
        return self.response

    def transfer_order_attachment_detail(self, params_data):
        """调拨单附件详情"""
        # eg_data = {
        #             "id": 10   # 附件表id
        #           }
        self.post(url="/central/merch/v1/internal/internalOrder/getAttachment", json=params_data, headers=header)
        return self.response

    def transfer_order_add_attachment(self, params_data):
        """调拨单附件新增/编辑"""
        self.post(url="/central/merch/v1/internal/internalOrder/editAttachment", json=params_data, headers=header)
        return self.response

    def transfer_order_delete_attachment(self, id=''):
        """调拨单附件删除"""
        json_data = {
                    "id": id   # 附件表id
                  }
        self.put(url="/central/merch/v1/internal/internalOrder/deleteAttachment", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
