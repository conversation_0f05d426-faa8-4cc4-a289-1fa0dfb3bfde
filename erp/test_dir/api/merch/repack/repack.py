import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class RepackManagement(weeeTest.TestCase):
    """repack相关接口"""

    def get_repack_list(self, data):
        """查询repack关系"""
        params_data = {
            "offset": 0,
            "limit": 20,
            "filter[inventory_id]": '',
            "filter[source_product_id]": '',
            "filter[target_product_id]": ''
        }
        params_data.update(data)
        self.get(url="/central/merch/v1/pi/productRepack/list", params=params_data, headers=header)
        return self.response

    def get_repack_detail(self, id):
        """查询repack关系"""
        url = '/central/merch/v1/pi/productRepack/' + id + '/edit'
        self.get(url=url, headers=header)
        return self.response

    def create_update_repack(self, data):
        """更新repack关系"""
        json_data = {
            "id": '',
            "source_product_id": "106001",
            "target_product_id": "105988",
            "source_product_quantity": "2",
            "target_product_quantity": "4",
            "repack_inventory_ids": ["25", "29"]
        }
        json_data.update(data)
        self.post(url="/central/merch/v1/pi/productRepack", json=json_data, headers=header)
        return self.response

    def delete_repack(self, id):
        url = '/central/merch/v1/pi/productRepack/' + id
        self.delete(url=url, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
