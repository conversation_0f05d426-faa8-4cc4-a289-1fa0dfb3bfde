import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class ProductWarehouseInterface(weeeTest.TestCase):

    def product_warehouse_list(self):
        """PW列表"""
        # 未指定分页参数，默认查询第1页，200条
        self.get(url="/central/merch/v1/pi/pw/getPwList?page=1&pageSize=20", headers=header)
        return self.response

    def product_warehouse_filter(self, filter=''):
        """PW列表查询-url中拼接条件"""
        # filter案例：&filter%5BscaId%5D=13347083
        self.get(url=f"/central/merch/v1/pi/pw/getPwList?{filter}", headers=header)
        return self.response

    def product_warehouse_filter_group(self, page='', pageSize='', productId='', purchaseStatus='',
                                       purchaseSubStatus='', purchaseMethod='',
                                       assortmentRole='', assortmentSubRole='', isPurchase='', physicalInventoryId='',
                                       primaryVendor='', secondaryVendor='', scaId=''):
        """PW列表组合查询"""
        params_data = {
            "page": page,
            "pageSize": pageSize,
            "filter[productId]": productId,
            "filter[purchaseStatus][0]": purchaseStatus,
            "filter[purchaseStatus][1]": purchaseSubStatus,
            "filter[purchaseMethod]": purchaseMethod,
            "filter[assortmentRole][0]": assortmentRole,
            "filter[assortmentRole][1]": assortmentSubRole,
            "filter[isPurchase]": isPurchase,
            "filter[physicalInventoryId]": physicalInventoryId,
            "filter[primaryVendor]": primaryVendor,
            "filter[secondaryVendor]": secondaryVendor,
            "filter[scaId]": scaId
        }
        self.get(url="/central/merch/v1/pi/pw/getPwList", params=params_data, headers=header)
        return self.response

    def product_warehouse_detail(self, pw_id):
        """PW详情页"""
        self.get(url=f"/central/merch/v1/pi/pw/getPw?id={pw_id}", headers=header)
        return self.response

    def product_warehouse_edit(self, id='', purchaseStatus='', purchaseCode='', purchaseMethod='', primaryVendor='',
                               secondaryVendor='', scaId='', outl='', assortmentRole=''):
        """PW编辑"""
        json_data = {
            "id": id,
            "purchaseStatus": purchaseStatus,
            "purchaseCode": purchaseCode,
            "purchaseMethod": purchaseMethod,
            "primaryVendor": primaryVendor,
            "secondaryVendor": secondaryVendor,
            "scaId": scaId,
            "outl": outl,
            "autoResPool": "N",
            "isPurchase": "N",
            "assortmentRole": assortmentRole,
            "comment": "auto"

        }
        self.put(url="/central/merch/v1/pi/pw/apiUpdatePw", headers=header, json=json_data)
        return self.response

    def product_warehouse_batch_update(self, json_data):
        """批量更新PW信息"""
        self.put(url="/central/merch/v1/pi/pw/apiBatchUpdatePwField", headers=header, json=json_data)
        return self.response

    def product_warehouse_batch_update_status(self, json_data):
        """批量更新Purchase Status"""
        self.put(url="/central/merch/v1/pi/pw/apiBatchUpdatePwStatusField", headers=header, json=json_data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
