import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
import random


class ProductInfo(weeeTest.TestCase):
    """商品模块相关接口"""

    def product_list(self, data):
        """商品列表查询"""
        params_data = {
            "filter[weee_buyer_id]": data,
        }
        self.get(url="/central/merch/v1/pi/product", params=params_data, headers=header)
        return self.response

    def product_filter(self):
        """商品页的过滤内容"""
        self.get(url="/central/merch/v1/pi/product/filter", headers=header)
        return self.response

    def product_detail(self, sku_id):
        """商品详情页"""
        self.get(url=f"/central/merch/v1/pi/product/detail/{sku_id}/edit", headers=header)
        return self.response

    def add_product(self, short_title_en='', short_title='', weee_buyer_id='', ethnicity='', storage_temperate_zone='',
                    division_num='', catalogue_num='', sub_catalogue_num='', product_id='', upc_code=''):
        """新增/编辑商品"""
        image_values = [
            "https://img06.weeecdn.com/product/image/704/917/1C05F407A86E669.jpeg",
            "https://img06.weeecdn.com/product/image/879/714/762BB80F79A56D78.jpeg",
            "https://img06.weeecdn.com/product/image/486/449/2107724E7F2F6F75.png",
            "https://img06.weeecdn.com/product/image/702/824/6D4B550FE15DB307.jpeg",
            "https://img06.weeecdn.com/product/image/456/487/5797F7F7DA8643A3.png",
            "https://img06.weeecdn.com/product/image/253/329/37D77272B0B7E948.png"
        ]
        photo_url = random.choice(image_values)
        json_data = {
            "short_title_en": short_title_en,
            "short_title": short_title,
            "weee_buyer_id": weee_buyer_id,
            "ethnicity": ethnicity,
            "ethnic_tags": [],
            "storage_temperate_zone": storage_temperate_zone,
            "division_num": division_num,
            "catalogue_num": catalogue_num,
            "sub_catalogue_num": sub_catalogue_num,
            "capacity": "",
            "keyword": "",
            "UPC_code": upc_code,
            "product_type": "",
            "ethnic_title": "",
            "brand_key": "BwhpDddW",
            "product_area": "5",
            "multiple_pack": "N",
            "unit": "G",
            "unit_min": 100,
            "unit_max": "",
            "chemical_substances": "",
            "tags": "",
            "is_gift_product": "",
            "is_tradein_forbid": "",
            "vender_id": "1",
            "purchase_price": 22,
            "price": 33,
            "vendor_product_title": "Vendor Product Title",
            "purchase_unit": "unit",
            "quantity_per_unit": 1,
            "vendor_SKU_code": "Vendor SKU Code",
            "season_code_start_week": "",
            "season_code_end_week": "",
            "crossdock_sku": "",
            "imported_sku": "N",
            "is_purchase_product": "",
            "volume": 90,
            "length": "",
            "width": "",
            "height": "",
            "weight": "",
            "check_shelf_life": "Y",
            "receiving_date_type": "expiration_date",
            "shelf_life": 178,
            "multi_lang": [{
                "lang": "en",
                "title": "a-peach",
                "sub_title": "",
                "description_html": "<p>yellow peach！</p>"
            }, {
                "lang": "zh",
                "title": "a-桃",
                "sub_title": "",
                "description_html": "<p>美味的黄桃</p>"
            }],
            "photos": [{
                "type_id": 1,
                "photos": []
            }, {
                "type_id": 2,
                "photos": []
            }, {
                "type_id": 3,
                "photos": []
            }, {
                "type_id": 99,
                "photos": []
            }, {
                "type_id": 100,
                "photos": [photo_url]
            }, {
                "type_id": 120,
                "photos": []
            }],
            "product_id": product_id,
            "pi_tags": []
        }

        self.post(url="/central/merch/v1/pi/product/detail", json=json_data, headers=header)
        return self.response


    def add_product_tag(self, title='',short_title_en='', short_title='', weee_buyer_id='', ethnicity='', storage_temperate_zone='',
                    division_num='', catalogue_num='', sub_catalogue_num='', product_id='', upc_code='',pi_tags=''):
        """新增/编辑商品-有产品tag"""
        image_values = [
            "https://img06.weeecdn.com/product/image/704/917/1C05F407A86E669.jpeg",
            "https://img06.weeecdn.com/product/image/879/714/762BB80F79A56D78.jpeg",
            "https://img06.weeecdn.com/product/image/486/449/2107724E7F2F6F75.png",
            "https://img06.weeecdn.com/product/image/702/824/6D4B550FE15DB307.jpeg",
            "https://img06.weeecdn.com/product/image/456/487/5797F7F7DA8643A3.png",
            "https://img06.weeecdn.com/product/image/253/329/37D77272B0B7E948.png"
        ]
        photo_url = random.choice(image_values)
        json_data = {
            "short_title_en": short_title_en,
            "short_title": short_title,
            "weee_buyer_id": weee_buyer_id,
            "ethnicity": ethnicity,
            "ethnic_tags": [],
            "storage_temperate_zone": storage_temperate_zone,
            "division_num": division_num,
            "catalogue_num": catalogue_num,
            "sub_catalogue_num": sub_catalogue_num,
            "capacity": "",
            "keyword": "",
            "UPC_code": upc_code,
            "product_type": "",
            "ethnic_title": "",
            "brand_key": "BwhpDddW",
            "product_area": "5",
            "multiple_pack": "N",
            "unit": "G",
            "unit_min": 100,
            "unit_max": "",
            "chemical_substances": "",
            "tags": "",
            "is_gift_product": "",
            "is_tradein_forbid": "",
            "vender_id": "1",
            "purchase_price": 22,
            "price": 33,
            "vendor_product_title": "Vendor Product Title",
            "purchase_unit": "unit",
            "quantity_per_unit": 1,
            "vendor_SKU_code": "Vendor SKU Code",
            "season_code_start_week": "",
            "season_code_end_week": "",
            "crossdock_sku": "",
            "imported_sku": "",
            "is_purchase_product": "",
            "volume": 90,
            "length": "",
            "width": "",
            "height": "",
            "weight": "",
            "check_shelf_life": "Y",
            "receiving_date_type": "expiration_date",
            "shelf_life": 178,
            "multi_lang": [{
                "lang": "en",
                "title": "auto_create",
                "sub_title": "",
                "description_html": "<p>yellow peach！</p>"
            }, {
                "lang": "zh",
                "title": title,
                "sub_title": "",
                "description_html": "<p>美味的黄桃</p>"
            }],
            "photos": [{
                "type_id": 1,
                "photos": []
            }, {
                "type_id": 2,
                "photos": []
            }, {
                "type_id": 3,
                "photos": []
            }, {
                "type_id": 99,
                "photos": []
            }, {
                "type_id": 100,
                "photos": [photo_url]
            }, {
                "type_id": 120,
                "photos": []
            }],
            "product_id": product_id,
            "pi_tags": pi_tags
        }

        self.post(url="/central/merch/v1/pi/product/detail", json=json_data, headers=header)
        return self.response


    def inactive_product(self, sku_id):
        """inactive商品"""
        self.put(url=f"/central/merch/v1/pi/product/detail/inactive/{sku_id}", headers=header)
        return self.response

    def active_product(self, sku_id):
        """active商品"""
        self.put(url=f"/central/merch/v1/pi/product/detail/active/{sku_id}", headers=header)
        return self.response

    def put_on_and_take_off_product(self, sku_id, json_data):
        """上架、下架商品"""
        self.put(url=f"/central/merch/v1/pi/product/detail/productWebSite/status/{sku_id}", json=json_data,
                 headers=header)
        return self.response

    def special_product_detail(self, sku_id):
        """包材商品详情"""
        self.get(url=f"/central/merch/v1/pi/product/special/{sku_id}/edit", headers=header)
        return self.response

    def add_edit_special_product(self, data):
        """新增或编辑包材商品"""
        self.post(url="/central/merch/v1/pi/product/special", json=data, headers=header)
        return self.response

    def add_product_direct_import(self, product_id='', indirect_supplier_name='', indirect_supplier_status='', landing_price=''):
        """产品详情新增Direct Import Task"""
        json_data = {
            "type": "sku_direct_import",
            "product_id": product_id,
            "indirect_supplier_name": indirect_supplier_name,
            "indirect_supplier_status": indirect_supplier_status,
            "landing_price": landing_price,
            "reason": "zjw_CreateDI_auto"
        }
        self.post(url="/central/merch/v1/task/approvalRequest", json=json_data, headers=header)
        return self.response

    def sku_performance_basic(self, sku_id):
        """sku performance页面基础信息"""
        self.get(url=f"/central/merch/v1/pi/performance/basicInfo?product_id={sku_id}", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    ProductInfo().product_list({"offset": 0, "limit": 1})
