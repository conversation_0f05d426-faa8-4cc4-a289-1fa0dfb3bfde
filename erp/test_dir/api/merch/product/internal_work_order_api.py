import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
from datetime import datetime, timedelta
from config.utils import Utils


class InternalWorkOrderInterface(weeeTest.TestCase):
    """盲盒商品下发作业相关接口"""

    def internal_work_order_list(self, enter_data):
        """
        work order订单查询列表接口
        :param enter_data:
        :return:
        """
        data = {
            "page": 1,
            "pageSize": 20,
            "local_timezone": "Asia/Shanghai",
            "filter[target_product_id]": "",
            "filter[keyword]": "",
            "filter[status]": "",
            "filter[inventory_id]": "",
            "filter[bundle_id]": "",
            "filter[rec_update_time][0]": "",
            "filter[rec_update_time][1]": ""
        }
        data.update(enter_data)
        self.get(url="/central/merch/v1/internal/wo/getWorkOrderList", headers=header, params=data)
        return self.response

    def internal_work_order_info(self, enter_data):
        """
        work order订单详情查询接口
        :param enter_data:
        :return:
        """
        data = {
            "id": enter_data
        }
        self.get(url=f"/central/merch/v1/internal/wo/getWo", headers=header, params=data)
        return self.response

    def query_bundle_config_list(self):
        """
        获取设置的激活状态的bundle config数据
        :return:
        """
        self.get(url="/central/merch/v1/pi/pb/getBundleList", headers=header)
        return self.response

    def query_bundle_info_wo(self, enter_data):
        """
        查询盲盒配置的商品详情
        :param enter_data:
        :return:
        """
        data = {
            "bundle_id": None,
            "inventory_id": None
        }
        data.update(enter_data)
        self.get(url="/central/merch/v1/internal/wo/getBundleDetailForWo", headers=header, params=data)
        return self.response

    def create_internal_work_order(self, enter_data):
        """
        创建work order订单
        :param enter_data:
        :return:
        """
        data = {
            "id": None,
            "inventory_id": None,
            "bundle_id": None,
            "qty": None,
            "outbound_shelf_life": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
            "comment": "QA  Comment"
        }
        data.update(enter_data)
        self.put(url="/central/merch/v1/internal/wo/apiUpdateWo", headers=header, json=data)
        return self.response

    def delete_internal_work_order(self, enter_data):
        """
        删除work order订单
        :param enter_data:
        :return:
        """
        data = {
            "id": enter_data
        }
        self.put(url="/central/merch/v1/internal/wo/apiDeleteWo", headers=header, json=data)
        return self.response

    def send_work_order_to_wms(self, enter_data):
        """
        下发work order订单到WMS
        :param enter_data:
        :return:
        """
        data = {
            "id": enter_data
        }
        self.post(url="/central/merch/v1/fns/ApiMerch/sendBundleWorkOrderToWms", headers=header, json=data)
        return self.response

    def query_pw_option(self):
        """
        获取商品配置pw的选项
        :return:
        """
        self.get(url="/central/merch/v1/pi/pw/getPwOptions", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
