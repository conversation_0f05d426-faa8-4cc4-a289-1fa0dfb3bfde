
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api.erp import header


class ProductPIMApi(weeeTest.TestCase):
    def startPhoto(self, product_id=None, review_status=None):
        # PIM 模块进行 Start / Submit
        json_data = {
            "product_id": product_id,
            "review_status": review_status
        }
        self.post(url="/admin_product_photo/api_update_status", data=json_data, headers=header)
        return self.response




if __name__ == '__main__':
    pass









