import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class NewProductReview(weeeTest.TestCase):
    """新品审核相关接口"""

    def new_product_review_list(self, params_data):
        """新商品审核列表查询"""
        # params_data = {
        #     "offset": 0,
        #     "limit": 20,
        #     "filter[rec_update_time][0]": "2023-06-30",
        #     "filter[rec_update_time][1]": "2023-07-30",
        #     "order_by[column]": "id",
        #     "order_by[rule]": "desc",
        #     "filter[keywords]": " ",
        #     "filter[rec_creator_id]": " ",
        #     "filter[status]": "D",
        #     "filter[vender_id]": "1",
        #     "filter[brand_key]": " ",
        #     "filter[weee_buyer_id]": "",
        #     "filter[storage]": "N1",
        #     "filter[catalogue_num]": "0102",
        #     "filter[sub_catalogue_num]": "010201",
        #     "filter[to-do]": "review",
        #     "filter[current_operator]": " ",
        #     "filter[source]": "vendor"
        # }
        self.get(url="/central/merch/v1/pi/newProductReview/list", params=params_data, headers=header)
        return self.response

    def product_review_detail_filter(self):
        """查询细节筛选信息"""
        self.get(url="/central/merch/v1/pi/newProductReview/detail/filter", headers=header)
        return self.response

    def new_product_review_detail(self, audit_id):
        """新品审核详情页"""
        self.get(url=f"/central/merch/v1/pi/newProductReview/detail/{audit_id}/edit", headers=header)
        return self.response

    def add_new_product_review(self, data):
        """新增/更新新商品审核"""
        # eg_data = {
        #             "title_en":"test ind",
        #             "title":"",
        #             "sub_title":"",
        #             "image_url":"https://img06.test.weeecdn.com/product/image/890/299/41183F5390685384.jpeg",
        #             "weee_buyer_id":"",
        #             "product_area":"5",
        #             "ethnicity":"indian",
        #             "product_label_image":"",
        #             "storage_temperate_zone":"R2",
        #             "division_num":"06",
        #             "catalogue_num":"0105",
        #             "sub_catalogue_num":"010501",
        #             "price":"44.00",
        #             "competitive_price":"",
        #             "competing_companies":"",
        #             "competing_websites":"",
        #             "unit":"L",
        #             "unit_min":"4.00",
        #             "unit_max":"",
        #             "volume":"4",
        #             "length":"",
        #             "width":"",
        #             "height":"",
        #             "weight":"",
        #             "capacity":"",
        #             "julian_code_format_id":"0",
        #             "UPC_code":"",
        #             "keyword":"",
        #             "brand_key":"geWqWnTc",
        #             "vender_id":"1",
        #             "purchase_price":"16.00",
        #             "purchase_unit":"unit",
        #             "quantity_per_unit":"1.00",
        #             "vendor_product_title":"44",
        #             "vendor_SKU_code":"",
        #             "short_title":"4",
        #             "short_title_en":"4",
        #             "check_shelf_life":"N",
        #             "shelf_life":"4",
        #             "receiving_shelf_life_limit":"3",
        #             "reminder_shelf_life_limit":"2",
        #             "outbound_shelf_life_limit":"4",
        #             "sample":"N",
        #             "is_delicate":"N",
        #             "is_fragile_product":"N",
        #             "imported_sku":"N",
        #             "crossdock_sku":"N",
        #             "sales_forecast":[
        #             ],
        #             "inventory_forecast":[
        #                 {
        #                     "active_sku_count":"0",
        #                     "expected_vendor_ready_date":"2023-07-27",
        #                     "inventory_id":"7",
        #                     "max_sku_count":"130",
        #                     "purchase_sku_count":"0",
        #                     "quantity":"4"
        #                 }
        #             ],
        #             "related_sku":[
        #                 {
        #                     "is_replacement":"N",
        #                     "related_product_id":"4"
        #                 }
        #             ],
        #             "special_reason":"44",
        #             "comment":"",
        #             "expected_po_date":"",
        #             "key_sku":"N",
        #             "need_detail_description":"N",
        #             "eta_sku_live_date":"",
        #             "id":"20358",
        #             "current_status":"D",
        #             "description_en":"<p>4</p>",
        #             "description_html":"<p></p>",
        #             "detail_description":"",
        #             "image_urls":[
        #                 "https://img06.test.weeecdn.com/product/image/890/299/41183F5390685384.jpeg"
        #             ],
        #             "property":[
        #                 "3248"
        #             ]
        #         }
        self.post(url="/central/merch/v1/pi/newProductReview/detail", json=data, headers=header)
        return self.response

    def get_new_product_review_owner(self, audit_id):
        """获取可以分配新商品审核的owner"""
        self.get(url=f"/central/merch/v1/pi/newProductReview/owner/{audit_id}", headers=header)
        return self.response

    def assign_new_product_review_owner(self, data):
        """分配新商品审核的owner"""
        # eg_data = {"assignee_user_id": "user_id", "id": "audit_id"}
        self.put(url="/central/merch/v1/pi/newProductReview/owner", json=data, headers=header)
        return self.response

    def approve_new_product_review(self, data):
        """审批新商品审核"""
        # eg_data = {"id": "audit_id"}
        self.get(url="/central/merch/v1/pi/newProductReview/approve", params=data, headers=header)
        return self.response

    def batch_approve_new_product_review(self, data):
        """批量审批新商品审核"""
        # eg_data = {"ids": "audit_id", "type": "approve"}
        self.get(url="/central/merch/v1/pi/newProductReview/bulkOperation", params=data, headers=header)
        return self.response

    def delete_new_product_review(self, audit_id):
        """删除新商品审核"""
        self.delete(url=f"/central/merch/v1/pi/newProductReview/detail/{audit_id}", headers=header)
        return self.response

    def create_sku_new_product_review(self, audit_id):
        """新商品审核创建商品SKU"""
        # eg_data = {"id": "audit_id"}
        self.get(url=f"/central/merch/v1/pi/newProductReview/createProduct", params=audit_id, headers=header)
        return self.response

    def copy_new_product_review(self, audit_id):
        """copy新商品审核"""
        self.get(url=f"/central/merch/v1/pi/newProductReview/detail/{audit_id}/edit?copy_audit_id", headers=header)
        return self.response

    def create_new_productH(self, product_area='', ethnicity='', storage_temperate_zone='', division_num='',
                            catalogue_num='', sub_catalogue_num='', price=None, unit='', unit_min=None, volume=None,
                            brand_key='',
                            vender_id='', purchase_price=None, purchase_unit='', vendor_product_title='',
                            vendor_SKU_code='', short_title='', short_title_en='', check_shelf_life='',
                            receiving_date_type='', shelf_life=None, receiving_shelf_life_limit=None,
                            reminder_shelf_life_limit=None, outbound_shelf_life_limit=None, sample='', is_delicate='',
                            is_fragile_product='', imported_sku='', crossdock_sku='', special_reason='',
                            is_purchase_product='', need_detail_description=''):
        # 创建新品
        json_data = {
            "product_area": product_area,
            "ethnicity": ethnicity,
            "storage_temperate_zone": storage_temperate_zone,
            "division_num": division_num,
            "catalogue_num": catalogue_num,
            "sub_catalogue_num": sub_catalogue_num,
            "price": price,
            "unit": unit,
            "unit_min": unit_min,
            "volume": volume,
            "brand_key": brand_key,
            "vender_id": vender_id,
            "purchase_price": purchase_price,
            "purchase_unit": purchase_unit,
            "vendor_product_title": vendor_product_title,
            "vendor_SKU_code": vendor_SKU_code,
            "short_title": short_title,
            "short_title_en": short_title_en,
            "check_shelf_life": check_shelf_life,
            "receiving_date_type": receiving_date_type,
            "shelf_life": shelf_life,
            "receiving_shelf_life_limit": receiving_shelf_life_limit,
            "reminder_shelf_life_limit": reminder_shelf_life_limit,
            "outbound_shelf_life_limit": outbound_shelf_life_limit,
            "sample": sample,
            "is_delicate": is_delicate,
            "is_fragile_product": is_fragile_product,
            "imported_sku": imported_sku,
            "crossdock_sku": crossdock_sku,
            "inventory_forecast": [
                {
                    "inventory_id": "16",
                    "quantity": "123",
                    "active_sku_count": "133",
                    "max_sku_count": "160",
                    "purchase_sku_count": 0,
                    "expected_vendor_ready_date": "2023-12-31",
                    "supply_chain_analyst_id": "7673902"
                }
            ],
            "related_sku": [
                {
                    "is_replacement": "Y",
                    "related_product_id": "10529"
                }
            ],
            "special_reason": special_reason,
            "is_purchase_product": is_purchase_product,
            "need_detail_description": need_detail_description,
            "multi_lang": [
                {
                    "lang": "en",
                    "title": "product-title",
                    "sub_title": "",
                    "description_html": "&lt;p&gt;Fresh vegetables and fruits！&lt;/p&gt;"
                },
                {
                    "lang": "zh",
                    "title": "农产品",
                    "sub_title": "",
                    "description_html": "&lt;p&gt;新鲜的蔬菜水果&lt;/p&gt;"
                }
            ],
            "photos": [
                {
                    "type_id": 1,
                    "photos": []
                },
                {
                    "type_id": 2,
                    "photos": []
                },
                {
                    "type_id": 3,
                    "photos": []
                },
                {
                    "type_id": 99,
                    "photos": []
                },
                {
                    "type_id": 100,
                    "photos": [
                        "https://img06.test.weeecdn.com/product/image/274/591/1FAC5BCED836C6EB.jpeg"
                    ]
                }
            ],
            "property": [
                "55",
                "57",
                "58",
                "56",
                "3248"
            ],
            "repack_inventory_ids": []

        }
        self.post(url="/central/merch/v1/pi/newProductReview/detail", json=json_data, headers=header)
        return self.response

    def create_new_product_zjw(self, product_area='', ethnicity='',title='', storage_temperate_zone='', division_num='',
                            catalogue_num='', sub_catalogue_num='', price=None, unit='', unit_min=None, volume=None,
                            brand_key='',UPC_code='',
                            vender_id='', purchase_price=None, purchase_unit='', vendor_product_title='',
                            vendor_SKU_code='', short_title='', short_title_en='', check_shelf_life='',
                            receiving_date_type='', shelf_life=None, receiving_shelf_life_limit=None,
                            reminder_shelf_life_limit=None, outbound_shelf_life_limit=None, sample='', is_delicate='',
                            is_fragile_product='', imported_sku='', crossdock_sku='', special_reason='',
                            is_purchase_product='', need_detail_description=''):
        # 创建新品用于删除
        json_data = {
            "product_area": product_area,
            "ethnicity": ethnicity,
            "storage_temperate_zone": storage_temperate_zone,
            "division_num": division_num,
            "catalogue_num": catalogue_num,
            "sub_catalogue_num": sub_catalogue_num,
            "price": price,
            "unit": unit,
            "unit_min": unit_min,
            "volume": volume,
            "brand_key": brand_key,
            "vender_id": vender_id,
            "UPC_code": UPC_code,
            "purchase_price": purchase_price,
            "purchase_unit": purchase_unit,
            "vendor_product_title": vendor_product_title,
            "vendor_SKU_code": vendor_SKU_code,
            "short_title": short_title,
            "short_title_en": short_title_en,
            "check_shelf_life": check_shelf_life,
            "receiving_date_type": receiving_date_type,
            "shelf_life": shelf_life,
            "receiving_shelf_life_limit": receiving_shelf_life_limit,
            "reminder_shelf_life_limit": reminder_shelf_life_limit,
            "outbound_shelf_life_limit": outbound_shelf_life_limit,
            "sample": sample,
            "is_delicate": is_delicate,
            "is_fragile_product": is_fragile_product,
            "imported_sku": imported_sku,
            "crossdock_sku": crossdock_sku,
            "inventory_forecast": [
                {
                    "inventory_id": "25",
                    "testing_period": 28,
                    "quantity": "100",
                    "active_sku_count": "5526",
                    "max_sku_count": "6860",
                    "purchase_sku_count": 18,
                    "expected_vendor_ready_date": "2025-01-01",
                    "supply_chain_analyst_id": "13347083"
                }
            ],
            "related_sku": [
                {
                    "is_replacement": "Y",
                    "related_product_id": "1"
                }
            ],
            "special_reason": special_reason,
            "is_purchase_product": is_purchase_product,
            "need_detail_description": need_detail_description,
            "multi_lang": [
                {
                    "lang": "en",
                    "title": "new_product-title",
                    "sub_title": "",
                    "description_html": "&lt;p&gt;Fresh vegetables and fruits！&lt;/p&gt;"
                },
                {
                    "lang": "zh",
                    "title": title,
                    "sub_title": "",
                    "description_html": "&lt;p&gt;新鲜的蔬菜水果&lt;/p&gt;"
                }
            ],
            "photos": [
                {
                    "type_id": 1,
                    "photos": []
                },
                {
                    "type_id": 2,
                    "photos": []
                },
                {
                    "type_id": 3,
                    "photos": []
                },
                {
                    "type_id": 99,
                    "photos": []
                },
                {
                    "type_id": 100,
                    "photos": [
                        "https://img06.test.weeecdn.com/product/image/486/449/7BF22392F7D3064E.png"
                    ]
                }
            ],
            "property": [
                "1313"
            ],
            "repack_inventory_ids": []

        }
        self.post(url="/central/merch/v1/pi/newProductReview/detail", json=json_data, headers=header)
        return self.response

    def isbuyer_newProduct(self, newProduct_id):
        # submit前校验是否有buyer权限
        self.get(url=f"/central/merch/v1/pi/newProductReview/owner/{newProduct_id}", headers=header)
        return self.response

    def approval_newProduct(self, newProduct_id):
        # submit/审核
        self.get(url=f"/central/merch/v1/pi/newProductReview/approve?id={newProduct_id}&comment=", headers=header)
        return self.response

    def createSKU(self, newProduct_id):
        # Create SKU
        self.get(url=f"/central/merch/v1/pi/newProductReview/createProduct?id={newProduct_id}", headers=header)
        return self.response

    def createPhotoRequest(self, product_id='', service_type='', comment='', sample_source=''):
        json_data = {
            "product_id": product_id,
            "service_type": service_type,
            "comment": comment,
            "sample_source": sample_source
        }
        self.post(url="/central/merch/v1/pi/product/request", json=json_data, headers=header)
        return self.response

    def pulishPhoto(self, product_id='',type=''):
        json_data = {
            "product_id": product_id,
            "type": type
        }
        self.post(url="/central/merch/v1/pi/product/publish", json=json_data, headers=header)
        return self.response




if __name__ == '__main__':
    # weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    pass
