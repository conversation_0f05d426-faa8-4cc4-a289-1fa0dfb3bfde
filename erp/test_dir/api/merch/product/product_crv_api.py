import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class ProductCrvInterface(weeeTest.TestCase):
    """商品CRV配置模块相关接口"""

    def inquire_product_crv_list(self, data):
        """商品crv配置列表查询"""
        crv_data = {
            "offset": 0,
            "limit": 10,
            "filter[product_id]": None,
            "filter[keyword]": None,
            "filter[state_id]": None,
            "filter[target_product_id]": None
        }
        crv_data.update(data)
        self.get(url="/central/merch/v1/pi/pb/getCrvList", params=crv_data, headers=header)
        return self.response

    def inquire_product_crv_option(self):
        """
        查询支持CRV配置的商品
        :return:
        """
        self.get(url="/central/merch/v1/pi/pb/apiCrvOptions", headers=header)
        return self.response

    def add_product_crv_conf(self, crv_data):
        """
        新增crv关系配置
        :param crv_data:
        :return:
        """
        data = {
            "price": "0.1",
            "pack_qty": 0,
            "state_id": "41",
            "source_product_id": "105388",
            "target_product_id": "2071546"
        }
        data.update(crv_data)
        self.put(url="/central/merch/v1/pi/pb/apiUpdateProductCrv", json=data, headers=header)
        return self.response

    def update_product_crv_conf(self, crv_data):
        """
        编辑crv配置关系
        :param crv_data:
        :return:
        """
        data = {
            "id": "",
            "price": "0.01",
            "pack_qty": 0,
            "state_id": "41",
            "source_product_id": "105388",
            "target_product_id": "2071546"
        }
        data.update(crv_data)
        self.put(url="/central/merch/v1/pi/pb/apiUpdateProductCrv", json=data, headers=header)
        return self.response

    def delete_product_crv_conf(self, crv_data):
        """
        删除crv配置关系
        :param crv_data:
        :return:
        """
        data = {
            "id": ""
        }
        data.update(crv_data)
        self.put(url="/central/merch/v1/pi/pb/apiDeleteProductCrv", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
