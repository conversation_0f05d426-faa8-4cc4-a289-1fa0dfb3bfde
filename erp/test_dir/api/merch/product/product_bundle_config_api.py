
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
from config.utils import Utils


class ProductBundleConfigInterface(weeeTest.TestCase):
    """盲盒商品配置相关接口"""

    def product_bundle_config_list(self, enter_data):
        """
        盲盒商品配置查询列表接口
        :param enter_data:
        :return:
        """
        data = {
            "page": 1,
            "pageSize": 20,
            "local_timezone": "Asia/Shanghai",
            "filter[product_id]": "",
            "filter[keyword]": "",
            "filter[status]": "",
            "filter[rec_create_time][0]": "",
            "filter[rec_create_time][1]": "",
            "filter[rec_update_time][0]": "",
            "filter[rec_update_time][1]": ""
        }
        data.update(enter_data)
        self.get(url="/central/merch/v1/pi/pb/getBundleList", headers=header, params=data)
        return self.response

    def product_bundle_config_info(self, enter_data):
        """
        盲盒商品配置详情查询接口
        :param enter_data:
        :return:
        """
        data = {
            "id": None,
        }
        data.update(enter_data)
        self.get(url="/central/merch/v1/pi/pb/getPb", headers=header, params=data)
        return self.response

    def query_bundle_product(self):
        """
        查询满足盲盒配置的bundle商品接口
        :return:
        """
        data = {
            "bundleProduct": True
        }
        self.get(url="/central/merch/v1/pi/pb/getProductOptions", headers=header, params=data)
        return self.response

    def query_product_info(self, enter_data):
        """
        查询商品详情接口
        :param enter_data:
        :return:
        """
        data = {
            "productId": None
        }
        data.update(enter_data)
        self.post(url="/central/merch/v1/fns/apiMerch/getInvProductDetail", headers=header, json=data)
        return self.response

    def create_product_bundle_config(self, enter_data):
        """
        创建盲盒商品配置接口
        :param enter_data:
        :return:
        """
        data = {
            "id": None,
            "title": Utils.random_title(),
            "product_id": None,
            "items": [
                {
                    "product_id": None,
                    "qty": None,
                    "pack_qty": None
                }
            ],
            "comment": "Create Comment"
        }
        data.update(enter_data)
        self.put(url="/central/merch/v1/pi/pb/apiUpdatePb", headers=header, json=data)
        return self.response

    def update_product_bundle_config(self, enter_data):
        """
        更新盲盒商品配置接口
        :param enter_data:
        :return:
        """
        data = {
            "id": None,
            "title": None,
            "product_id": None,
            "items": [
                {
                    "product_id": None,
                    "qty": None,
                    "pack_qty": None
                }
            ],
            "comment": "Create Comment"
        }
        data.update(enter_data)
        self.put(url="/central/merch/v1/pi/pb/apiUpdatePb", headers=header, json=data)
        return self.response

    def delete_product_bundle_config(self, enter_data):
        """
        删除未做work order的盲盒配置接口
        :param enter_data: 盲盒配置ID
        :return:
        """
        data = {
            "id": None
        }
        data.update(enter_data)
        self.put(url="/central/merch/v1/pi/pb/apiDeletePb", headers=header, json=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    ProductBundleConfigInterface().query_product_info(enter_data={"productId": "68987"})
