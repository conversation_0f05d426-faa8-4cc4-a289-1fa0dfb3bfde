
import weeeTest
from weeeTest import weeeConfig
# from test_dir.api.vendor import header
from erp.test_dir.api_case import vendor_header


class VendorOrderInterfaces(weeeTest.TestCase):
    """供应商平台订单相关接口"""

    def vendor_order_list(self, data, header_type):
        """
        供应商采购订单列表查询接口
        :param data: 查询条件
        :return:
        """
        params_data = {
                    "offset": 0,
                    "limit": 20,
                    "order_by[column]": "id",
                    "order_by[rule]": "desc",
                    "filter[rec_create_time]": "",
                    "filter[status]": "",
                    "filter[inbound_inventory_id]": "",
                    "filter[proposal_status]": "",
                    "filter[po_id]": "",
                    "filter[vendor_invoice_num]": "",
                    "filter[to_dos]": ""
                    }
        params_data.update(data)
        vendor_header.update({"Vendor-Id": header_type})
        self.get(url="/vendor/service/v1/po/orders", params=params_data, headers=vendor_header)
        return self.response

    def vendor_order_info(self, data, header_type):
        """
        供应商采购订单详情接口
        :param data: 订单ID
        :return:
        """
        vendor_header.update({"Vendor-Id": header_type})
        self.get(url=f"/vendor/service/v1/po/orders/{data}", headers=vendor_header)
        return self.response

    def vendor_order_invoice_file(self, data):
        """
        供应商采购订单详情附件接口
        :param data: 订单ID
        :return:
        """
        self.get(url=f"/vendor/service/v1/po/invoices/invoiceFiles/{data}", headers=vendor_header)
        return self.response

    def vendor_order_review_task(self, data):
        """
        供应商采购订单review_task
        :param data: 订单ID
        :return:
        """
        self.get(url=f"/vendor/service/v1/po/orders/reviewTask/{data}", headers=vendor_header)
        return self.response

    def vendor_delivery_appointment_list(self, data, header_type):
        """
        供应商送货预约列表查询接口
        :param data: 查询条件
        :return:
        """
        params_data = {
            "offset": 0,
            "limit": 20,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[start_time][0]": "2023-11-16 00:00:00",
            "filter[start_time][1]": "2025-11-15 00:00:00",
            "filter[status]": None,
            "filter[purchase_order_id]": None,
            "filter[inventory_id]": None,
            "filter[storage_type]": None
        }
        params_data.update(data)
        vendor_header.update({"Vendor-Id": header_type})
        self.get(url="/vendor/service/v1/po/proposals/appointmentList", params=params_data, headers=vendor_header)
        return self.response

    def vendor_get_po_info(self, data, header_type):
        """
        供应商获取po的详情信息接口
        :param data:
        :param header_type: 类型及供应商ID
        :return:
        """
        vendor_header.update({"Vendor-Id": header_type})
        self.get(url=f"/vendor/service/v1/po/orders/{data}/detail?", headers=vendor_header)
        return self.response

    def vendor_get_active_time(self, data, header_type):
        """
        供应商获取可以配送的日期
        :param data:
        :return:
        """
        json_data = {
            "id": "0",
            "purchase_order_id": None,
            "date": None,
            "purchase_order_list": []
        }
        json_data.update(data)
        vendor_header.update(header_type)
        self.post(url="/vendor/service/v1/po/proposals/getAvailableTime", json=json_data, headers=vendor_header)
        return self.response

    def vendor_add_delivery_appointment(self, data, header_type):
        """
        供应商增加或修改配送日期
        :param data:
        :param header_type: 类型及供应商ID
        :return:
        """
        vendor_header.update(header_type)
        json_data = {
            "id": 0,
            "calendar_event_id": 0,
            "type": "S",
            "purchase_order_id": 265071,
            "date": "2023-11-28",
            "time": "10:00-11:00",
            "remark": "Description",
            "eta_date": "11/28/2023",
            "vendor_id": "1 - Test Vender 1",
            "inventory_id": "25 - LA - La Mirada"
        }
        json_data.update(data)
        self.post(url="/vendor/service/v1/po/proposals/appointment", json=json_data, headers=vendor_header)
        return self.response

    def vendor_cancel_delivery_appointment(self, data, header_type):
        """
        供应商取消已配置的配送日期
        :param data:
        :param header_type: 类型及供应商ID
        :return:
        """
        vendor_header.update(header_type)
        json_data = {
            "id": "25244",
            "purchase_order_id": "265071",
            "calendar_event_id": "24037",
            "type": "S"
        }
        json_data.update(data)
        self.post(url="/vendor/service/v1/po/proposals/appointmentCancel", json=json_data, headers=vendor_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    header_type = {"type": "vendor", "Vendor-Id": "1"}
    VendorOrderInterfaces().vendor_get_active_time(data={})
