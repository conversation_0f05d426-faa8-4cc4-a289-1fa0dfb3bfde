import weeeTest
from weeeTest import weeeConfig
#from test_dir.api.vendor import header
from erp.test_dir.api_case import vendor_header


class VendorProductInterfaces(weeeTest.TestCase):
    """供应商平台商品相关接口"""

    def vendor_active_product_list(self, data):
        """
        供应商平台已激活商品列表接口
        :param data:
        :return:
        """
        params_data = {
            "offset": 0,
            "limit": 20,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[product_id]": None,
            "filter[item_code]": None,
            "filter[vendor_product_title]": None,
            "filter[upc_code]": None
        }
        params_data.update(data)
        self.get(url="/vendor/service/v1/pi/products", json=params_data, headers=vendor_header)
        return self.response

    def vendor_update_item_code(self, data):
        """
        供应商平台更新商品code
        :param data:
        :return:
        """
        json_data = {
            "item_code": None,
            "product_vendor_id": None,
            "type": 2
        }
        json_data.update(data)
        self.put(url="/vendor/service/v1/pi/products/createRequest", json=json_data, headers=vendor_header)
        return self.response

    def vendor_review_product_list(self, data):
        """
        供应商平台Review商品列表查询
        :param data:
        :return:
        """
        params_data = {
            "offset": 0,
            "limit": 20,
            "order_by[column]": "product_id",
            "order_by[rule]": "desc",
            "filter[product_id]": None,
            "filter[proposal_status]": None,
            "filter[vendor_SKU_code]": None,
            "filter[title_en]": None,
            "filter[UPC_code]": None
        }
        params_data.update(data)
        self.get(url="/vendor/service/v1/pi/productReview", params=params_data, headers=vendor_header)
        return self.response

    def vendor_review_product_info(self, data):
        """
        供应商平台Review商品详情
        :param data:
        :return:
        """
        self.get(url=f"/vendor/service/v1/pi/productReview/detail/{data}", headers=vendor_header)
        return self.response

    def vendor_create_review_product(self, data, header_type):
        """
        供应商平台创建商品review
        :param data:
        :param header_type:
        :return:
        """
        json_data = {
            "product_id": "",
            "title_en": "",
            "title": "test-自动化",
            "sub_title": "",
            "UPC_code": "",
            "brand_key": "",
            "storage_temperate_zone": "",
            "product_area": "1",
            "unit": "",
            "unit_min": "",
            "unit_max": "",
            "image_url": "https://img06.test.weeecdn.com/product/image/537/425/36FCB87FED69FAF7.png",
            "product_label_image": "",
            "shelf_life": "",
            "vendor_SKU_code": "",
            "price": "",
            "purchase_price": "",
            "purchase_unit": "",
            "quantity_per_unit": "",
            "special_reason": "",
            "volume": "",
            "length": "",
            "width": "",
            "height": "",
            "description_en": "",
            "description_html": "",
            "emails": "",
            "bracket_info": [
                {
                    "id": "layer",
                    "bracket": "Layer"
                },
                {
                    "id": "pallet",
                    "bracket": "Pallet"
                }
            ],
            "status": "V"
        }
        json_data.update(data)
        vendor_header.update(header_type)
        self.put(url="/vendor/service/v1/pi/productReview/save", json=json_data, headers=vendor_header)
        return self.response

    def vendor_request_product_list(self, data, header_type):
        """
        供应商平台-商品申请列表查询
        :param data:
        :return:
        """
        params_data = {
            "offset": 0,
            "limit": 20,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[product_id]": None,
            "filter[item_code]": None,
            "filter[product_name]": None,
            "filter[status]": None
        }
        params_data.update(data)
        vendor_header.update(header_type)
        self.get(url="/vendor/service/v1/pi/productRequest", params=params_data, headers=vendor_header)
        return self.response

    def vendor_request_product_cancel(self, data):
        """
        供应商平台-取消商品申请
        :param data:
        :return:
        """
        self.get(url=f"/vendor/service/v1/pi/productRequest/cancel/{data}", headers=vendor_header)
        return self.response

    def vendor_product_update_list(self, data):
        """
        供应商平台-更新商品列表查询
        :param data:
        :return:
        """
        params_data = {
            "offset": 0,
            "limit": 20,
            "order_by[column]": "id",
            "order_by[rule]": "desc",
            "filter[product_id]": None,
            "filter[item_code]": None,
            "filter[vendor_product_title]": None,
            "filter[upc_code]": None
        }
        params_data.update(data)
        self.get(url="/vendor/service/v1/pi/products", params=params_data, headers=vendor_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    VendorProductInterfaces().vendor_update_item_code(data={})
