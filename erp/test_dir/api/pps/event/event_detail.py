import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
import urllib.parse


class EventDetail(weeeTest.TestCase):
    """相关配置接口"""

    # ------------------------------------------- 以下是exposure product相关接口 -----------------------------------------

    def add_single_exposure_product(self, sku='', group_id='', sales_org='', tag='', start_date='', end_date='',
                                    event_code=''):
        """新增单个曝光产品"""
        json_data = {
            "productId": sku,
            "groupId": group_id,
            "salesOrgIds": sales_org,
            "tag": tag,
            "startDate": start_date,
            "endDate": end_date,
            "userId": 10937481,
            "userEmail": "<EMAIL>",
            "eventCode": event_code
        }
        self.post(url="/central/promotion_planning/v2/promo_exposure_products", json=json_data, headers=header)
        return self.response

    def bulk_delete_exposure_products(self, event_code='', ids=''):
        """批量删除曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_code + '/bulk_delete'
        json_data = {
            "ids": ids,
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def delete_exposure_product(self, event_code='', id=''):
        """删除单个曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_code + '/delete/' + id
        json_data = {
            "user": "<EMAIL>"
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def get_exposure_products_list(self, ethnicity=None, category=None, sales_org=None, approve_status=None,
                                   group_id=None,
                                   product_owner=None, tag=None, event_code=''):
        """获取曝光产品列表"""
        params_data = {
            "ethnicity": ethnicity,
            "category": category,
            "salesOrgIds": sales_org,
            "is_approved": approve_status,
            "event_group_id": group_id,
            "ownerIds": product_owner,
            "tag": tag
        }
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_code
        self.get(url=url, params=params_data, headers=header)
        return self.response

    def approve_reject_single_exposure_product(self, event_method='', id=''):
        """单独approve/reject曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_method + id
        json_data = {
            "user": "<EMAIL>"
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def bulk_approve_reject_exposure_products(self, event_method='', ids=''):
        """批量 approve/reject 曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_method
        json_data = {
            "approverUser": "<EMAIL>",
            "ids": ids
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def feature_for_single_exposure_product(self, event_code, tag='', id='', start_date='', end_date=''):
        """单独 feature/unfeature 曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_code + '/edit'
        json_data = {
            "tag": tag,
            "userId": 10937481,
            "userEmail": "<EMAIL>",
            "id": id,
            "startDate": start_date,
            "endDate": end_date,
            "eventCode": event_code
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def move_group_for_single_exposure_product(self, event_code='', group_id='', ids=''):
        """批量 move group 曝光产品"""
        url = '/central/promotion_planning/v2/promo_exposure_products/event/' + event_code + '/move_to_group/' + group_id
        json_data = {
            "approverUser": "<EMAIL>",
            "ids": ids
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    # ------------------------------------------- 以下是promotion product相关接口 -----------------------------------------

    def get_promotion_products_list(self, ethnicity=None, category=None, sales_org=None, approve_status=None,
                                    group_id=None, product_owner=None, tag=None, event_code=''):
        """获取促销产品列表"""
        params_data = {
            "ethnicity": ethnicity,
            "category": category,
            "salesOrgIds": sales_org,
            "is_approved": approve_status,
            "event_group_id": group_id,
            "ownerIds": product_owner,
            "tag": tag
        }

        url = '/central/promotion_planning/v2/promo_event_products/event/' + event_code
        self.get(url=url, params=params_data, headers=header)
        return self.response

    def action_for_single_promotion_product(self, event_method='', id=''):
        """单独 approve/reject/unlink 促销产品"""
        url = '/central/promotion_planning/v2/promo_event_products/event/' + event_method + id
        json_data = {
            "user": "<EMAIL>"
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def bulk_action_for_promotion_products(self, event_method='', ids=''):
        """批量 approve/reject 促销产品"""
        url = '/central/promotion_planning/v2/promo_event_products/event/' + event_method
        json_data = {
            "approverUser": "<EMAIL>",
            "ids": ids
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def feature_for_single_promotion_product(self, event_code, tag='', id=''):
        """单独 feature/unfeature 促销产品"""
        url = '/central/promotion_planning/v2/promo_event_products/event/' + event_code + '/edit'
        json_data = {
            "tag": tag,
            "userId": 10937481,
            "userEmail": "<EMAIL>",
            "id": id
        }
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def update_event_group(self, group_id='', event_code='', title='', group='', start_time='', end_time='', desc=''):
        """更新 event下面的group"""
        url = '/central/promotion_planning/v2/event_groups/' + group_id
        json_data = {
            "eventCode": event_code,
            "title": title,
            "groupId": group,
            "startTime": start_time,
            "endTime": end_time,
            "description": desc,
            "recUpdateUser": "<EMAIL>"
        }
        self.put(url=url, json=json_data, headers=header)
        return self.response

    def add_event_group(self, event_code='', title='', group='', start_time='', end_time='', desc=''):
        """新增 event下面的group"""
        json_data = {
            "eventCode": event_code,
            "title": title,
            "groupId": group,
            "startTime": start_time,
            "endTime": end_time,
            "description": desc,
            "recUpdateUser": "<EMAIL>"
        }
        self.post(url="/central/promotion_planning/v2/event_groups", json=json_data, headers=header)
        return self.response

    def delete_event_group(self, group_id=''):
        """删除 event下面的group"""
        url = '/central/promotion_planning/v2/event_groups/' + group_id
        self.delete(url=url, headers=header)
        return self.response

    def get_event_group(self, event_code=''):
        """查询 event下面的group"""
        url = '/central/promotion_planning/v2/event_groups/event_code/' + event_code
        self.get(url=url, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
