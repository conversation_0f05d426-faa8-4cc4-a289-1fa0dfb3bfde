import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class EventList(weeeTest.TestCase):
    """相关配置接口"""

    def add_single_event(self, title='', start_date='', end_date='', storefronts='', event_type_id='',
                         description=None):
        """新增单个event"""
        # 这里json_data需要[]，因为可以批量新增
        json_data = [{
            "title": title,
            "start_date": start_date,
            "end_date": end_date,
            "storefronts": storefronts,
            "event_type_id": event_type_id,
            "owner_email": "<EMAIL>",
            "description": description
        }]
        self.post(url="/central/promotion_planning/v2/events/update_insert", json=json_data, headers=header)
        return self.response

    def get_event_list(self, start_date='', end_date='', event_title=None, event_owner=None, store_fronts=None,
                       event_type=None, product_owner=None):
        """获取event list"""
        params_data = {
            "startDate": start_date,
            "endDate": end_date,
            "title": event_title,
            "ownerEmail": event_owner,
            "storefronts": store_fronts,
            "eventTypeIds": event_type,
            "productOwnerId": product_owner
        }
        self.get(url="/central/promotion_planning/v2/events", params=params_data, headers=header)
        return self.response

    def delete_event(self, event_code):
        url = '/central/promotion_planning/v2/events/' + event_code
        self.delete(url=url, headers=header)
        return self.response

    def update_event(self, data, url):
        # event_code = '202503_Futur_1'
        # url = '/central/promotion_planning/v2/events/' + event_code
        json_data = {
            "title": '',
            "start_date": '',
            "end_date": '',
            "storefronts": '',
            "owner_email": "<EMAIL>",
            "description": ''
        }
        json_data.update(data)
        self.put(url=url, json=json_data, headers=header)
        return self.response

    def get_store_front_keys(self):
        self.get(url="/central/promotion_planning/v2/storefronts/key_label", headers=header)
        return self.response

    def get_event_type_keys(self):
        self.get(url="/central/promotion_planning/v2/events/types/key_label", headers=header)
        return self.response

    def get_event_sales_org(self):
        self.get(url="/central/promotion_planning/v2/sales_regions", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
