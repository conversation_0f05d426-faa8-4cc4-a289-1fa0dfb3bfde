import weeeTest
#from test_dir.api.pps import header
from erp.test_dir.api_case import header


class TestPromotionPlanEventProduct(weeeTest.TestCase):
    """
    event product页面相关接口
    """
    def test_promotion_plan_product_sales_owner(self):
        """
        planning product页面 product owner查询接口
        """
        self.get(url='/central/promotion_planning/product_sales/owners', headers=header)
        assert self.response['result'] == True

    def test_promotion_plan_event_groups(self):
        """
        planning product页面 event code 查询接口
        """
        self.get(url='/central/promotion_planning/event_groups/event_code/202308_Teste_2', headers=header)
        assert self.response['result'] == True

    def test_promotion_plan_catalogues(self):
        """
        planning product 页面 catalogues 查询接口
        """
        self.get(url='/central/promotion_planning/catalogues/key_label', headers=header)
        assert self.response['result'] == True

    def test_promotion_plan_event_sku(self):
        """
        planning product 页面 event 信息查询接口
        """
        self.get(url='/central/promotion_planning/events/202308_Teste_2', headers=header)
        assert self.response['result'] == True

    def test_promotion_plan_product(self):
        """
        planning product页面 event 商品相关信息查询接口
        """
        self.get(url='/central/promotion_planning/product_sales_regions/event/product_view/202308_Teste_2?category=&salesRegionIds=-999&ethnicity=&tag=&is_approved=&event_group_id=&ownerIds=')
        assert self.response['result'] == True
