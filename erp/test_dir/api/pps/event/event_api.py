import logging
import weeeTest
from datetime import datetime
#from test_dir.api import header
from erp.test_dir.api_case import header



class PromotionPlanEvents(weeeTest.TestCase):
    """
    event 页面相关接口
    """

    def test_promotion_plan_events(self, startDate='', endDate='', assert_data=0, title="", storefronts="",
                                   salesRegionIds=""):
        """
        planning 页面 event 列表查询接口
        """
        self.get(
            url='/central/promotion_planning/events?startDate=%s&endDate=%s&title=%s&salesRegionIds=%s&storefronts=%s' % (
                startDate, endDate, title, salesRegionIds, storefronts), headers=header)
        if len(self.response['object']) == 0:
            raise Exception("接口未返回数据，请校验是否真实存在数据")
        if title:
            assert assert_data in self.response['object'][0]["title"]
        elif storefronts:
            assert assert_data in self.response['object'][0]["storefronts"]

        startDate_date = datetime.strptime(startDate, "%Y-%m-%d")
        endDate_date = datetime.strptime(endDate, "%Y-%m-%d")
        assert datetime.strptime(self.response['object'][0]["startDate"],
                                 "%Y-%m-%d") >= startDate_date or datetime.strptime(
            self.response['object'][0]["endDate"], "%Y-%m-%d") <= endDate_date

    def test_promotion_plan_sales_regions(self):
        """
        planning 页面 sales region 查询接口
        """
        self.get(url='/central/promotion_planning/sales_regions', headers=header)
        assert self.response['result'] == True

    def test_promotion_plan_storefront(self):
        """
        planning 页面 sales storefront 查询接口
        """
        self.get(url='/central/promotion_planning/storefronts/key_label', headers=header)
        assert self.response['result'] == True
