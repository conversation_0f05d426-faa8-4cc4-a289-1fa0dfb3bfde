import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class ProductsView(weeeTest.TestCase):
    """相关配置接口"""

    def add_single_promotion(self, sku='', sales_org='', price_priority='', promotion_price='', discount='',
                             lightning_flag='', start_time='', end_time='', business_reason='',
                             max_qty='', limit_qty=None, event_code=None, event_group=None, feature_tag=None):
        """新增单个promotion"""
        json_data = {
            "productId": sku,
            "salesOrgId": sales_org,
            "pricePriority": price_priority,
            "promotionPrice": promotion_price,
            "discountPercentage": discount,
            "isLightningDeal": lightning_flag,
            "startTime": start_time,
            "endTime": end_time,
            "businessReason": business_reason,
            "maxQuantity": max_qty,
            "limitQuantity": limit_qty,
            "eventCode": event_code,
            "groupId": event_group,
            "tag": feature_tag,
            "userId": 10937481,
            "userEmail": "<EMAIL>"
        }
        self.post(url="/central/promotion_planning/v2/promo_event_products", json=json_data, headers=header)
        return self.response

    def edit_single_promotion(self, pep_ids='', price_special_ids='', price_priority='', promotion_price='',
                              discount='',
                              lightning_flag='', start_time='', end_time='', business_reason='', reason_id='',
                              max_qty='', limit_qty=None):
        """编辑单个promotion"""
        json_data = {
            "pepIds": pep_ids,
            "priceSpecialIds": price_special_ids,
            "pricePriority": price_priority,
            "promotionPrice": promotion_price,
            "discountPercentage": discount,
            "isLightningDeal": lightning_flag,
            "startTime": start_time,
            "endTime": end_time,
            "businessReason": business_reason,
            "businessReasonId": reason_id,
            "maxQuantity": max_qty,
            "limitQuantity": limit_qty,
            "userId": 10937481,
            "userEmail": "<EMAIL>"
        }
        self.post(url="/central/promotion_planning/v2/promo_event_products/edit", json=json_data, headers=header)
        return self.response

    def get_promotion_list(self, sales_org='', start_date='', end_date='', event_type='', event_code='',
                           department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
                           is_unlinked_only='', page_no='', page_size='', sort_by='', sort_order='', source=''):
        """获取event list"""
        params_data = {
            "sales_org_ids": sales_org,
            "start_date": start_date,
            "end_date": end_date,
            "event_type_id": event_type,
            "event_code": event_code,
            "department": department,
            "product_owner_id": local_owner_id,
            "product_ids": sku,
            "title": title,
            "promo_reason": promo_reason,
            "promo_type": promo_type,
            "is_unlinked_only": is_unlinked_only,
            "pageNo": page_no,
            "pageSize": page_size,
            "sortBy": sort_by,
            "sortOrder": sort_order,
            "source": source
        }
        self.get(url="/central/promotion_planning/v2/promo_event_products_new", params=params_data, headers=header)
        return self.response

    def delete_promotion(self, pep_ids, price_special_ids):
        json_data = {
            "pepIds": pep_ids,
            "priceSpecialIds": price_special_ids,
            "user": "<EMAIL>"
        }
        self.post(url="/central/promotion_planning/v2/promo_event_products/delete", json=json_data, headers=header)
        return self.response

    def promotion_link_event(self, json_data):
        self.post(url="/central/promotion_planning/v2/promo_event_products/link", json=json_data, headers=header)
        return self.response

    def product_view_export(self, sales_org='', start_date='', end_date='', event_type='', event_code='',
                            department='', local_owner_id='', sku='', title='', promo_reason='', promo_type='',
                            is_unlinked_only='', page_no='', page_size='', sort_by='', sort_order='', source=''):
        """获取event list"""
        params_data = {
            "sales_org_ids": sales_org,
            "start_date": start_date,
            "end_date": end_date,
            "event_type_id": event_type,
            "event_code": event_code,
            "department": department,
            "product_owner_id": local_owner_id,
            "product_ids": sku,
            "title": title,
            "promo_reason": promo_reason,
            "promo_type": promo_type,
            "is_unlinked_only": is_unlinked_only,
            "pageNo": page_no,
            "pageSize": page_size,
            "sortBy": sort_by,
            "sortOrder": sort_order,
            "source": source
        }
        self.get(url="/central/promotion_planning/v2/promo_event_products/download", params=params_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
