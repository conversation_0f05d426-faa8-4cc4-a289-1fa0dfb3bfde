
import weeeTest
from weeeTest.utils import jmespath
from weeeTest import weeeConfig


class UserLogin(weeeTest.TestCase):

    def get_common_auth(self, account, password, login_platform="WMS"):
        """
        获取登录账号的token
        :param account: 登录账号的user_id
        :param password: 登录账号的密码
        :param login_platform: 登录平台 如:“WMS”
        :return:
        """
        body = {
            "account": account,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.post(url="/hub/auth/user/login", json=body)
        if jmespath(self.response, "object.token"):
            token = "Bearer " + jmespath(self.response, "object.token")
            return token

    def get_vendor_auth(self, account, password):
        """
        获取vendor-portal平台的token
        :param account:
        :param password:
        :return:
        """
        body = {
            "email": account,
            "password": password
        }
        self.post(url="/vendor/service/v1/user/loginIn", json=body)
        if jmespath(self.response, "object.token"):
            token = "Bearer " + jmespath(self.response, "object.token")
            return token


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    # UserLogin().get_common_auth("7169981", "chen13142")
    print(UserLogin().get_vendor_auth("<EMAIL>", 123456))










