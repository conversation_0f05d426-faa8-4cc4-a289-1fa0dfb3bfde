
import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath

class UserLogin(weeeTest.TestCase):

    def user_login(self, account: str, password: str):
        if account is None or password is None:
            raise Exception('登录的account,password不能为空')
        """
        获取登录账号的token
        :param account: 登录账号的user_id
        :param password: 登录账号的密码
        :param login_platform: 登录平台 如:“cs”
        :return:
        """
        body = {
            "account": account,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": "cs"
        }

        header = {}
        self.post(url="/hub/auth/user/login", json=body)
        auth = jmespath(self.response, "object.token")
        username = jmespath(self.response, "object.user_name")
        log.info(f'登录成功,user_id: {account}, user_name:{username}')
        if auth is not None and len(auth) > 0:
            log.info(f'登录成功auth:{auth}')
            header["authorization"] = 'Bearer ' + auth
            header["weee_user"] = username
        else:
            log.info(f'登录失败,msg:{jmespath(self.response, "message")}')

        message_id = jmespath(self.response, "message_id")

        # header_str = str(header)
        #result = {"header": header_str, "message_id": message_id}

        #return result
        return header, message_id
        # return header










