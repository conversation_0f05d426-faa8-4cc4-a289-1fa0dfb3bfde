import weeeTest
from weeeTest import weeeConfig


class VendorLoginInterfaces(weeeTest.TestCase):
    """供应商平台登录相关接口"""

    def vendor_login(self, data):
        """
        供应商平台登录
        :param data: 供应商账号
        :return:
        """
        json_data = {

            "email": None,
            "password": None
        }
        header = {"User-Agent": "weee-web1"}
        json_data.update(data)
        self.post(url="/vendor/service/v1/user/loginIn", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
