import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header

class MkplListQuery(weeeTest.TestCase):
    """Mkpl_Settlement -> Business Voucher相关列表查询"""

    def mkpl_settlement_list(self, biz_type=''):
        """Business Voucher -> MKPL tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 20,
            "pageNum": 1,
            "bizType": biz_type
        }
        self.post(url="/central/finance/fsWssVoucherMkplRest/listGroupByOrderId", json=json_data, headers=header)
        return self.response

    def storageFee_list(self):
        """Business Voucher -> Storage Fee tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 30,
            "sorter": {
                "field": "",
                "order": "",
            }
        }
        self.post(url="/central/finance/businessStorageRest/queryStorageFeeListGroup", json=json_data, headers=header)
        return self.response

    def vas_list(self):
        """Business Voucher -> VAS tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 30,
            "sorter": {
                "field": "",
                "order": "",
            }
        }
        self.post(url="/central/finance/businessVasRest/queryVasListGroup", json=json_data, headers=header)
        return self.response

    def csbw_list(self):
        """Business Voucher -> CSBW tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 30,
            "sorter": {
                "field": "",
                "order": "",
            }
        }
        self.post(url="/central/finance/businessIndirectRest/queryIndirectListGroup", json=json_data, headers=header)
        return self.response

    def roReturn_list(self):
        """Business Voucher -> RO Return tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 30,
            "sorter": {
                "field": "",
                "order": "",
            }
        }
        self.post(url="/central/finance/businessVasRest/getRoReturnGroup", json=json_data, headers=header)
        return self.response


class FinanceVoucherListQuery(weeeTest.TestCase):
    """Mkpl_Settlement -> Finance Voucher相关列表查询"""
    def financeMkpl_list(self, business_type=''):
        """Finance Voucher -> MKPL/FBW/MFBW Finance Voucher tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 20,
            "pageNum": 1,
            "dataSelectType": 1,
            "businessType": business_type
        }
        self.post(url="/central/finance/fsWssFinanceVoucherRest/pageList", json=json_data, headers=header)
        return self.response

    def revenue_list(self, business_type=''):
        """Finance Voucher -> MKPL Revenue tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 20,
            "pageNum": 1,
            "dataSelectType": 2,
            "businessType": business_type
        }
        self.post(url="/central/finance/fsWssRevenueRest/pageList", json=json_data, headers=header)
        return self.response

    def finance_ro_return_list(self):
        """Finance Voucher -> RO_RETURN tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 30,
            "filters": {
                "categoryType":"RO_RETURN"
            }
        }
        self.post(url="/central/finance/fsWssArVoucherRest/arVoucherList", json=json_data, headers=header)
        return self.response

    def finance_tms_approval_list(self):
        """Finance Voucher -> TMS Approval Voucher tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 20,
            "pageNum": 1
        }
        self.post(url="/central/finance/fsWssFinanceVoucherPaymentRest/pageList", json=json_data, headers=header)
        return self.response

    def finance_tms_sap_list(self):
        """Finance Voucher -> TMS sap Voucher tab查询"""
        json_data = {
            "current": 1,
            "pageSize": 20,
            "pageNum": 1
        }
        self.post(url="/central/finance/fsWssFinanceVoucherPaymentRest/sapVoucherPageList", json=json_data, headers=header)
        return self.response






if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
