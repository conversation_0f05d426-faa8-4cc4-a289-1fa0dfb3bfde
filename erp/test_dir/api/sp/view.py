import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class ViewManagement(weeeTest.TestCase):
    """相关配置接口"""

    def get_org_view_tree(self):
        """获取org view的组织架构tree"""
        self.get(url="/central/merchandising/sps/view/org/58", headers=header)
        return self.response

    def get_org_view_plan_list(self, params_data):
        """获取指定用户可查看的plan list"""
        self.get(url="/central/merchandising/sps/view/getViewList", params=params_data, headers=header)
        return self.response

    def get_org_view_config(self):
        """获取org view的配置信息"""
        self.get(url="/central/merchandising/sps/view/config/58/618", headers=header)
        return self.response

    def get_org_view_options(self):
        """获取org view的下拉选项"""
        self.get(url="/central/merchandising/sps/view/options/618", headers=header)
        return self.response

    def update_review_content(self, json_data, plan_id):
        """更新plan的review内容"""
        url = "/central/merchandising/sps/view/review/" + plan_id
        self.post(url=url, json=json_data, headers=header)
        return self.response

    def get_org_view_chart_data(self, chart_id, params_data):
        """获取org view的图表数据"""
        url = "/central/merchandising/sps/view/series/" + chart_id
        self.get(url=url, headers=header, params=params_data)
        return self.response

    def get_my_view_share_report_data(self, method):
        """获取my view下面的share和report的数据"""
        url = "/central/merchandising/sps/view/" + method + '/58'
        self.get(url=url, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
