import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class PlanManagement(weeeTest.TestCase):
    """相关配置接口"""

    def add_update_plan(self, json_data, metrics_list=None):
        """新增plan"""
        json_data.update({"metrics": metrics_list})
        print(json_data)
        self.post(url="/central/merchandising/sps/plan", json=json_data, headers=header)
        return self.response

    def import_plan_metrics(self):
        """创建plan的时候导入active的metrics"""
        self.get(url="/central/merchandising/sps/plan/importMetrics", headers=header)
        return self.response

    def get_plan_list(self, params_data):
        """获取plan列表"""
        self.get(url="/central/merchandising/sps/plan", params=params_data, headers=header)
        return self.response

    def get_single_plan_detail(self, plan_id=''):
        """获取单个plan详情"""
        url = "/central/merchandising/sps/plan/" + plan_id
        self.get(url=url, headers=header)
        return self.response

    def update_plan_status(self, metrics_id='', status=''):
        """获取plan列表"""
        url = "/central/merchandising/sps/plan/" + metrics_id
        json_data = {
            "status": status
        }
        self.patch(url=url, json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
