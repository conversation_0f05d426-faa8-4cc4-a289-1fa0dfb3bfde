import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class MetricsManagement(weeeTest.TestCase):
    """相关配置接口"""

    def add_update_metrics(self, json_data):
        """新增metrics"""
        self.post(url="/central/merchandising/sps/metrics", json=json_data, headers=header)
        return self.response

    def get_metrics_list(self, params_data):
        """获取metrics列表"""
        self.get(url="/central/merchandising/sps/metrics", params=params_data, headers=header)
        return self.response

    def get_single_metrics_detail(self, metrics_id=''):
        """获取单个metrics详情"""
        url = "/central/merchandising/sps/metrics/" + metrics_id
        self.get(url=url, headers=header)
        return self.response

    def update_metrics_status(self, metrics_id='', status=''):
        """获取metrics列表"""
        url = "/central/merchandising/sps/metrics/" + metrics_id
        json_data = {
            "status": status
        }
        self.patch(url=url, json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
