import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class DeskOrderCase(weeeTest.TestCase):

    def get_user_info(self):
        """获取用户详情"""
        self.get(url="/cs/desk/auth/getUserInfo", headers=header)
        return self.response

    def desk_order_case_create(self, data):
        """desk_order_create接口"""
        self.post(url="/cs/desk/case/create", json=data, headers=header)
        return self.response

    def case_interaction_List(self, data):
        """case_interaction_List接口"""
        self.post(url="/cs/desk/case/interactionList", json=data, headers=header)
        return self.response

    def send_EmailAndResolve(self, data):
        """回复客人并处理完case"""
        self.post(url="/cs/desk/case/sendEmailAndResolve", json=data, headers=header)
        return self.response
    def pageQuery_DataView(self, data):
        """页面查询case view all"""
        self.post(url="/cs/desk/search/pageQueryDataView?dataViewId=30", json=data, headers=header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'