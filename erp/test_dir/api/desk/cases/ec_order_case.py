import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class EcOrderCase(weeeTest.TestCase):

    def ec_order_case_create(self, data):
        """ec_order_create接口"""
        self.post(url="/ec/cs/case/create/order", json=data, headers=desk_header)
        return self.response
    def page_query_data_view(self, data):
        """页面查询data view"""
        self.post(url="/cs/desk/search/pageQueryDataView?dataViewId=30", json=data, headers=desk_header)
        return self.response
    def assigned_to_me(self, data):
        """指给某人处理"""
        self.post(url="/cs/desk/case/assignedToMe", json=data, headers=desk_header)
        return self.response
    def tree_ByCase_Type(self, data):
        """更新category"""
        self.post(url="/cs/desk/case/treeByCaseType", json=data, headers=desk_header)
        return self.response
    def update_Description(self, data):
        """更新回复描述"""
        self.post(url="/cs/desk/case/updateDescription", json=data, headers=desk_header)
        return self.response
    def reply_Message_And_Resolve(self, data):
        """回复客人并解决问题"""
        self.post(url="/cs/desk/case/replyMessageAndResolve", json=data, headers=desk_header)
        return self.response
if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'