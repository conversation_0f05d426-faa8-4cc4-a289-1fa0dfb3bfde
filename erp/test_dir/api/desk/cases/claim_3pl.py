import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class Claim3pl(weeeTest.TestCase):
    def desk_claim_3pl_create(self, data):
        """desk claim 3pl 创建接口"""
        self.post(url=f"/central/cs/claim/tp/create", json=data, headers=desk_header)
        return self.response
    def claim_3pl_find_list(self, data):
        """desk claim 3pl查询列表接口"""
        self.post(url=f"/central/cs/claim/tp/findList", json=data, headers=desk_header)
        return self.response
    def claim_3pl_edit(self, c_id):
        """desk claim 3pl编辑接口"""
        self.get(url=f"/central/cs/claim/tp/edit/{c_id}", headers=desk_header)
        return self.response
    def claim_3pl_modify_status(self, data):
        """desk claim 3pl修改状态"""
        self.post(url=f"/central/cs/claim/tp/modifyStatus", json=data, headers=desk_header)
        return self.response
    def claim_3pl_pending(self, data):
        """desk claim 3pl pending状态"""
        self.post(url=f"/central/cs/claim/tp/findList", json=data, headers=desk_header)
        return self.response
    def claim_3pl_approved(self, data):
        """desk claim 3pl pending状态"""
        self.post(url=f"/central/cs/claim/tp/findList", json=data, headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'