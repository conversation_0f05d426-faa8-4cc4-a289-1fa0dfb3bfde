import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskPhoneCase(weeeTest.TestCase):
    def desk_call_record_create_case(self, data):
        """desk创建phone case"""
        self.post(url="/cs/desk/call/record/createCase", json=data, headers=desk_header)
        return self.response
    def add_Relate_Data(self, data):
        """绑定相关订单"""
        self.post(url="/cs/desk/caseData/addRelateData", json=data, headers=desk_header)
        return self.response
    def update_Description(self, data):
        """更新描述和category"""
        self.post(url="/cs/desk/case/updateDescription", json=data, headers=desk_header)
        return self.response
    def send_email_and_resolve(self, data):
        """发送邮件回复客人并解决case"""
        self.post(url="/cs/desk/case/sendEmailAndResolve", json=data, headers=desk_header)
        return self.response
    def pageQuery_DataView(self, data):
        """页面查询case view all"""
        self.post(url="/cs/desk/search/pageQueryDataView?dataViewId=30", json=data, headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'