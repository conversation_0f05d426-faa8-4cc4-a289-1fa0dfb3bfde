
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header



class CustomerServiceCases(weeeTest.TestCase):

    def case_view_apple_menu(self, data=3):
        """cases菜单"""
        self.get(url=f"/cs/desk/dataViewApply/list/{data}", headers=header)
        return self.response

    def get_display_field_list(self, data=29):
        """cases查询列表tab"""
        self.get(url=f"/cs/desk/dataViewApply/getDisplayFieldList/{data}", headers=header)
        return self.response

    def get_case_page_data_list(self, data=29):
        """cases查询数据结果"""
        json_data = {"pageNum": 1, "pageSize": 50}
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId={data}", json=json_data, headers=header)
        return self.response

    def ticket_pending_to_weee(self, data):
        """pending to weee"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/cases/doPendingToWeee", json=data, headers=header)
        return self.response

    def ticket_pending_to_customer(self, data):
        """pending to customer"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/cases/doPendingToCustomer", json=data, headers=header)
        return self.response

    def ticket_do_processing(self, data):
        """processing"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/cases/doProcessing", json=data, headers=header)
        return self.response

    def ticket_do_handover(self, data):
        """Handover"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/cases/doHandover", json=data, headers=header)
        return self.response

    def ticket_do_solved(self, data):
        """Handover"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/cases/doSolved", json=data, headers=header)
        return self.response

    def owner_assignee_info_list(self, data):
        """Assignee分配人员列表"""
        # data = {"param": "qifang.ye"}
        self.post(url="/cs/desk/cases/ownerAssigneeInfoList", json=data, headers=header)
        return self.response

    def assignee_to_other(self, data):
        """Assignee分配操作"""
        # data = {"caseNumber":"100016053","assignee":"4","assigneeType":"1"}
        self.post(url="/cs/desk/cases/assignedTo", json=data, headers=header)
        return self.response

    def assigned_to_me(self, data):
        """操作分配给自己"""
        # data = {"caseNumber":"*********"}
        self.post(url=f"/cs/desk/cases/assignedToMe", json=data, headers=header)
        return self.response

    def get_category_tree_by_case_type(self, data):
        """根据case type获取category tree"""
        # data = {"caseType": 1}
        self.post(url="/cs/desk/cases/treeByCaseType", json=data, headers=header)
        return self.response

    def update_case_description(self, data):
        """更新case描述"""
        # eg_data={"ownerId":29,"ownerType":1,"assignee":29,"assigneeType":1,"priority":10,"importance":2,"language":"zh","caseType":1,"categoryIds":",285,290,326,",
        #          "salesOrg":4,"email":"<EMAIL>","seller":{"name":"-1 - Weee!","id":-1},"weeeUserId":8107410,"phone":"","accountId":568703,"caseId":"15931"}
        self.post(url="/cs/desk/cases/updateDescription", json=data, headers=header)
        return self.response

    def batch_update_case_description(self, data):
        """批量更新case描述"""
        # data = {"caseType": 5, "categoryIds": ",288,322,524,", "caseIdList": [16082]}
        self.post(url="/cs/desk/cases/batchUpdateDescription", json=data, headers=header)
        return self.response

    def merge_case(self, data):
        """merge合并case"""
        # data = {"caseNumbers": ["*********","*********"]}
        self.post(url="/cs/desk/cases/mergeCaseView", json=data, headers=header)
        return self.response

    def batch_transfer_skill_type_case(self, data):
        """批量转变cases kill_type"""
        # data = {"caseNumberList":["*********"],"skillTypeId":1,"comment":"QA Batch Transfer"}
        self.post(url="/cs/desk/cases/batchTransfer", json=data, headers=header)
        return self.response

    def case_interaction_list(self, data):
        """活动列表"""
        # data = {"caseNumberList":["*********"],"skillTypeId":1,"comment":"QA Batch Transfer"}
        self.post(url="/cs/desk/cases/interactionList", json=data, headers=header)
        return self.response

    def get_interaction_info(self, data):
        """获取互动数据"""
        self.get(url=f"/cs/desk/cases/getInteractionInfo/{data}", headers=header)
        return self.response

    def case_description(self, data):
        """ """
        self.get(url=f"/cs/desk/cases/caseDescription/{data}",headers=header)
        return self.response

    def get_case_follow_status(self, data):
        """获取case是否follow"""
        self.get(url=f"/cs/desk/cases/hasFollow/{data}", headers=header)
        return self.response

    def get_seller_info(self, data):
        """获取seller详情"""
        self.get(url=f"/cs/desk/cases/queryMkplSeller/{data}", headers=header)
        return self.response

    def get_seller_case_list(self, data):
        """获取seller case列表"""
        self.get(url=f"/cs/desk/cases/sellerCaseList/{data}", headers=header)
        return self.response

    def get_images_list(self, data):
        """获取image"""
        self.get(url=f"/cs/desk/caseFile/imageList/{data}", headers=header)
        return self.response

    def get_files_list(self, data):
        """获取附件"""
        self.get(url=f"/cs/desk/caseFile/fileList/{data}", headers=header)
        return self.response

    def get_related_case_list(self, data):
        """获取related cases"""
        self.get(url=f"/cs/desk/relateCase/list/{data}", headers=header)
        return self.response

    def get_tag_list(self, data):
        """获取tag"""
        self.get(url=f"/cs/desk/tag/{data}", headers=header)
        return self.response

    def get_email_list(self, data):
        """获取email"""
        self.get(url=f"/cs/desk/email/cases/{data}?pageNum=1&pageSize=3", headers=header)
        return self.response

    def case_ticket_list(self, data):
        """获取关联ticket列表"""
        # eg_data = {"caseId":"16082","pageNum":1,"pageSize":3}
        self.post(url="/cs/desk/caseTicket/listPage", json=data, headers=header)
        return self.response

    def get_ticket_related_info(self, data):
        """获取关联ticket详情"""
        # eg_data = {"caseNumber":"*********"}
        self.post(url="/cs/desk/ticket/getTicketRelatedInfo", json=data, headers=header)
        return self.response

    def delete_related_ticket(self, data):
        """删除关联ticket"""
        # eg_data = {"caseId":16082}
        self.post(url="/cs/desk/caseTicket/delAllRelateTicket", json=data, headers=header)
        return self.response

    def add_related_ticket(self, data):
        """增加关联ticket"""
        # eg_data = {"caseId":16082,"ticketNumber":"2023071200001"}
        self.post(url="/cs/desk/caseTicket/addRelateTicket", json=data, headers=header)
        return self.response

    def new_add_ticket(self, data):
        """新增增加ticket"""
        # eg_data = {"category":[201,122,135],"subject":"QA Subject","description":"<p>Description</p>",
        #         "accessTime":"2023-07-24","userId":10930772,"caseNumber":"*********",
        #         "userEmail":"<EMAIL>","type":1,"categoryIds":",201,122,135,"}
        self.post(url="/cs/desk/ticket/addTicket", json=data, headers=header)
        return self.response

    def validate_ticket(self, data):
        """新增增加ticket"""
        # eg_data = {"category":[201,122,135],"subject":"QA Subject","description":"<p>Description</p>",
        #         "accessTime":"2023-07-24","userId":10930772,"caseNumber":"*********",
        #         "userEmail":"<EMAIL>","type":1,"categoryIds":",201,122,135,"}
        self.post(url="/cs/desk/ticket/validateTicket", json=data, headers=header)
        return self.response

    def do_escalate(self, data):
        """升级Escalate"""
        # data = {"caseNumber":"*********","assignee":"1","assigneeType":"2","comment":"Escalate Comment"}
        self.post(url="/cs/desk/cases/doEscalate", json=data, headers=header)
        return self.response

    def get_category_predefined_field(self, data):
        """获取类别预定义字段"""
        self.post(url=f"/cs/desk/ticket/getCategoryPredefinedField/{data}", headers=header)
        return self.response

    def add_case_comment(self, data):
        """获取类别预定义字段"""
        # eg_data={"caseId":15931,"content":"<p><span style=\"color: rgba(0, 0, 0, 0.85);\">QA Comments</span></p>","mentions":[]}
        self.post(url="/cs/desk/caseComment/addComment", json=data, headers=header)
        return self.response

    def case_comment_list(self, data):
        """获取类别预定义字段"""
        # eg_data={"caseId":"15931","sortBy":"1"}
        self.post(url="/cs/desk/caseComment/list", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'

























