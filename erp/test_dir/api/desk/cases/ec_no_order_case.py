
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class EcNoOrderCase(weeeTest.TestCase):

    def get_ec_user_info(self):
        """获取用户详情"""
        self.get(url="/ec/cs/user/getUserInfo", headers=desk_header)
        return self.response

    def get_ec_no_order_case_list(self, data):
        """获取no order列表详情"""
        self.post(url="/ec/cs/case/queryCaseList", json=data, headers=desk_header)
        return self.response

    def get_ec_no_order_case_category(self, data):
        """获取no order Category"""
        self.post(url="/ec/cs/case/queryCaseCategory", json=data, headers=desk_header)
        return self.response

    def create_ec_no_order_case(self, data):
        """创建no order cases"""
        self.post(url="/ec/cs/case/createCase", json=data, headers=desk_header)
        return self.response

    def get_ec_no_order_case_detail(self, data):
        """获取no order详情"""
        self.post(url="/ec/cs/case/getCaseInfo", json=data, headers=desk_header)
        return self.response

    def cancel_ec_no_order_case(self, data):
        """取消no order"""
        self.post(url="/ec/cs/case/cancelCase", json=data, headers=desk_header)
        return self.response

    def create_ec_no_order_case_message(self, data):
        """回复no_order消息"""
        self.post(url="/ec/cs/case/createCaseMessage", json=data, headers=desk_header)
        return self.response

    def update_ec_no_order_case_message_status(self, data):
        """更新no order消息"""
        self.post(url="/ec/cs/case/updateCaseMessageStatus", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'


