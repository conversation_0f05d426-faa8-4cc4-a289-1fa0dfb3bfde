import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class DeskCaseProfile(weeeTest.TestCase):

    def desk_case_profile_create(self, data):
        """desk_case_profile_create接口"""
        self.post(url=f"/ec/cs/case/createCase", json=data, headers=desk_header)
        return self.response

    def pageQuery_Data_View(self, data):
        """页面查询all view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=30", json=data, headers=desk_header)
        return self.response

    def get_search_case_list(self, data):
        self.post(url=f"/cs/desk/search/objPageList", json=data, headers=desk_header)
        return self.response

    def enter_CaseDetail(self, c_id):
        """进入case详情页"""
        self.get(url=f"/cs/desk/case/enterCaseDetail/{c_id}", headers=desk_header)
        return self.response

    def assigned_ToMe(self, data):
        """指给业务处理"""
        self.post(url=f"/cs/desk/case/assignedToMe", json=data, headers=desk_header)
        return self.response

    def update_WeeeUserId(self, data):
        """绑定另一个customer"""
        self.post(url=f"/cs/desk/case/updateWeeeUserId", json=data, headers=desk_header)
        return self.response

    def unbind_Order(self, data):
        """取消绑定某个订单"""
        self.post(url=f"/cs/desk/caseData/unbindOrder", json=data, headers=desk_header)
        return self.response

    def upload_Image(self, data):
        """上传图片"""
        self.post(url=f"/cs/desk/caseFile/uploadImage", json=data, headers=desk_header)
        return self.response

    def upload_File(self, data):
        """上传文件"""
        self.post(url=f"/cs/desk/caseFile/uploadFile", json=data, headers=desk_header)
        return self.response

    def add_All(self, data):
        """新增tag"""
        self.post(url=f"/cs/desk/tag/addAll", json=data, headers=desk_header)
        return self.response

    def validate_Ticket(self, data):
        """新增编辑关联ticket"""
        self.post(url=f"/cs/desk/ticket/validateTicket", json=data, headers=desk_header)
        return self.response

    def add_Ticket(self, data):
        """新增编辑关联ticket成功"""
        self.post(url=f"/cs/desk/ticket/addTicket", json=data, headers=desk_header)
        return self.response

    def add_Related_Date(self, data):
        """新增相关联数据"""
        self.post(url=f"/cs/desk/caseData/addRelateData", json=data, headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
