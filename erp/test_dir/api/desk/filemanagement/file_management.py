import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskFileManagement(weeeTest.TestCase):
    def owned_by_me(self, data):
        """owned by me"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response

    def shared_with_me(self, data):
        """shared with me"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response

    def recently_viewed(self, data):
        """recently viewed"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response
    def following_viewed(self, data):
        """following viewed"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response
    def cs_viewed(self, data):
        """cs viewed"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response
    def seller_viewed(self, data):
        """seller viewed"""
        self.post(url=f"/cs/desk/file/listPage", json=data, headers=desk_header)
        return self.response
if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'