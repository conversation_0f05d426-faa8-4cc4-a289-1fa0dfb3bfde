import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class DeskMyTask(weeeTest.TestCase):
    def my_task_view(self, data):
        """页面查询my task 视图"""
        self.get(url=f"/cs/desk/my_task/view", json=data, headers=desk_header)
        return self.response

    def my_task_recently(self, data):
        """页面查询my task recently list"""
        self.post(url=f"/cs/desk/my_task/recently", json=data, headers=desk_header)
        return self.response

    def my_task_ticket_detail(self, data):
        """页面查询my_task_ticket_detail"""
        self.post(url=f"/cs/desk/ticket/ticketDetail", json=data, headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'