import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskEmailCenter(weeeTest.TestCase):
    def email_center_view(self, data):
        """email center all view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=2178", json=data, headers=desk_header)
        return self.response

    def email_center_detail(self, c_id):
        """email center detail"""
        self.get(url=f"/cs/desk/email/{c_id}", headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
