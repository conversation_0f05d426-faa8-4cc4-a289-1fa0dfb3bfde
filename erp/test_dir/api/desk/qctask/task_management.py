import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class TaskManagement(weeeTest.TestCase):
    def qc_task_page(self, data):
        """qc_task_page"""
        self.post(url=f"/cs/desk/qc_task/page", json=data, headers=desk_header)
        return self.response

    def qc_task_assign_agent(self, data):
        """qc task assign agent"""
        self.post(url=f"/cs/desk/qc_task/assign_agent", json=data, headers=desk_header)
        return self.response

    def qc_task_add_tag(self, data):
        """qc task add tag"""
        self.post(url=f"/cs/desk/qc_task/add_tag", json=data, headers=desk_header)
        return self.response

    def qc_task_remove_tag(self, data):
        """qc task remove tag"""
        self.post(url=f"/cs/desk/qc_task/remove_tag", json=data, headers=desk_header)
        return self.response

    def qc_score_all_view(self, data):
        """qc score all view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=5109", json=data, headers=desk_header)
        return self.response

    def qc_score_case_view(self, data):
        """qc score case view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=5110", json=data, headers=desk_header)
        return self.response

    def qc_score_call_view(self, data):
        """qc score call view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=5111", json=data, headers=desk_header)
        return self.response

    def qc_score_my_score_view(self, data):
        """qc score my score view"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=5112", json=data, headers=desk_header)
        return self.response

    def qc_task_new_case_view(self, data):
        """my qc task new case data view"""
        self.post(url=f"/cs/desk/qc_task/data_view", json=data, headers=desk_header)
        return self.response

    def qc_task_new_call_view(self, data):
        """my qc task new call data view"""
        self.post(url=f"/cs/desk/qc_task/data_view", json=data, headers=desk_header)
        return self.response

    def qc_task_complete_view(self, data):
        """my qc task complete data view"""
        self.post(url=f"/cs/desk/qc_task/data_view", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'