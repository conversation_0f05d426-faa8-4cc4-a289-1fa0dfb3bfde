import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class Space(weeeTest.TestCase):

    def desk_kb_space_create(self, data):
        """desk创建space"""
        self.post(url="/cs/desk/kb/space/create", json=data, headers=desk_header)
        return self.response

    def space_list(self, data):
        """space list"""
        self.get(url="/cs/desk/kb/space?type=&pageNum=1&pageSize=50", params=data, headers=desk_header)
        return self.response

    def add_article(self, data):
        """add article"""
        self.post(url="/cs/desk/kb/article/addArticle", json=data, headers=desk_header)
        return self.response

    def edit_article_detail(self, data):
        """article edit"""
        self.post(url="/cs/desk/kb/article/editArticleDetail", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'