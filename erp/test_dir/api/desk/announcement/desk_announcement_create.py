import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskAnnouncementCreate(weeeTest.TestCase):
    def desk_announcement_create(self, data):
        """desk_announcement_create接口"""
        self.post(url=f"/cs/desk/announcement/create", json=data, headers=desk_header)
        return self.response
    def page_query_data_view(self, data):
        """页面查询my announcement"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=26", json=data, headers=desk_header)
        return self.response
    def desk_announcement_detail(self, c_id):
        """announcement detail"""
        self.get(url=f"/cs/desk/announcement/detail/{c_id}", headers=desk_header)
        return self.response
    def desk_announcement_edit(self, data):
        """announcement edit"""
        self.post(url=f"/cs/desk/announcement/edit", json=data, headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
