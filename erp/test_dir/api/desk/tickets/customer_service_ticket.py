
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header



class CustomerServiceTickets(weeeTest.TestCase):

    def ticket_menu_list(self):
        """ticket菜单列表"""
        self.get(url="/cs/desk/ticket/viewList", headers=header)
        return self.response

    def ticket_list(self, data):
        """ticket列表"""
        # data = {"pageNum": 1, "pageSize": 50, "type": "all"}
        self.post(url="/cs/desk/ticket/list", json=data, headers=header)
        return self.response

    def validate_ticket(self, data):
        """新增ticket"""
        # eg_data={"category":[201,122,135],"subject":"QA Subject","description":"<p>Description</p>",
        #         "accessTime":"2023-07-24","userId":10930772,"caseNumber":"100016082",
        #         "userEmail":"<EMAIL>","type":1,"categoryIds":",201,122,135,"}
        self.post(url="/cs/desk/ticket/validateTicket", json=data, headers=header)
        return self.response

    def ticket_detail(self, data):
        """ticket详情"""
        # eg_data={"processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","processInstanceId":"3c9aafdf-29f9-11ee-b999-f28617956cdc",
        #            "taskId":"3df36229-29f9-11ee-b999-f28617956cdc","taskKey":"cs_engineering","ticketNumber":"2023072400005"}
        self.post(url="/cs/desk/ticket/ticketDetail", json=data, headers=header)
        return self.response

    def ticket_follow(self, data):
        """ticket follow, 1-unfollow, 2-follow"""
        # eg_data={"followType":1,"ticketId":274}
        self.post(url="/cs/desk/ticket/ticketFollow", json=data, headers=header)
        return self.response

    def ticket_flow_operation(self, data):
        """BPM流程节点"""
        # eg_data={"processInstanceId": "3c9aafdf-29f9-11ee-b999-f28617956cdc"}
        self.get(url="/central/bpm/admin/flow/flowOperation/listFlowTaskComment", params=data, headers=header)
        return self.response

    def add_ticket_chat_interaction(self, data):
        """新增回复"""
        # eg_data={"chatLogDto":{"chatContent":"<p><img src=\"https://img06.test.weeecdn.com/cs/image/221/905/34C5E1DEDC2EC663_0x0.jpeg\">测试数据</p>","chatType":4,
        #          "processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","processInstanceId":"3c9aafdf-29f9-11ee-b999-f28617956cdc","chatMentionUsers":[]}}
        self.post(url="/central/bpm/task/operator/chatLog/add", json=data, headers=header)
        return self.response

    def ticket_chat_log(self, data):
        """回复log"""
        # eg_data={"chatLogDtoFilter":{"processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","zoneId":"America/Chicago",
        #          "processInstanceId":"3c9aafdf-29f9-11ee-b999-f28617956cdc"},"orderParam":[{"fieldName":"recCreateTime","asc":1}]}
        self.post(url="/central/bpm/task/operator/chatLog/list", json=data, headers=header)
        return self.response

    def ticket_submit_task(self, data):
        """回复ticket的task状态 reject agree stop"""
        # eg_data={"comment":"Stop Comment","processInstanceId":"c300c4af-06a2-11ee-846b-dee94e7f8d2b",
        #          "taskId":"8296354c-131f-11ee-ba3f-1a31944ed195","ticketNumber":"2023060900013","type":"stop"}
        self.post(url="/cs/desk/ticket/submitTask", json=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'



















