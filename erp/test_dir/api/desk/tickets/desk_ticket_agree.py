import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskTicketAgree(weeeTest.TestCase):
    def add_ticket(self, data):
        """新增ticket"""
        self.post(url=f"/cs/desk/ticket/addTicket", json=data, headers=desk_header)
        return self.response

    def list_page(self, data):
        """列表页"""
        self.post(url=f"/cs/desk/caseTicket/listPage", json=data, headers=desk_header)
        return self.response

    def ticket_detail(self, data):
        """ticket 详情页"""
        self.post(url=f"/cs/desk/ticket/ticketDetail", json=data, headers=desk_header)
        return self.response

    def review_ticket(self, data):
        """review ticket"""
        self.post(url=f"/cs/desk/ticket/review", json=data, headers=desk_header)
        return self.response

    def submit_task(self, data):
        """提交task"""
        self.post(url=f"/cs/desk/ticket/submitTask", json=data, headers=desk_header)
        return self.response

    def ticket_list(self, data):
        """ticket list"""
        self.post(url=f"/cs/desk/ticket/list", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'