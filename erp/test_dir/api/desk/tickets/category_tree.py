
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class DeskCategoryTree(weeeTest.TestCase):

    def ticket_category_tree_info(self):
        """category"""
        self.get(url="/cs/desk/category/ticket/tree", headers=header)
        return self.response

    def ticket_category_tree_detail(self, data):
        """具体category详情"""
        self.get(url=f"/cs/desk/category/detail/{data}", headers=header)
        return self.response

    def ticket_add_category_tree(self, data):
        """新增category分类"""
        # eg_data={"name":"三级测试03","pid":199}
        self.post(url="/cs/desk/category/ticket/add", json=data, headers=header)
        return self.response

    def ticket_rename_category_tree(self, data):
        """修改category详情"""
        # eg_data={"name":"三级测试03","id":230}
        self.post(url="/cs/desk/category/ticket/rename", json=data, headers=header)
        return self.response

    def ticket_delete_category_tree(self, data):
        """删除category详情"""
        self.get(url=f"/cs/desk/category/ticket/delete/{data}", headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'




















