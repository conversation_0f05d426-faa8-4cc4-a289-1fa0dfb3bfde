import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskQuickTextAdd(weeeTest.TestCase):
    def desk_quick_text_add(self, data):
        """desk_quick_text_add接口"""
        self.post(url=f"/cs/desk/quickText/add", json=data, headers=desk_header)
        return self.response
    def quick_text_page_list(self,data):
        """quick text page list"""
        self.post(url=f"/cs/desk/search/pageQueryDataView?dataViewId=23", json=data, headers=desk_header)
        return self.response
    def quick_text_detail(self, c_id):
        """quick text detail"""
        self.get(url=f"/cs/desk/quickText/detail/{c_id}", headers=desk_header)
        return self.response
    def quick_text_edit(self, data):
        """quick text edit"""
        self.post(url=f"/cs/desk/quickText/edit", json=data, headers=desk_header)
        return self.response
    def quick_text_delete(self, c_id):
        """quick text delete"""
        self.get(url=f"/cs/desk/quickText/delete/{c_id}", headers=desk_header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'