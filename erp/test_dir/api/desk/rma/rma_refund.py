
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class RmaRefund(weeeTest.TestCase):

    def select_rma_refund_list(self, data):
        self.post(url="/cs/desk/rmaRefund/findList", json=data, headers=header)
        return self.response

    def create_rma_refund(self, data):
        self.post(url="/cs/desk/rmaRefund/createRefund", json=data, headers=header)
        return self.response

    def edit_rma_refund(self, data):
        self.post(url="/cs/desk/rmaRefund/editRefund", json=data, headers=header)
        return self.response

    def action_rma_refund(self, data):
        self.post(url="/cs/desk/rmaRefund/doAction", json=data, headers=header)
        return self.response

    def batch_approve_rma_refund(self, data):
        self.post(url="/cs/desk/rmaRefund/batchApprove", json=data, headers=header)
        return self.response

    def select_rma_refund_detail(self, data):
        self.get(url="/cs/desk/rmaRefund/getRefundDetailById", params=data, headers=header)
        return self.response

    def select_rma_refund_log(self, data):
        self.get(url="/cs/desk/rmaRefund/log", params=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = RmaRefund().select_rma_refund_list(data={"pageNum":1,"pageSize":1, "sortMap":{},"applyTimeRange":["2023-04-20","2023-07-21"]})
    print(resp)




