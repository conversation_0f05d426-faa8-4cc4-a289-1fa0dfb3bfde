import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header


class DeskRmaRefund(weeeTest.TestCase):
    def desk_get_valid_product_info(self, data):
        """获取有效产品信息"""
        self.get(url="/cs/desk/rmaRequest/getValidProductInfo", params=data, headers=desk_header)
        return self.response

    def desk_rma_create_request(self, data):
        """desk创建request"""
        self.post(url="/cs/desk/rmaRequest/createRequest?autoAction=true", json=data, headers=desk_header)
        return self.response

    def desk_rma_refund_do_action(self, data):
        """approved refund"""
        self.post(url="/cs/desk/rmaRefund/doAction", json=data, headers=desk_header)
        return self.response

    def desk_rma_get_refund_detail(self, data):
        """获取refund详情"""
        self.get(url="/cs/desk/rmaRefund/getRefundDetailById", params=data, headers=desk_header)
        return self.response

    def desk_rma_refund_find_list(self, data):
        """查询refund list"""
        self.post(url="/cs/desk/rmaRefund/findList", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
