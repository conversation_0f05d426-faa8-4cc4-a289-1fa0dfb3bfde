import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class RmaMkplOrderCaseApprove(weeeTest.TestCase):

    def ec_mkpl_order_case_create(self, data):
        """ec_mkpl_order_case_create接口"""
        self.post(url="/ec/cs/case/create/order", json=data, headers=desk_header)
        return self.response

    def get_Rma_Detail(self, data):
        """获取rma detail"""
        self.get(url=f"/cs/desk/rmaRequest/getRmaDetail?id={data['req_id']}&language=undefined", headers=desk_header)
        return self.response

    def rma_Request_approve(self, data):
        """审核request"""
        self.post(url="/cs/desk/rmaRequest/approve", json=data, headers=desk_header)
        return self.response

    def ec_cs_case_cancel_order(self,data):
        """ec取消mkpl order"""
        self.post(url="/ec/cs/case/cancel/order", json=data, headers=desk_header)
        return self.response

    def ec_cs_case_cancel_order_new(self,header, data):
        """ec取消mkpl order"""
        self.post(url="/ec/cs/case/cancel/order", json=data, headers=header)
        return self.response

if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
