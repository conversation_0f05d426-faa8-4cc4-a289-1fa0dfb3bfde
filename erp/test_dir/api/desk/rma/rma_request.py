
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class RmaRequest(weeeTest.TestCase):

    def select_rma_request_list(self, data):
        self.post(url="/cs/desk/rmaRequest/getAgentRequestPage", json=data, headers=header)
        return self.response

    def create_rma_request(self, data):
        self.post(url="/cs/desk/rmaRequest/createRequest", json=data, headers=header)
        return self.response

    def rma_request_detail(self, data):
        self.get(url="/cs/desk/rmaRequest/getRmaDetail", params=data, headers=header)
        return self.response

    def void_rma_request(self, data):
        self.post(url="/cs/desk/rmaRequest/voidRequest", json=data, headers=header)
        return self.response

    def get_ec_reason_rma(self):
        self.get(url="/cs/desk/rmaRequest/getEcReason", headers=header)
        return self.response

    def get_valid_product_info_rma(self):
        self.get(url="/cs/desk/rmaRequest/getValidProductInfo", headers=header)
        return self.response

    def get_reason_code_by_order_rma(self, data):
        self.get(url="/cs/desk/rmaRefund/getReasonCodeByOrderId", params=data, headers=header)
        return self.response

    def send_conversation_message_rma(self, data):
        self.post(url="/cs/desk/rmaRequest/sendMessage", params=data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = RmaRequest().select_rma_request_list(data={"pageNum":1,"pageSize":20})
    print(resp)







