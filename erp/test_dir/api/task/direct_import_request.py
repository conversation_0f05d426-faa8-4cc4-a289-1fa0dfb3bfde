import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header


class DirectImportManagement(weeeTest.TestCase):
    """相关配置接口"""

    def submit_request(self, processInstanceId='', taskId='', solution=''):
        """提交ticket"""
        json_data = {
            "flowTaskCommentDto": {"approvalType": "agree"},
            "processInstanceId": processInstanceId,
            "taskId": taskId,
            "taskVariableData": {},
            "masterData": {"solution": solution},
            "slaveData": {"10003": []}, "otherData": {"files": []}}
        self.post(url="/central/bpm/admin/flow/flowOnlineOperation/submitUserTaskForCommon", json=json_data, headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
