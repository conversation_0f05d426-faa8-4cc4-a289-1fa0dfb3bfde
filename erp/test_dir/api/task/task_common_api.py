import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import header
from datetime import datetime, timedelta


class TaskCommonApi(weeeTest.TestCase):
    """相关配置接口"""

    def create_request(self, json_data):
        """发起ticket"""
        self.post(url="/central/merch/v1/task/approvalRequest", json=json_data, headers=header)
        return self.response

    def get_request_detail(self, params_data):
        """获取ticket详情"""
        self.get(url="/central/bpm/admin/flow/flowOperation/viewTask", params=params_data, headers=header)
        return self.response

    def get_request_detail_content(self, params_data):
        """获取ticket详情"""
        self.get(url="/central/bpm/expand/formDesign/getBusinessFiledData", params=params_data, headers=header)
        return self.response

    def cancel_request(self, params_data):
        """取消ticket"""
        self.get(url="/central/merch/v1/task/cancelRequest", params=params_data, headers=header)
        return self.response

    def get_request_list(self, task_id):
        """搜索获取task list"""
        params_data = {
            "filter[type][0]": "",
            "filter[location]": "",
            "filter[keyword]": "",
            "filter[due_before]": "",
            "filter[rec_creator_name]": "",
            "filter[id]": task_id,
            "filter[status]": ""
        }
        # params_data.update(data)
        self.get(url="/central/merch/v1/task/list", params=params_data, headers=header)
        return self.response

    def submit_request(self, processInstanceId='', taskId='', solution=''):
        """提交ticket"""
        json_data = {
            "flowTaskCommentDto": {"approvalType": "agree"},
            "processInstanceId": processInstanceId,
            "taskId": taskId,
            "taskVariableData": {},
            "masterData": {"solution": solution},
            "slaveData": {"10003": []}, "otherData": {"files": []}}
        self.post(url="/central/bpm/admin/flow/flowOnlineOperation/submitUserTaskForCommon", json=json_data,
                  headers=header)
        return self.response

    def submit_purchase_cost_update_request(self, processInstanceId='', taskId='', comment='', solution=''):
        """提交ticket"""
        json_data = {
            "flowTaskCommentDto": {
                "approvalType": "multi_agree",
                "comment": comment
            },
            "processInstanceId": processInstanceId,
            "taskId": taskId,
            "taskVariableData": {},
            "masterData": {
                "buttonExtendJson": {"condition": "in", "comment": "0", "type": "solution",
                                     "value": ["owner_solution_1"], "key": "ownerSolution",
                                     "url": "https://internal-api.tb1.sayweee.net/central/merch/v1/srm/product/priceCostVerify",
                                     "urlParam": "productId,vendorId,costPerUnitAfter"},
                "solution": solution
            },
            "otherData": {"files": []}
        }
        self.post(url="/central/bpm/admin/flow/flowOnlineOperation/submitUserTaskForCommon", json=json_data,
                  headers=header)
        return self.response

    def get_next_business_day(self, days):
        today = datetime.now().strftime("%Y-%m-%d")
        current_date = datetime.strptime(today, "%Y-%m-%d")
        days_to_add = days
        while days_to_add > 0:
            current_date += timedelta(days=1)
            if current_date.weekday() < 5:  # 0-4 represent Monday to Friday
                days_to_add -= 1
        return current_date.strftime('%Y-%m-%d')


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
