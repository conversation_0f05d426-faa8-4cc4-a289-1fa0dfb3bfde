import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import basic_user


class PurchaseStatusManagement(weeeTest.TestCase):
    """相关配置接口"""

    def create_request_from_warehouse_list(self, product='', inventory='', value_to=None):
        """发起purchase status update ticket"""
        json_data = {
            "type": "purchase_status_change",
            "product_id": product,
            "inventory_id": inventory,
            "fields": [{
                "field": "purchase_status",
                "value_from": [],
                "value_to":value_to
            }],
            "reason": "test"
        }
        self.post(url="/central/merch/v1/task/approvalRequest", json=json_data, headers=basic_user)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
