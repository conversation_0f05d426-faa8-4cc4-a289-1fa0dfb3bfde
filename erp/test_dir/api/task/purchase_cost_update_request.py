import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import basic_user
from erp.test_dir.api_case import header


class PurchaseCostUpdateManagement(weeeTest.TestCase):
    """相关配置接口"""

    def update_vendor_product_cost(self, json_data):
        """发起purchase cost update ticket"""
        self.post(url="/central/merch/v1/vp/updateProductVendor", json=json_data, headers=basic_user)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
