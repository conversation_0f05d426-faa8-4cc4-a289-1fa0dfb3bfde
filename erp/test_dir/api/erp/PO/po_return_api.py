import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api.erp import header
from datetime import datetime, timedelta
from erp.test_dir.api.utils_url import UtilsUrl


class PoReturnInterface(weeeTest.TestCase):
    """采购订单退货单相关接口"""

    def po_return_list(self, enter_data):
        """
        退货单列表查询接口
        :param enter_data: 入参查询条件
        :return:
        """
        data = {
            "draw": 1,
            "columns[0][data]": "id",
            "columns[0][name]": None,
            "columns[0][searchable]": True,
            "columns[0][orderable]": True,
            "columns[0][search][value]": None,
            "columns[0][search][regex]": False,
            "columns[1][data]": "status",
            "columns[1][name]": None,
            "columns[1][searchable]": True,
            "columns[1][orderable]": True,
            "columns[1][search][value]": None,
            "columns[1][search][regex]": False,
            "columns[2][data]": "purchase_order_id",
            "columns[2][name]": None,
            "columns[2][searchable]": True,
            "columns[2][orderable]": True,
            "columns[2][search][value]": None,
            "columns[2][search][regex]": False,
            "columns[3][data]": "vendor_id",
            "columns[3][name]": None,
            "columns[3][searchable]": True,
            "columns[3][orderable]": True,
            "columns[3][search][value]": None,
            "columns[3][search][regex]": False,
            "columns[4][data]": "return_amount",
            "columns[4][name]": None,
            "columns[4][searchable]": True,
            "columns[4][orderable]": True,
            "columns[4][search][value]": None,
            "columns[4][search][regex]": False,
            "columns[5][data]": "rec_creator_id",
            "columns[5][name]": None,
            "columns[5][searchable]": True,
            "columns[5][orderable]": True,
            "columns[5][search][value]": None,
            "columns[5][search][regex]": False,
            "columns[6][data]": "rec_create_time",
            "columns[6][name]": None,
            "columns[6][searchable]": True,
            "columns[6][orderable]": True,
            "columns[6][search][value]": None,
            "columns[6][search][regex]": False,
            "order[0][column]": 0,
            "order[0][dir]": "desc",
            "start": 0,
            "length": 50,
            "search[value][gb_po_return__inventory_id]": "8",
            "search[value][gb_po_return__purchase_order_id]": "",
            "search[value][gb_po_return__status]": "",
            "search[value][gb_purchase_order__vendor_id]": "",
        }
        data.update(enter_data)
        self.get(special_url=UtilsUrl.special_url, url="/admin_po_return/api_table_query_return", headers=header,
                 params=data)
        return self.response

    def po_return_info(self, enter_data):
        """
        退货单详情查询接口
        :param enter_data: 入参退货单ID
        :return:
        """
        self.get(special_url=UtilsUrl.special_url, url=f"/admin_po_return/returns/{enter_data}", headers=header)
        return self.response

    def create_po_return(self, enter_data):
        """
        创建退货单接口
        :param enter_data:
        :return:
        """
        data = {
            "payment_method": None,
            "return_type": None,
            "return_date": (datetime.now() + timedelta(days=2)).strftime("%Y-%m-%d"),
            "vendor_contact": None,
            "comment": None,
            "return_id": None,
            "purchase_order_id": None,
            "product_id[]": None,
            "po_return_quantity[]": None,
            "return_line_amount[]": None
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_po_return/api_save_return", headers=header, data=data)
        return self.response

    def update_po_return(self, enter_data):
        """
        更新退货单接口
        :param enter_data:
        :return:
        """
        data = {
            "payment_method": None,
            "return_type": None,
            "return_date": None,
            "vendor_contact": None,
            "comment": None,
            "return_id": None,
            "purchase_order_id": None,
            "product_id[]": None,
            "po_return_quantity[]": None,
            "return_line_amount[]": None
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_po_return/api_save_return", headers=header, data=data)
        return self.response

    def delete_po_return(self, enter_data):
        """
        删除退货单接口
        :param enter_data: 退货单ID
        :return:
        """
        data = {
            "return_id": None
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_po_return/api_delete_return", headers=header, data=data)
        return self.response

    def submit_po_return(self, enter_data):
        """
        提交下发退货单到WMS接口
        :param enter_data: 退货单ID
        :return:
        """
        data = {
            "return_id": None
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_po_return/api_confirm_return", headers=header,
                  data=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
