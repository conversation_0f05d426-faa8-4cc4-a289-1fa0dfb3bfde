import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api.erp import header
import datetime
from erp.test_dir.api.utils_url import UtilsUrl


class PoRelateInterface(weeeTest.TestCase):
    """PO订单关联接口"""

    def create_po_order(self, po_data):
        """
        创建PO单
        :param po_data: 入参
        :return:
        """
        data = {
            "inventory_id": "8",
            "inbound_inventory_id": "34",
            "is_transfer": "",
            "delivery_inventory_id": "8",
            "third_inventory_id": "",
            "vendor_id": "134",
            "purchase_org_code": "US00",
            "delivery_mode": "Delivery",
            "pickup_start_time": "",
            "pickup_end_time": "",
            "est_delivery_date": (datetime.datetime.today() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
            "est_delivery_date_to_carrier": "",
            "payment_mode": "check",
            "payment_term": "21",
            "actual_term": "21",
            "internal_comment": "QA Test Data",
            "comment": "QA Test Data",
            "display_vendor": "1",
            "direct_import": "",
            "is_material": "N",
            "need_auto_confirm": "Y",
            "shipping": "0.00",
            "return_process_fee": "0.00",
            "container_offload_fee": "0.00"
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url='/admin_gb_po/api_edit_order', headers=header, data=data)
        return self.response

    def update_po_order_lines(self, po_data):
        """
        为PO添加商品lines
        :param po_data: 入参
        :return:
        """
        data = {
            "order_id": "",
            "operate_source": "Buyer Operate",
            "sales_product_id_select[]": "9253",
            "purchase_product_id[]": "93104",
            "sales_product_id[]": "9253",
            "purchase_price[]": "15",
            "purchase_product_quantity[]": "10",
            "changed[]": "N",
            "bracket[]": "",
            "is_bracket_price[]": "0",
            "bracket_qty[]": "",
            "promotion_id[]": "0",
            "no_discount_price[]": "0",
            "promotion_remark[]": "",
            "forward[]": "",
            "forward_days[]": "",
            "vendor_oos[]": "",
            "path": ""
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_update_lines", headers=header, data=data)
        return self.response

    def protocol_po_order(self, po_data):
        """
        protocol PO订单
        :param po_data: 入参
        :return:
        """
        data = {
            "order_id": "",
            "vendor_portal_emails[0][name]": "vendor_portal_email[]",
            "vendor_portal_emails[0][value]": "<EMAIL>"
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_protocol_order", headers=header, data=data)
        return self.response

    def confirm_po_order(self, po_data):
        """
        确认生效PO订单
        :param po_data: 入参
        :return:
        """
        data = {
            "order_id": "",
            "confirm_inventory_volume": "0"
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_confirm_order", headers=header, data=data)
        return self.response

    def cancel_po_order(self, po_data):
        """
        为PO添加商品lines
        :param po_data: 入参
        :return:
        """
        data = {
            "order_id": ""
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_cancel_order", headers=header, data=data)
        return self.response

    def delete_po_order(self, po_data):
        """
        删除PO
        :param po_data: 入参
        :return:
        """
        data = {
            "order_id": ""
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_delete_order", headers=header, data=data)
        return self.response

    def sync_po_price(self, po_data):
        """
        同步采购订单cost
        :param po_data: 入参
        :return:
        """
        data = {
            "id": '',
            "vendor_product_ids": '11844',
            "type": 'po',
            "double_check": 'Y'
        }
        data.update(po_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_po/api_sync_po_price", headers=header, data=data)
        return self.response

    def cost_review_request(self):
        """
        查询成本审核
        :param po_data: 入参
        :return:
        """
        params_data = {
            "search[value][keywords]": '10827',
            "search[value][pi_purchase_cost_request__source]": 'erp',
            "search[value][pi_purchase_cost_request__vendor_id]": '24',
            "search[value][pi_purchase_cost_request__status]": 'P',
            "draw": '1',
            "search[regex]": False,
            "length": '50',
            "start": '0',
            "columns[0][data]": 'id',
            "columns[0][name]": '',
            "columns[0][searchable]": True,
            "columns[0][orderable]": False,
            "columns[0][search][value]": '',
            "columns[0][search][regex]": False,
            "columns[1][data]": 'request_id',
            "columns[1][name]": '',
            "columns[1][searchable]": True,
            "columns[1][orderable]": False,
            "columns[1][search][value]": '',
            "columns[1][search][regex]": False,
            "columns[2][data]": 'vender_id',
            "columns[2][name]": '',
            "columns[2][searchable]": True,
            "columns[2][orderable]": True,
            "columns[2][search][value]": '',
            "columns[2][search][regex]": False,
            "columns[3][data]": 'product_id',
            "columns[3][name]": '',
            "columns[3][searchable]": True,
            "columns[3][orderable]": True,
            "columns[3][search][value]": '',
            "columns[3][search][regex]": False,
            "columns[4][data]": 'short_title_en',
            "columns[4][name]": '',
            "columns[4][searchable]": True,
            "columns[4][orderable]": True,
            "columns[4][search][value]": '',
            "columns[4][search][regex]": False,
            "columns[5][data]": 'tier',
            "columns[5][name]": '',
            "columns[5][searchable]": True,
            "columns[5][orderable]": True,
            "columns[5][search][value]": '',
            "columns[5][search][regex]": False,
            "columns[6][data]": 'status',
            "columns[6][name]": '',
            "columns[6][searchable]": True,
            "columns[6][orderable]": True,
            "columns[6][search][value]": '',
            "columns[6][search][regex]": False,
            "columns[7][data]": 'comments',
            "columns[7][name]": '',
            "columns[7][searchable]": True,
            "columns[7][orderable]": False,
            "columns[7][search][value]": '',
            "columns[7][search][regex]": False,
            "columns[8][data]": 'weee_buyer_id',
            "columns[8][name]": '',
            "columns[8][searchable]": True,
            "columns[8][orderable]": True,
            "columns[8][search][value]": '',
            "columns[8][search][regex]": False,
            "columns[9][data]": 'source',
            "columns[9][name]": '',
            "columns[9][searchable]": True,
            "columns[9][orderable]": True,
            "columns[9][search][value]": '',
            "columns[9][search][regex]": False,
            "columns[10][data]": 'purchase_unit',
            "columns[10][name]": '',
            "columns[10][searchable]": True,
            "columns[10][orderable]": True,
            "columns[10][search][value]": '',
            "columns[10][search][regex]": False,
            "columns[11][data]": 'before_quantity_per_unit',
            "columns[11][name]": '',
            "columns[11][searchable]": True,
            "columns[11][orderable]": True,
            "columns[11][search][value]": '',
            "columns[11][search][regex]": False,
            "columns[12][data]": 'quantity_per_unit',
            "columns[12][name]": '',
            "columns[12][searchable]": True,
            "columns[12][orderable]": True,
            "columns[12][search][value]": '',
            "columns[12][search][regex]": False,
            "columns[13][data]": 'before_cost',
            "columns[13][name]": '',
            "columns[13][searchable]": True,
            "columns[13][orderable]": True,
            "columns[13][search][value]": '',
            "columns[13][search][regex]": False,
            "columns[14][data]": 'product',
            "columns[14][name]": '',
            "columns[14][searchable]": True,
            "columns[14][orderable]": True,
            "columns[14][search][value]": '',
            "columns[14][search][regex]": False,
            "columns[15][data]": 15,
            "columns[15][name]": '',
            "columns[15][searchable]": True,
            "columns[15][orderable]": False,
            "columns[15][search][value]": '',
            "columns[15][search][regex]": False,
            "columns[16][data]": 16,
            "columns[16][name]": '',
            "columns[16][searchable]": True,
            "columns[16][orderable]": False,
            "columns[16][search][value]": '',
            "columns[16][search][regex]": False,
            "columns[17][data]": 'creator_name',
            "columns[17][name]": '',
            "columns[17][searchable]": True,
            "columns[17][orderable]": True,
            "columns[17][search][value]": '',
            "columns[17][search][regex]": False,
            "columns[18][data]": 'start_date',
            "columns[18][name]": '',
            "columns[18][searchable]": True,
            "columns[18][orderable]": True,
            "columns[18][search][value]": '',
            "columns[18][search][regex]": False,
            "columns[19][data]": 'rec_create_time',
            "columns[19][name]": '',
            "columns[19][searchable]": True,
            "columns[19][orderable]": True,
            "columns[19][search][value]": '',
            "columns[19][search][regex]": False,
            "order[0][column]": '0',
            "order[0][dir]": 'desc',
            "search[value][gb_product_sales__weee_buyer_id]": '',
            "search[value][pi_purchase_cost_request__catman_lead_id_like]": '',
            "search[value][update_time_min]": '',
            "search[value][update_time_max]": '',
            "search[value][to_dos]": ''
        }
        self.get(special_url=UtilsUrl.special_url, url="/admin_vendor_product_request/api_table_query_product_request", headers=header,
                 params=params_data)
        return self.response

    def cost_review_approve(self, id=''):
        """
        成本审核approve
        :param po_data: 入参
        :return:
        """
        data = {
            "id": id,
            "comment": 'test',
            "product": '32',
        }
        self.post(special_url=UtilsUrl.special_url, url="/admin_vendor_product_request/api_edit_and_approve_cost_request", headers=header, data=data)
        return self.response

    def create_po_order_type(self, inventory_id='',inbound_inventory_id='',delivery_inventory_id='',vendor_id='',delivery_mode='',pickup_start_time='',pickup_end_time=''):
        """创建PO单-自定义仓库&delivery_mode"""
        json_data = {
            "inventory_id": inventory_id,
            "inbound_inventory_id": inbound_inventory_id,
            "is_transfer": "",
            "delivery_inventory_id": delivery_inventory_id,
            "third_inventory_id": "",
            "vendor_id": vendor_id,
            "purchase_org_code": "US00",
            "delivery_mode": delivery_mode,
            "pickup_start_time": pickup_start_time,
            "pickup_end_time": pickup_end_time,
            "est_delivery_date": (datetime.datetime.today() + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
            "est_delivery_date_to_carrier": "",
            "payment_mode": "check",
            "payment_term": "21",
            "actual_term": "21",
            "internal_comment": "auto_create_po",
            "comment": "auto_create_po",
            "display_vendor": "1",
            "direct_import": "",
            "is_material": "N",
            "need_auto_confirm": "Y",
            "shipping": "0.00",
            "return_process_fee": "0.00",
            "container_offload_fee": "0.00"
        }

        # header_erp = {
        #     'Cookie': 'site_lang=zh; b_cookie=4887782; _gcl_au=1.1.1912966624.1731487866; afUserId=20858825-bc31-4e4c-bf6c-af82b7ef6dc6-p; showTimeBanner=1; landing_url=%2Fzh; _fbp=fb.1.1731561487716.77343793865273349; ftu_cookie=4887782; showTimeBannerLogin=1; __stripe_mid=d9b62e38-ae4c-4e89-887b-02becb178010984ff9; affiliate_referral_id=13347083; OptanonAlertBoxClosed=2024-11-20T01:28:39.674Z; auth_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IjljYjNiOGJhLTEwYzEtNDEwZS04ODQ5LWY0YWFlNTg3NzRmMyJ9.************************************************************************************************************************************************************************************************************************************.ouMsJR651yM9egeiX8mr-slbt5lPpFX3PikDlcOOyJizBivTl7YgHARt4VJP01S4r6EVp1LTuJr_VAeH3zRXhGmIxwKYC-xKAAqjsUZ0zMJpR78AiK2crl651uZR1240qCdUHacC4YGcn3cN03Em5CuIW3ZQ9aHvfmgd2XAZLLg; OptanonConsent=isGpcEnabled=0&datestamp=Tue+Jan+14+2025+14%3A13%3A22+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202303.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=b5bdb46c-9add-433f-a365-49adde5d9a37&interactionCount=1&landingPath=NotLandingPage&groups=C0002%3A0%2CC0001%3A1%2CC0003%3A0%2CC0004%3A0&AwaitingReconsent=false&geolocation=CN%3BSH; _ga_3VHGVRJPPJ=GS1.1.1737082872.16.0.1737082872.0.0.0; _ga_154E0GT6ZG=GS1.1.1737349452.11.0.1737349452.0.0.0; weee_inventory=7; ab-act=0; IS_LOGIN=1; NEW_ZIP_CODE=94538; NEW_ZIP_CITY=Fremont; NEW_SALES_ORG_ID=1; order_token=MzRmMTg2NGQtZTMzOS00YzcwLWE1NjUtMjZjZDA2Zjc2YmQ4; shipping_fee=0; pantry_free_fee=35; pantry_fee=5.99; assets_cdn=%7B%220%22%3A0%2C%221%22%3A0%2C%222%22%3A0%2C%223%22%3A0%2C%224%22%3A0%2C%225%22%3A0%7D; weee_sales_org=1; user_id=13347083; _clck=1t36wg8%7C2%7Cfss%7C0%7C1779; _gid=GA1.2.502886900.1737527217; keycloak_user_email=<EMAIL>; keycloak_user_id=13347083; keycloak_token=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; aws-waf-token=d7be8042-8255-473c-8e52-5d8be9dc584f:EwoAitI42aHuAAAA:fGXcytxDxUNMMJ0wepwiHloFA+1pMAyCnbUJ7qyU4LYPR2VUD0tOb6v0OirMWigU83INDwAMTbxCXPqKh5aOjmdWTmPScXKNEQ1YXQDfEFvODm0pOVnkofcUBAWDJJoWy/RDRbRbaqLPLV+EtVZEbqGQm257XPzemqeRpX9mewb1sRFOErBGXCtsUlJO7cujuHgCaXwbk5a9e0xShIZETVuXjt02slmw5SgBmuFU/f1xntcYcVB78Q==; weee_sales_org=2; _ga_FVWC4GKEYS=GS1.1.1737604683.1.1.1737609004.0.0.0; checkout_type=normal; checkout_type=normal; AF_SYNC=1737610176438; _ga_S6Y3RBT7R9=GS1.1.1737610175.57.1.1737610178.57.0.0; _uetsid=26e72e80d89011efba84e3cc0c61101c; _uetvid=6b555550a19c11ef9e592d430bbb8299; keycloak_user_email=<EMAIL>; keycloak_user_id=13347083; keycloak_token=*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; _ga_MGBY6PWBE0=GS1.1.1737610175.45.1.1737610181.54.0.0; _ga=GA1.1.593945554.1731482065; ci_session=a%3A8%3A%7Bs%3A10%3A%22session_id%22%3Bs%3A32%3A%226513b9fb4a3d5bf68ec4ee4b8a4633d5%22%3Bs%3A10%3A%22ip_address%22%3Bs%3A14%3A%22137.59.100.131%22%3Bs%3A10%3A%22user_agent%22%3Bs%3A111%3A%22Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F130.0.0.0+Safari%2F537.36%22%3Bs%3A13%3A%22last_activity%22%3Bi%3A1737610559%3Bs%3A9%3A%22user_data%22%3Bs%3A0%3A%22%22%3Bs%3A12%3A%22session_user%22%3Ba%3A5%3A%7Bs%3A14%3A%22Global_User_ID%22%3Bs%3A8%3A%2213347083%22%3Bs%3A6%3A%22userId%22%3Bs%3A36%3A%22920aa8c9-16ac-4c28-b966-81ad69681071%22%3Bs%3A8%3A%22roleType%22%3Bs%3A45%3A%22Y2K1Y1Y0Y6DJVCWBY8FQHY9SGMPY7Z0ROY5LZ1KUY3Y4A%22%3Bs%3A10%3A%22headImgUrl%22%3Bs%3A96%3A%22https%3A%2F%2Flh3.googleusercontent.com%2Fa%2FACg8ocItCfJwnvBeYT-m99rgZjeUF-GFQ6lKQtSEx0E6gEFTmPFMCg%3Ds96-c%22%3Bs%3A11%3A%22wxSnsOpenId%22%3BN%3B%7Ds%3A20%3A%22session_pre_order_id%22%3Bi%3A13347083%3Bs%3A26%3A%22flash%3Aold%3Aoperation_result%22%3Ba%3A2%3A%7Bs%3A6%3A%22status%22%3Bs%3A7%3A%22success%22%3Bs%3A7%3A%22message%22%3Bs%3A12%3A%22%E6%93%8D%E4%BD%9C%E6%88%90%E5%8A%9F%22%3B%7D%7D45a776bce1af569cd6dee5233a8074179d095e7a; _ga_KS00TKF4CS=GS1.1.1737611906.10.1.1737613132.0.0.0; weee_session_token=4930183; _ga_JCWTX0V9SC=GS1.1.1737615226.3.1.1737615286.0.0.0; weee_inventory_multi=8; DELIVERY_DATE=2025-01-26; deal_id=555970; auth_token=eyJraWQiOiI5Y2IzYjhiYS0xMGMxLTQxMGUtODg0OS1mNGFhZTU4Nzc0ZjMiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************.AXuNj4HJc9UWRf41qCaSeGQO9teNZ20eG6cjAP0UwR9yxPCAhDnzKvdPcPUyvzmnoQQqI08wg2m3LHTVyuRlQ7q4E9OJ7WAUjOAUSY7xuO2FCxI8pZCsJ5qapxJK1U4FHtp55Wr9lzexuKMv6R_Sh61Mg4SSaNG4iyrwnsSsh3s; _ga_GJCVPXJH2L=GS1.1.1737609039.197.1.1737616890.0.0.0; _ga_SETRDTEPCY=GS1.1.1737615125.138.1.1737616891.0.0.0; ci_session=a%3A7%3A%7Bs%3A10%3A%22session_id%22%3Bs%3A32%3A%2297adb5507e5b0b155759da2459834a16%22%3Bs%3A10%3A%22ip_address%22%3Bs%3A14%3A%22137.59.100.131%22%3Bs%3A10%3A%22user_agent%22%3Bs%3A111%3A%22Mozilla%2F5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F130.0.0.0+Safari%2F537.36%22%3Bs%3A13%3A%22last_activity%22%3Bi%3A1737616897%3Bs%3A9%3A%22user_data%22%3Bs%3A0%3A%22%22%3Bs%3A12%3A%22session_user%22%3Ba%3A5%3A%7Bs%3A14%3A%22Global_User_ID%22%3Bs%3A8%3A%2213347083%22%3Bs%3A6%3A%22userId%22%3Bs%3A36%3A%22920aa8c9-16ac-4c28-b966-81ad69681071%22%3Bs%3A8%3A%22roleType%22%3Bs%3A45%3A%22Y2K1Y1Y0Y6DJVCWBY8FQHY9SGMPY7Z0ROY5LZ1KUY3Y4A%22%3Bs%3A10%3A%22headImgUrl%22%3Bs%3A96%3A%22https%3A%2F%2Flh3.googleusercontent.com%2Fa%2FACg8ocItCfJwnvBeYT-m99rgZjeUF-GFQ6lKQtSEx0E6gEFTmPFMCg%3Ds96-c%22%3Bs%3A11%3A%22wxSnsOpenId%22%3BN%3B%7Ds%3A20%3A%22session_pre_order_id%22%3Bi%3A13347083%3B%7D68218ac253e3668a895d8b58496c05d94eda2737',
        #     'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        # }

        self.post(special_url=UtilsUrl.special_url, url='/admin_gb_po/api_edit_order', headers=header, data=json_data)
        return self.response


    def adjust_po_order_line(self, order_id='',product_id='',purchase_product_id='',purchase_price='',quantity=''):
        """采购订单增加商品行"""
        json_data = {
            "order_id": order_id,
            "path": "purchase_line",
            "operate_source": "Buyer Operate",
            "sales_product_id_select[]": product_id,
            "purchase_product_id[]": purchase_product_id,
            "sales_product_id[]": product_id,
            "changed[]": "Y",
            "bracket[]": "",
            "is_bracket_price[]": 0,
            "bracket_qty[]": "",
            "promotion_id[]": 0,
            "no_discount_price[]": 0,
            "promotion_remark[]": "",
            "purchase_price[]": purchase_price,
            "purchase_product_quantity[]": quantity,
            "forward[]": "",
            "forward_days[]": "",
            "vendor_oos[]": ""
        }

        self.post(special_url=UtilsUrl.special_url, url='/admin_gb_po/api_update_lines', headers=header, data=json_data)
        return self.response



if __name__ == '__main__':
    weeeConfig.base_url = 'https://tb1.sayweee.net'
    PoRelateInterface().create_po_order(po_data={})
