import re
import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api.erp import header
import datetime
from erp.test_dir.api.utils_url import UtilsUrl


class VendorSponsoredPromotionInterface(weeeTest.TestCase):
    """供应商赞助的促销关联接口"""

    def vendor_sponsored_promotion_list(self, enter_data):
        """
        促销列表查询接口
        :param enter_data: 入参
        :return:
        """
        data = {
            "draw": 1,
            "columns[0][data]": "id",
            "columns[0][name]": None,
            "columns[0][searchable]": True,
            "columns[0][orderable]": False,
            "columns[0][search][value]": None,
            "columns[0][search][regex]": False,
            "columns[1][data]": "id",
            "columns[1][name]": None,
            "columns[1][searchable]": True,
            "columns[1][orderable]": True,
            "columns[1][search][value]": None,
            "columns[1][search][regex]": False,
            "columns[2][data]": "vendor_id",
            "columns[2][name]": None,
            "columns[2][searchable]": True,
            "columns[2][orderable]": True,
            "columns[2][search][value]": None,
            "columns[2][search][regex]": False,
            "columns[3][data]": "title",
            "columns[3][name]": None,
            "columns[3][searchable]": True,
            "columns[3][orderable]": True,
            "columns[3][search][value]": None,
            "columns[3][search][regex]": False,
            "columns[4][data]": "product_id",
            "columns[4][name]": None,
            "columns[4][searchable]": True,
            "columns[4][orderable]": True,
            "columns[4][search][value]": None,
            "columns[4][search][regex]": False,
            "columns[5][data]": "short_title_en",
            "columns[5][name]": None,
            "columns[5][searchable]": True,
            "columns[5][orderable]": True,
            "columns[5][search][value]": None,
            "columns[5][search][regex]": False,
            "columns[6][data]": "label",
            "columns[6][name]": None,
            "columns[6][searchable]": True,
            "columns[6][orderable]": True,
            "columns[6][search][value]": None,
            "columns[6][search][regex]": False,
            "columns[7][data]": "type",
            "columns[7][name]": None,
            "columns[7][searchable]": True,
            "columns[7][orderable]": True,
            "columns[7][search][value]": None,
            "columns[7][search][regex]": False,
            "columns[8][data]": "amount",
            "columns[8][name]": None,
            "columns[8][searchable]": True,
            "columns[8][orderable]": True,
            "columns[8][search][value]": None,
            "columns[8][search][regex]": False,
            "columns[9][data]": "status",
            "columns[9][name]": None,
            "columns[9][searchable]": True,
            "columns[9][orderable]": True,
            "columns[9][search][value]": None,
            "columns[9][search][regex]": False,
            "columns[10][data]": "rec_creator_id",
            "columns[10][name]": None,
            "columns[10][searchable]": True,
            "columns[10][orderable]": True,
            "columns[10][search][value]": None,
            "columns[10][search][regex]": False,
            "columns[11][data]": "rec_create_time",
            "columns[11][name]": None,
            "columns[11][searchable]": True,
            "columns[11][orderable]": True,
            "columns[11][search][value]": None,
            "columns[11][search][regex]": False,
            "columns[12][data]": "start_date",
            "columns[12][name]": None,
            "columns[12][searchable]": True,
            "columns[12][orderable]": True,
            "columns[12][search][value]": None,
            "columns[12][search][regex]": False,
            "columns[13][data]": "end_date",
            "columns[13][name]": None,
            "columns[13][searchable]": True,
            "columns[13][orderable]": True,
            "columns[13][search][value]": None,
            "columns[13][search][regex]": False,
            "order[0][column]": 0,
            "order[0][dir]": "desc",
            "start": 0,
            "length": 50,
            "search[value][gb_product_vendor_promotion__vendor_id]": None,
            "search[value][gb_product_vendor_promotion__product_id]": None,
            "search[value][gb_product_vendor_promotion__start_date]": None,
            "search[value][gb_product_vendor_promotion__end_date]": None,
            "search[value][gb_product_vendor_promotion__type]": None,
            "search[value][gb_product_vendor_promotion__po_id]": None,
            "search[value][gb_product__department]": None,
            "search[value][gb_product__catalogue_num]": None,
            "search[value][gb_product__sub_catalogue_num]": None,
            "search[regex]": False,
            "_": 1703740786946
        }
        data.update(enter_data)
        self.get(special_url=UtilsUrl.special_url, url="/admin_gb_vendor/api_table_query_sponsored_promotion",
                 headers=header, params=data)
        return self.response

    def add_vendor_product_promotion(self, enter_data):
        """
        新增供应商商品促销接口
        :param enter_data: 入参
        :return:
        """
        data = {
            "id": 0,
            "vendor_id": None,
            "product_id": None,
            "type": None,
            "start_date": datetime.datetime.today().strftime("%Y-%m-%d"),
            "end_date": (datetime.datetime.today() + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
            "base_purchase_qty": None,
            "base_gift_qty": None,
            "comment": "QA Comment",
            "compensate_type": None,
            "discount_percentage": None,
            "amount": None,
            "sales_amount": None,
            "sales_qty": None,
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_po_promotion/api_update_product_promotion",
                  headers=header, data=data)
        return self.response

    def vendor_sponsored_promotion_info(self, enter_data):
        """
        供应商商品促销配置详情查询接口
        :param enter_data:
        :return:
        """
        self.get(special_url=UtilsUrl.special_url, url=f"/admin_gb_vendor/promotions/{enter_data}", headers=header)
        return self.response

    def delete_vendor_product_promotion(self, enter_data):
        """
        删除供应商商品促销接口
        :param enter_data: 入参
        :return:
        """
        data = {
            "id": None
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_gb_vendor/api_delete_sponsored_promotion",
                  headers=header, data=data)
        return self.response

    def vendor_product_info(self, vendor_id):
        """
        供应商下的商品查询
        :param vendor_id: 供应商ID
        :return:
        """
        self.get(special_url=UtilsUrl.special_url, url=f"/admin_po_promotion/api_query_vendor_product_info/{vendor_id}",
                 headers=header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    resp = VendorSponsoredPromotionInterface().vendor_sponsored_promotion_info(enter_data=22146)
    matches = re.findall(r'id="comment">(.*?)</textarea>', resp)
    # 打印所有匹配的内容
    for match in matches:
        print(match)
