import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api.erp import header
from erp.test_dir.api.utils_url import UtilsUrl


class ProductCostAuditInterface(weeeTest.TestCase):
    """商品成本审核相关接口"""

    def product_cost_audit_list(self, enter_data):
        """
        商品成本审核列表查询接口
        :param enter_data: 入参查询条件
        :return:
        """
        data = {
            "draw": 1,
            "columns[0][data]": "id",
            "columns[0][name]": "",
            "columns[0][searchable]": True,
            "columns[0][orderable]": False,
            "columns[0][search][value]": "",
            "columns[0][search][regex]": False,
            "columns[1][data]": "request_id",
            "columns[1][name]": "",
            "columns[1][searchable]": True,
            "columns[1][orderable]": False,
            "columns[1][search][value]": "",
            "columns[1][search][regex]": False,
            "columns[2][data]": "vender_id",
            "columns[2][name]": "",
            "columns[2][searchable]": True,
            "columns[2][orderable]": True,
            "columns[2][search][value]": "",
            "columns[2][search][regex]": False,
            "columns[3][data]": "product_id",
            "columns[3][name]": "",
            "columns[3][searchable]": True,
            "columns[3][orderable]": True,
            "columns[3][search][value]": "",
            "columns[3][search][regex]": False,
            "columns[4][data]": "short_title_en",
            "columns[4][name]": "",
            "columns[4][searchable]": True,
            "columns[4][orderable]": True,
            "columns[4][search][value]": "",
            "columns[4][search][regex]": False,
            "columns[5][data]": "tier",
            "columns[5][name]": "",
            "columns[5][searchable]": True,
            "columns[5][orderable]": True,
            "columns[5][search][value]": "",
            "columns[5][search][regex]": False,
            "columns[6][data]": "status",
            "columns[6][name]": "",
            "columns[6][searchable]": True,
            "columns[6][orderable]": True,
            "columns[6][search][value]": "",
            "columns[6][search][regex]": False,
            "columns[7][data]": "comments",
            "columns[7][name]": "",
            "columns[7][searchable]": True,
            "columns[7][orderable]": False,
            "columns[7][search][value]": "",
            "columns[7][search][regex]": False,
            "columns[8][data]": "weee_buyer_id",
            "columns[8][name]": "",
            "columns[8][searchable]": True,
            "columns[8][orderable]": True,
            "columns[8][search][value]": "",
            "columns[8][search][regex]": False,
            "columns[9][data]": "source",
            "columns[9][name]": "",
            "columns[9][searchable]": True,
            "columns[9][orderable]": True,
            "columns[9][search][value]": "",
            "columns[9][search][regex]": False,
            "columns[10][data]": "purchase_unit",
            "columns[10][name]": "",
            "columns[10][searchable]": True,
            "columns[10][orderable]": True,
            "columns[10][search][value]": "",
            "columns[10][search][regex]": False,
            "columns[11][data]": "before_quantity_per_unit",
            "columns[11][name]": "",
            "columns[11][searchable]": True,
            "columns[11][orderable]": True,
            "columns[11][search][value]": "",
            "columns[11][search][regex]": False,
            "columns[12][data]": "quantity_per_unit",
            "columns[12][name]": "",
            "columns[12][searchable]": True,
            "columns[12][orderable]": True,
            "columns[12][search][value]": "",
            "columns[12][search][regex]": False,
            "columns[13][data]": "before_cost",
            "columns[13][name]": "",
            "columns[13][searchable]": True,
            "columns[13][orderable]": True,
            "columns[13][search][value]": "",
            "columns[13][search][regex]": False,
            "columns[14][data]": "product",
            "columns[14][name]": "",
            "columns[14][searchable]": True,
            "columns[14][orderable]": True,
            "columns[14][search][value]": "",
            "columns[14][search][regex]": False,
            "columns[15][data]": 15,
            "columns[15][name]": "",
            "columns[15][searchable]": True,
            "columns[15][orderable]": False,
            "columns[15][search][value]": "",
            "columns[15][search][regex]": False,
            "columns[16][data]": 16,
            "columns[16][name]": "",
            "columns[16][searchable]": True,
            "columns[16][orderable]": False,
            "columns[16][search][value]": "",
            "columns[16][search][regex]": False,
            "columns[17][data]": "creator_name",
            "columns[17][name]": "",
            "columns[17][searchable]": True,
            "columns[17][orderable]": True,
            "columns[17][search][value]": "",
            "columns[17][search][regex]": False,
            "columns[18][data]": "start_date",
            "columns[18][name]": "",
            "columns[18][searchable]": True,
            "columns[18][orderable]": True,
            "columns[18][search][value]": "",
            "columns[18][search][regex]": False,
            "columns[19][data]": "rec_create_time",
            "columns[19][name]": "",
            "columns[19][searchable]": True,
            "columns[19][orderable]": True,
            "columns[19][search][value]": "",
            "columns[19][search][regex]": False,
            "order[0][column]": 0,
            "order[0][dir]": "desc",
            "start": 0,
            "length": 50,
            "search[value][pi_purchase_cost_request__source]": "",
            "search[value][keywords]": "",
            "search[value][pi_purchase_cost_request__vendor_id]": "",
            "search[value][gb_product_sales__weee_buyer_id]": "",
            "search[value][pi_purchase_cost_request__status]": "",
            "search[value][pi_purchase_cost_request__catman_lead_id_like]": "",
            "search[value][update_time_min]": "",
            "search[value][update_time_max]": "",
            "search[value][to_dos]": "",
            "search[regex]": False,
            "_": 1724294342961
        }
        data.update(enter_data)
        self.get(special_url=UtilsUrl.special_url, url="/admin_vendor_product_request/api_table_query_product_request",
                 headers=header, params=data)
        return self.response

    def product_cost_audit_info(self, enter_data):
        """
        商品成本审核详情查询接口
        :param enter_data: 入参查询条件
        :return:
        """
        self.get(special_url=UtilsUrl.special_url, url=f"/admin_vendor_product_request/edit_request/{enter_data}",
                 headers=header)
        return self.response

    def update_product_cost(self, enter_data):
        """
        更新商品的成本价格接口
        :param enter_data:
        :return:
        """
        data = {}
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url, url="/admin_groupbuy/api_update_product_vendor", headers=header,
                  params=data)
        return self.response

    def approve_product_cost_request(self, enter_data):
        """
        审批商品成本申请接口
        :param enter_data:
        :return:
        """
        data = {
            "ids": None,
            "comment": "Local Owner Approve"
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url,
                  url="/admin_vendor_product_request/api_update_cost_request_by_approval", headers=header, data=data)
        return self.response

    def approve_update_product_cost_request(self, enter_data):
        """
        更新cost并审批商品成本申请接口
        :param enter_data:
        :return:
        """
        data = {
            "ids": None,
            "product": None,
            "comment": "Local Owner Approve"
        }
        data.update(enter_data)
        self.post(special_url=UtilsUrl.special_url,
                  url="/admin_vendor_product_request/api_edit_and_approve_cost_request", headers=header, data=data)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
    ProductCostAuditInterface().product_cost_audit_list(enter_data={})
