import json
from config.basic_db import ConnectDatabase
from config.secret import get_secret


class DBConnect(object):
    db_config = get_secret()

    def __init__(self):
        self.db = ConnectDatabase(host='weee.db.tb1.sayweee.net', user=self.db_config['db_erp_username'],
                                  password=self.db_config['db_erp_password'], db_name='weee_comm')

    def select_data_from_mysql(self, sql: str):
        """
        查询数据
        :param sql: 查询语句
        sql = "SELECT * FROM wms.wh_storage_location WHERE location_type = 50 AND flag = 0 and warehouse = '' LIMIT 1"
        """
        return self.db.query_data(sql)

    def update_data_from_mysql(self, sql: str):
        """
        更新数据
        :param sql: insert、update、delete
        sql = "update wms.wh_storage_location set flag=3 where warehouse_number=xx and location_no=xx"
        """
        return self.db.update_data(sql)

    def select_data_deal(self, sql: str):
        """
        查询结果二次处理
        :param sql: 查询语句
        sql = "SELECT * FROM wms.wh_storage_location WHERE location_type = 50 AND flag = 0 and warehouse = '' LIMIT 1"
        """
        ret = json.loads(self.select_data_from_mysql(sql))
        if len(ret) == 1:
            return ret[0]
        else:
            return ret


def db_query(sql_data):
    return DBConnect().select_data_from_mysql(sql_data)


def db_update(sql_data):
    return DBConnect().update_data_from_mysql(sql_data)


if __name__ == '__main__':
    sql = "SELECT * FROM `weee_cs`.`cs_coupon_requisition` ORDER BY `id` DESC LIMIT 1;"
    print(DBConnect().select_data_from_mysql(sql)[0][0])
