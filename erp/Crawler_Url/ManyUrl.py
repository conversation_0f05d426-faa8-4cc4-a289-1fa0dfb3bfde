from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib
import datetime
import requests
from time import sleep
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

TimeNum = 5  # 超时时间设置
num7 = 10  # 几次循环
address = 2  # 1为tb1 2为Jenkins运行

OnlineUrl = []
AllUrlList = [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []]
AllUrlList_table = []  # 合并table
AllUrlList_table1 = []  # 合并table1
AllUrlList_table2 = []  # 合并table2

PassUrlList = [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []]
PassUrlList_table = []  # 合并table1
PassUrlList_table1 = []  # 合并table1
PassUrlList_table2 = []  # 合并table2

ErrorUrlList = [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []]
ErrorUrlList_table = []  # 合并table1
ErrorUrlList_table1 = []  # 合并table1
ErrorUrlList_table2 = []  # 合并table2

TimeOutDic = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]  # 超时字典
EmailTimeOutError = []
TimeOutDic_table1 = []  # 合并table1
TimeOutDic_table2 = []  # 合并table2

ExterLinks = [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []]  # 外链URL列表
ExterLinks_table = []  # 合并
ExterLinks_table3 = []  # 合并去重
ExterLinks_table1 = []  # 合并table1
ExterLinks_table2 = []  # 合并table2

inList1 = []  # 点击弹框列表
inList2 = []  # 点击弹框列表
urlList = []  # 临时存储
AllList = [AllUrlList, PassUrlList, ErrorUrlList, TimeOutDic, ExterLinks]


def inc(it, e):  # 判断是在List 组中
    return True if e in it else any(inc(iit, e) for iit in it if type(iit) in (list, tuple))


start = datetime.datetime.now()  # 开始时间


class get_url_2():
    global url, driver
    num = 0
    FirstUrl = ''
    inputnum = None
    if address == 1:
        # inputnum = int(input("请选择环境：输入'1'或者'2'（1：TB1环境，2：生产环境）:"))  # 本地调试用
        inputnum = int(1)  # 本地调试用
    elif address == 2:
        inputnum = int(2)  # 生产用

    if inputnum == 1:
        FirstUrl = '://tb1.sayweee.net/'
    elif inputnum == 2:
        FirstUrl = '://www.sayweee.com/'

    OnlineUrl.extend(['https' + FirstUrl + 'en', 'https' + FirstUrl + 'zh', 'https' + FirstUrl + 'zht',
                      'https' + FirstUrl + 'es', 'https' + FirstUrl + 'ko', 'https' + FirstUrl + 'ja',
                      'https' + FirstUrl + 'vi', 'https' + FirstUrl + 'en?grocery-store=chinese',
                      'https' + FirstUrl + 'en?grocery-store=mexican',
                      'https' + FirstUrl + 'en?grocery-store=japanese',
                      'https' + FirstUrl + 'en?grocery-store=korean',
                      'https' + FirstUrl + 'en?grocery-store=vietnamese',
                      'https' + FirstUrl + 'en?grocery-store=filipino',
                      'https' + FirstUrl + 'en?grocery-store=indian',
                      'https' + FirstUrl + 'en?grocery-store=american'])
    num5 = 0
    for Alist in OnlineUrl:
        AllUrlList[num5].append(Alist)
        num5 += 1
    inList1.extend(['http' + FirstUrl + 'zh', 'http' + FirstUrl + 'zhen',
                    'https' + FirstUrl + 'en', 'https' + FirstUrl + 'en', 'https' + FirstUrl + 'zh',
                    'https' + FirstUrl + 'zht',
                    'https' + FirstUrl + 'es', 'https' + FirstUrl + 'ko', 'https' + FirstUrl + 'ja',
                    'https' + FirstUrl + 'vi'])
    inList2.extend(['https' + FirstUrl + 'en?grocery-store=chinese',
                    'https' + FirstUrl + 'en?grocery-store=mexican',
                    'https' + FirstUrl + 'en?grocery-store=japanese',
                    'https' + FirstUrl + 'en?grocery-store=korean',
                    'https' + FirstUrl + 'en?grocery-store=vietnamese',
                    'https' + FirstUrl + 'en?grocery-store=filipino',
                    'https' + FirstUrl + 'en?grocery-store=indian',
                    'https' + FirstUrl + 'en?grocery-store=american'])

    num6 = 0
    while 1 == 1:
        try:
            print('查看列表：', AllUrlList, num, num6)
            url = AllUrlList[num][num6]
        except Exception as e:
            print('错误URL：', AllUrlList[num][num6], e)

        chrome_options = Options()
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--headless')
        chrome_options.page_load_strategy = 'eager'

        # 本地浏览器运行
        # driver = webdriver.Chrome()
        # driver.implicitly_wait(10)
        # driver.maximize_window()

        if address == 1:
            # driver = webdriver.Chrome(options=chrome_options)  # 本地调试用
            chrome_path = './chromedriver'  # 地址
            driver = webdriver.Chrome(chrome_path, options=chrome_options)  # 发布代码用
        elif address == 2:
            chrome_path = './chromedriver'  # 地址
            driver = webdriver.Chrome(chrome_path, options=chrome_options)  # 发布代码用

        try:
            driver.get(url)
        except Exception as e:
            print('打开url异常:', url, e)

        sleep(0.5)
        if url in inList1:
            num1 = 0
            try:
                driver.find_element(By.XPATH, '//div[@id="loginNavbar"]//div[@class="Logo_logoLink__WtNsg"]').click()
                sleep(0.5)
                while num1 < 15:
                    try:
                        num1 = num1 + 1
                        driver.find_element(By.XPATH, '//button[@class="ant-modal-close"]/span/i').click()
                        sleep(0.5)
                        break
                    except:
                        sleep(0.5)
            except:
                while num1 < 15:
                    try:
                        num1 = num1 + 1
                        driver.find_element(By.XPATH, '//div[@class="ant-modal-content"]/div/i').click()
                        sleep(0.5)
                        break
                    except Exception as e:
                        print('未找到' + str(num1) + '次元素', e)
                        sleep(0.5)
        elif url in inList2:
            num1 = 0
            try:
                driver.find_element(By.XPATH,
                                    '//input[@class="ant-input Input_input__SeyEe PosterMain_iptZipcode__fCudi"]').send_keys(
                    '94538')
                sleep(0.5)
                driver.find_element(By.XPATH,
                                    '//button[@class="Button_button___o6h5 Button_primary__wuw_M '
                                    'Button_size-default__ZUJqm Button_inline__m0TMo '
                                    'PosterMain_btnShop__S4DzD"]').click()
                sleep(0.5)
            except:
                while num1 < 15:
                    try:
                        num1 = num1 + 1
                        driver.find_element(By.XPATH, '//div[@class="ant-modal-content"]/div/i').click()
                        sleep(0.5)
                        break
                    except:
                        sleep(0.5)
        num8 = 0
        num9 = 0
        if url in OnlineUrl:
            while num8 < 30:
                try:
                    driver.find_element(By.XPATH, '//i[@role="swiper-main-button-next"]').click()
                    sleep(0.5)
                    num8 += 1
                except:
                    sleep(0.2)
                    num9 += 1
                    if num9 == 10:
                        break
        num4 = 0
        sleep(0.5)
        while num4 < 8501:  # 向下移动坐标
            num4 = num4 + 100
            driver.execute_script('window.scrollTo(0,' + str(num4) + ')')
            sleep(0.1)
        sleep(0.5)
        buttons = driver.find_elements(By.XPATH, '//a')  # 获取当前页面所有a标签
        PassUrlList[num].append(url)
        print("一共：", len(buttons), '个标签')
        num2 = 0
        for button in buttons:
            links = button.get_attribute("href")  # 获取a标签下href的 URL
            if str(links).startswith('http://tb1.sayweee') or str(links).startswith('https://tb1.sayweee') or str(
                    links).startswith('http://www.sayweee') or str(links).startswith('https://www.sayweee') or str(
                links).startswith('https://sayweee'):  # 以什么开头的URL
                incAllList = inc(AllList, links)  # 判断是否在AllList 组中
                incPassUrlList = inc(PassUrlList, links)
                incErrorUrlList = inc(ErrorUrlList, links)
                incTimeOutDic = inc(TimeOutDic, links)
                # if incs is not True and links is not None:
                if links is not None:
                    num2 = num2 + 1
                    print("这是第", num6 + 1, "次大循环---", "第", num + 1, "次中循环--", "第", num2, "次小循环", "在URL：", url,
                          '内发现URL：', links)
                    if incAllList is not True:
                        try:
                            link = requests.get(links)  # 接口调一下url
                            Time = '{:.2f}'.format(link.elapsed.total_seconds())  # 获取响应时间，小数后取2位
                            if Time > str(TimeNum):
                                if links not in TimeOutDic[num].keys() and links not in PassUrlList[
                                    num] and links not in \
                                        ErrorUrlList[num] and links is not None:
                                    TimeOutDic[num][links] = str(Time) + 's'  # 超时加入字典
                                    urlList.append(links)  # 添加到临时列表中
                                    print("超时URL：", links, "总时长为：", Time, 's')
                            elif link.status_code == 200 and Time <= str(TimeNum):  # 判断是否为200
                                if links not in urlList:
                                    urlList.append(links)
                                    if links not in PassUrlList[num] and links not in TimeOutDic[num].keys():
                                        PassUrlList[num].append(links)
                            elif link.status_code != 200:
                                if links not in ErrorUrlList[num]:
                                    print("非200异常URL：", links)
                                    ErrorUrlList[num].append(links)
                                    urlList.append(links)
                        except:
                            if links not in ErrorUrlList[num] and links is not None:
                                ErrorUrlList[num].append(links)
                                urlList.append(links)
                                print("异常URL：", links)
                    elif incAllList is True:
                        if incPassUrlList is True:
                            if links not in urlList:
                                urlList.append(links)
                                if links not in PassUrlList[num] and links not in TimeOutDic[num].keys():
                                    PassUrlList[num].append(links)
                        elif incErrorUrlList is True:
                            if links not in ErrorUrlList[num] and links is not None:
                                ErrorUrlList[num].append(links)
                                urlList.append(links)
                                print("异常URL：", links)
                        elif incTimeOutDic is True:
                            if links not in TimeOutDic[num].keys() and links not in PassUrlList[num] and links not in \
                                    ErrorUrlList[num] and links is not None:
                                Time2 = TimeOutDic[num][links]
                                TimeOutDic[num][links] = str(Time2)  # 超时加入字典
                                urlList.append(links)  # 添加到临时列表中
                                print("超时URL：", links, "总时长为：", Time2)

            elif links not in ExterLinks[num] and links is not None:
                ExterLinks[num].append(links)
                urlList.append(links)
                print("外链URL：", links)
        num3 = 0
        while num3 < len(urlList):
            if urlList[num3] not in AllUrlList[num] and urlList[num3] is not None:
                AllUrlList[num].append(urlList[num3])
                num3 = num3 + 1
            else:
                num3 = num3 + 1
        num = num + 1
        urlList = []
        driver.quit()
        if num == 15:
            num = 0
            num6 += 1
            if num6 == num7:  # 设置循环次数
                break


end = datetime.datetime.now()

for it in AllUrlList:  # 合并
    AllUrlList_table += it
for it in AllUrlList[0:7]:  # 合并1
    AllUrlList_table1 += it
for it in AllUrlList[7:]:  # 合并2
    AllUrlList_table2 += it

for it in PassUrlList:  # 合并
    PassUrlList_table += it
for it in PassUrlList[0:7]:  # 合并1
    PassUrlList_table1 += it
for it in PassUrlList[7:]:  # 合并2
    PassUrlList_table2 += it

for it in ErrorUrlList:  # 合并
    ErrorUrlList_table += it
for it in ErrorUrlList[0:7]:  # 合并1
    ErrorUrlList_table1 += it
for it in ErrorUrlList[7:]:  # 合并2
    ErrorUrlList_table2 += it

for it in TimeOutDic[0:7]:  # 合并1
    TimeOutDic_table1 += it
for it in TimeOutDic[7:]:  # 合并2
    TimeOutDic_table2 += it

for it in ExterLinks:  # 合并
    ExterLinks_table += it
    for it1 in ExterLinks_table:
        if it1 not in ExterLinks_table3:
            ExterLinks_table3.append(it1)
for it in ExterLinks[0:7]:  # 合并1
    ExterLinks_table1 += it
for it in ExterLinks[7:]:  # 合并2
    ExterLinks_table2 += it

if len(ErrorUrlList) > 0 or len(TimeOutDic) > 0:
    msg = MIMEMultipart()

    chi = {}
    for it in TimeOutDic:  # 合并成一个dict
        chi2 = it
        chi.update(chi2)

    EmailErrorUrlList = ''
    EmailTimeOutDic = ''
    EmailExterLinks = ''
    d = dict(sorted(chi.items(), key=lambda item: item[1], reverse=True))  # 字典倒序排列
    for i, j in d.items():  # 循环出字典数据格式
        EmailTimeOutError1 = str(i + ' , ' + j)
        EmailTimeOutError.append(EmailTimeOutError1)

print("所有URL如下：", AllUrlList, len(AllUrlList_table), len(AllUrlList_table1), len(AllUrlList_table2))
print("正常URL如下：", PassUrlList, len(PassUrlList_table), len(PassUrlList_table1), len(PassUrlList_table2))
print("外链URL如下：", ExterLinks, len(ExterLinks_table3), len(ExterLinks_table1), len(ExterLinks_table2))
print("异常URL如下：", ErrorUrlList, ErrorUrlList, len(ErrorUrlList_table), len(ErrorUrlList_table), len(ErrorUrlList_table))
print("超时URL如下：", TimeOutDic, EmailTimeOutError, len(EmailTimeOutError), TimeOutDic_table1, len(TimeOutDic_table1),
      TimeOutDic_table2, len(TimeOutDic_table2))

print('程序运行时间为: %s Seconds' % (end - start))

mail_host = "smtp.gmail.com"  # SMTP服务器地址
mail_sender = "<EMAIL>"  # 账号
# mail_passwd = "vhnpzvxgxocqxmvy"  # 密码
# mail_passwd = "pqabxyvjyguetipr"  # 密码
mail_passwd = "dzekwbcmtvzycafw"

ErrorUrlLists = 'Total of ' + str(len(ErrorUrlList_table)) + " Error URLs:"
EmailTimeOutDics = 'Total of ' + str(len(EmailTimeOutError)) + " Time Out URLs:"
EmailExterLinks = 'Total of ' + str(len(ExterLinks_table3)) + " Exter URLs："

msg = MIMEMultipart('related')
msg["Subject"] = "Online Daily URL Check"
msg["From"] = mail_sender  # 发送人

# msg["To"] = "<EMAIL>"  # 接收人
receiver = ['<EMAIL>', '<EMAIL>']  # 收件人


def data_to_html_1():  # head设置
    alarm_html1 = '<head>'
    alarm_html1 += '<meta charset="UTF-8">'
    alarm_html1 += "</head>"
    alarm_html1 += "<br>"
    return alarm_html1


def data_to_html_2(message2):  # 邮件开头
    alarm_html2 = '<div>'
    alarm_html2 += '<div">Dear:</div>'
    alarm_html2 += '<div style="white-space:pre">%s</div>' % message2
    alarm_html2 += "</div>"
    alarm_html2 += "<br>"
    return alarm_html2


def data_to_html_3(data3):  # table1
    alarm_html3 = '<table border="2px" cellpadding="10" style="border-collapse:collapse;">'
    for row in data3:
        alarm_html3 += '<tr align="center">'
        for item in row:
            if item in data3[0]:  # 第一行
                alarm_html3 += '<td style="background-color:#c9daf8"><b>%s</td>' % item
            elif item in data3[1][0] or item in data3[2][0] or item in data3[3][0] or item in data3[4][0] or item in \
                    data3[5][0] or item in data3[6][0] or item in data3[7][0] or item in data3[8][0] or item in \
                    data3[9][0]:  # 第一列
                alarm_html3 += '<td><b>%s</td>' % item
            elif item in data3[1][1:3] or item in data3[2][1:3] or item in data3[3][1:3] or item in \
                    data3[4][1:3] or item in data3[5][1:3] or item in data3[6][1:3] or item in \
                    data3[7][1:3] or item in data3[8][1:3] or item in data3[9][1:3]:  # 2、3列
                alarm_html3 += '<td>%s</td>' % item
            elif item in data3[1][3:5] or item in data3[2][3:5] or item in data3[3][3:5] or item in \
                    data3[4][3:5] or item in data3[5][3:5] or item in data3[6][3:5] or item in \
                    data3[7][3:5]:  # 4、5列
                if item > '0':
                    alarm_html3 += '<td style="color:red;">%s</td>' % item
                else:
                    alarm_html3 += '<td>%s</td>' % item
            elif item in data3[1][5:] or item in data3[2][5:] or item in data3[3][5:] or item in data3[4][
                                                                                                 5:] or item in \
                    data3[5][5:] or item in data3[6][5:] or item in data3[7][5:]:  # 6列
                alarm_html3 += '<td>%s</td>' % item
            elif item in data3[8][3:5]:  # 4、5 百分比
                if item > '0.00%':
                    alarm_html3 += '<td style="color:red;">%s</td>' % item
                else:
                    alarm_html3 += '<td>%s</td>' % item
            elif item in data3[9][3:4]:  # 9行4列
                if data3[8][3] == '0.00%':
                    alarm_html3 += '<td>%s</td>' % 'Successful'
                else:
                    alarm_html3 += '<td style="color:red;">%s</td>' % 'Failed'
            elif item in data3[9][4:5]:  # 9行5列
                if data3[8][4] == '0.00%':
                    alarm_html3 += '<td>%s</td>' % 'Successful'
                else:
                    alarm_html3 += '<td style="color:red;">%s</td>' % 'Failed'
    alarm_html3 += '</tr>'
    alarm_html3 += '</table>'
    alarm_html3 += "<br>"
    return alarm_html3


def data_to_html_4(data4):  # table2
    alarm_html4 = '<table border="2px" cellpadding="10" style="border-collapse:collapse;">'
    for row in data4:
        alarm_html4 += '<tr align="center">'
        for item in row:
            if item in data4[0]:
                alarm_html4 += '<td style="background-color:#c9daf8"><b>%s</td>' % item
            elif item in data4[1][0] or item in data4[2][0] or item in data4[3][0] or item in data4[4][0] or item in \
                    data4[5][0] or item in data4[6][0] or item in data4[7][0] or item in data4[8][0] or item in \
                    data4[9][0] or item in data4[10][0]:
                alarm_html4 += '<td><b>%s</td>' % item
            elif item in data4[1][1:3] or item in data4[2][1:3] or item in data4[3][1:3] or item in data4[4][
                                                                                                    1:3] or item in \
                    data4[5][1:3] or item in data4[6][1:3] or item in data4[7][1:3] or item in data4[8][1:3] or item in \
                    data4[9][1:3] or item in data4[10][1:3]:
                alarm_html4 += '<td>%s</td>' % item
            elif item in data4[1][3:5] or item in data4[2][3:5] or item in data4[3][3:5] or item in data4[4][
                                                                                                    3:5] or item in \
                    data4[5][3:5] or item in data4[6][3:5] or item in data4[7][3:5] or item in data4[8][3:5]:
                if item > '0':
                    alarm_html4 += '<td style="color:red;">%s</td>' % item
                else:
                    alarm_html4 += '<td>%s</td>' % item
            elif item in data4[1][5:] or item in data4[2][5:] or item in data4[3][5:] or item in data4[4][
                                                                                                 5:] or item in \
                    data4[5][5:] or item in data4[6][5:] or item in data4[7][5:] or item in data4[8][5:]:
                alarm_html4 += '<td>%s</td>' % item
            elif item in data4[9][3:5]:  # 4、5 百分比
                if item > '0.00%':
                    alarm_html4 += '<td style="color:red;">%s</td>' % item
                else:
                    alarm_html4 += '<td>%s</td>' % item
            elif item in data4[10][3:4]:
                if data4[9][3] == '0.00%':
                    alarm_html4 += '<td>%s</td>' % 'Successful'
                else:
                    alarm_html4 += '<td style="color:red;">%s</td>' % 'Failed'
            elif item in data4[10][4:5]:
                if data4[9][4] == '0.00%':
                    alarm_html4 += '<td>%s</td>' % 'Successful'
                else:
                    alarm_html4 += '<td style="color:red;">%s</td>' % 'Failed'
    alarm_html4 += '</tr>'
    alarm_html4 += '</table>'
    alarm_html4 += "<br>"
    return alarm_html4


def data_to_html_5(message5, data5):  # 超时设置
    alarm_html5 = '<div>'
    alarm_html5 += '<div>%s</div>' % message5
    for item5 in data5:
        alarm_html5 += '<div>%s</div>' % item5
    alarm_html5 += "</div>"
    alarm_html5 += "<br>"
    return alarm_html5


def data_to_html_6(message6, data6):  # error
    alarm_html6 = '<div>'
    alarm_html6 += '<div>%s</div>' % message6
    for item6 in data6:
        alarm_html6 += '<div>%s</div>' % item6
    alarm_html6 += "</div>"
    alarm_html6 += "<br>"
    return alarm_html6


def data_to_html_7(message7, data7):  # error
    alarm_html7 = '<div>'
    alarm_html7 += '<div>%s</div>' % message7
    for item7 in data7:
        alarm_html7 += '<div>%s</div>' % item7
    alarm_html7 += "</div>"
    alarm_html7 += "<br>"
    return alarm_html7


if __name__ == '__main__':

    data3 = [
        ['Language', 'Normal URL', 'Exter URL', 'Error URL', 'Time Out URL', 'Total URL'],
        ['English', str(len(PassUrlList[0])), str(len(ExterLinks[0])), str(len(ErrorUrlList[0])),
         str(len(TimeOutDic[0])), str(len(AllUrlList[0]))],
        ['简体中文', str(len(PassUrlList[1])), str(len(ExterLinks[1])), str(len(ErrorUrlList[1])), str(len(TimeOutDic[1])),
         str(len(AllUrlList[1]))],
        ['繁体中文', str(len(PassUrlList[2])), str(len(ExterLinks[2])), str(len(ErrorUrlList[2])), str(len(TimeOutDic[2])),
         str(len(AllUrlList[2]))],
        ['西班牙语', str(len(PassUrlList[3])), str(len(ExterLinks[3])), str(len(ErrorUrlList[3])), str(len(TimeOutDic[3])),
         str(len(AllUrlList[3]))],
        ['韩语', str(len(PassUrlList[4])), str(len(ExterLinks[4])), str(len(ErrorUrlList[4])), str(len(TimeOutDic[4])),
         str(len(AllUrlList[4]))],
        ['日语', str(len(PassUrlList[5])), str(len(ExterLinks[5])), str(len(ErrorUrlList[5])), str(len(TimeOutDic[5])),
         str(len(AllUrlList[5]))],
        ['越南语', str(len(PassUrlList[6])), str(len(ExterLinks[6])), str(len(ErrorUrlList[6])), str(len(TimeOutDic[6])),
         str(len(AllUrlList[6]))],
        ['Per', '{:.2f}'.format(len(PassUrlList_table1) * 100 / len(AllUrlList_table1)) + '%',
         '{:.2f}'.format(len(ExterLinks_table1) * 100 / len(AllUrlList_table1)) + '%',
         '{:.2f}'.format(len(ErrorUrlList_table1) * 100 / len(AllUrlList_table1)) + '%',
         '{:.2f}'.format(len(TimeOutDic_table1) * 100 / len(AllUrlList_table1)) + '%', ''],
        ['Status', 'Successful', 'Successful', 'Failed1', 'Failed2', ''],
    ]
    data4 = [
        ['Store', 'Normal URL', 'Exter URL', 'Error URL', 'Time Out URL', 'Total URL'],
        ['Chinese', str(len(PassUrlList[7])), str(len(ExterLinks[7])), str(len(ErrorUrlList[7])),
         str(len(TimeOutDic[7])), str(len(AllUrlList[7]))],
        ['Mexican', str(len(PassUrlList[8])), str(len(ExterLinks[8])), str(len(ErrorUrlList[8])),
         str(len(TimeOutDic[8])), str(len(AllUrlList[8]))],
        ['Japanese', str(len(PassUrlList[9])), str(len(ExterLinks[9])), str(len(ErrorUrlList[9])),
         str(len(TimeOutDic[9])), str(len(AllUrlList[9]))],
        ['Korean', str(len(PassUrlList[10])), str(len(ExterLinks[10])), str(len(ErrorUrlList[10])),
         str(len(TimeOutDic[10])), str(len(AllUrlList[10]))],
        ['Vietnamese', str(len(PassUrlList[11])), str(len(ExterLinks[11])), str(len(ErrorUrlList[11])),
         str(len(TimeOutDic[11])), str(len(AllUrlList[11]))],
        ['Filipino', str(len(PassUrlList[12])), str(len(ExterLinks[12])), str(len(ErrorUrlList[12])),
         str(len(TimeOutDic[12])), str(len(AllUrlList[12]))],
        ['Indian', str(len(PassUrlList[13])), str(len(ExterLinks[13])), str(len(ErrorUrlList[13])),
         str(len(TimeOutDic[13])), str(len(AllUrlList[13]))],
        ['American', str(len(PassUrlList[14])), str(len(ExterLinks[14])), str(len(ErrorUrlList[14])),
         str(len(TimeOutDic[14])), str(len(AllUrlList[14]))],
        ['Per', '{:.2f}'.format(len(PassUrlList_table2) * 100 / len(AllUrlList_table2)) + '%',
         '{:.2f}'.format(len(ExterLinks_table2) * 100 / len(AllUrlList_table2)) + '%',
         '{:.2f}'.format(len(ErrorUrlList_table2) * 100 / len(AllUrlList_table2)) + '%',
         '{:.2f}'.format(len(TimeOutDic_table2) * 100 / len(AllUrlList_table2)) + '%', ''],
        ['Status', 'Successful', 'Successful', 'Failed1', 'Failed2', ''],
    ]

    # message1 = '        在' + time.strftime("%Y-%m-%d_%H:%M:%S") + '\t进行了线上回归测试，请查看结果！\r\n'
    message2 = '        Online regression test was performed at' + ' ' + time.strftime(
        "%Y-%m-%d_%H:%M:%S") + ', Please check the results!'
    # message3 = EmailExterLinks
    message6 = ErrorUrlLists
    message5 = EmailTimeOutDics
    message7 = EmailExterLinks

    html1 = data_to_html_1()
    html2 = data_to_html_2(message2)
    html3 = data_to_html_3(data3)  # table1
    html4 = data_to_html_4(data4)  # table2
    html5 = ''
    if len(EmailTimeOutError) > 0:
        html5 = data_to_html_5(message5, EmailTimeOutError)  # 超时
    html6 = ''
    if len(ErrorUrlList_table) > 0:
        html6 = data_to_html_6(message6, ErrorUrlList_table)  # Error
    html7 = ''
    if len(ExterLinks_table3) > 0:
        html7 = data_to_html_7(message7, ExterLinks_table3)  # Error
    msg_txt = html1 + html2 + html3 + html4 + html6 + html5 + html7
    print(msg_txt)
    if len(EmailTimeOutError) > 0 or len(ErrorUrlList_table) > 0: #新增内容
        try:
            msg.attach(MIMEText(msg_txt, 'html', 'utf-8'))
            server = smtplib.SMTP('smtp.gmail.com: 587')  # 谷歌 SMTP:587  POP3:995
            server.starttls()
            server.login(mail_sender, mail_passwd)  # 登录邮箱账号密码
            server.sendmail(mail_sender, receiver, msg.as_string())  # 发送邮件
            # server.sendmail(mail_sender, '<EMAIL>', msg.as_string())  # 发送邮件
            server.quit()
            print('success')
        except smtplib.SMTPException as e:
            print('error', e)
    else:
        try:
            msg.attach(MIMEText(msg_txt, 'html', 'utf-8'))
            server = smtplib.SMTP('smtp.gmail.com: 587')  # 谷歌 SMTP:587  POP3:995
            server.starttls()
            server.login(mail_sender, mail_passwd)  # 登录邮箱账号密码
            # server.sendmail(mail_sender, receiver, msg.as_string())  # 发送邮件
            server.sendmail(mail_sender, '<EMAIL>', msg.as_string())  # 发送邮件
            server.quit()
            print('success')
        except smtplib.SMTPException as e:
            print('error', e)
