<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="16" time="23.702" timestamp="2024-02-19T14:02:53.865565" hostname="SHLAP10424"><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_select['订单列表查询接口-根据订单ID查询-{method: post, url: /cent..., pageNo: 1, pageSize: 10}}-{message_id: 10000, orderId: 34124917}']" time="2.294" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_select['订单列表查询接口-根据用户ID查询-{method: post, url: /cent..., pageNo: 1, pageSize: 10}}-{message_id: 10000, userId: 7169981}']" time="2.146" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_detail['订单详情查询接口-成功-{method: get, url: /centr...lInfo/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="2.685" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_summary['订单汇总详情查询接口-成功-{method: get, url: /centr...mmary/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.108" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_amount['订单支付金额查询接口-成功-{method: get, url: /centr...mount/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.057" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_payment_info['订单payment_info查询接口-成功-{method: get, url: /centr...tInfo/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.120" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_return_list['订单退款信息查询接口-成功-{method: get, url: /centr...nList/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.488" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_activity_list['订单日志记录查询接口-成功-{method: get, url: /centr...yList/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.140" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_promotion_info['订单促销记录查询接口-成功-{method: get, url: /centr...nInfo/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.281" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_device_info['订单关联设备及IP记录查询接口-成功-{method: get, url: /centr...eInfo/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.170" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_stripe['订单关联设备及IP记录查询接口-成功-{method: get, url: /centr...tripe/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.158" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_order['订单备注查询接口-成功-{method: get, url: /centr...rNote/{data}, data: 37710140}-{message_id: 10000, result: True}']" time="1.153" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_add_or_update_order_note['更新订单备注接口-成功-internal_note-{method: post, url: /cent...note: update Internal Notes}}-{message_id: 10000, result: True}']" time="1.593" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_order_fp_note['查询订单FP备注信息接口-成功-{method: post, url: /cent...geSize: 10, userId: 7169981}}-{message_id: 10000, result: True}']" time="1.728" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_add_order_fp_note['新增订单FP备注note接口-成功-{method: post, url: /cent...ype: 2, referId: 37709347}}-{message_id: 10000, result: True}']" time="1.095" /><testcase classname="test_dir.api_case.cs.simple.order_management.test_order_select.TestOrderSelect" name="test_delete_order_fp_note['删除订单FP备注note信息接口-成功-{method: get, url: /centr...delete/{note_id}, data: 1131}-{message_id: 10000, result: True}']" time="1.123" /></testsuite></testsuites>