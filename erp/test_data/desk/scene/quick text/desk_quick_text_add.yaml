desk_quick_text_add:
 - name: desk_quick_text_add接口
   description: 新增quick text成功
   request:
     method: post
     url: "/cs/desk/quickText/add"
     json_data: {"label":"11111111","name":"1","description":"test","content":"test","language":"zh","channel":[1,2,3,4,5,6],"categoryIds":",18,472,"}
   expected_result:
     message_id: "10000"
     result: true

quick_text_page_list:
 - name: quick text page list
   description: 查询quick text page list成功
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=23"
     json_data: {"pageNum":1,"pageSize":50}
   expected_result:
     message_id: "10000"
     result: true

quick_text_detail:
 - name: quick text detail
   description: 进入quick text detail
   request:
     method: get
     url: "/cs/desk/quickText/detail/"
     json_data: {}
   expected_result:
     message_id: "10000"
     result: true

quick_text_edit:
 - name: quick text edit
   description: quick text edit成功
   request:
     method: post
     url: "/cs/desk/quickText/edit"
     json_data: {"label":"11111111","name":"1","description":"test","content":"test","language":"zh","channel":[1,2,3,4,6],"id":"2092","categoryIds":",18,472,"}
   expected_result:
     message_id: "10000"
     result: true

quick_text_delete:
 - name: quick text delete
   description: quick text delete成功
   request:
     method: get
     url: "/cs/desk/quickText/delete/1502"
     json_data: {}
   expected_result:
     message_id: "10000"
     result: true