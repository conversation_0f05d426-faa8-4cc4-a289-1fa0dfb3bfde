
ticket_menu:
  -
    name: 获取ticket的菜单分类接口
    description: 成功
    request:
      method: get
      url: "/cs/desk/ticket/viewList"
      json_data:
      params_data:
    expected_result:
      message_id: "10000"
      result: True

ticket_list:
  -
    name: 获取ticket的列表接口
    description: 成功
    request:
      method: post
      url: "/cs/desk/ticket/list"
      json_data: {"pageNum": 1, "pageSize": 50, "type": "all"}
      params_data:
    expected_result:
      message_id: "10000"
      result: True

ticket_validate:
  -
    name: 新增ticket接口
    description: 成功
    request:
      method: post
      url: "/cs/desk/ticket/validateTicket"
      json_data: {"category":[201,122,135],"subject":"QA Subject","description":"<p>Description</p>",
                 "accessTime":"2023-07-24","userId":10930772,"caseNumber":"100016082",
                 "userEmail":"<EMAIL>","type":1,"categoryIds":",201,122,135,"}
      params_data:
    expected_result:
      message_id: "10000"
      result: True

ticket_detail:
  -
    name: ticket详情接口
    description: 成功
    request:
      method: post
      url: "/cs/desk/ticket/ticketDetail"
      json_data: {
          "processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d",
          "processInstanceId":"477e24e6-980d-11ee-913e-864553e91dc3",
          "taskId":"48dd3fd0-980d-11ee-913e-864553e91dc3",
          "taskKey":"cs_engineering",
          "ticketNumber":"2023121100001"
      }
    expected_result:
      message_id: "10000"
      result: True

ticket_follow:
  -
    name: ticket follow接口
    description: follow成功
    request:
      method: post
      url: "/cs/desk/ticket/ticketFollow"
      json_data: {"followType":1,"ticketId":274}
      params_data:
    expected_result:
      message_id: "10000"
      result: True
  -
    name: ticket follow接口
    description: unfollow成功
    request:
      method: post
      url: "/cs/desk/ticket/ticketFollow"
      json_data: {"followType":2,"ticketId":274}
      params_data:
    expected_result:
      message_id: "10000"
      result: True

ticket_flow_operation:
  -
    name: ticket 流程节点接口
    description: 成功
    request:
      method: post
      url: "/central/bpm/admin/flow/flowOperation/listFlowTaskComment"
      json_data:
      params_data: {"processInstanceId": "3c9aafdf-29f9-11ee-b999-f28617956cdc"}
    expected_result:
      message_id: "10000"
      result: True

ticket_add_chat_interaction:
  -
    name: ticket 新增聊天信息接口
    description: 成功
    request:
      method: post
      url: "/central/bpm/task/operator/chatLog/add"
      json_data: {"chatLogDto":{"chatContent":"<p>测试数据</p>","chatType":4,"processInstanceId":"3c9aafdf-29f9-11ee-b999-f28617956cdc",
                  "processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","chatMentionUsers":[]}}
      params_data:
    expected_result:
      message_id: "10000"
      success: True

ticket_chat_log:
  -
    name: ticket chat_log接口
    description: 成功
    request:
      method: post
      url: "/central/bpm/task/operator/chatLog/list"
      json_data: {"chatLogDtoFilter":{"processDefinitionId":"cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","zoneId":"America/Chicago",
                  "processInstanceId":"3c9aafdf-29f9-11ee-b999-f28617956cdc"},"orderParam":[{"fieldName":"recCreateTime","asc":1}]}
      params_data:
    expected_result:
      message_id: "10000"
      success: True

ticket_submit_task:
  -
    name: submit_task接口
    description: 失败
    request:
      method: post
      url: "/cs/desk/ticket/submitTask"
      json_data: {"comment":"Stop Comment","processInstanceId":"c300c4af-06a2-11ee-846b-dee94e7f8d2b",
                  "taskId":"8296354c-131f-11ee-ba3f-1a31944ed195","ticketNumber":"2023060900013","type":"stop"}
      params_data:
    expected_result:
      message_id: "66010"
      result: False

  -
    name: submit_task接口
    description: 失败
    request:
      method: post
      url: "/cs/desk/ticket/submitTask"
      json_data: {"comment":"Reject Comment","processInstanceId":"c300c4af-06a2-11ee-846b-dee94e7f8d2b",
                  "taskId":"8296354c-131f-11ee-ba3f-1a31944ed195","ticketNumber":"2023060900013","type":"reject"}
      params_data:
    expected_result:
      message_id: "66010"
      result: False

  -
    name: submit_task接口
    description: 失败
    request:
      method: post
      url: "/cs/desk/ticket/submitTask"
      json_data: {"comment":"Agree Comment","processInstanceId":"c300c4af-06a2-11ee-846b-dee94e7f8d2b",
                  "taskId":"8296354c-131f-11ee-ba3f-1a31944ed195","ticketNumber":"2023060900013","type":"agree"}
      params_data:
    expected_result:
      message_id: "66010"
      result: False



