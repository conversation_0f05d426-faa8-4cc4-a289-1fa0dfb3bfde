
ticket_category_tree:
  -
    name: 获取ticket分类树接口
    description: 成功
    request:
      method: get
      url: "/cs/desk/category/ticket/tree"
      json_data:
      params_data:
    expected_result:
      message_id: "10000"
      result: True

ticket_category_tree_detail:
  -
    name: 获取category分类树的详情接口
    description: 成功
    request:
      method: get
      url: "/cs/desk/category/detail/{data}"
      json_data:
      params_data:
      data: 189
    expected_result:
      message_id: "10000"
      result: True

ticket_add_category_tree:
  -
    name: 新增category分类树接口
    description: 成功
    request:
      method: post
      url: "/cs/desk/category/ticket/add"
      json_data: {"name":"四级测试03","pid":199}
      params_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

ticket_rename_category_tree:
  -
    name: 修改category分类树接口
    description: 成功
    request:
      method: post
      url: "/cs/desk/category/ticket/rename"
      json_data: {"name":"test01","id":400}
      params_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

ticket_delete_category_tree:
  -
    name: 删除category分类树接口
    description: 成功
    request:
      method: get
      url: "/cs/desk/category/ticket/delete/{data}"
      json_data:
      params_data:
      data: 399
    expected_result:
      message_id: "10000"
      result: True










