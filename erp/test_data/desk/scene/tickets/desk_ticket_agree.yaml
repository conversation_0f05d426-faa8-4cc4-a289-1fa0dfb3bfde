add_ticket:
  - name: 新增ticket
    description: 新增成功
    request:
      method: post
      url: "/cs/desk/ticket/addTicket"
      json_data: { "category": [ 189,87,129 ],"subject": "test ticket agree","description": "<p>test ticket agree</p>","userId": 10060559,"caseNumber": "103012122","userEmail": "<EMAIL>","type": 1,"categoryIds": ",189,87,129," }
    expected_result:
      message_id: "10000"
      result: true

list_page:
  - name: 列表页
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/caseTicket/listPage"
      json_data: { "caseId": "3012122","pageNum": 1,"pageSize": 3 }
    expected_result:
      message_id: "10000"
      result: true

ticket_detail:
  - name: ticket 详情页
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/ticket/ticketDetail"
      json_data: {"processDefinitionId": "cs_to_crm:15:e3ba498e-e589-11ed-af84-66fa7f46f45d","processInstanceId": "2fe2451d-cc07-11ef-ae05-3e6342617982","taskId": "319b8dfd-cc07-11ef-ae05-3e6342617982","taskKey": "cs_engineering","ticketNumber": "" }
    expected_result:
      message_id: "10000"
      result: true

review_ticket:
  - name: review ticket
    description: review ticket
    request:
      method: post
      url: "/cs/desk/ticket/review"
      json_data: {"issueType":"0","issueOwner":"CS","consult":1,"memo":"1","ticketNumber":""}
    expected_result:
      message_id: "10000"
      result: true

submit_task:
  - name: 提交task
    description: 提交成功
    request:
      method: post
      url: "/cs/desk/ticket/submitTask"
      json_data: { "comment": "agree","issueType": "0","processInstanceId": "","taskId": "","ticketNumber": "","type": "agree" }
    expected_result:
      message_id: "10000"
      result: true

ticket_list:
  - name: all ticket list
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/ticket/list"
      json_data: { "pageNum": 1,"pageSize": 50,"type": "all" }
    expected_result:
      message_id: "10000"
      result: true