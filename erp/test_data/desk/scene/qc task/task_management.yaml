qc_task_page:
 - name: qc task list
   description: 查询qc task list成功
   request:
     method: post
     url: "/cs/desk/qc_task/page"
     json_data: {"pageNum":1,"current":1,"pageSize":20,"type":"","status":"A","createTimeRange":["2025-03-28","2025-03-28"],"sort":{},"filter":{}}
   expected_result:
     message_id: "10000"
     result: true

qc_task_assign_agent:
 - name: qc task assign agent
   description: qc task 指给agent
   request:
     method: post
     url: "/cs/desk/qc_task/assign_agent"
     json_data: {"taskIds":[125,158],"userIds":["4"]}
   expected_result:
     message_id: "10000"
     result: true

qc_task_add_tag:
 - name: qc task add tag
   description: qc task 新增tag成功
   request:
     method: post
     url: "/cs/desk/qc_task/add_tag"
     json_data: {"taskIds":[971],"tagIds":"73"}
   expected_result:
     message_id: "10000"
     result: true

qc_task_remove_tag:
 - name: qc task remove tag
   description: qc task remove tag成功
   request:
     method: post
     url: "/cs/desk/qc_task/remove_tag"
     json_data: {"taskIds":[971],"tagIds":"73"}
   expected_result:
     message_id: "10000"
     result: true

qc_score_all_view:
 - name: qc score all view
   description: qc score all view
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=5109"
     json_data: {"pageNum":1,"pageSize":20}
   expected_result:
     message_id: "10000"
     result: true

qc_score_case_view:
 - name: qc score case view
   description: qc score case view
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=5110"
     json_data: {"pageNum":1,"pageSize":20}
   expected_result:
     message_id: "10000"
     result: true

qc_score_call_view:
 - name: qc score call view
   description: qc score call view
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=5111"
     json_data: {"pageNum":1,"pageSize":20}
   expected_result:
     message_id: "10000"
     result: true

qc_score_my_score_view:
 - name: qc score my score view
   description: qc score my score view
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=5112"
     json_data: {"pageNum":1,"pageSize":20}
   expected_result:
     message_id: "10000"
     result: true

qc_task_new_case_view:
 - name: qc task new case data view
   description: my qc task new case data view
   request:
     method: post
     url: "/cs/desk/qc_task/data_view"
     json_data: {"pageNum":1,"pageSize":50,"key":"caseTask"}
   expected_result:
     message_id: "10000"
     result: true

qc_task_new_call_view:
 - name: qc task new call data view
   description: my qc task new call data view
   request:
     method: post
     url: "/cs/desk/qc_task/data_view"
     json_data: {"pageNum":1,"pageSize":50,"key":"callTask"}
   expected_result:
     message_id: "10000"
     result: true

qc_task_complete_view:
 - name: qc task complete data view
   description: my qc task complete data view
   request:
     method: post
     url: "/cs/desk/qc_task/data_view"
     json_data: {"pageNum":1,"pageSize":50,"key":"completeTask"}
   expected_result:
     message_id: "10000"
     result: true