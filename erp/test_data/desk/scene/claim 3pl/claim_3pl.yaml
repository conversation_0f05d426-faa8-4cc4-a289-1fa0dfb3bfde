desk_claim_3pl_create:
 - name: desk claim 3pl 创建接口
   description: 创建成功
   request:
     method: post
     url: "/central/cs/claim/tp/create"
     json_data: {"invoiceId":30691632,"salesOrg":"Seattle","seller":"Weee!","deliveryDate":"2022-12-14","responsible":"GLS US","claimType":"3PL","tracking":["G763041403522109440"],"trackingAll":["G763041403522109440"],"shippingCharge":"9.81","fees":0,"amountReceived":70.26,"amount":80.07000000000001,"note":"TEST","claimItems":[{"sku":12821,"productName":"Passion Fruit Popsicle 16ct","quantity":1,"subTotal":15.49,"returnId":4752073,"deskCaseId":"100260702V","status":"Approved","returnReason":"logistic_nrt(Logistic issue (Not received when tracking shows delivered -claimable))"},{"sku":13230,"productName":"Nozomi Super Premium Short Grain Rice","quantity":1,"subTotal":44.99,"returnId":4752074,"deskCaseId":"100260702V","status":"Approved","returnReason":"logistic_nrt(Logistic issue (Not received when tracking shows delivered -claimable))"},{"sku":21904,"productName":"Jinzai Fried Spicy Fish Snack","quantity":1,"subTotal":5.29,"returnId":4752075,"deskCaseId":"100260702V","status":"Approved","returnReason":"logistic_nrt(Logistic issue (Not received when tracking shows delivered -claimable))"},{"sku":34782,"productName":"Lejinji Cheese Flavored Butter Bread, 8pcs","quantity":1,"subTotal":4.49,"returnId":4752076,"deskCaseId":"100260702V","status":"Approved","returnReason":"logistic_nrt(Logistic issue (Not received when tracking shows delivered -claimable))"}],"status":"M"}
   expected_result:
     message_id: "10000"
     result: true

claim_3pl_find_list:
 - name: desk claim 3pl查询列表接口
   description: 查询成功
   request:
     method: post
     url: "/central/cs/claim/tp/findList"
     json_data: {"current":1,"pageSize":10,"salesOrg":"","sortMap":{},"pageNum":1}
   expected_result:
     message_id: "10000"
     result: true

claim_3pl_edit:
 - name: desk claim 3pl编辑接口
   description: 编辑成功
   request:
     method: get
     url: "/central/cs/claim/tp/edit/{}"
     json_data: {}
   expected_result:
     message_id: "10000"
     result: true

claim_3pl_modify_status:
 - name: desk claim 3pl修改状态
   description: 修改成功
   request:
     method: post
     url: "/central/cs/claim/tp/modifyStatus"
     json_data: {"claimId":"18566","status":"X"}
   expected_result:
     message_id: "10000"
     result: true

claim_3pl_pending:
 - name: desk claim 3pl pending状态
   description: 查询成功
   request:
     method: post
     url: "/central/cs/claim/tp/findList"
     json_data: {"current":1,"pageSize":10,"salesOrg":"","status":"S","sortMap":{},"pageNum":1}
   expected_result:
     message_id: "10000"
     result: true

claim_3pl_approved:
 - name: desk claim 3pl approved状态
   description: 查询成功
   request:
     method: post
     url: "/central/cs/claim/tp/findList"
     json_data: {"current":1,"pageSize":10,"salesOrg":"","status":" A","sortMap":{},"pageNum":1}
   expected_result:
     message_id: "10000"
     result: true