desk_kb_space_create:
  - name: desk创建space
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/kb/space/create"
      json_data: {"name":"space Interface testing","description":"space Interface testing","type":1,"masterLanguage":"en","logoUrl":"https://img06.test.weeecdn.com/cs/image/970/936/3507D854ABF92FDA.png","roleAssignments":[{"roleId":1,"memberId":1,"memberType":2},{"roleId":2,"memberId":1,"memberType":2}]}
    expected_result:
      message_id: "10000"
      result: true

space_list:
  - name: space list
    description: 查询成功
    request:
      method: get
      url: "/cs/desk/kb/space?type=&pageNum=1&pageSize=50"
      json_data: {"type":"","pageNum":"1","pageSize":"50"}
    expected_result:
      message_id: "10000"
      result: true


add_article:
  - name: add article
    description: 新增成功
    request:
      method: post
      url: "/cs/desk/kb/article/addArticle"
      json_data: {"spaceId":"122","title":"App端如何申请售后退款？","content":"<p><span style=\"color: rgb(0, 0, 0);\">1.点开我的 账户，我的订单--已发货--售后问题订单--点击“售后/退款”</span><img src=\"https://img06.weeecdn.com/cs/image/542/024/217E0323FADE8CDA.png\" width=\"369\"><img src=\"https://img06.weeecdn.com/cs/image/673/018/6DF8584908D83E69.png\" width=\"404\"></p><p><span style=\"color: rgb(0, 0, 0);\">2.填写售后原因并提交</span></p><p><img src=\"https://img06.weeecdn.com/cs/image/229/884/200F11353A399E0C.png\" width=\"331\"></p><p>温馨提示：<span style=\"color: rgb(0, 0, 0);\">如果选择商品的时候，显示“该商品已退款，无法再次申请售后”，那么说明系统已经退款，不用再申请退款了。</span></p><p>以上内容对您有帮助吗？</p><p><br></p><p><br></p>","categoryId":560,"type":"2","language":"en"}
    expected_result:
      message_id: "10000"
      result: true

edit_article_detail:
  - name: article edit
    description: 编辑成功
    request:
      method: post
      url: "/cs/desk/kb/article/editArticleDetail"
      json_data: {"spaceId":"122","articleId":1590,"articleDetailId":3003,"title":"App端如何申请售后退款？","content":"<p><span style=\"color: rgb(0, 0, 0);\">1.点开我的 账户，我的订单--已发货--售后问题订单--点击“售后/退款”</span><img src=\"https://img06.weeecdn.com/cs/image/542/024/217E0323FADE8CDA.png\" width=\"369\"><img src=\"https://img06.weeecdn.com/cs/image/673/018/6DF8584908D83E69.png\" width=\"404\"></p><p><span style=\"color: rgb(0, 0, 0);\">2.填写售后原因并提交</span></p><p><img src=\"https://img06.weeecdn.com/cs/image/229/884/200F11353A399E0C.png\" width=\"331\"></p><p>温馨提示：<span style=\"color: rgb(0, 0, 0);\">如果选择商品的时候，显示“该商品已退款，无法再次申请售后”，那么说明系统已经退款，不用再申请退款了。</span></p><p>以上内容对您有帮助吗？</p>","categoryId":560,"language":"en"}
    expected_result:
      message_id: "10000"
      result: true

