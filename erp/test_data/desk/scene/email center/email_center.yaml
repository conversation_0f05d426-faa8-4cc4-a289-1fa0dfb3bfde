email_center_view:
  - name: email center all view
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=2178"
      json_data: {"pageNum":1,"pageSize":50}
    expected_result:
      message_id: "10000"
      result: true

email_center_detail:
  - name: email center detail
    description: 页面数据加载正常
    request:
      method: get
      url: "/cs/desk/email/{}"
      json_data: {}
    expected_result:
      message_id: "10000"
      result: true

