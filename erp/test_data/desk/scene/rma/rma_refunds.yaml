
select_refund_list:
  -
    name: 查询refund列表接口
    description: 默认查询
    request:
      method: post
      url: "/cs/desk/rmaRefund/findList"
      json_data: {"sortMap": {}, "pageNum": 1, "pageSize": 2, "applyTimeRange":["2023-04-20","2023-07-21"]}
    expected_result:
      message_id: "10000"
      result: True

  -
    name: 查询return列表接口
    description: 默认查询
    request:
      method: post
      url: "/cs/desk/rmaRefund/findList"
      json_data: {"id": "503", "sortMap": {}, "pageNum": 1, "pageSize": 2, "applyTimeRange":["2023-04-20","2023-07-21"]}
    expected_result:
      message_id: "10000"
      result: True

create_rma_refund:
  - name: 创建refund接口
    description: 创建失败
    request:
      method: post
      url: "/cs/desk/rmaRequest/createRequest"
      json_data: {"id":null, "orderId":34124401,"requestId":823, "rmaStatus":"Returning","orderType":"S-normal-0",
                  "userId":7694015,"comment":"QA测试", "status":"A","sellerId":7556,"returnType":"out"}
    expected_result:
          message_id: "70002"
          result: False

edit_rma_refund:
  - name: 编辑refund接口
    description: 创建失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/editRefund"
      json_data: {"id":307, "requestId":793}
    expected_result:
          message_id: "70002"
          result: False

approve_rma_refund:
  - name: 审批refund接口
    description: approve失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/doAction"
      json_data: {"id":"505","action":"approve","isMandatory":"N"}
    expected_result:
      message_id: "10000"
      result: True

batch_approve_rma_refund:
  - name: 批量审批refund接口
    description: batch approve失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/batchApprove"
      json_data: [504]
    expected_result:
      message_id: "10000"
      result: True

decline_rma_refund:
  - name: 审批refund接口
    description: decline失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/doAction"
      json_data: {"id":"505","comment":"QA Decline","action":"decline","isMandatory":"N"}
    expected_result:
      message_id: "70001"
      result: False

delete_rma_refund:
  - name: 审批refund接口
    description: delete失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/doAction"
      json_data: {"id":"502","comment":"QA delete","action":"void","isMandatory":"N"}
    expected_result:
      message_id: "70001"
      result: False

modify_rma_refund:
  - name: 审批refund接口
    description: Modify失败
    request:
      method: post
      url: "/cs/desk/rmaRefund/doAction"
      json_data: {"id":"505","comment":"QA Modify","action":"recall","isMandatory":"N"}
    expected_result:
      message_id: "70001"
      result: False

rma_refund_log:
  - name: 日志查询接口
    description: 查询日志
    request:
      method: post
      url: "/cs/desk/rmaRefund/log"
      json_data:
      params_data: {"id":"505"}
    expected_result:
      message_id: "10000"
      result: True

rma_refund_detail:
  - name: 查询refund详情接口
    description: 查询详情
    request:
      method: post
      url: "/cs/desk/rmaRefund/getRefundDetailById"
      json_data:
      params_data: {"id":"505"}
    expected_result:
      message_id: "10000"
      result: True






