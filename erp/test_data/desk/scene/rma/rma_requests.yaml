
select_request_list:
  - name: 查询request列表接口
    description: 默认查询
    request:
      method: post
      url: "/cs/desk/rmaRequest/getAgentRequestPage"
      json_data: {"current":1,"pageSize":20,"rmaStatus":"","applyTimeRange":["2024-08-30","2024-09-14"],"sortMap":{},"pageNum":1}
    expected_result:
      message_id: "10000"
      result: True

create_request_list:
  - name: 创建request接口
    description: 创建失败
    request:
      method: post
      url: "/cs/desk/rmaRequest/createRequest"
      json_data: {"id":"","requestType":1,"orderId":"34123175","status":"New"}
    expected_result:
      message_id: "99999"
      result: False

rma_request_detail:
  - name: 查询request详情接口
    description: 详情
    request:
      method: get
      url: "/cs/desk/rmaRequest/getRmaDetail"
      json_data:
      params_date: {"id": "1126"}
    expected_result:
      message_id: "10000"
      result: True

void_rma_request:
  - name: void request详情接口
    description: 作废失败
    request:
      method: post
      url: "/cs/desk/rmaRequest/voidRequest"
      json_data: {"requestId": "923", "comment": "QA-void"}
      params_date:
    expected_result:
      message_id: "70002"
      result: False

















