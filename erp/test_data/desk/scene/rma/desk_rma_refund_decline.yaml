desk_get_valid_product_info:
  - name: 获取有效产品信息
    description: 获取成功
    request:
      method: get
      url: "/cs/desk/rmaRequest/getValidProductInfo?orderId=42628494&language="
      json_data: {"orderId":42628494,"language":}
    expected_result:
      message_id: "10000"
      result: Ture

desk_rma_create_request:
  - name: desk创建request
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/rmaRequest/createRequest?autoAction=true"
      json_data: {"id":null,"requestType":1,"requestTypeStr":null,"requestTime":null,"confirmedTime":null,"paymentCategory":"B","status":"New","orderId":"42628494","category":"Product quality","salesOrg":null,"totalAmount":null,"customerInfo":null,"steps":null,"nextStep":null,"sellerName":"乡味坊","isMarket":true,"deadline":null,"paymentMode":"online","username":"xiao.ye888","canEditAmount":false,"productList":[{"id":null,"status":null,"requestId":null,"oosStatus":null,"rmaProductId":null,"productId":,"orderProductId":,"productTitle":"","requestQty":1,"requestAmount":,"rmaRefundId":null,"storageType":null,"percentage":100,"refundPercentage":100,"returnReasonOption":null,"maxRefundAmount":,"tax":0,"unitPrice":,"shippedQty":1,"refundQty":1,"refundProduct":,"refundTax":0,"refundStatus":null,"maximumQty":1,"returnType":null,"internalReason":null,"subTotal":,"taxRate":0,"discount":null,"refundAmount":,"needReturn":"N","replaceProductId":null,"replaceProductTitle":null,"replaceQty":null,"imageUrl":"https://img06.weeecdn.com/item/image/361/098/722E318ED11C5E5.jpeg","replaceImageUrl":null,"category":null,"sellerSku":null,"replaceSellerSku":null,"receivedQty":null,"refundedQty":null,"replacedQty":null,"canRefund":true,"sellerId":null,"sellerName":null,"riskLevel":null,"statusType":1,"taxCanRefund":0,"crvOrderProductId":null,"type":"normal","targetOrderProductId":null,"restockable":1}],"shipmentInfo":null,"operationLogInfo":null,"nextAction":null,"selectedKeys":[],"comment":"test rma refund decline","subOrderType":null,"deliveryMode":"shipping","relatedOrders":null,"isOnDemandOrder":false,"returnMethod":null,"canEditRefundMethod":false,"canEditReturnMethod":false,"pickupDate":null,"carrierId":22,"trackingNumber":"33333333","returnAddress":null,refundReason":"quality-issue","refundReasonOption":["quality-issue","quality-issue"],"source":"desk","autoRefund":false,"orderSoure":"weee"}
    expected_result:
      message_id: "10000"
      result: True

desk_rma_refund_do_action:
  - name: decline refund
    description: decline 成功
    request:
      method: post
      url: "/cs/desk/rmaRefund/doAction"
      json_data: {"id":"","comment":"test rma refund decline","action":"decline","isMandatory":"N"}
    expected_result:
      message_id: "10000"
      result: Ture

desk_rma_refund_void_request:
  - name: void request
    description: void 成功
    request:
      method: post
      url: "/cs/desk/rmaRequest/voidRequest"
      json_data: {"requestId":,"comment":"void"}
    expected_result:
      message_id: "10000"
      result: Ture


desk_rma_refund_find_list:
  - name: 查询refund list
    description: 获取成功
    request:
      method: post
      url: "/cs/desk/rmaRefund/findList"
      json_data: {"current":1,"pageSize":100,"origin":["A"],"applyTimeRange":["2024-03-21","2024-06-22"],"sortMap":{},"pageNum":1}
    expected_result:
      message_id: "10000"
      result: Ture

