owned_by_me:
 - name: owned by me
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"ownedByMe"}}
   expected_result:
     message_id: "10000"
     result: true

shared_with_me:
 - name: shared with me
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"sharedWithMe"}}
   expected_result:
     message_id: "10000"
     result: true

recently_viewed:
 - name: recently viewed
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"recentlyViewed"}}
   expected_result:
     message_id: "10000"
     result: true

following_viewed:
 - name: following viewed
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"following"}}
   expected_result:
     message_id: "10000"
     result: true

cs_viewed:
 - name: cs viewed
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"category","value":424}}
   expected_result:
     message_id: "10000"
     result: true

seller_viewed:
 - name: seller viewed
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/file/listPage"
     json_data: {"pageNum":1,"pageSize":50,"filter":{"type":"category","value":425}}
   expected_result:
     message_id: "10000"
     result: true