get_user_info:
  - name: 获取用户信息接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/auth/getUserInfo"
      json_data: { "object": {
        "id": 1,
        "weeeUserId": ********,
        "userName": "xiao.ye",
        "avatar": "https://img06.weeecdn.com/social/image/097/841/social_avatar_10.jpeg",
        "type": 1,
        "status": 1,
        "phone": "***********",
        "email": "<EMAIL>",
        "employeeNumber": null,
        "deptId": 1,
        "emailSignature": null,
        "callCenterAccount": null,
        "liveChatAccount": null,
        "language": "zh",
        "timeZone": "Asia/Shanghai",
        "superiorApproverId": null,
        "receiveApprovalRequestEmails": null,
        "maxProcessCapacity": null,
        "phoneStatus": null,
        "liveChatStatus": null,
        "routingStatus": 1,
        "recCreatorId": -1,
        "recCreatorName": "system",
        "recCreateTime": "2022-12-26T01:47:02.000+00:00",
        "recUpdateId": 4,
        "recUpdateName": "yongzhi",
        "recUpdateTime": "2023-11-29T06:55:08.000+00:00",
        "isDelete": 0,
        "roleName": "CS agent,CS Desk Agent,CS Desk TL,RMA-Manager,CS-Call-Agent,MKPL BD\t"
      } }
    expected_result:
      message_id: "10000"
      result: true

desk_fp_case_create:
  - name: desk_fp_case_create接口
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/case/create"
      json_data: {"weeeUserId":"********","email":"<EMAIL>","phone":"wv_488975926537781892","language":"en","caseType":"4","origin":6,"subject":"test origin desk fp case","priority":30,"importance":2,"categoryIds":",713,714,"}
    expected_result:
      message_id: "10000"
      result: true

case_interaction_List:
  - name: case_interaction_List接口
    description: 处理中
    request:
      method: post
      url: "/cs/desk/case/interactionList"
      json_data: { "caseId": "", "sortBy": "1" }
    expected_result:
      message_id: "10000"
      result: true

send_EmailAndResolve:
  - name: 回复客人并处理完case
    description: 已处理
    request:
      method: post
      url: "/cs/desk/case/sendEmailAndResolve"
      json_data: {"toList":["<EMAIL>"],"fromAddress":"<EMAIL>","replyToTrackId":"64a059bb-40a5-4db4-b4c6-f242285a7cd7","subject":"test origin desk fp case","bodyHtml":"<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body><p><br /></p><p><span style=\"color: rgb(0, 0, 0);\">您好，</span></p><p><br /></p><p><span style=\"color: rgb(0, 0, 0);\">非常抱歉给您带来不便，关于商品质量问题，我们已经反馈给相关部门引起重视并尽快改善。</span></p><p><br /></p><p><span style=\"color: rgb(0, 0, 0);\">上述商品已经为您申请退款，退款会按原支付途径返回，大概1-3个工作日到账，请您注意查收。</span></p><p><br /></p><p><span style=\"color: rgb(0, 0, 0);\">Weee!十分重视您的反馈，如果您有任何疑问，请随时联系我们。</span></p><p><br /></p><p><span style=\"color: rgb(0, 0, 0);\">感谢您的理解和支持！</span></p><p><span style=\"color: rgb(0, 0, 0);\">Weee!客户服务</span></p><p><br /></p></body></html>","caseId":"","emailTemplateId":0,"attachments":[],"ccList":[],"bccList":[],"bodyPlain":"您好，非常抱歉给您带来不便，关于商品质量问题，我们已经反馈给相关部门引起重视并尽快改善。上述商品已经为您申请退款，退款会按原支付途径返回，大概1-3个工作日到账，请您注意查收。Weee!十分重视您的反馈，如果您有任何疑问，请随时联系我们。感谢您的理解和支持！Weee!客户服务"}
    expected_result:
      message_id: "10000"
      result: true

pageQuery_DataView:
  - name: 页面查询case view all
    description: 处理中
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
      json_data: {"pageNum":1,"pageSize":100}
    expected_result:
      message_id: "10000"
      result: true