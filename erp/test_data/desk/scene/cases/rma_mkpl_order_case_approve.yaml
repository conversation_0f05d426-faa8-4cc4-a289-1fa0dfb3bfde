ec_mkpl_order_case_create:
  - name: ec_mkpl_order_case_create接口
    description: 创建成功
    request:
      method: post
      url: "/ec/cs/case/create/order"
      json_data: {
        "case_category":"Shortage","case_tags":[],"image_urls":[{"url":"https://img06.test.weeecdn.com/cs/image/063/726/BF2ADD99303DFFA.gif","file_size":907773}],"comment":"test mkpl order case","user_max_refund_amount":20.23,"original_user_max_refund_amount":20.23,"is_all_return":false,"percentage":100,"products":[{"order_product_id":*********,"product_id":2846294,"product_quantity":1,"amount":20.23,"sale_type":null}],"order_id":42657790,"payment_mode":"wp"
      }
    expected_result:
      message_id: "10000"
      result: true


get_Rma_Detail:
  - name: 获取rma detail
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/rmaRequest/getRmaDetail?id=8058&language=undefined"
      json_data: {"id":"","language":"undefined"}
    expected_result:
      message_id: "10000"
      result: true

rma_Request_approve:
  - name: 审核request
    description: 审核通过
    request:
      method: post
      url: "/cs/desk/rmaRequest/approve"
      json_data: {"id":,"deskCaseId":null,"requestType":"1","requestTypeStr":"","requestTime":"","confirmedTime":null,"orderStatus":"","status":"","orderId":42657790,"category":"Product quality","salesOrg":null,"totalAmount":,"customerInfo":{"userId":7616787,"username":"qq/ww","phoneNum":"0712563698","encryptPhoneNum":"M3ehaI9jr48kypJ8QD9bmcD/Q==","address":"47467 Fremont Blvd, Fremont, California, 94538, United States","comment":"test mkpl order case","email":"<EMAIL>","imageList":["https://img06.test.weeecdn.com/cs/image/766/684/4340F6700C77AADB.png"],"tags":[],"userTag":[{"id":null,"name":"Loyal customers","type":null,"bind":null},{"id":null,"name":"High Value","type":null,"bind":null},{"id":5,"name":"WatchList","type":"Rule","bind":true},{"id":13,"name":"CCPA","type":"Manual","bind":true}]},"steps":["New","Refunding","Completed"],"nextStep":"Confirm","sellerId":8113,"sellerName":"乡味坊","isMarket":true,"deadline":48,"paymentMode":"online","userId":null,"username":null,"canEditAmount":true,"productList":[{"id":4080,"status":null,"requestId":8484,"oosStatus":null,"rmaProductId":4080,"productId":2846294,"orderProductId":*********,"productTitle":"[Nezha*2]  refrigerator sticker","requestQty":1,"requestAmount":,"rmaRefundId":null,"storageType":null,"percentage":,"refundPercentage":,"returnReasonOption":null,"maxRefundAmount":,"tax":0,"unitPrice":,"shippedQty":1,"refundQty":1,"refundProduct":,"refundTax":0,"refundStatus":null,"maximumQty":1,"returnType":null,"internalReason":null,"subTotal":,"taxRate":0,"discount":null,"refundAmount":,"needReturn":"N","replaceProductId":null,"replaceProductTitle":null,"replaceQty":null,"imageUrl":"https://img06.weeecdn.com/item/image/363/992/69E2B1FE46782CD2.jpeg","replaceImageUrl":null,"category":"Product quality","sellerSku":null,"replaceSellerSku":null,"receivedQty":null,"refundedQty":null,"replacedQty":null,"canRefund":null,"sellerId":null,"sellerName":null,"riskLevel":null,"statusType":null,"taxCanRefund":0}],"shipmentInfo":[{"id":null,"type":1,"carrierId":13,"trackingUrl":"https://track.amazon.com/tracking/3333333333","requestId":null,"status":null,"shipmentValue":"Original Order","carrier":"Amazon","trackingNum":"3333333333","attachments":null}],"operationLogInfo":[{"id":11031,"type":"Create","operator":"system","operatorId":7616787,"operateTime":"2024-04-30T07:46:56.000+00:00","comment":"save request"}],"nextAction":[{"buttonValue":"Confirm","isPrimary":true},{"buttonValue":"Reject","isPrimary":false}],"selectedKeys":null,"comment":"test mkpl order case","orderType":"S-normal-0","subOrderType":"seller_mail","deliveryMode":"shipping","relatedOrders":[{"id":*********,"type":"Case#","linkUrl":"/cases/case-profile/25271/*********","createTime":"2024-30-12T07:46:57.000+00:00","updateTime":"2024-04-30T07:46:57.000+00:00","status":"Unassigned","operatorId":null,"operatorName":null,"approveId":null,"approveName":null}],"isOnDemandOrder":false,"returnMethod":null,"canEditRefundMethod":true,"canEditReturnMethod":false,"pickupDate":null,"carrierId":null,"trackingNumber":null,"refundReason":null,"refundReasonOption":null}
    expected_result:
      message_id: "10000"
      result: true

ec_cs_case_cancel_order:
  - name: ec取消 mkpl order
    description: 操作成功
    request:
      method: POST
      url: "/ec/cs/case/cancel/order"
      json_data: {"case_id":""}
    expected_result:
      message_id: "10000"
      result: true
