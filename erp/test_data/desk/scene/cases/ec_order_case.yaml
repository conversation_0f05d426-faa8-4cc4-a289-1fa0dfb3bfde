ec_order_case_create:
  - name: ec_order_create接口
    description: 创建成功
    request:
      method: post
      url: "/ec/cs/case/create/order"
      json_data: {"case_category":"Product quality","case_tags":[],"image_urls":[{"url":"https://img06.test.weeecdn.com/cs/image/398/322/8B92E37CEB8D14D.png","file_size":10747}],"comment":"test","order_product_id":258883706,"product_id":77150,"product_quantity":1,"percentage":100,"user_max_refund_amount":11.99,"is_all_return":false,"products":[{"order_product_id":258883706,"product_id":77150,"product_quantity":1}],"order_id":42597360,"payment_mode":"online"}
    expected_result:
      message_id: "10000"
      result: true

page_query_data_view:
  - name: 页面查询dataview
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
      json_data: {"pageNum":1,"pageSize":100}
    expected_result:
      message_id: "10000"
      result: true

assigned_to_me:
  - name: 指给某人处理
    description: 操作成功
    request:
      method: post
      url: "/cs/desk/case/assignedToMe"
      json_data: {"caseNumber":"*********"}
    expected_result:
      message_id: "10000"
      result: true

tree_ByCase_Type:
  - name: 填写category
    description: 更新成功
    request:
      method: post
      url: "/cs/desk/case/treeByCaseType"
      json_data: {"caseType":1,"caseNumber":"*********"}
    expected_result:
      message_id: "10000"
      result: true

update_Description:
  - name: 更新描述
    description: 更新成功
    request:
      method: post
      url: "/cs/desk/case/updateDescription"
      json_data: {"ownerId":1,"ownerType":1,"assignee":1,"assigneeType":1,"priority":40,"importance":2,"language":"zh","caseType":1,"categoryIds":",615,616,","salesOrg":7,"email":"q*****<EMAIL>","seller":{"name":"-1 - Weee!","id":-1},"weeeUserId":********,"phone":"","accountId":2,"caseId":"30573"}
    expected_result:
      message_id: "10000"
      result: true

reply_Message_And_Resolve:
  - name: 回复客人并解决问题
    description: 回复成功
    request:
      method: post
      url: "/cs/desk/case/replyMessageAndResolve"
      json_data: {"message":"<p>已处理谢谢您的反馈</p>","caseId":"30357","contentType":"html"}
    expected_result:
      message_id: "10000"
      result: true