get_user_info:
  - name: 获取用户信息接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/auth/getUserInfo"
      json_data: { "object": {
        "id": 1,
        "weeeUserId": ********,
        "userName": "xiao.ye",
        "avatar": "https://img06.weeecdn.com/social/image/097/841/social_avatar_10.jpeg",
        "type": 1,
        "status": 1,
        "phone": "***********",
        "email": "<EMAIL>",
        "employeeNumber": null,
        "deptId": 1,
        "emailSignature": null,
        "callCenterAccount": null,
        "liveChatAccount": null,
        "language": "zh",
        "timeZone": "Asia/Shanghai",
        "superiorApproverId": null,
        "receiveApprovalRequestEmails": null,
        "maxProcessCapacity": null,
        "phoneStatus": null,
        "liveChatStatus": null,
        "routingStatus": 1,
        "recCreatorId": -1,
        "recCreatorName": "system",
        "recCreateTime": "2022-12-26T01:47:02.000+00:00",
        "recUpdateId": 4,
        "recUpdateName": "yongzhi",
        "recUpdateTime": "2023-11-29T06:55:08.000+00:00",
        "isDelete": 0,
        "roleName": "CS agent,CS Desk Agent,CS Desk TL,RMA-Manager,CS-Call-Agent,MKPL BD\t"
      } }
      params_data:
      data:
    expected_result:
      message_id: "10000"
      result: true

desk_order_case_create:
  - name: desk_order_create接口
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/case/create"
      json_data: { "weeeUserId": "********","email": "<EMAIL>","phone": "wv_488975926537781892","language": "zh","caseType": "1","origin": 6,"subject": "test origin desk order case ","priority": 30,"importance": 2,"categoryIds": ",690,691," }
    expected_result:
      message_id: "10000"
      result: true

case_interaction_List:
  - name: case_interaction_List接口
    description: 处理中
    request:
      method: post
      url: "/cs/desk/case/interactionList"
      json_data: { "caseId": "", "sortBy": "1" }
      params_data:
      data:
    expected_result:
      message_id: "10000"
      result: true

send_EmailAndResolve:
  - name: 回复客人并处理完case
    description: 已处理
    request:
      method: post
      url: "/cs/desk/case/sendEmailAndResolve"
      json_data: { "toList": [ "<EMAIL>" ],"fromAddress": "<EMAIL>","replyToTrackId": "75b2406d-6977-4d03-934e-9c06e58fcd4e","subject": "qa测试","bodyHtml": "<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body><p>已处理</p></body></html>","caseId": "","emailTemplateId": 0,"attachments": [ ],"ccList": [ ],"bccList": [ ],"bodyPlain": "已处理" }
      params_data:
      data:
    expected_result:
      message_id: "10000"
      result: true

pageQuery_DataView:
  - name: 页面查询case view all
    description: 处理中
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
      json_data: {"pageNum":1,"pageSize":100}
    expected_result:
      message_id: "10000"
      result: true