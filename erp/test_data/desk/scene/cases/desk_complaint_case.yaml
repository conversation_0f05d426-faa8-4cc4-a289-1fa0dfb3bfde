get_user_info:
  - name: 获取用户信息接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/auth/getUserInfo"
      json_data: { "object": {
        "id": 1,
        "weeeUserId": ********,
        "userName": "xiao.ye",
        "avatar": "https://img06.weeecdn.com/social/image/097/841/social_avatar_10.jpeg",
        "type": 1,
        "status": 1,
        "phone": "***********",
        "email": "<EMAIL>",
        "employeeNumber": null,
        "deptId": 1,
        "emailSignature": null,
        "callCenterAccount": null,
        "liveChatAccount": null,
        "language": "zh",
        "timeZone": "Asia/Shanghai",
        "superiorApproverId": null,
        "receiveApprovalRequestEmails": null,
        "maxProcessCapacity": null,
        "phoneStatus": null,
        "liveChatStatus": null,
        "routingStatus": 1,
        "recCreatorId": -1,
        "recCreatorName": "system",
        "recCreateTime": "2022-12-26T01:47:02.000+00:00",
        "recUpdateId": 4,
        "recUpdateName": "yongzhi",
        "recUpdateTime": "2023-11-29T06:55:08.000+00:00",
        "isDelete": 0,
        "roleName": "CS agent,CS Desk Agent,CS Desk TL,RMA-Manager,CS-Call-Agent,MKPL BD\t"
      } }
    expected_result:
      message_id: "10000"
      result: true

desk_complaint_case_create:
  - name: desk_complaint_case_create接口
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/case/create"
      json_data: {"weeeUserId":"********","email":"<EMAIL>","phone":"wv_488975926537781892","language":"zh","caseType":"3","origin":6,"subject":"test complaint case","priority":30,"importance":2,"categoryIds":",707,"}
    expected_result:
      message_id: "10000"
      result: true

case_interaction_List:
  - name: case_interaction_List接口
    description: 处理中
    request:
      method: post
      url: "/cs/desk/case/interactionList"
      json_data: { "caseId": "", "sortBy": "1" }
    expected_result:
      message_id: "10000"
      result: true

send_EmailAndResolve:
  - name: 回复客人并处理完case
    description: 已处理
    request:
      method: post
      url: "/cs/desk/case/sendEmailAndResolve"
      json_data: {"toList":["<EMAIL>"],"fromAddress":"<EMAIL>","replyToTrackId":"8752250a-f1f3-416a-8958-c57270a4670c","subject":"test complaint case","bodyHtml":"<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body><p>尊敬的客户您好，</p><p>感谢您的支持和反馈！</p><p>我们是Weee! 客户服务团队。为了不断提高商品品质及服务质量，我们特此进行此项客户满意度调研。期盼您在百忙之中给予我们客观的评价，并提出宝贵的意见和建议。</p><p>您的评价和建议是我们奋进的动力！</p><p>Weee!十分重视您的反馈，我们将虚心听取并及时改进，为您提供更好的服务。</p><p>请您点击链接<a href=\"https://desk.tb1.sayweee.net/rating-complain/zh/100024954\" rel=\"noopener noreferrer\" target=\"_blank\"> https://desk.tb1.sayweee.net/rating-complain/zh/100024954</a> 进行评价</p><p>如果今后您对于我们有任何意见或是建议，也可以通过************************邮箱与我们进行反馈联系。</p><p>Weee!客户关怀团队</p></body></html>","ccList":["<EMAIL>"],"bccList":["<EMAIL>"],"caseId":"","emailTemplateId":0,"attachments":[],"bodyPlain":"尊敬的客户您好，感谢您的支持和反馈！我们是Weee! 客户服务团队。为了不断提高商品品质及服务质量，我们特此进行此项客户满意度调研。期盼您在百忙之中给予我们客观的评价，并提出宝贵的意见和建议。您的评价和建议是我们奋进的动力！Weee!十分重视您的反馈，我们将虚心听取并及时改进，为您提供更好的服务。请您点击链接 https://desk.tb1.sayweee.net/rating-complain/zh/100024954 进行评价如果今后您对于我们有任何意见或是建议，也可以通过************************邮箱与我们进行反馈联系。Weee!客户关怀团队"}
    expected_result:
      message_id: "10000"
      result: true

pageQuery_DataView:
  - name: 页面查询case view all
    description: 处理中
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
      json_data: {"pageNum":1,"pageSize":100}
    expected_result:
      message_id: "10000"
      result: true