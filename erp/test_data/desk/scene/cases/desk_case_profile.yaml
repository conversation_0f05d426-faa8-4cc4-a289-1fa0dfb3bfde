desk_case_profile_create:
 - name: desk_case_profile_create接口
   description: 创建成功
   request:
     method: post
     url: "/ec/cs/case/createCase"
     json_data: {"caseType":"N","caseCategory":"order_issue-cancel_order","caseCategoryLabel":"I want to cancel my order","comment":"接口测试case profile","email":"<EMAIL>","phone":"(*************","orderId":"42630922","imageUrls":[{"url":"https://img06.test.weeecdn.com/cs/image/240/451/4CC08C9631260B.png"}]}
   expected_result:
     message_id: "10000"
     result: true


pageQuery_Data_View:
 - name: 页面查询all view
   description: 查询成功
   request:
     method: post
     url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
     json_data: {"pageNum":1,"pageSize":50,"orderList":[{"orderRule":"desc","orderColumn":"caseNumber"}]}
   expected_result:
     message_id: "10000"
     result: true

search_case_list:
  - name: 页面搜索查询case
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/search/objPageList"
      json_data: {"compositeInfo":"","objType":"case","pageNum":1,"pageSize":10,"columns":[]}
    expected_result:
      message_id: "10000"
      result: true


enter_CaseDetail:
 - name: 进入case详情页
   description: 操作成功
   request:
     method: get
     url: "/cs/desk/case/enterCaseDetail/41632"
     json_data: {}
   expected_result:
     message_id: "10000"
     result: true


assigned_ToMe:
 - name: 指给业务处理
   description: 操作成功
   request:
     method: post
     url: "/cs/desk/case/assignedToMe"
     json_data: {"caseNumber":"*********"}
   expected_result:
     message_id: "10000"
     result: true


update_WeeeUserId:
 - name: 绑定另一个customer
   description: 绑定成功
   request:
     method: post
     url: "/cs/desk/case/updateWeeeUserId"
     json_data: {"accountId":2,"caseId":41632,"weeeUserId":5955706}
   expected_result:
     message_id: "10000"
     result: true


unbind_Order:
 - name: 取消绑定某个订单
   description: 取消绑定成功
   request:
     method: post
     url: "/cs/desk/caseData/unbindOrder"
     json_data: {"caseId":"41632","dataId":********}
   expected_result:
     message_id: "10000"
     result: true


upload_Image:
 - name: 上传图片
   description: 上传图片成功
   request:
     method: post
     url: "/cs/desk/caseFile/uploadImage"
     json_data: {"caseId":41665,"list":[{"fileName":"3478E54403ED7580.png","fileType":"image/png","url":"https://img06.test.weeecdn.com/cs/image/937/596/3478E54403ED7580.png"}]}
   expected_result:
     message_id: "10000"
     result: true


upload_File:
 - name: 上传文件
   description: 上传文件成功
   request:
     method: post
     url: "/cs/desk/caseFile/uploadFile"
     json_data: {"caseId":41665,"list":[{"fileName":"3478E54403ED7580.png","fileType":"image/png","url":"https://img06.test.weeecdn.com/cs/image/937/596/3478E54403ED7580.png","fileVersionId":303171,"fileSize":10747}]}
   expected_result:
     message_id: "10000"
     result: true


add_All:
 - name: 新增tag
   description: 新增tag成功
   request:
     method: post
     url: "/cs/desk/tag/addAll"
     json_data: {"caseId":"41665","tagIds":[20],"caseType":2}
   expected_result:
     message_id: "10000"
     result: true

validate_Ticket:
   - name: 编辑新增相关ticket
     description: 编辑新增相关ticket成功
     request:
       method: post
       url: "/cs/desk/ticket/validateTicket"
       json_data: {"category": "[505, 506]","subject": "1","description": "自动化测试",
                    "type":1,"categoryIds":",505,506,"}
     expected_result:
       message_id: "10000"
       result: true

add_Ticket:
  - name: 新增相关ticket
    description: 新增相关ticket成功
    request:
      method: post
      url: "/cs/desk/ticket/addTicket"
      json_data: {"category": "[505, 506]","subject": "1","description": "自动化测试",
                   "type":1,"categoryIds": ",505,506," }
    expected_result:
      message_id: "10000"
      result: true

add_Related_Date:
 - name: 新增相关数据
   description: 新增相关数据成功
   request:
     method: post
     url: "/cs/desk/caseData/addRelateData"
     json_data: {"caseId":"3023737","dataType":2,"dataId":"7443731"}
   expected_result:
     message_id: "10000"
     result: true