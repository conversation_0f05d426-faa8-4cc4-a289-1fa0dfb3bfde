get_user_info:
  - name: 获取用户信息接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/auth/getUserInfo"
      json_data: { "object": {
        "id": 1,
        "weeeUserId": ********,
        "userName": "xiao.ye",
        "avatar": "https://img06.weeecdn.com/social/image/097/841/social_avatar_10.jpeg",
        "type": 1,
        "status": 1,
        "phone": "***********",
        "email": "<EMAIL>",
        "employeeNumber": null,
        "deptId": 1,
        "emailSignature": null,
        "callCenterAccount": null,
        "liveChatAccount": null,
        "language": "zh",
        "timeZone": "Asia/Shanghai",
        "superiorApproverId": null,
        "receiveApprovalRequestEmails": null,
        "maxProcessCapacity": null,
        "phoneStatus": null,
        "liveChatStatus": null,
        "routingStatus": 1,
        "recCreatorId": -1,
        "recCreatorName": "system",
        "recCreateTime": "2022-12-26T01:47:02.000+00:00",
        "recUpdateId": 4,
        "recUpdateName": "yongzhi",
        "recUpdateTime": "2023-11-29T06:55:08.000+00:00",
        "isDelete": 0,
        "roleName": "CS agent,CS Desk Agent,CS Desk TL,RMA-Manager,CS-Call-Agent,MKPL BD\t"
      } }
    expected_result:
      message_id: "10000"
      result: true

desk_seller_support_case_create:
  - name: desk_seller_support_case_create接口
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/case/create"
      json_data: {"weeeUserId":"********","email":"<EMAIL>","phone":"wv_488975926537781892","language":"en","caseType":"5","origin":6,"subject":"test seller support case","internalDescription":"qa测试数据","priority":30,"importance":2,"categoryIds":",700,701,"}
    expected_result:
      message_id: "10000"
      result: true

case_interaction_List:
  - name: case_interaction_List接口
    description: 处理中
    request:
      method: post
      url: "/cs/desk/case/interactionList"
      json_data: { "caseId": "", "sortBy": "1" }
    expected_result:
      message_id: "10000"
      result: true

get_Quick_Text_Content:
  - name: 获取快速回复文本接口
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/quickText/getQuickTextContent"
      json_data: {"caseId":"","quickTextId":1432}
    expected_result:
      message_id: "10000"
      result: true

send_EmailAndResolve:
  - name: 回复客人并处理完case
    description: 已处理
    request:
      method: post
      url: "/cs/desk/case/sendEmailAndResolve"
      json_data: {"toList":["<EMAIL>"],"fromAddress":"<EMAIL>","replyToTrackId":"9a655460-1d39-4e98-8baf-24eb293188d2","subject":"test seller support case","bodyHtml":"<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body><p>你好，已处理</p><p>Dear Customer, </p><p><br /></p><p>Thank you for contacting us. </p><p><br /></p><p>Regarding your feedback, we will issue you a 50% refund. We wonder if it is acceptable for you. </p><p><br /></p><p>We've already referred the issue to our relevant departments. Improvement will be made to ensure the quality of our product and service. </p><p><br /></p><p>If you have any further questions, please feel free to contact us. </p><p><br /></p><p>Sincerely, </p><p>Weee！Customer Service</p><p><br /></p><p><img src=\"https://www.fedex.com/content/dam/fedex-com/ens/spacer.gif\" height=\"1\" width=\"1\" /></p></body></html>","caseId":"","emailTemplateId":0,"attachments":[{"name":"1b7bbab0f4df7780324887f0a6fd0d5b.jpeg","url":"https://img06.test.weeecdn.com/cs/image/981/210/41E091E229DD0EB1.jpeg","content-type":"image/jpeg","fileVersionId":220008}],"ccList":[],"bccList":[],"bodyPlain":"你好，已处理Dear Customer, Thank you for contacting us. Regarding your feedback, we will issue you a 50% refund. We wonder if it is acceptable for you. We've already referred the issue to our relevant departments. Improvement will be made to ensure the quality of our product and service. If you have any further questions, please feel free to contact us. Sincerely, Weee！Customer Service"}
    expected_result:
      message_id: "10000"
      result: true

search_case_list:
  - name: 页面查询case list
    description: 查询成功
    request:
      method: post
      url: "/cs/desk/search/objPageList"
      json_data: {"compositeInfo":"","objType":"case","pageNum":1,"pageSize":10,"columns":[]}
    expected_result:
      message_id: "10000"
      result: true