desk_call_record_create_case:
  - name: desk创建phone case
    description: 创建成功
    request:
      method: post
      url: "/cs/desk/call/record/createCase"
      json_data: {"id":"134"}
    expected_result:
      message_id: "10000"
      result: true

add_Relate_Data:
  - name: 绑定相关订单
    description: 绑定成功
    request:
      method: post
      url: "/cs/desk/caseData/addRelateData"
      json_data: {"caseId":30843,"dataId":"********","dataType":1}
    expected_result:
      message_id: "10000"
      result: true

update_Description:
  - name:
    description: 更新成功
    request:
      method: post
      url: "/cs/desk/case/updateDescription"
      json_data: {"ownerId":1,"ownerType":1,"assignee":1,"assigneeType":1,"priority":10,"importance":2,"language":"en","caseType":1,"categoryIds":",615,616,","salesOrg":null,"email":"<EMAIL>","seller":{"name":"-1 - Weee!","id":-1},"weeeUserId":********,"phone":"861569***9091","accountId":2,"caseId":"30843"}
    expected_result:
      message_id: "10000"
      result: true

send_email_and_resolve:
  - name:
    description: 发送成功
    request:
      method: post
      url: "/cs/desk/case/sendEmailAndResolve"
      json_data: {"toList":["<EMAIL>"],"fromAddress":"<EMAIL>","replyToTrackId":"1ae4f23f-d2cd-41d9-aedd-f23a506be39f","subject":"phone 接口测试","bodyHtml":"<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body><p>已处理</p><p><br /></p></body></html>","caseId":"30843","emailTemplateId":0,"attachments":[],"ccList":[],"bccList":[],"bodyPlain":"已处理"}
    expected_result:
      message_id: "10000"
      result: true

pageQuery_DataView:
  - name: 页面查询case view all
    description: 处理中
    request:
      method: post
      url: "/cs/desk/search/pageQueryDataView?dataViewId=30"
      json_data: {"pageNum":1,"pageSize":100}
    expected_result:
      message_id: "10000"
      result: true