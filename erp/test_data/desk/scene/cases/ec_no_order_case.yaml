
get_user_info:
  -
    name: 获取用户信息接口
    description: 默认查询
    request:
      method: get
      url: "/ec/cs/user/getUserInfo"
      json_data:
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

no_order_case_list:
  -
    name: 获取no_order列表接口
    description: 处理中
    request:
      method: post
      url: "/ec/cs/cases/queryCaseList"
      json_data: {"filterType":0, "caseType":"N"}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True
  -
    name: 获取no_order列表接口
    description: 已关闭
    request:
      method: post
      url: "/ec/cs/cases/queryCaseList"
      json_data: {"filterType":1, "caseType":"N"}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True
  -
    name: 获取no_order列表接口
    description: 全部
    request:
      method: post
      url: "/ec/cs/cases/queryCaseList"
      json_data: {"filterType":2, "caseType":"N"}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

no_order_case_category:
  -
    name: 获取no_order_category接口
    description: 获取category
    request:
      method: post
      url: "/ec/cs/case/queryCaseCategory"
      json_data: {"caseType":"N"}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

create_no_order_case:
  -
    name: 创建no_order_case接口
    description: 创建成功
    request:
      method: post
      url: "/ec/cs/case/createCase"
      json_data: {"caseType":"N","caseCategory":"order_issue-other","caseCategoryLabel":"其它订单问题","comment":"QA测试","email":"<EMAIL>","phone":"(*************","orderId":"42630198","imageUrls":[{"url":"https://img06.test.weeecdn.com/cs/image/454/539/10232A2646D37A6C.jpeg"}]}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

no_order_case_detail:
  -
    name: no_order_case详情接口
    description: 获取成功
    request:
      method: post
      url: "/ec/cs/cases/getCaseInfo"
      json_data: {"caseId":5988,"channel":1}
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

cancel_no_order_case:
  -
    name: no_order_case详情接口
    description: 获取成功
    request:
      method: post
      url: "/ec/cs/cases/cancelCase"
      json_data: {"caseId":1133}
      parmas_data:
      data:
    expected_result:
      message_id: "30002"
      result: False

create_no_order_case_message:
  -
    name: no_order_case详情接口
    description: 获取成功
    request:
      method: post
      url: "/ec/cs/cases/createCaseMessage"
      json_data: {
                  "caseId":5988,
                  "message":"回复数据01",
                  "contentType":"text",
                  "channel":1
                  }
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True

update_no_order_case_message_status:
  -
    name: no_order_case详情接口
    description: 获取成功
    request:
      method: post
      url: "/ec/cs/cases/updateCaseMessageStatus"
      json_data: {
                  "caseId":5988,
                  "channel":1
                  }
      parmas_data:
      data:
    expected_result:
      message_id: "10000"
      result: True









