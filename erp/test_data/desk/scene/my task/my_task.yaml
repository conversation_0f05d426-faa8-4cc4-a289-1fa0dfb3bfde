my_task_view:
 - name: 页面查询my task 视图
   description: 查询my task 视图成功
   request:
     method: get
     url: "/cs/desk/my_task/view"
     json_data:
   expected_result:
     message_id: "10000"
     result: true

my_task_recently:
 - name: 页面查询my task recently list
   description: 查询my task recently list成功
   request:
     method: post
     url: "/cs/desk/my_task/recently"
     json_data: {"pageNum":1,"pageSize":50}
   expected_result:
     message_id: "10000"
     result: true

my_task_ticket_detail:
 - name: 页面查询my_task_ticket_detail
   description: 查询my_task_ticket_detail成功
   request:
     method: post
     url: "/cs/desk/ticket/ticketDetail"
     json_data: {"processDefinitionId":"cs_to_catman:4:c7a9294c-036b-11ee-bbb0-4ac3f8798cff","processInstanceId":"efa91643-6e8b-11ef-804d-ea41b2402c2e","taskId":"e8ae2454-6e8f-11ef-804d-ea41b2402c2e","taskKey":"cs_catman_customer_end","ticketNumber":"2024090900007"}
   expected_result:
     message_id: "10000"
     result: true