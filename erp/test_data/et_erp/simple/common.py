# -*- coding:utf-8 -*-
import copy
import json

import weeeTest
from weeeTest import weeeConfig, log, jmespath
from erp.test_dir.api.auth.central_login import UserLogin
from erp.test_dir.api.auth.vendor_login import VendorLoginInterfaces


class Header(weeeTest.TestCase):
    # def __init__(self):
    #     self.l_header = copy.deepcopy(self.header)

    def login_header(self, account=None, password='123456'):
        if account is None:
            if weeeTest.weeeConfig.base_url != 'https://api.sayweee.net':
                account = '********'
            else:
                account = '7226349'
        login = None
        try:
            login = UserLogin().user_login(account=account, password=password)
        except Exception as e:
            log.info("登陆失败" + str(e))
        return login[0]

    def basic_user_header(self, account='********', password='123456'):
        login = None
        try:
            login = UserLogin().user_login(account=account, password=password)
        except Exception as e:
            log.info("登陆失败" + str(e))
        return login[0]

    def login_desk_header(self, account='********', password='123456'):
        login = None
        try:
            login = UserLogin().user_login(account=account, password=password)
        except Exception as e:
            log.info("登陆失败" + str(e))
        return login[0]

    def vendor_header(self, email="<EMAIL>", password="123456"):
        vendor_login = None
        header = {}
        try:
            vendor_login = VendorLoginInterfaces().vendor_login(data={"email": email, "password": password})
        except Exception as e:
            log.info("登陆失败" + str(e))

        try:
            token = jmespath(vendor_login, "object.token")
            header['authorization'] = 'Bearer ' + token
            header['User-Agent'] = "weee-web1"
            return header
        except Exception as e:
            log.info("获取token失败" + str(e))


if __name__ == '__main__':
    pass
