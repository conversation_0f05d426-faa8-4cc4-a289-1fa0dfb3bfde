link_event_success:
  - request:
      # 更新时间
      json_data: {
        "eventCode": ["202504_AdsWi_1"],
        "eventGroupId": '',
        "promotionDtos": [{"pepIds":[],"priceSpecialIds":[14285838]},{"pepIds":[],"priceSpecialIds":[14122806]}]
      }
      message: "SKU : 54845, SO : 3, Start : 2025-02-06, End : 2025-02-28 - Successfully Linked to 202504_AdsWi_1##SKU : 105760, SO : 3, Start : 2025-02-08, End : 2025-02-08 - Successfully Linked to 202504_AdsWi_1"

link_event_fail:
  - request:
      json_data: {
        "eventCode": ["202505_MOC85_6"],
        "eventGroupId": ,
        "promotionDtos": [{"pepIds":[76023895],"priceSpecialIds":[13830452]}]
      }
      message: "SKU : 17304, SO : 3, Start : 2025-01-31, End : 2025-02-06 - Max Order Quantity should be less than or equal to 2 to link to Crazy 8 Event 202505_MOC85_6"
  - request:
      json_data: {
        "eventCode": ["202501_2Lant_3"],
        "eventGroupId": 3215,
        "promotionDtos": [{"pepIds":[76023895],"priceSpecialIds":[13830452]}]
      }
      message: "SKU : 17304, SO : 3, Start : 2025-01-31, End : 2025-02-06 - Link already exists in 202501_2Lant_3"
  - request:
      json_data: {
        "eventCode": [ "202505_INCra_2" ],
        "eventGroupId": 5174,
        "promotionDtos": [{ "pepIds": [76096596],"priceSpecialIds": [14594318]}]
      }
      message: "SKU : 107344, SO : 1, Start : 2025-02-15, End : 2025-02-19 - The maximum number of promotion price records allowed for this Crazy 8 Event has been reached."
