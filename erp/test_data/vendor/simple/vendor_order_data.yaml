
vendor_order_list:
  -
    title:
      name: 供应商平台订单列表查询
      description: 默认条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: Date Filter条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[rec_create_time]": "2023-10-16"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: PO# 条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[po_id]": "2655618"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: Status条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[status]": "P"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: Region条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[inbound_inventory_id]": "8"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: Invoice Number条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[vendor_invoice_num]": "WEEE_170794"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"
  -
    title:
      name: 供应商平台订单列表查询
      description: To-Dos条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/orders"
      params_data: {"filter[to_dos]": "reply"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "orders"

vendor_order_info:
  - title:
      name: 供应商平台订单详情
      description: 详情页
    request:
      method: get
      url: "/vendor/service/v1/po/orders/{}"
      data: 2655618
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "id"











