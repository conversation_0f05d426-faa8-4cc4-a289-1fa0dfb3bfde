
vendor_appointment_list:
  -
    title:
      name: 供应商平台预约送货列表查询
      description: 默认条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台预约送货列表查询
      description: Status条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {"filter[status]": "A"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台预约送货列表查询
      description: PO ID条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {"filter[purchase_order_id]": "228023"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台预约送货列表查询
      description: Warehouse条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {"filter[inventory_id]": "20"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台预约送货列表查询
      description: Storage Type条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {"filter[storage_type]": "F"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台预约送货列表查询
      description: Type条件查询
    request:
      method: get
      url: "/vendor/service/v1/po/proposals/appointmentList"
      params_data: {"filter[appointment_type]": "R"}
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "list"

vendor_order_detail:
  - title:
      name: 供应商平台订单详情信息查询
      description: 订单详情信息
    request:
      method: get
      url: "/vendor/service/v1/po/orders/{}/detail"
      data: 265155
      header:
        vendor_id: "1"
    expected_result:
      result: True
      object: "id"

vendor_order_active_time:
  - title:
      name: 供应商平台订单可用时间查询
      description: 可用时间
    request:
      method: post
      url: "/vendor/service/v1/po/proposals/getAvailableTime"
      json_data: {
          "id":0,
          "purchase_order_id":265155,
          "date":"2023-12-26",
          "purchase_order_list":[]
      }
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "id"

vendor_order_appointment_time:
  - title:
      name: 供应商平台订单预约时间新增
      description: 新增
    request:
      method: post
      url: "/vendor/service/v1/po/proposals/getAvailableTime"
      json_data: {
          "id":0,
          "calendar_event_id":0,
          "type":"S",
          "purchase_order_id":265155,
          "date":"2023-12-26",
          "time":"13:00-14:00",
          "remark":"Description",
          "eta_date":"12/26/2023",
          "vendor_id":"1 - Test Vender 1",
          "inventory_id":"25 - LA - La Mirada"
      }
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      message: "success"

vendor_order_cancel_appointment_time:
  - title:
      name: 供应商平台订单预约时间取消
      description: 取消
    request:
      method: post
      url: "/vendor/service/v1/po/proposals/appointmentCancel"
      json_data: {
          "id":"25248",
          "purchase_order_id":"265071",
          "calendar_event_id":"24041",
          "type":"S"
      }
      header: {"Vendor-Id": "1"}
    expected_result:
      result: False
      message: "Cancel calendar failure"






