
vendor_active_product_list:
  -
    title:
      name: 供应商平台商品中心的active商品列表
      description: 默认查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台商品中心的active商品列表
      description: Weee SKU查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[product_id]": "103178"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台商品中心的active商品列表
      description: Item Code查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[item_code]": "1111"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台商品中心的active商品列表
      description: Product Name查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[vendor_product_title]": "Apple"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台商品中心的active商品列表
      description: Unit UPC查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[upc_code]": "20000000044224"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"

vendor_update_code:
  -
    title:
      name: 供应商平台商品中心-更新供应商code
      description: 更新成功
    request:
      method: put
      url: "/vendor/service/v1/pi/products/createRequest"
      json_data: {
          "item_code": "update1",
          "product_vendor_id": "115992", # product_vendor_id 与 SKU ID 不一致
          "type": 2
      }
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True












