
vendor_product_request_list:
  -
    title:
      name: 供应商平台Product Request列表
      description: 默认查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productRequest"
      params_data: {}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Product Request列表
      description: Weee SKU查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productRequest"
      params_data: {"filter[product_id]": 42}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Product Request列表
      description: Item Code查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productRequest"
      params_data: {"filter[item_code]": "123"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Product Request列表
      description: Product Name查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productRequest"
      params_data: {"filter[product_name]": "Test Product 1"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Product Request列表
      description: Status查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productRequest"
      params_data: {"filter[status]": "P"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"


vendor_product_update_list:
  -
    title:
      name: 供应商平台Product Update列表
      description: 默认查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台Product Update列表
      description: Weee SKU查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[product_id]": "42"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台Product Update列表
      description: Item Code查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[item_code]": "123123"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台Product Update列表
      description: Product Name查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[vendor_product_title]": "福州鱼丸 1磅/袋 11"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"
  -
    title:
      name: 供应商平台Product Update列表
      description: UPC Code查询
    request:
      method: get
      url: "/vendor/service/v1/pi/products"
      params_data: {"filter[upc_code]": "692628502333"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "products"