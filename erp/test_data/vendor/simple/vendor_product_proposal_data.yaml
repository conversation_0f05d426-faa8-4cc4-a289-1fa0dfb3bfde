
vendor_review_product_list:
  -
    title:
      name: 供应商平台Review商品列表
      description: 默认查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Review商品列表
      description: Status查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {"filter[proposal_status]": "A"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Review商品列表
      description: Weee SKU查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {"filter[product_id]": "42"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Review商品列表
      description: Item Code查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {"filter[vendor_SKU_code]": "107326"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Review商品列表
      description: Product Name查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {"filter[title_en]": "Superior Light Soy Sauce"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"
  -
    title:
      name: 供应商平台Review商品列表
      description: Unit UPC查询
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview"
      params_data: {"filter[UPC_code]": "722337819048"}
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "list"

vendor_review_product_info:
  -
    title:
      name: 供应商平台Review商品详情
      description: Review商品详情
    request:
      method: get
      url: "/vendor/service/v1/pi/productReview/detail/{}"
      data: 18558
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "id"

vendor_create_review_product:
  -
    title:
      name: 供应商平台创建商品Review
      description: 创建
    request:
      method: put
      url: "/vendor/service/v1/pi/productReview/save"
      json_data: {
          "title_en":"test-auto",
          "storage_temperate_zone":"N1",
          "unit":"M",
          "unit_min":1,
          "shelf_life":360,
          "purchase_price":2,
          "purchase_unit":"unit",
          "quantity_per_unit":1,
          "description_en":"Description En-test",
      }
      header: {"Vendor-Id": "1"}
    expected_result:
      result: True
      object: "id"

