get_assortment_list:
  - request:
      # 查询仓库和存储类型
      params_data: {
        "filter[inventory_id]": 37,
        "filter[storage_type]": 'R',
        "filter[division]": '06'
      }
      sql: "SELECT * FROM weee_merch.pi_assortment_rule WHERE inventory_id ='37' AND storage_type ='R' and division ='06'"
  - request:
      # 没有结果
      params_data: {
        "filter[inventory_id]": 41,
        "filter[storage_type]": 'F',
        "filter[division]": '05',
        "filter[ethnicity]": 'korean',
        "filter[assortment_role_name]": 'Testing'
      }
      sql: "SELECT * FROM weee_merch.pi_assortment_rule WHERE inventory_id ='41' AND storage_type ='F' and division ='05' and ethnicity ='korean' and assortment_role_name ='Testing'"
  - request:
      # 查询group/role
      params_data: {
        "filter[assortment_role_name]": 'Core',
        "filter[assortment_role]": 'Core-F-03-JP'
      }
      sql: "SELECT * FROM weee_merch.pi_assortment_rule WHERE assortment_role ='Core-F-03-JP' and assortment_role_name ='Core' ORDER BY inventory_id ASC, storage_type ASC"
#  - request:
#      # 查询所有
#      params_data: {
#        #        "filter[inventory_id]": ,
#        #        "filter[storage_type]": '',
#        #        "filter[division]": '',
#        #        "filter[ethnicity]": '',
#        #        "filter[assortment_role_name]": '',
#        #        "filter[assortment_role]": ''
#      }
#      sql: "SELECT * FROM weee_merch.pi_assortment_rule ORDER BY inventory_id ASC, storage_type ASC"
  - request:
      # 所有filter
      params_data: {
        "filter[inventory_id]": 25,
        "filter[storage_type]": 'F',
        "filter[division]": '03',
        "filter[ethnicity]": 'chinese',
        "filter[assortment_role_name]": 'NonCore',
        "filter[assortment_role]": 'NonCore-F-03-CN'
      }
      sql: "SELECT * FROM weee_merch.pi_assortment_rule WHERE inventory_id =25 AND storage_type ='F' and division ='03' and ethnicity ='chinese' and assortment_role ='NonCore-F-03-CN' and assortment_role_name ='NonCore' ORDER BY inventory_id ASC, storage_type ASC"
