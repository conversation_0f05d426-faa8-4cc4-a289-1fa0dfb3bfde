repack_list:
  - request:
      # 数据库查出来有 A，X
      params_data: { "filter[inventory_id]": 23, "filter[source_product_id]": 106001 }
  - request:
      # 数据库查出来只有 X
      params_data: { "filter[inventory_id]": 25, "filter[source_product_id]": 106002 }
  - request:
      # 数据库查出来只有 A
      params_data: { "filter[inventory_id]": 25, "filter[source_product_id]": 102516 }
  - request:
      # 数据库查出来有几个 X
      params_data: { "filter[inventory_id]": 25, "filter[source_product_id]": 105995 }
  - request:
      # 数据库查出来是 空
      params_data: { "filter[inventory_id]": 25, "filter[source_product_id]": 105988 }

update_repack_success:
  - request:
      # 更新仓库
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "105988",
        "source_product_quantity": "2",
        "target_product_quantity": "4",
        "repack_inventory_ids": [ "20","25","29" ] }
  - request:
      # 更新数量
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "105988",
        "source_product_quantity": "5",
        "target_product_quantity": "1",
        "repack_inventory_ids": [ "23","35"] }

update_repack_fail:
  - request:
      # 更新target product id
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "2611",
        "source_product_quantity": "2",
        "target_product_quantity": "4",
        "repack_inventory_ids": [ "20","25","29" ] }
      message: "There is an existing repack relationship.Please check."
  - request:
      # 更新仓库数量为0
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "105988",
        "source_product_quantity": "5",
        "target_product_quantity": "1",
        "repack_inventory_ids": [] }
      message: "The Affected Warehouse field is required."
  - request:
      # 更新quantity为小数
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "105988",
        "source_product_quantity": "5.6",
        "target_product_quantity": "-1",
        "repack_inventory_ids": [ "20","25","29" ] }
      message: "The Source SKU Quantity field must contain an integer.</br>The Target SKU Quantity field must contain a number greater than 0."
  - request:
      # 更新source product为is_purchase=N的产品
      json_data: {
        "id": "865",
        "source_product_id": "1528",
        "target_product_id": "105988",
        "source_product_quantity": "5",
        "target_product_quantity": "1",
        "repack_inventory_ids": [ "20","25","29" ] }
      message: "Source SKU[1528] should be a Purchasing SKU."
  - request:
      # 更新target product为is_purchase=Y的产品
      json_data: {
        "id": "865",
        "source_product_id": "106001",
        "target_product_id": "105999",
        "source_product_quantity": "5",
        "target_product_quantity": "1",
        "repack_inventory_ids": [ "20","25","29" ] }
      message: "Target SKU[105999] cannot be a Purchasing SKU."
  - request:
      # 更新source product = target product
      json_data: {
        "id": "865",
        "source_product_id": "105988",
        "target_product_id": "105988",
        "source_product_quantity": "5",
        "target_product_quantity": "1",
        "repack_inventory_ids": [ "20","25","29" ] }
      message: "Source SKU and target SKU cannot be the same"

delete_repack:
  - request:
      # 查询指定的repack
      params_data: { "filter[inventory_id]": 20, "filter[source_product_id]": 106000 }
      # 创建repack
      json_data: {
        "id": ,
        "source_product_id": "106000",
        "target_product_id": "106025",
        "source_product_quantity": "2",
        "target_product_quantity": "4",
        "repack_inventory_ids": [ "20" ] }