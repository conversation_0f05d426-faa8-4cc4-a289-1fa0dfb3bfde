query_additional_delivery_list:
  - request:
      # 查询结果不是空
      params_data: { "filter[vendor_id]": 1, "filter[inventory_id]": 37 }
  - request:
      # 查询结果为空
      params_data: { "filter[vendor_id]": 29, "filter[inventory_id]": 101 }

pr_add_additional_delivery_success:
  - request:
      # 没有第三方仓库关系，存储类型为多选
      json_data: {
        "vendor_id": 35,
        "inventory_id": 25,
        "storage_type": "N",
        "order_date": "2025-12-17",
        "eta_connection_date": ,
        "eta_final_date": "2025-12-19"
      }
  - request:
      # 有第三方仓库关系，有eta_connection_date
      json_data: {
        "vendor_id": 50,
        "inventory_id": 41,
        "storage_type": "N",
        "order_date": "2025-12-17",
        "eta_connection_date": "2025-12-18",
        "eta_final_date": "2025-12-19"
      }

pr_add_additional_delivery_fail:
  - request:
      # 已有关系，重复
      json_data: {
        "vendor_id": "1",
        "inventory_id": "37",
        "storage_type": "N",
        "order_date": "2025-12-17",
        "eta_connection_date": "2025-12-18",
        "eta_final_date": "2025-12-19"
      }

pr_update_additional_delivery_fail:
  - request:
      # 改日期
      json_data: {
        "id": "631",
        "vendor_id": 86,
        "inventory_id": 25,
        "storage_type": "N",
        "order_date": "2024-02-17",
        "eta_connection_date": ,
        "eta_final_date": "2024-02-18"
      }
      message: "Order Date cannot less than or equal to current date; "
  - request:
      # 改存储类型
      json_data: {
        "id": "631",
        "vendor_id": 86,
        "inventory_id": 25,
        "storage_type": "F",
        "order_date": "2025-12-17",
        "eta_connection_date": ,
        "eta_final_date": "2025-12-18"
      }
      message: "Please add inventory-vendor relationship."

pr_update_additional_delivery_success:
  - request:
      # 改日期
      json_data: {
        "id": "697",
        "vendor_id": 15,
        "inventory_id": 25,
        "storage_type": "N",
        "order_date": "2025-12-17",
        "eta_connection_date": ,
        "eta_final_date": "2025-12-18"
      }
  - request:
      # 改存储类型
      json_data: {
        "id": "680",
        "vendor_id": 441,
        "inventory_id": 25,
        "storage_type": "F",
        "order_date": "2025-12-17",
        "eta_connection_date": "2025-12-19",
        "eta_final_date": "2025-12-20"
      }











