
delivery_calendar_list:
  -
    name: 交货日历列表查询接口
    description: keywords-成功
    request:
      method: get
      url: "/central/merch/v1/config/pr/delivery/calendar"
      params_data: {"filter[keywords]": 1}
    expected_result:
      result: True
      object: "list"
  -
    name: 交货日历列表查询接口
    description: Vendor-成功
    request:
      method: get
      url: "/central/merch/v1/config/pr/delivery/calendar"
      params_data: {"filter[vendor_id][0]": 1}
    expected_result:
      result: True
      object: "list"
  -
    name: 交货日历列表查询接口
    description: Inventory-成功
    request:
      method: get
      url: "/central/merch/v1/config/pr/delivery/calendar"
      params_data: {"filter[inventory_id][0]": 25}
    expected_result:
      result: True
      object: "list"
  -
    name: 交货日历列表查询接口
    description: Storage Type-成功
    request:
      method: get
      url: "/central/merch/v1/config/pr/delivery/calendar"
      params_data: {"filter[storage_type][0]": "F"}
    expected_result:
      result: True
      object: "list"

add_delivery_calendar:
  -
    name: 新增交货日历接口
    description: 失败
    request:
      method: put
      url: "/central/merch/v1/config/pr/delivery/calendar"
      json_data: {
                      "vendor_id":"1",
                      "inventory_id":"25",
                      "storage_type":"F",
                      "order_weekday":"0",
                      "frequency":1,
                      "quantity":1,
                      "quantity_dollar":1,
                      "quantity_weight_in_lbs":1,
                      "quantity_case":1,
                      "quantity_pallet":1,
                      "lead_time_connection":2,
                      "eta_connection_weekday":2,
                      "eta_connection_addweek":0,
                      "lead_time":2,
                      "eta_final_weekday":2,
                      "eta_final_addweek":0
                  }
    expected_result:
      result: False
      message: "Duplicate vendor order weekday config for this warehouse, stop adding"

update_delivery_calendar:
  -
    request:
      method: put
      url: "/central/merch/v1/config/pr/delivery/calendar"
      json_data: {
                    "id":"8949",
                    "vendor_id":"8143",
                    "inventory_id":"41",
                    "storage_type":"N",
                    "order_weekday":"4",
                    "frequency":"1",
                    "quantity":"0",
                    "quantity_dollar":"0",
                    "quantity_weight_in_lbs":"0",
                    "quantity_case":"0",
                    "quantity_pallet":"0",
                    "lead_time_connection":"",
                    "eta_connection_weekday":"",
                    "eta_connection_addweek":"",
                    "lead_time":"3",
                    "eta_final_weekday":"0",
                    "eta_final_addweek":"1"
                }

delete_delivery_calendar:
  -
    name: 删除交货日历接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/config/pr"
      json_data: {
                      "ids":"5884",
                      "config_type":"pr_calendar"
                  }
    expected_result:
      result: True
      message: Null














