
pr_list:
  -
    name: 采购预约查询接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/detail"
      json_data:
      params_data: {"order_by[rule]": "desc", "order_by[column]": "id", "limit": 2}
      data:
    expected_result:
      object: "orders"
      result: True

delete_pr_list:
  -
    name: 采购预约列表删除接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/Pr/order/apiBatchDeleteOrder"
      json_data: {
                    "id":"174678"
                }
    expected_result:
      message: "Cannot delete since PR[174678] is already generated PO."
      result: False

get_pr_product_list:
  -
    name: 采购预约列表商品接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/Pr/order/getProductList"
      params_data: {
                    "vendor_id": 1
                }
    expected_result:
      result: True
      object: "product_list"

add_pr:
  -
    name: 采购预约新增接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/detail"
      json_data:
      params_data: {"order_by[rule]": "desc", "order_by[column]": "id", "limit": 2}
      data:
    expected_result:
      object: "orders"
      result: True












