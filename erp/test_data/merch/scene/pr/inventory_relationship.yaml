pr_inventory_relationship_list:
    - request:
        # 查询vendor*inventory*storage*supply
        params_data: {
          "filter[vendor_id][0]": 1,
          "filter[vendor_id][1]": 8,
          "filter[inventory_id][0]": 8,
          "filter[inventory_id][1]": 25,
          "filter[storage_type][0]": "F",
          "filter[storage_type][1]": "R",
          "filter[supply_mode]": 1
        }
        sql: "SELECT * FROM weee_merch.pr_config_inventory_relation WHERE vendor_id in (1,8) and inventory_id in (8,25) and supply_mode =1 and (storage_type like '%F%' or storage_type like '%R%') ORDER BY id DESC"
    - request:
        # 查询vendor*storage
        params_data: {
          "filter[vendor_id][0]": 1,
          "filter[storage_type][0]": "F",
          "limit": 20
        }
        sql: "SELECT * FROM weee_merch.pr_config_inventory_relation WHERE storage_type like '%F%' AND vendor_id =1 ORDER BY id DESC"
    - request:
        # 查询结果为空
        params_data: {
          "filter[vendor_id][0]": 11,
          "filter[inventory_id][0]": 35,
          "filter[storage_type][0]": "N",
          "filter[supply_mode]": 2
        }
        sql: "SELECT * FROM weee_merch.pr_config_inventory_relation WHERE vendor_id in (11) and inventory_id in (35) and supply_mode =2 and storage_type like '%N%' ORDER BY id DESC"


scene_add_direct_inventory_relationship:
    - request:
        # 查询vendor*inventory*storage*supply
        params_data: {
          "filter[vendor_id][0]": 24,
          "filter[inventory_id][0]": 14,
          "filter[supply_mode]": 1
        }


pr_add_inventory_relationship:
  - name: 新增接口
    description: 失败-Direct shipping库存关系
    request:
      method: put
      url: "/central/merch/v1/config/pr/relation/inventory"
      json_data: {
        "vendor_id": "1",
        "inventory_id": "8",
        "storage_type": "N",
        "supply_mode": "1"
      }
    expected_result:
      result: False

  - name: 新增接口
    description: 失败-Cross Dock库存关系
    request:
      method: put
      url: "/central/merch/v1/config/pr/relation/inventory"
      json_data: {
        "vendor_id": "1",
        "inventory_id": "8",
        "storage_type": "F",
        "supply_mode": "2",
        "connection_inventory_type": "1",
        "connection_inventory_id": "34"
      }
    expected_result:
      result: False

pr_update_inventory_relationship:
  - request:
      json_data: {
        "id": "1234",
        "vendor_id": "288",
        "inventory_id": "25",
        "storage_type": "R",
        "supply_mode": "1",
      }


pr_delete_inventory_relationship:
  - name: 删除接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/config/pr"
      json_data: {
        "ids": "4940",
        "config_type": "pr_inventory"
      }
    expected_result:
      result: True