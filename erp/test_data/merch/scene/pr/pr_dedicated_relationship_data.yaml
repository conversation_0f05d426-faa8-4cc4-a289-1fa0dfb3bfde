pr_dedicated_relationship_list:
  -
    name: 列表接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/config/pr/relation/dedicated"
      params_data: {"limit":"2"}
    expected_result:
      result: True
      object: "list"

add_dedicated_relationship:
  -
    name: 新增接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/config/pr/relation/dedicated"
      json_data: {
                        "vendor_id":"1",
                        "warehouse_id":"8",
                        "dedicated_user_id":"7660033",
                        "storage_type":"N",
                        "ethnicity":[
                            "chinese",
                            "korean"
                        ],
                        "timezone":"America/New_York"
                    }
    expected_result:
      result: False

update_dedicated_relationship:
  - request:
      json_data: {
                    "id":"2878",
                    "vendor_id":"1",
                    "warehouse_id":"8",
                    "dedicated_user_id":"7169981",
                    "storage_type":"R",
                    "ethnicity":[
                        "chinese",
                        "korean"
                    ],
                    "timezone":"America/Los_Angeles"
                }

delete_dedicated_relationship:
  -
    name: 删除接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/config/pr"
      json_data: {
                      "ids":"2559",
                      "config_type":"pr_dedicated"
                  }
    expected_result:
      result: True
