vendor_list:
  -
    name: 供应商管理列表查询接口
    description: 查询成功
    request:
      method: get
      url: "/central/vendor/v1/manager/vendor"
      params_data: {"pageNum":1,"pageSize":2}

vendor_info:
  -
    name: 供应商详情接口
    description: 查询成功
    request:
      method: get
      url: "/central/vendor/v1/manager/vendor/detail/{vendor_id}"
      params_data: 8944

vendor_info_update:
  -
    name: 供应商详情保存更新接口
    request:
      method: post
      url: "/central/vendor/v1/manager/vendor/save"
      json_data: {
                "id": "7366",
                "source": "vendor",
                "type": "G",
                "sales_org_id": "2",
                "title": "<PERSON> Contaniers-CA-Test06",
                "legal_name": "<PERSON> Contaniers-CA-Test06",
                "vendor_location": "SF",
                "payment_term": "30",
                "actual_term": "30",
                "billing_country": "2",
                "billing_street": "9591 Irvine Center Dr",
                "billing_city": "Irvine",
                "billing_state": "41",
                "billing_zipcode": "92618",
                "billing_address": "9591 Irvine Center Dr, Irvine, California, 92618, United States",
                "pickup_address": "9591 Irvine Center Dr, Irvine, California, 92618, United States",
                "phone_number": "9497837084",
                "vendor_group": null,
                "image_url": "https://img06.test.weeecdn.com/merch/image/801/405/69323449F8E69EE6.png",
                "description": "cc",
                "remark": "",
                "max_inventory_volume": "",
                "ethnicity": [

                ],
                "is_restaurant": "N",
                "owner_id": 0,
                "return_country": null,
                "return_street": null,
                "return_city": null,
                "return_state": null,
                "return_zipcode": null,
                "return_address": null,
                "brand_represented": null,
                "restaurant_logo_url": null,
                "restaurant_short_desc": null,
                "restaurant_status": null,
                "restaurant_user_id": null,
                "restaurant_pos": "0",
                "inventory": [

                ],
                "emails": [
                    {
                        "id": "28996",
                        "vendor_id": "7366",
                        "email": "<EMAIL>",
                        "name": "bao06",
                        "rec_create_time": "2024-07-25 06:57:27",
                        "is_remittance": "1",
                        "phone_number": "",
                        "status": "A",
                        "invite_date": "2023-01-19 23:14:36",
                        "creator_name": "轻寻-测试"
                    }
                ]
            }

vendor_finance_update:
  -
    name: 供应商详情页财务更新商家信息接口
    description: 更新成功
    request:
      method: post
      url: "/central/vendor/v1/manager/vendor/finance_update"
      json_data: {
                    "id": "8944",
                    "legal_name": "Test Vendor 0308",
                    "primary_vender_id": "8944",
                    "billing_country": "1",
                    "billing_street": "Wuning Road",
                    "billing_city": "Shanghai",
                    "billing_state": "11",
                    "billing_zipcode": "200123",
                    "quickbook_id": "852456789741"
                }

vendor_finance_approve:
  -
    name: 财务审核供应商接口
    request:
      method: post
      url: "/central/vendor/v1/manager/vendor/approve"
      json_data: {"id": "8361"}

vendor_inactive:
  -
    name: 供应商inactive
    request:
      method: post
      url: "/central/vendor/v1/manager/vendor/inActiveVendor"
      json_data: {"id": "7613"}









