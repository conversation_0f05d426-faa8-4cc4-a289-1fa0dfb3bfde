vendor_review_list:
  -
    name: 供应商审核列表查询接口
    description: 查询成功
    request:
      method: get
      url: "/central/merch/v1/vendor/review"
      params_data: {"pageNum":1,"pageSize":2}

vendor_review_info:
  -
    name: 供应商详情接口
    description: 查询成功
    request:
      method: get
      url: "/central/vendor/v1/manager/vendor/detail/{vendor_id}"
      params_data: 9079

vendor_review_info_update:
  -
    name: 供应商review详情更新接口
    request:
      method: post
      url: "/central/merch/v1/vendor/review/saveVendor"
      json_data: {
                    "id": "8749",
                    "type": "G",
                    "sales_org_id": "1",
                    "title": "test--2024-01-25 15:31:47.087687",
                    "legal_name": "Vendor Legal Name (DBA)",
                    "image_url": "",
                    "description": "",
                    "reason": null,
                    "ethnicity": [],
                    "owner_id": "7673902",
                    "secondary_owner_id": "7662189",
                    "vendor_type": ["18"],
                    "shellfish_shipper": null,
                    "fei_num": null,
                    "usda_establish": null,
                    "payment_term": "30",
                    "payment_method": "check",
                    "billing_country": "1",
                    "billing_street": "Billing street",
                    "billing_city": "Billing city",
                    "billing_state": "3",
                    "billing_zipcode": "400000",
                    "billing_address": "China Beijing Billing city Billing street 400000",
                    "phone_number": "400001",
                    "quickbook_id": "",
                    "primary_vender_id": "8749",
                    "signer_name": "1334",
                    "signer_title": "54323",
                    "signer_email": "<EMAIL>",
                    "return_country": "1",
                    "return_street": "Billing street",
                    "return_city": "Billing city",
                    "return_state": "3",
                    "return_zipcode": "400000",
                    "return_address": "China Beijing Billing city Billing street 400000",
                    "brand_represented": null,
                    "emailsModelList": [
                        {
                            "id": "25269",
                            "vendor_id": "8749",
                            "email": "<EMAIL>",
                            "name": "huan",
                            "rec_create_time": "2024-03-07 08:43:18",
                            "is_remittance": "1",
                            "phone_number": ""
                        }
                    ],
                    "ach_info": {},
                    "source": "vendor"
                }


