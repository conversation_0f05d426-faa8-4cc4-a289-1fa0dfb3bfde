product_bundle_config_list:
  -
    description: 盲盒商品配置列表查询
    request:
      url: "/central/merch/v1/pi/pb/getBundleList"
      params_data: {"filter[product_id]": 106016}
  -
    description: 盲盒商品配置列表查询
    request:
      url: "/central/merch/v1/pi/pb/getBundleList"
      params_data: {"filter[keyword]": 43}

  -
    description: 盲盒商品配置列表查询
    request:
      url: "/central/merch/v1/pi/pb/getBundleList"
      params_data: {"filter[status]": "A"}

product_bundle_config_info:
  -
    description: 盲盒商品配置详情查询
    request:
      url: "/central/merch/v1/pi/pb/getPb"
      params_data: {"id": 34}

product_bundle_config_detail:
  -
    description: 盲盒商品数据详情查询
    request:
      url: "/central/merch/v1/fns/apiMerch/getInvProductDetail"
      params_data: {"productId": "105920"}

create_product_bundle_config:
  -
    description: 新建盲盒配置
    request:
      url: "/central/merch/v1/pi/pb/apiUpdatePb"
      data: {
              "product_id": "105920",
              "items": [
                  {
                      "product_id": 105517,
                      "qty": 1,
                      "pack_qty": 1
                  }
              ],
              "comment": "QA Test"
          }







