
product_list:
  - name: 商品列表查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/pi/product"
      json_data:
      params_data: {"offset": 0, "limit": 20}
    expected_result:
      object: "list"
      result: True

product_detail:
  - name: 商品详情查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/pi/product/detail/{sku_id}/edit"
      json_data:
      params_data:
      data: 102914
    expected_result:
      object: "basic_info"
      result: True

edit_product_detail:
  - name: 编辑商品详情接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/detail"
      json_data: {
                  "title_en":"test123 merch hhh",
                  "short_title_en":"*Description Material En",
                  "title":"test0727200-301",
                  "short_title":"*Description Material En",
                  "weee_buyer_id":"7662189",
                  "ethnicity":"chinese",
                  "ethnic_tags":[

                  ],
                  "storage_temperate_zone":"R2",
                  "division_num":"04",
                  "catalogue_num":"1202",
                  "sub_catalogue_num":"",
                  "capacity":null,
                  "sub_title":"",
                  "keyword":"",
                  "ethnic_title":[

                  ],
                  "brand_key":"iJc638bE",
                  "product_area":"5",
                  "unit":"L",
                  "unit_min":"11.00",
                  "unit_max":null,
                  "chemical_substances":[
                      "59",
                      "55",
                      "55",
                      "57"
                  ],
                  "tags":[

                  ],
                  "is_gift_product":false,
                  "is_tradein_forbid":false,
                  "season_code_start_week":"",
                  "season_code_end_week":"",
                  "sample":false,
                  "crossdock_sku":false,
                  "imported_sku":false,
                  "is_purchase_product":false,
                  "volume":"22",
                  "length":"",
                  "width":"",
                  "height":"",
                  "weight":"",
                  "is_delicate":false,
                  "is_fragile_product":false,
                  "check_shelf_life":true,
                  "receiving_date_type":"expiration_date",
                  "shelf_life":"365",
                  "receiving_shelf_life_limit":"183",
                  "reminder_shelf_life_limit":"91",
                  "outbound_shelf_life_limit":"37",
                  "product_feature":false,
                  "multi_lang":[
                      {
                          "lang":"en",
                          "title":"test123 merch hhh",
                          "sub_title":"",
                          "description_html":"<p>22++++test0727200-300 <span style=\"background-color:#ffffff\">Description Material En</span></p>"
                      },
                      {
                          "lang":"zh",
                          "title":"test123",
                          "description_html":"<p>5555</p>"
                      },
                      {
                          "lang":"zht",
                          "title":"test0727200-300",
                          "sub_title":"",
                          "description_html":"<p> </p>"
                      },
                      {
                          "lang":"ko",
                          "title":"test0727200-300",
                          "sub_title":"",
                          "description_html":"<p>22</p>"
                      },
                      {
                          "lang":"ja",
                          "title":"test0727200-300",
                          "sub_title":"",
                          "description_html":"<p>22</p>"
                      },
                      {
                          "lang":"vi",
                          "title":"test0727200-300",
                          "sub_title":"",
                          "description_html":"<p>22</p>"
                      }
                  ],
                  "photos":[
                      {
                          "type_id":1,
                          "photos":[

                          ]
                      },
                      {
                          "type_id":2,
                          "photos":[

                          ]
                      },
                      {
                          "type_id":3,
                          "photos":[

                          ]
                      },
                      {
                          "type_id":99,
                          "photos":[

                          ]
                      },
                      {
                          "type_id":100,
                          "photos":[
                              "https://img06.test.weeecdn.com/product/image/221/905/3BBFC0232B453610.jpeg"
                          ]
                      }
                  ],
                  "product_id":"102902"
              }
      params_data:
      data:
    expected_result:
      object: "product_id"
      result: True

add_product:
  - name: 商品详情查询接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/detail"
      json_data: {
                    "title_en":"Test Product 001",
                    "title":"测试商品001",
                    "short_title":"Test  001",
                    "image_url":"https://img06.test.weeecdn.com/product/image/841/769/2497AB74194D7B07.jpeg",
                    "short_title_en":"Test  001",
                    "weee_buyer_id":"7169981",
                    "ethnicity":"chinese",
                    "ethnic_tags":["chinese_essential"],
                    "storage_temperate_zone":"R1",
                    "division_num":"06",
                    "catalogue_num":"0102",
                    "sub_catalogue_num":"010201",
                    "UPC_code":"",
                    "brand_key":"BwhpDddW",
                    "product_area":"1",
                    "keyword":"",
                    "photo_urls":"",
                    "unit":"X",
                    "unit_min":1,
                    "unit_max":"",
                    "chemical_substances":"",
                    "tags":"",
                    "capacity":"",
                    "sub_title":"",
                    "ethnic_title":"",
                    "description_en":"<p>Description en</p>",
                    "is_gift_product":"",
                    "is_tradein_forbid":"",
                    "vender_id":"1",
                    "purchase_price":10,
                    "price":20,
                    "season_code_start_week":"",
                    "season_code_end_week":"",
                    "sample":"",
                    "crossdock_sku":"",
                    "imported_sku":"",
                    "is_purchase_product":"",
                    "volume":50,
                    "length":2,
                    "width":1,
                    "height":5,
                    "weight":"",
                    "julian_code_format_id":"",
                    "is_delicate":"",
                    "is_fragile_product":"",
                    "check_shelf_life":"N"
                }
      params_data:
      data:
    expected_result:
      object: "product_id"
      result: True

inactive_product:
  - name: inactive商品接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/pi/product/detail/inactive/{sku_id}"
      json_data:
      params_data:
      data: 102860
    expected_result:
      object:
      result: True

active_product:
  - name: active商品接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/pi/product/detail/active/{sku_id}"
      json_data:
      params_data:
      data: 102914
    expected_result:
      message: "The input status is not modified"
      result: False

put_on_take_off_product:
  - name: 上架下架商品接口
    description: 下架成功
    request:
      method: put
      url: "/central/merch/v1/pi/product/detail/productWebSite/status/{sku_id}"
      json_data: { "operate":"down"}
      params_data:
      data: 102914
    expected_result:
      object:
      result: True

  - name: 上架下架商品接口
    description: 上架成功
    request:
      method: put
      url: "/central/merch/v1/pi/product/detail/productWebSite/status/{sku_id}"
      json_data: {"operate":"up"}
      params_data:
      data: 102914
    expected_result:
      object:
      result: True

special_product_detail:
  - name: 包材商品详情接口
    description: 查询成功
    request:
      method: get
      url: "/central/merch/v1/pi/product/special/{sku_id}/edit"
      json_data:
      params_data:
      data: 102857
    expected_result:
      object: "vendor_id"
      result: True

edit_special_product:
  - name: 编辑包材商品接口
    description: 编辑成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/special"
      json_data: {
                    "product_id":"102857",
                    "vendor_product_title":"test",
                    "short_title_en":"test",
                    "title_en":"test",
                    "inventory_image_url":"https://img06.test.weeecdn.com/product/image/890/299/760D324B029C0020.jpeg",
                    "vendor_id":"7115",
                    "storage_temperate_zone":"N1",
                    "division_num":"09",
                    "catalogue_num":"9801",
                    "purchase_price":"22.00",
                    "purchase_unit":"unit",
                    "quantity_per_unit":"1.00",
                    "weee_buyer_id":"10060559",
                    "volume":"1"
                  }
      params_data:
      data:
    expected_result:
      object: "product_id"
      result: True

add_special_product:
  - name: 新增包材商品接口
    description: 新增成功
    request:
      method: post
      url: "/central/merch/v1/pi/product/special"
      json_data: {
                    "vendor_product_title":"Test pack 002",
                    "short_title_en":"Test pack 002",
                    "title_en":"Test pack 002",
                    "inventory_image_url":"https://img06.test.weeecdn.com/product/image/327/331/56960E6F1FD4259E.jpeg",
                    "vendor_id":"7115",
                    "storage_temperate_zone":"N1",
                    "division_num":"09",
                    "catalogue_num":"9801",
                    "purchase_price":10,
                    "purchase_unit":"case",
                    "quantity_per_unit":20,
                    "weee_buyer_id":"7169981",
                    "volume":10,
                    "length":2,
                    "width":5,
                    "height":1
                  }
      params_data:
      data:
    expected_result:
      object: "product_id"
      result: True



