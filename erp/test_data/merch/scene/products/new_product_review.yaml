
new_product_review_list:
  - name: 新品审核列表查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/list"
      json_data:
      params_data: {"limit": 1, "order_by[column]": "id", "order_by[rule]": "desc"}
    expected_result:
      result: True
      object: "list"

new_product_review_detail:
  - name: 新品审核详情查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/detail/{audit_id}/edit"
      json_data:
      params_data:
      data: 20358
    expected_result:
      result: True
      object: "approval_info"

edit_new_product_review:
  - name: 编辑新品审核详情接口
    description: 成功
    request:
      method: post
      url: "/central/merch/v1/pi/newProductReview/detail"
      json_data: {
                    "title_en":"test0907",
                    "title":"",
                    "sub_title":"",
                    "image_url":"https://img06.test.weeecdn.com/product/image/685/093/60D6898B5A8B18D6.jpeg",
                    "product_label_image":"",
                    "weee_buyer_id":"",
                    "product_area":"1",
                    "ethnicity":"chinese",
                    "storage_temperate_zone":"N1",
                    "division_num":"04",
                    "catalogue_num":"1202",
                    "sub_catalogue_num":"",
                    "price":"11.00",
                    "competitive_price":"",
                    "competing_companies":"",
                    "competing_websites":"",
                    "unit":"G",
                    "unit_min":"1.00",
                    "unit_max":"",
                    "volume":"1",
                    "length":"",
                    "width":"",
                    "height":"",
                    "weight":"",
                    "capacity":"",
                    "julian_code_format_id":"0",
                    "UPC_code":"",
                    "keyword":"",
                    "brand_key":"iJc638bE",
                    "vender_id":"1",
                    "purchase_price":"5.00",
                    "purchase_unit":"unit",
                    "quantity_per_unit":"1.00",
                    "vendor_product_title":"test0907",
                    "vendor_SKU_code":"",
                    "short_title":"test0907",
                    "short_title_en":"test0907",
                    "check_shelf_life":"Y",
                    "receiving_date_type":"production_date",
                    "shelf_life":"120",
                    "receiving_shelf_life_limit":"60",
                    "reminder_shelf_life_limit":"30",
                    "outbound_shelf_life_limit":"12",
                    "sample":"N",
                    "is_delicate":"N",
                    "is_fragile_product":"N",
                    "imported_sku":"N",
                    "crossdock_sku":"N",
                    "sales_forecast":[

                    ],
                    "inventory_forecast":[
                        {
                            "active_sku_count":"N/A",
                            "expected_vendor_ready_date":"2023-09-08",
                            "inventory_id":"7",
                            "max_sku_count":"N/A",
                            "purchase_sku_count":"N/A",
                            "quantity":"12",
                            "supply_chain_analyst_id":"7396210"
                        }
                    ],
                    "related_sku":[
                        {
                            "is_replacement":"Y",
                            "related_product_id":"102"
                        }
                    ],
                    "special_reason":"123",
                    "comment":"",
                    "expected_po_date":"",
                    "need_detail_description":"N",
                    "eta_sku_live_date":"",
                    "id":"20374",
                    "current_status":"D",
                    "description_en":"<p>123</p>",
                    "description_html":"<p></p>",
                    "detail_description":"",
                    "image_urls":[
                        "https://img06.test.weeecdn.com/product/image/685/093/60D6898B5A8B18D6.jpeg"
                    ],
                    "property":[
                        "55",
                        "57"
                    ]
                }
      params_data:
    expected_result:
      result: True
      object: "id"

new_product_review_owner:
  - name: 获取新品审核可指定的owner查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/owner/{audit_id}"
      json_data:
      params_data:
      data: 20358
    expected_result:
      result: True
      object: "owner_user_list"

assign_new_product_review_owner:
  - name: 指定新品审核的owner接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/pi/newProductReview/owner"
      json_data: {"assignee_user_id":"9630332", "id":"20358"}
      params_data:
    expected_result:
      result: False
      message: "The data has been reviewed and cannot be modified"

approve_new_product_review:
  - name: 审批新商品接口
    description: 失败
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/approve"
      json_data:
      params_data: {"id":"20271"}
    expected_result:
      result: False
      message: "You do not have permission to approve"


batch_approve_new_product_review:
  - name: 批量审批新商品接口
    description: 失败-相同状态无权限的新品
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/bulkOperation"
      json_data:
      params_data: {"id":"20348, 20349", "type": "approve"}
    expected_result:
      result: False
      message: "Please select new products in the same state for batch operation."

  - name: 批量审批新商品接口
    description: 失败-不同状态的新品
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/bulkOperation"
      json_data:
      params_data: {"id":"20359, 20362", "type": "approve"}
    expected_result:
      result: False
      message: "This status is not available for batch operations"

  - name: 批量审批新商品接口
    description: delete失败-不同状态的新品
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/bulkOperation"
      json_data:
      params_data: {"id":"20358, 20362", "type": "delete"}
    expected_result:
      result: False
      message: "Please select new products in the same state for batch operation."

  - name: 批量审批新商品接口
    description: reject失败-不同状态的新品
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/bulkOperation"
      json_data:
      params_data: {"id":"20358, 20362", "type": "reject"}
    expected_result:
      result: False
      message: "Please select new products in the same state for batch operation."

delete_new_product_review:
  - name: 批量审批新商品接口
    description: 失败-不同状态的新品
    request:
      method: delete
      url: "/central/merch/v1/pi/newProductReview/detail/{audit_id}"
      json_data:
      params_data:
      data: 20358
    expected_result:
      result: False
      message: "Only draft status can be deleted"

create_sku_product_review:
  - name: 新品创建SKU
    description: 失败
    request:
      method: get
      url: "/central/merch/v1/pi/newProductReview/createProduct"
      json_data:
      params_data: {"id": "20090"}
      data:
    expected_result:
      result: False
      message: "Status cannot be changed"

