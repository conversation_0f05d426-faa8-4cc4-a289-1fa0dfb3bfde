
product_crv_list:
  - name: 商品列表查询接口
    request:
      method: get
      url: "/central/merch/v1/pi/pb/getCrvList"
      params_data: {"offset": 0, "limit": 10}


update_product_crv_conf:
  - name: 更新商品crv配置
    request:
      method: put
      url: "/central/merch/v1/pi/pb/apiUpdateProductCrv"
      json_data: {
          "id": "414",
          "price": "0.05",
          "pack_qty": 0,
          "source_product_id": "100566",
          "state_id": "41",
          "target_product_id": "2071547"
      }

