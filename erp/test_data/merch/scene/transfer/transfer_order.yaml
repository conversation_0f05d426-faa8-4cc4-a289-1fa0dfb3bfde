
transfer_order_list:
  -
    name: 调拨单列表查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/internal/internalOrder"
      json_data:
      params_data: {"order_by[rule]": "desc", "order_by[column]": "id", "limit": 2}
    expected_result:
      object: "orders"
      result: True

transfer_order_detail:
  - request:
      params_data: {"id": "3262"}


transfer_order_itinerary:
  -
    name: 调拨单线路查询接口
    description: 成功-非DC仓
    request:
      method: get
      url: "/scm/lhms/api/v1/itinerary/reference"
      json_data:
      params_data: {
                      "originReferenceId":25,
                      "destinationReferenceId":20
                    }
    expected_result:
      message_id: "10000"
      result: True

transfer_order_inventory_list:
  -
    name: 调拨单可用仓库查询接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/internal/internalOrder/getInternalOrderInventoryList"
      json_data:
      params_data:
    expected_result:
      object: "inventory_list"
      result: True

create_transfer_order:
  -
    name: 创建调拨单接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiOrder"
      json_data: {
                    "physical_inventory_id":"29",
                    "physical_inbound_inventory_id":"20",
                    "email":"<EMAIL>",
                    "delivery_date":"2023-09-20",
                    "eta_date":"2023-09-21",
                    "comment":"QA 测试数据"
                  }
      params_data:
    expected_result:
      object: "id"
      result: True

update_transfer_order:
  -
    name: 跟新调拨单接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiOrder"
      json_data: {
                    "id":"1968",
                    "physical_inventory_id":"25",
                    "physical_inbound_inventory_id":"29",
                    "email":"<EMAIL>",
                    "delivery_date":"2023-09-22",
                    "eta_date":"2023-09-22",
                    "comment":"测试2"
                }
      params_data:
    expected_result:
      object: "id"
      result: True

external_create_transfer_order:
  -
    request:
      json_data: {
                    "order": {
                      "physical_inventory_id": "29",
                      "physical_inbound_inventory_id": "20",
                      "delivery_date": "2023-09-01",
                      "eta_date": "2023-09-01",
                      "email": "<EMAIL>",
                      "source": "S"
                    },
                    "product": [
                      {
                        "product_id": "42",
                        "quantity": "20",
                        "quantity_per_unit": ""
                      }
                    ]
                  }

cancel_transfer_order:
  - request:
      json_data: {
                    "comment":"QA Cancel Reason",
                    "id":"3279"
                 }

transfer_order_add_product:
  -
    name: 调拨单添加商品接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiOrderLine"
      json_data: {
                      "order_id":"2378",
                      "product":[
                          {
                              "itemKey":"01691552358402",
                              "product_id":"42",
                              "quantity":10,
                              "quantity_per_unit":"20.00"
                          }
                      ]
                  }
      params_data:
    expected_result:
      success: 1
      result: True


attachment_detail:
  -
    name: 调拨单附件详情接口
    description: 成功
    request:
      method: get
      url: "/central/merch/v1/internal/internalOrder/getAttachment"
      json_data:
      params_data: {"id": 10}
    expected_result:
      success: True
      result: True

add_attachment:
  - request:
      json_data: {
                    "invoice_internal_id":"1968",
                    "file":"C:\\fakepath\\2022112501.pdf",
                    "comment":"测试",
                    "file_path":"https://weee-test-documents.s3.us-east-2.amazonaws.com/merch/internalOrder/618/328/1EE798FA49C7A667.pdf",
                    "s3_file_key":"merch/internalOrder/618/328/1EE798FA49C7A667.pdf",
                    "s3_file_url":"https://weee-test-documents.s3.us-east-2.amazonaws.com/merch/internalOrder/618/328/1EE798FA49C7A667.pdf",
                    "file_name":"2022112501.pdf"
                  }

delete_attachment:
  - request:
      params_data: {"id": "1968"}

to_send_email:
  -
    name: 调拨单发送邮件接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiSendInvoiceToEmail"
      json_data: {
                    "id": 2054
                 }
      params_data:
    expected_result:
      success: 1
      result: True

transfer_order_change_quantity:
  -
    name: 调拨单修改商品数量接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiChangeQuantity"
      json_data: {
                    "quantity":2,
                    "product_id":"44218",
                    "id":"3016"
                  }
      params_data:
    expected_result:
      msg: "Only can modify quantity when the order is created"
      result: True

transfer_order_change_per_unit:
  -
    name: 调拨单修改商品箱规接口
    description: 成功
    request:
      method: put
      url: "/central/merch/v1/internal/internalOrder/apiChangeQuantityPerUnit"
      json_data: {
                    "id":"2074",
                    "product_id":"42",
                    "quantity_per_unit":4
                }
      params_data:
    expected_result:
      success: 1
      result: True

transfer_order_submit:
  -
    name: 调拨单提交下发到WMS接口
    description: 失败
    request:
      method: post
      url: "/central/merch/v1/internal/internalOrder/submitOrder"
      json_data: {
                    "id": 2054
                  }
      params_data:
    expected_result:
      success: 0
      result: True

transfer_order_mark_out:
  - request:
      json_data: {
                    "id": 3066
                  }






