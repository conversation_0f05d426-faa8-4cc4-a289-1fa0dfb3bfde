work_order_list:
  -
    description: work order列表查询-target sku
    request:
      url: "/central/merch/v1/internal/wo/getWorkOrderList"
      params_data: {"filter[target_product_id]": 10001}
  -
    description: work order列表查询-keyword
    request:
      url: "/central/merch/v1/internal/wo/getWorkOrderList"
      params_data: {"filter[keyword]": 124}
  -
    description: work order列表查询-status
    request:
      url: "/central/merch/v1/internal/wo/getWorkOrderList"
      params_data: {"filter[status]": "P"}
  -
    description: work order列表查询-warehouse
    request:
      url: "/central/merch/v1/internal/wo/getWorkOrderList"
      params_data: {"filter[inventory_id]": 25}
  -
    description: work order列表查询-configration
    request:
      url: "/central/merch/v1/internal/wo/getWorkOrderList"
      params_data: {"filter[bundle_id]": 53}


work_order_info:
  -
    description: work order详情查询
    request:
      url: "/central/merch/v1/internal/wo/getWo?id=108"
      params_data: 108

create_work_order:
  -
    description: 创建work order
    request:
      url: "/central/merch/v1/internal/wo/apiUpdateWo"
      params_data: {
          "inventory_id": "25",
          "bundle_id": "53",
          "qty": "1",
      }

