new_user_coupon_whitelist_create:
 - name: 新增新用户优惠券白名单
   description: 新增成功
   request:
     method: post
     url: "/central/cs/newUserCoupon<PERSON><PERSON>elist/create"
     json_data: {"userId":"5955706","comment":"测试"}
   expected_result:
     message_id: "10000"
     result: true

query_list:
 - name: 查询列表
   description: 查询新用户优惠券白名单列表成功
   request:
     method: post
     url: "/central/cs/newUserCouponWhitelist/queryList"
     json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"status":"","applyTimeRange":["2024-01-31","2025-01-28"],"filter":{}}
   expected_result:
     message_id: "10000"
     result: true