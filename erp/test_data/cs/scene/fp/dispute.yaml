cs_dispute:
 - name: 新增dispute
   description: 新增dispute成功
   request:
     method: post
     url: "/central/cs/dispute"
     json_data: {"paymentChannel":"Braintree","disputeId":"7676","disputeType":"Chargeback","transactionId":"2s7zp8v4","disputeReason":"Fraud","disputeStatus":"Open","disputeDate":"2025-11-20T03:39:30.933Z","dueDate":"2025-11-28T03:46:40.418Z","disputeAmount":25.9,"evidenceStatus":"Uploaded","requireNewCase":true}
   expected_result:
     message_id: "10000"
     result: true


query_list:
 - name: 查询列表
   description: 查询dispute列表成功
   request:
     method: post
     url: "/central/cs/dispute/queryList"
     json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"disputeId":"","orderId":"42640605","assignee":"ALL","filter":{},"pageNo":1}
   expected_result:
     message_id: "10000"
     result: true

create_WatchList_FromDispute:
 - name: 批量加黑名单
   description: 批量加黑名单成功
   request:
     method: post
     url: "/central/cs/dispute/createWatchListFromDispute"
     json_data: {"disputeList":[{"id":,"orderId":"42640605"}],"memo":"测试","reason":["Fraud Dispute","Confirm Stolen Financial(Normal)"]}
   expected_result:
     message_id: "10000"
     result: true

query_list1:
 - name: 查询列表
   description: 查询dispute列表成功
   request:
     method: post
     url: "/central/cs/dispute/queryList"
     json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"disputeId":"","orderId":"42640605","assignee":"ALL","filter":{},"pageNo":1}
   expected_result:
     message_id: "10000"
     result: true