add_suspicious_order:
  - name: 新增可疑订单
    description: 新增成功
    request:
      method: post
      url: "/central/cs/suspiciousOrder/addSuspectedOrder"
      json_data: {"orderId":"42640674","type":"2","detectionReason":"Relating to suspicious orders"}
    expected_result:
      message_id: "10000"
      result: true


select_suspicious_order_list:
  - name: 查询可疑订单
    description: 查询成功
    request:
      method: post
      url: "/central/cs/suspiciousOrder/queryList"
      json_data: {"pageNum":1,"sortMap":{},"pageSize":10,"createTimeRange":[1738482269000,1738746000000],"isSolve":"ALL","salesOrgId":"ALL","orderStatus":"ALL","status":"ALL","orderId":"42640674","type":"0","orderType":"ALL","operatorId":"ALL","filter":{}}
    expected_result:
      message_id: "10000"
      result: true

assign_suspicious_order:
  - name: 可疑订单指派给
    description: 指派成功
    request:
      method: post
      url: "/central/cs/suspiciousOrder/updateOperatorId"
      json_data: {"userId":5955706,"suspiciousIdList":[35557]}
    expected_result:
      message_id: "10000"
      result: true

assign_to_me_suspicious_order:
  - name: 可疑订单指派给某人
    description: 指派成功
    request:
      method: post
      url: "/central/cs/suspiciousOrder/updateOperatorId"
      json_data: {"userId":"10060559","suspiciousIdList":[35557]}
    expected_result:
      message_id: "10000"
      result: true