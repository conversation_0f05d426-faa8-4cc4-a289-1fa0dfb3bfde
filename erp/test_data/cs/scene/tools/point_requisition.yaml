
point_requisition_list:
  -
    name: 积分申请列表查询
    description: 查询成功
    request:
      method: post
      url: "/central/cs/pointsRequisition/queryPointsRequisitionPage"
      json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"status":"0","filter":{}}
    expected_result:
      message_id: "10000"
      result: True

add_point_requisition:
  -
    name: 新增积分申请
    description: 新增成功
    request:
      method: post
      url: "/central/cs/pointsRequisition/addPointsRequisition"
      json_data: {
                      "category":"Other",
                      "reasonArray":["Compensation", "Other"],
                      "note":"测试数据",
                      "pointsComment":"测试数据",
                      "approverId":"7169981",
                      "channel":"1",
                      "credits":"",
                      "userIds":"",
                      "creditsUserList":[
                          {
                              "userId":7164848,
                              "credits":10,
                              "userStr":"7164848 - weee.7164848"
                          }]
                  }
    expected_result:
      message_id: "10000"
      result: True

edit_point_requisition:
  -
    name: 编辑积分申请
    description: 编辑成功
    request:
      method: post
      url: "/central/cs/pointsRequisition/editPointsRequisition"
      json_data: {
                    "category":"Other",
                    "pointsComment":"测试Remark",
                    "approverId":"7169981",
                    "reasonArray":["Compensation","Other"],
                    "channel":"1",
                    "caseNumber":"",
                    "note":"测试Note",
                    "isOwner":1,
                    "isApprover":1,
                    "id":"1884",
                    "creditsUserList":[
                        {
                            "credits":"10",
                            "userId":"7164848",
                            "userStr":"7164848 - weee.7164848"
                        }]
                    }
    expected_result:
      message_id: "10000"
      result: True

approve_point_requisition:
  -
    name: 审批积分申请
    description: 审批失败
    request:
      method: post
      url: "/central/cs/pointsRequisition/approvePointsRequisition"
      json_data: {"id": "1936"}
    expected_result:
      message_id: "10000"
      result: False

batch_approve_point_requisition:
  -
    name: 批量审批积分申请
    description: 审批失败
    request:
      method: post
      url: "/central/cs/pointsRequisition/batchApprovePointsRequisition"
      json_data: {"idList": ["1936","1935"]}
    expected_result:
      message_id: "10000"
      result: False


void_point_requisition:
  -
    name: void积分申请
    description: 审批失败
    request:
      method: post
      url: "/central/cs/pointsRequisition/voidPointsRequisition"
      json_data: {"id": "1247"}
    expected_result:
      message_id: "10000"
      result: False

decline_point_requisition:
  -
    name: decline积分申请
    description: 审批失败
    request:
      method: post
      url: "/central/cs/pointsRequisition/declinePointsRequisition"
      json_data: {"id": "1247","memo":"测试Decline"}
    expected_result:
      message_id: "10000"
      result: False

batch_decline_point_requisition:
  -
    name: 批量审批积分申请
    description: 审批失败
    request:
      method: post
      url: "/central/cs/pointsRequisition/batchDeclinePointsRequisition"
      json_data: {"idList": ["1936","1935"], "memo":"测试Decline"}
    expected_result:
      message_id: "10000"
      result: "[Applying]"


