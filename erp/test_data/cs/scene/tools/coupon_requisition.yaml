
coupon_requisition_list:
  -
    name: 优惠券申请列表查询
    description: 查询成功
    request:
      method: post
      url: "/central/cs/couponRequisition/queryPage"
      json_data: {"pageNum": 1, "pageSize": "20", "userIds" : "7164848"}

coupon_requisition_info:
  - name: 优惠券申请详情查询
    description: 查询成功
    request:
      method: get
      url: "/central/cs/couponRequisition/getDetail/17002"
      data: 17002

add_coupon_requisition:
  -
    name: 新增优惠券申请
    description: 新增成功
    request:
      method: post
      url: "/central/cs/couponRequisition/addCouponRequisition"
      json_data: {
                    "userIds": "7164848",
                }

approve_coupon_requisition:
  -
    name: approve优惠券申请
    description: approve失败
    request:
      method: post
      url: "/central/cs/couponRequisitionManage/approve/17026"
      data: 17026

batch_approve_coupon_requisition:
  -
    name: batch approve优惠券申请
    description: approve失败
    request:
      method: post
      url: "/central/cs/couponRequisition/batchApproveCouponRequisition"
      json_data: {"idList":["11136","11137"]}
    expected_result:
      message_id: "10000"
      result: False

void_coupon_requisition:
  -
    name: Void优惠券申请
    description: void失败
    request:
      method: post
      url: "/central/cs/couponRequisition/voidCouponRequisition"
      json_data: {"id":"11135"}
    expected_result:
      message_id: "10000"
      result: "The status must be [Applying] or [Declined]"

decline_coupon_requisition:
  -
    name: decline优惠券申请
    description: decline失败
    request:
      method: post
      url: "/central/cs/couponRequisition/declineCouponRequisition"
      json_data: {"id":"11136"}
    expected_result:
      message_id: "10000"
      result: "The status must be [Applying]"

batch_decline_coupon_requisition:
  -
    name: Void优惠券申请
    description: void失败
    request:
      method: post
      url: "/central/cs/couponRequisition/batchDeclineCouponRequisition"
      json_data: {"idList":["11137"]}
    expected_result:
      message_id: "10000"
      result: "[Applying]"
















