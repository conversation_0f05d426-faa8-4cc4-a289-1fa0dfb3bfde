cs_order_list:
 - name: 根据订单ID查询
   description: 查询成功
   request:
     method: post
     url: "/central/cs/salesOrder/queryList"
     json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"orderIds":"","userId":"********","orderType":null,"deliveryMode":null,"subOrderType":null,"status":null,"orderSource":"","useCoupon":null,"usePoints":"","carrierName":"","salesOrgId":"","filter":{},"pageNo":1}
   expected_result:
      message_id: "10000"
      result: true
      userId: "********"

order_cancel_order:
  - name: 取消订单接口
    description: 成功
    request:
      method: post
      url: "/central/cs/orderManage/batchCancelOrder"
      json_data: {"orderIds":"********","comment_type":"Other_Other","reason_comment":"test","autoSendEmail":1,"emailContent":"Dear Weee! customer,\r\n\r\nWe are sorry to inform you that your order #{Order ID} scheduled for delivery on {DayOfWeek}, {MM/DD/YYYY} has been cancelled. The refund will be issued within 1-3 business days to the original payment method.\r\n\r\nIf you have any questions, please feel free to contact us. You can go to Weee! Help Center and contact <a href=\"https://www.sayweee.com/account/case/add\">Customer Service</a>.\r\n\r\nThank you for your understanding and support!\r\n\r\nWeee! Customer Service","refund_channel":"original_payment_method"}
    expected_result:
      message_id: "10000"
      result: true

cs_order_detail:
 - name: order detail
   description: 进入 order  detail
   request:
     method: get
     url: "/central/cs/salesOrder/queryDetailInfo/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true
     orderId: "********"

order_insights_list:
 - name: order insights list
   description: 进入 order  insights list
   request:
     method: post
     url: "/central/cs/orderInsights/list"
     json_data: {"pageNum":1,"orderType":"R-normal-0","deliveryMode":"delivery","orderStatus":"F","transactionStatus":"Completed","createTimeRange":["2024-02-10T03:32:29.178Z","2025-02-11T03:32:29.178Z"],"userId":********,"paypalId":null,"phone":"wv_514624502747923073","email":"<EMAIL>","formatAddressId":1204929,"address":"wv_506227300887466594","deviceId":"3682825","deviceFingerprint":null,"ip":"**************","createDateStart":"2024-02-10 00:00:00","createDateEnd":"2025-02-11 00:00:00","andor":"or"}
   expected_result:
     message_id: "10000"
     result: true


cs_order_detail_query_product_list:
 - name: order detail 查询product list
   description: 查询product list 成功
   request:
     method: get
     url: "/central/cs/salesOrder/queryProductList/{data}/default"
     data: ********
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_query_refund_count:
 - name: order detail 查询refund count
   description: 查询refund count成功
   request:
     method: get
     url: "/central/cs/salesOrder/queryRefundCount/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_query_log:
 - name: order detail 查询log
   description: 查询log成功
   request:
     method: get
     url: "/central/cs/suspiciousOrder/queryLog/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true
     orderId: "********"

cs_order_promotion:
 - name: 查询order 促销信息
   description: 查询成功
   request:
     method: get
     url: "/central/cs/orderPromotion/queryInfo/{data}"
     data: 42637429
   expected_result:
     message_id: "10000"
     result: true

query_device_info:
 - name: 查询设备信息
   description: 查询成功
   request:
     method: get
     url: "/central/cs/salesOrder/queryDeviceInfo/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true
     orderId: "********"


cs_order_detail_query_same_payment:
 - name: order detail 查询same payment
   description: 查询same payment成功
   request:
     method: get
     url: "/central/cs/salesOrder/querySamePayment/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true
     orderId: "********"

cs_order_detail_query_request_list:
 - name: order detail 查询相关售后申请
   description: 查询成功
   request:
     method: get
     url: "/central/cs/salesOrder/queryRequestList/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_query_order_related_case:
 - name: order detail 查询相关case
   description: 查询相关case
   request:
     method: get
     url: "/central/cs/salesOrder/orderRelatedCaseList/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_get_user_tag:
 - name: order detail 获取用户标签
   description: 查询用户标签成功
   request:
     method: get
     url: "/central/cs/userTag/getUserTag/{data}"
     data: ********
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_user_note:
 - name: order detail 查询用户备注
   description: 查询用户备注成功
   request:
     method: post
     url: "/central/cs/userNote/queryList"
     json_data: {"pageNum":1,"sortMap":{},"pageSize":20,"userId":********,"filter":{}}
   expected_result:
     message_id: "10000"
     result: true

cs_order_detail_blacklist_exisblack:
 - name: order detail 查询用户是否有被黑名单拦截
   description: 查询成功 false没有
   request:
     method: post
     url: "/central/cs/blackList/existBlack"
     json_data: {"itemType":5,"itemValue":"3682825"}
   expected_result:
     message_id: "10000"
     result: true