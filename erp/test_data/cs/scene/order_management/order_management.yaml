order_list:
  -
    name: 订单列表查询接口
    description: 根据订单ID查询
    request:
      method: post
      url: "/central/cs/salesOrder/queryList"
      json: { "pageNum": 1,"sortMap": { },"pageSize": 20,"orderIds": "42630198","orderType": null,"deliveryMode": null,"subOrderType": null,"status": null,"createDateRange": [ "2023-12-06","2024-12-06" ],"orderSource": "","useCoupon": null,"usePoints": "","carrierName": "","salesOrgId": "","filter": { },"pageNo": 1 }
    validate:
      message_id: "10000"
      orderId: "42630198"


order_detail:
  -
    name: 订单详情查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryDetailInfo/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      orderId: 37710140

order_summary:
  -
    name: 订单汇总详情查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/querySummary/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      orderId: 37710140

order_amount:
  -
    name: 订单支付金额查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryOrderAmount/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      itemAmount: 4.99

order_payment_info:
  -
    name: 订单payment_info查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryPaymentInfo/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      platform: "pc"

order_return_list:
  -
    name: 订单退款信息查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryReturnList/{data}"
      data: 42606236
    validate:
      message_id: "10000"
      refundId: 1821

order_activity_list:
  -
    name: 订单日志记录查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryOrderActivityList/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      orderId: 37710140

order_promotion_info:
  -
    name: 订单促销记录查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryPromotionInfo/{data}"
      data: 42607067
    validate:
      message_id: "10000"
      couponCode: "XWQBIRJUOCCD"

order_device_info:
  -
    name: 订单关联设备及IP记录查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryDeviceInfo/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      deviceId: "396868"

order_stripe:
  -
    name: 订单关联设备及IP记录查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryStripe/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      result: True

query_order_note:
  -
    name: 订单备注查询接口
    description: 成功
    request:
      method: get
      url: "/central/cs/salesOrder/queryOrderNote/{data}"
      data: 37710140
    validate:
      message_id: "10000"
      result: True

update_order_note:
  -
    name: 更新订单备注接口
    description: 成功-internal_note
    request:
      method: post
      url: "/central/cs/salesOrder/updeteOrAddNote"
      json_data: {
                    "rec_id":3384,
                    "order_id":"37709347",
                    "type":"internal_note",
                    "note":"update Internal Notes"
                }
    validate:
      message_id: "10000"
      result: True

query_order_fp_note:
  -
    name: 查询订单FP备注信息接口
    description: 成功
    request:
      method: post
      url: "/central/cs/userNote/queryList"
      json_data: {
                    "pageNum": 1,
                    "pageSize": 10,
                    "userId": 7169981
                }
    validate:
      message_id: "10000"
      userId: 7169981

add_order_fp_note:
  -
    name: 新增订单FP备注note接口
    description: 成功
    request:
      method: post
      url: "/central/cs/userNote/add"
      json_data: {
                    "userId":7169981,
                    "comment":"QA Note",
                    "type":2,
                    "referId":"37709347"
                }
    validate:
      message_id: "10000"
      result: True

delete_order_fp_note:
  -
    name: 删除订单FP备注note信息接口
    description: 成功
    request:
      method: get
      url: "/central/cs/userNote/delete/{note_id}"
      data: 1131
    validate:
      message_id: "10000"
      result: True

