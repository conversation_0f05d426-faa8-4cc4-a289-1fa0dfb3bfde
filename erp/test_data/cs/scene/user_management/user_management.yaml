user_list:
  -
    name: 用户列表查询接口
    description: 根据Member ID查询
    request:
      method: post
      url: "/central/cs/user/queryUserList"
      json_data: {"memberId":"415096", "pageNum": 1, "pageSize": 10}
    expected_result:
      message_id: "10000"
      result: True

user_detail:
  -
    name: 用户详情查询接口
    description: 用户详情
    request:
      method: get
      url: "/central/cs/user/getUserDetail/7616787"
      json_data: ""
      params_data: "7616787"
    expected_result:
      message_id: "10000"
      result: True

check_user_delete:
  -
    name: 校验用户是否删除详情查询接口
    description: 账号被删
    request:
      method: get
      url: "/central/cs/user/checkUserDelete/10929401"
      json_data: ""
      params_data: 10929401
    expected_result:
      message_id: "10000"
      object: True

address_book_list:
  -
    name: 地址簿查询列表接口
    description: 查询成功
    request:
      method: get
      url: "/central/cs/address/queryAddressList"
      json_data: {"pageNum":1,"pageSize":20,"userId":"7616787"}
      params_data: ""
    expected_result:
      message_id: "10000"
      userId: 7616787

address_book_detail:
  -
    name: 地址簿详情接口
    description: 查询成功
    request:
      method: get
      url: "/central/cs/address/queryAddressDetail/{data}"
      json_data: ""
      params_data: ""
      data: "17895"
    expected_result:
      message_id: "10000"
      id: 17895

edit_address_book:
  -
    name: 编辑地址簿接口
    description: 编辑成功
    request:
      method: post
      url: "/central/cs/address/editUserAddress"
      json_data: {
                    "id":17895,
                    "phone":"wv_555350159081373969",
                    "userId":7616787,
                    "deliveryAddrAddress":"14555 Philippine St",
                    "addressDefault":false,
                    "addrFirstname":"Hua",
                    "addrLastname":"Dan",
                    "addrAddress":"14555 Philippine St",
                    "addrApt":"",
                    "addrCity":"Houston",
                    "addrState":"80",
                    "addrZipcode":"94538",
                    "maskPhone":"586***5889",
                    "email":"<EMAIL>",
                    "internalComment":null,
                    "gateCode":null,
                    "addressType":"H",
                    "verifiedFlag":"0"}
      params_data: ""
      data: ""
    expected_result:
      message_id: "10000"
      addrZipcode: "94538"

user_related_account_list:
  -
    name: 相关账户
    description: 查询成功
    request:
      method: post
      url: "/central/cs/user/queryRelatedAccountList"
      json_data: {"pageNum":1,"sortMap":{},"pageSize":10,"currentUserId":"7616787","filter":{}}
    expected_result:
      message_id: "10000"




