
select_user_coupon_list:
  -
    name: 查询优惠券接口
    description: 查询成功
    request:
      method: post
      url: "/central/cs/userCoupon/queryUserCouponPage"
      json_data: {
                    "pageNum":1,
                    "pageSize":20,
                    "code": "AELNKXOQ",
                    "user_id": "7169981"
                  }
    expected_result:
      message_id: "10000"
      code: "AELNKXOQ"

disable_user_coupon:
  -
    name: 作废优惠券接口
    description: disable失败
    request:
      method: post
      url: "/central/cs/userCoupon/cancelCoupon"
      json_data: {
                    "code":"XSIIPUEB",
                    "reason":"Test Data"
                  }
    expected_result:
      message_id: "PROMO002"
      result: False

coupon_log:
  -
    name: 优惠券日志接口
    description: 查询日志
    request:
      method: post
      url: "/central/cs/userCoupon/cancelCoupon"
      json_data: {
                    "code":"XSIIPUEB"
                  }
    expected_result:
      code: "XSIIPUEB"
      user_id: 7169981

