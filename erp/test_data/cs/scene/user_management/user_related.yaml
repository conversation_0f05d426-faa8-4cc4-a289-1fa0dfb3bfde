
user_account_related_list:
  -
    name: 用户关联账号查询接口
    description: 查询成功
    request:
      method: post
      url: "/central/cs/user/queryRelatedAccountList"
      json_data: {"userId":"7164848","pageNum":1,"pageSize":10}
      params_data: ""
    expected_result:
      message_id: "10000"
      userId: 7169981

add_account_related:
  -
    name: 新增关联账号接口
    description: 新增失败
    request:
      method: post
      url: "/central/cs/relatedUser/addRelatedUser"
      json_data: {"currentUserId":"7164848","relatedUserId":"45107","comment":"QA测试数据"}
      params_data: ""
    expected_result:
      message_id: "32013"
      message: "EC:Request Error: user had relation"











