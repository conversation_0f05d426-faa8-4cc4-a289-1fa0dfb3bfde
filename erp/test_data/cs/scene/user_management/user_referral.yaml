
user_referral:
  -
    name: 用户的邀请关系查询接口
    description: 根据Invitor User Id查询Weee类型
    request:
      method: post
      url: "/central/cs/userReferral/queryUserInviteRelationPage"
      json_data: {"pageNum":1,"pageSize":20,"invitorUserId":"4","type":"Weee"}
    expected_result:
      message_id: "10000"
      inviteeUserId: 7713999

user_referral_count:
  -
    name: 用户的邀请关系数量统计接口
    description: 根据Invitor User Id查询
    request:
      method: post
      url: "/central/cs/userReferral/countUserReferral"
      json_data: {"pageNum":1,"pageSize":20,"invitorUserId":"4"}
    expected_result:
      message_id: "10000"
      weee_num: 27
