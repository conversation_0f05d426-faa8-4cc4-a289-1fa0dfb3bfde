select_user_point_list:
  -
    name: 查询用户积分接口
    description: 查询成功
    request:
      method: get
      url: "/central/cs/userPoints/summary/{user_id}"
      json_data: ""
      data: "7169981"
    expected_result:
      message_id: "10000"
      result: True

user_point_available_list:
  -
    name: 用户积分detail接口
    description: 查询成功
    request:
      method: post
      url: "/central/cs/userPoints/availableList"
      json_data: {
                    "userId":"7169981",
                    "activeStatus":"1"
                  }
      data: ""
    expected_result:
      message_id: "10000"
      result: True

user_point_log:
  -
    name: 用户积分log接口
    description: 查询Active Points
    request:
      method: post
      url: "/central/cs/userPoints/pointsLog"
      json_data: {
                    "userId":"7169981",
                    "refId": "42604774"
                  }
    expected_result:
      message_id: "10000"
      result: True

transaction_detail:
  -
    name: 用户积分transaction_detail接口
    description: 查询全部
    request:
      method: post
      url: "/central/cs/userPoints/detailList"
      json_data: {
                    "userId":"7169981"
                }
    expected_result:
      message_id: "10000"
      result: True
















