vendor_sponsored_promotion_list:
  -
    description: 供应商促销优惠列表查询-Vendor
    request:
      url: "/admin_gb_vendor/api_table_query_sponsored_promotion"
      params_data: {"search[value][gb_product_vendor_promotion__vendor_id]": 1}
  -
    description: 供应商促销优惠列表查询-SKU
    request:
      url: "/admin_gb_vendor/api_table_query_sponsored_promotion"
      params_data: {"search[value][gb_product_vendor_promotion__product_id]": 1}
  -
    description: 供应商促销优惠列表查询-type
    request:
      url: "/admin_gb_vendor/api_table_query_sponsored_promotion"
      params_data: {"search[value][gb_product_vendor_promotion__type]": F}
  -
    description: 供应商促销优惠列表查询-PO Number
    request:
      url: "/admin_gb_vendor/api_table_query_sponsored_promotion"
      params_data: {"search[value][gb_product_vendor_promotion__po_id]": 2653662}

query_vendor_product_info:
  -
    description: 根据供应商查询支持的促销商品
    request:
      url: "/admin_po_promotion/api_query_vendor_product_info/{vendor_id}"
      params_data: 134

vendor_sponsored_promotion_info:
  -
    description: 供应商促销配置详情查询
    request:
      url: "/admin_gb_vendor/promotions/{id}"
      params_data: 22113

create_vendor_sponsored_promotion:
  -
    description: 创建供应商促销配置
    request:
      url: "/admin_po_promotion/api_update_product_promotion"
      params_data: 134

delete_vendor_sponsored_promotion:
  -
    description: 删除供应商促销配置
    request:
      url: "/admin_gb_vendor/api_delete_sponsored_promotion"
      params_data: {"id": 22131}
