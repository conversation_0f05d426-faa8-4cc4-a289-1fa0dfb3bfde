po_return_list:
  -
    description: PO退货订单列表接口
    request:
      method: get
      url: "/admin_po_return/api_table_query_return"
      params_data: {"search[value][gb_po_return__inventory_id]": 8}

po_return_info:
  -
    description: PO退货订单详情接口
    request:
      method: get
      url: "/admin_po_return/returns/5040"
      params_data: 5040

create_po_return_order:
  -
    description: PO退货订单创建接口
    request:
      method: post
      url: "/admin_po_return/api_save_return"
      params_data: {
          "payment_method": "credit",
          "return_type": "A",
          "vendor_contact": "",
          "comment": "QA",
          "purchase_order_id": "2652052",
          "product_id[]": "14157",
          "po_return_quantity[]": 1,
          "return_line_amount[]": 1.5
      }
delete_po_return_order:
  -
    description: PO退货订单删除接口
    request:
      method: post
      url: "/admin_po_return/api_save_return"
      params_data: {"return_id": 5087}

update_po_return_order:
  -
    description: PO退货订单更新接口
    request:
      method: post
      url: "/admin_po_return/api_save_return"
      params_data: {
          "return_id": 5092,
          "payment_method": "credit",
          "return_type": "A",
          "vendor_contact": "",
          "comment": "QA",
          "purchase_order_id": "2651783",
          "product_id[]": "3961",
          "po_return_quantity[]": 1,
          "return_line_amount[]": 2.77
      }


