add_plan_success:
  - request:
      # 新增plan成功
      json_data: {
        "endDate": '2024-09-08',
        "planName": '01_test_plan',
        "startDate": '2024-07-31',
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE plan_name="01_test_plan"'

add_plan_fail:
  - request:
      # 没有导入metrics
      json_data: {
        "endDate": '2024-09-08',
        "planName": '02_test_plan',
        "startDate": '2024-08-31',
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE plan_name="02_test_plan"'

update_plan_name_date_success:
  - request:
      # 更新plan name和plan period
      json_data: {
        "id": '97',
        "planName": '0805_001_update',
        "startDate":'2024-08-12',
        "endDate":'2024-08-19',
        "status":"I",
        "metrics":[{"id":3957,"metricsId":154,"metricsName":"0627_001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"Y","goal":null},{"id":3958,"metricsId":159,"metricsName":"0708_hong_re","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3959,"metricsId":163,"metricsName":"0710001","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":7341930,"ownerUserName":"liu.yang","isGoalSet":"Y","goal":null},{"id":3960,"metricsId":165,"metricsName":"0711001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":7341930,"ownerUserName":"liu.yang","isGoalSet":"Y","goal":null},{"id":3961,"metricsId":166,"metricsName":"0715001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":3350546,"ownerUserName":"Julia zhu","isGoalSet":"Y","goal":null},{"id":3962,"metricsId":167,"metricsName":"0715002","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"Y","goal":null},{"id":3963,"metricsId":169,"metricsName":"0715003","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":3350546,"ownerUserName":"Julia zhu","isGoalSet":"Y","goal":null},{"id":3964,"metricsId":168,"metricsName":"071500301","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":11760520,"ownerUserName":"Jiayi Guo","isGoalSet":"Y","goal":null},{"id":3965,"metricsId":170,"metricsName":"0716001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"N","goal":null},{"id":3966,"metricsId":202,"metricsName":"0722_ann_availability","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"N","goal":null},{"id":3967,"metricsId":201,"metricsName":"0722_hongdou_availability","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3968,"metricsId":203,"metricsName":"0722_hongdou_rev","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3969,"metricsId":197,"metricsName":"ab","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"N","goal":null},{"id":3970,"metricsId":198,"metricsName":"ab 2","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3971,"metricsId":134,"metricsName":"annie_061905_revenue_update","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"N","goal":null},{"id":3972,"metricsId":199,"metricsName":"annie_061906_availability_update","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"Y","goal":null},{"id":3973,"metricsId":135,"metricsName":"annie_061906_margin_update","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"Y","goal":null},{"id":3974,"metricsId":164,"metricsName":"annie_test","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":30700,"ownerUserName":"Arwen Li","isGoalSet":"Y","goal":null},{"id":3975,"metricsId":136,"metricsName":"an_hongdou_061907_revenue","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3976,"metricsId":137,"metricsName":"an_hongdou_061908_margin","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3977,"metricsId":152,"metricsName":"an_ya_0627_margin_no_goal","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":8657395,"ownerUserName":"yaru.han","isGoalSet":"N","goal":null},{"id":3978,"metricsId":151,"metricsName":"an_ya_0627_revenue_no_goal","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":8657395,"ownerUserName":"yaru.han","isGoalSet":"N","goal":null},{"id":3979,"metricsId":162,"metricsName":"test metrics 2","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":23907,"ownerUserName":"Julie","isGoalSet":"N","goal":null},{"id":3980,"metricsId":161,"metricsName":"test metrics xx 77","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"N","goal":null}]
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE id =97'

update_plan_goal_amount_success:
  - request:
      # 更新全部Y的goal amount
      json_data: {
        "id": '97',
        "planName": '0805_001_update',
        "startDate": '2024-08-12',
        "endDate": '2024-08-19',
        "status": "I",
        "metrics": [ { "id": 3957,"metricsId": 154,"metricsName": "0627_001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": 100000 },{ "id": 3958,"metricsId": 159,"metricsName": "0708_hong_re","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 3959,"metricsId": 163,"metricsName": "0710001","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": 60 },{ "id": 3960,"metricsId": 165,"metricsName": "0711001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": 100000 },{ "id": 3961,"metricsId": 166,"metricsName": "0715001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": 100000 },{ "id": 3962,"metricsId": 167,"metricsName": "0715002","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": 60 },{ "id": 3963,"metricsId": 169,"metricsName": "0715003","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": 100000 },{ "id": 3964,"metricsId": 168,"metricsName": "071500301","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 11760520,"ownerUserName": "Jiayi Guo","isGoalSet": "Y","goal": 100000 },{ "id": 3965,"metricsId": 170,"metricsName": "0716001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3966,"metricsId": 202,"metricsName": "0722_ann_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3967,"metricsId": 201,"metricsName": "0722_hongdou_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 68 },{ "id": 3968,"metricsId": 203,"metricsName": "0722_hongdou_rev","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 3969,"metricsId": 197,"metricsName": "ab","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3970,"metricsId": 198,"metricsName": "ab 2","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 75 },{ "id": 3971,"metricsId": 134,"metricsName": "annie_061905_revenue_update","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3972,"metricsId": 199,"metricsName": "annie_061906_availability_update","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": 75 },{ "id": 3973,"metricsId": 135,"metricsName": "annie_061906_margin_update","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": 60 },{ "id": 3974,"metricsId": 164,"metricsName": "annie_test","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 30700,"ownerUserName": "Arwen Li","isGoalSet": "Y","goal": 100000 },{ "id": 3975,"metricsId": 136,"metricsName": "an_hongdou_061907_revenue","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 3976,"metricsId": 137,"metricsName": "an_hongdou_061908_margin","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 60 },{ "id": 3977,"metricsId": 152,"metricsName": "an_ya_0627_margin_no_goal","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 3978,"metricsId": 151,"metricsName": "an_ya_0627_revenue_no_goal","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 3979,"metricsId": 162,"metricsName": "test metrics 2","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 23907,"ownerUserName": "Julie","isGoalSet": "N","goal": null },{ "id": 3980,"metricsId": 161,"metricsName": "test metrics xx 77","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "N","goal": null } ]
      }
      sql: 'SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id =97'
  - request:
      # 更新部分Y的goal amount
      json_data: {
        "id": '98',
        "planName": '0805_002_update',
        "startDate": '2024-08-19',
        "endDate": '2024-08-26',
        "status": "I",
        "metrics": [ { "id": 3981,"metricsId": 154,"metricsName": "0627_001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": 896574 },{ "id": 3982,"metricsId": 159,"metricsName": "0708_hong_re","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 3983,"metricsId": 163,"metricsName": "0710001","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": 58 },{ "id": 3984,"metricsId": 165,"metricsName": "0711001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": 100000 },{ "id": 3985,"metricsId": 166,"metricsName": "0715001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": 100000 },{ "id": 3986,"metricsId": 167,"metricsName": "0715002","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": 60 },{ "id": 3987,"metricsId": 169,"metricsName": "0715003","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": 100000 },{ "id": 3988,"metricsId": 168,"metricsName": "071500301","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 11760520,"ownerUserName": "Jiayi Guo","isGoalSet": "Y","goal": 100000 },{ "id": 3989,"metricsId": 170,"metricsName": "0716001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3990,"metricsId": 202,"metricsName": "0722_ann_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3991,"metricsId": 201,"metricsName": "0722_hongdou_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 78 },{ "id": 3992,"metricsId": 203,"metricsName": "0722_hongdou_rev","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 3993,"metricsId": 197,"metricsName": "ab","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3994,"metricsId": 198,"metricsName": "ab 2","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 75 },{ "id": 3995,"metricsId": 134,"metricsName": "annie_061905_revenue_update","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3996,"metricsId": 199,"metricsName": "annie_061906_availability_update","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": 75 },{ "id": 3997,"metricsId": 135,"metricsName": "annie_061906_margin_update","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": 60 },{ "id": 3998,"metricsId": 164,"metricsName": "annie_test","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 30700,"ownerUserName": "Arwen Li","isGoalSet": "Y","goal": 100000 },{ "id": 3999,"metricsId": 136,"metricsName": "an_hongdou_061907_revenue","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 100000 },{ "id": 4000,"metricsId": 137,"metricsName": "an_hongdou_061908_margin","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": 60 },{ "id": 4001,"metricsId": 152,"metricsName": "an_ya_0627_margin_no_goal","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 4002,"metricsId": 151,"metricsName": "an_ya_0627_revenue_no_goal","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 4003,"metricsId": 162,"metricsName": "test metrics 2","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 23907,"ownerUserName": "Julie","isGoalSet": "N","goal": null },{ "id": 4004,"metricsId": 161,"metricsName": "test metrics xx 77","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "N","goal": null } ]
      }
      sql: 'SELECT * FROM weee_merch.sp_planning_metrics WHERE planning_id =98'

update_plan_fail:
  - request:
      # 手动更新finalized plan状态到in progress
      json_data: {
        "id": 84,
        "planName": 'fail_to_update',
        "startDate":'2024-08-12',
        "endDate":'2024-08-19',
        "status":"P",
        "metrics":[{"id":3957,"metricsId":154,"metricsName":"0627_001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"Y","goal":null},{"id":3958,"metricsId":159,"metricsName":"0708_hong_re","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3959,"metricsId":163,"metricsName":"0710001","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":7341930,"ownerUserName":"liu.yang","isGoalSet":"Y","goal":null},{"id":3960,"metricsId":165,"metricsName":"0711001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":7341930,"ownerUserName":"liu.yang","isGoalSet":"Y","goal":null},{"id":3961,"metricsId":166,"metricsName":"0715001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":3350546,"ownerUserName":"Julia zhu","isGoalSet":"Y","goal":null},{"id":3962,"metricsId":167,"metricsName":"0715002","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"Y","goal":null},{"id":3963,"metricsId":169,"metricsName":"0715003","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":3350546,"ownerUserName":"Julia zhu","isGoalSet":"Y","goal":null},{"id":3964,"metricsId":168,"metricsName":"071500301","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":11760520,"ownerUserName":"Jiayi Guo","isGoalSet":"Y","goal":null},{"id":3965,"metricsId":170,"metricsName":"0716001","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"N","goal":null},{"id":3966,"metricsId":202,"metricsName":"0722_ann_availability","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"N","goal":null},{"id":3967,"metricsId":201,"metricsName":"0722_hongdou_availability","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3968,"metricsId":203,"metricsName":"0722_hongdou_rev","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3969,"metricsId":197,"metricsName":"ab","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"N","goal":null},{"id":3970,"metricsId":198,"metricsName":"ab 2","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3971,"metricsId":134,"metricsName":"annie_061905_revenue_update","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"N","goal":null},{"id":3972,"metricsId":199,"metricsName":"annie_061906_availability_update","metricsType":3,"metricsTypeStr":"Availability","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"Y","goal":null},{"id":3973,"metricsId":135,"metricsName":"annie_061906_margin_update","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":10937481,"ownerUserName":"yi.shen","isGoalSet":"Y","goal":null},{"id":3974,"metricsId":164,"metricsName":"annie_test","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":30700,"ownerUserName":"Arwen Li","isGoalSet":"Y","goal":null},{"id":3975,"metricsId":136,"metricsName":"an_hongdou_061907_revenue","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3976,"metricsId":137,"metricsName":"an_hongdou_061908_margin","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":10020815,"ownerUserName":"hongdou.shen","isGoalSet":"Y","goal":null},{"id":3977,"metricsId":152,"metricsName":"an_ya_0627_margin_no_goal","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":8657395,"ownerUserName":"yaru.han","isGoalSet":"N","goal":null},{"id":3978,"metricsId":151,"metricsName":"an_ya_0627_revenue_no_goal","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":8657395,"ownerUserName":"yaru.han","isGoalSet":"N","goal":null},{"id":3979,"metricsId":162,"metricsName":"test metrics 2","metricsType":2,"metricsTypeStr":"Margin","skuScope":null,"salesScope":null,"ownerUserId":23907,"ownerUserName":"Julie","isGoalSet":"N","goal":null},{"id":3980,"metricsId":161,"metricsName":"test metrics xx 77","metricsType":1,"metricsTypeStr":"Revenue","skuScope":null,"salesScope":null,"ownerUserId":26105,"ownerUserName":"Yanzhen Chen","isGoalSet":"N","goal":null}]
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE id =84'
  - request:
      # 手动更新closed plan的name/date
      json_data: {
        "id": 69,
        "planName": 'fail_to_update',
        "startDate": '2024-08-12',
        "endDate": '2024-08-19',
        "status": "C",
        "metrics": [ { "id": 3957,"metricsId": 154,"metricsName": "0627_001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": null },{ "id": 3958,"metricsId": 159,"metricsName": "0708_hong_re","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3959,"metricsId": 163,"metricsName": "0710001","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": null },{ "id": 3960,"metricsId": 165,"metricsName": "0711001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 7341930,"ownerUserName": "liu.yang","isGoalSet": "Y","goal": null },{ "id": 3961,"metricsId": 166,"metricsName": "0715001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": null },{ "id": 3962,"metricsId": 167,"metricsName": "0715002","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "Y","goal": null },{ "id": 3963,"metricsId": 169,"metricsName": "0715003","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 3350546,"ownerUserName": "Julia zhu","isGoalSet": "Y","goal": null },{ "id": 3964,"metricsId": 168,"metricsName": "071500301","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 11760520,"ownerUserName": "Jiayi Guo","isGoalSet": "Y","goal": null },{ "id": 3965,"metricsId": 170,"metricsName": "0716001","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3966,"metricsId": 202,"metricsName": "0722_ann_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "N","goal": null },{ "id": 3967,"metricsId": 201,"metricsName": "0722_hongdou_availability","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3968,"metricsId": 203,"metricsName": "0722_hongdou_rev","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3969,"metricsId": 197,"metricsName": "ab","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3970,"metricsId": 198,"metricsName": "ab 2","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3971,"metricsId": 134,"metricsName": "annie_061905_revenue_update","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "N","goal": null },{ "id": 3972,"metricsId": 199,"metricsName": "annie_061906_availability_update","metricsType": 3,"metricsTypeStr": "Availability","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": null },{ "id": 3973,"metricsId": 135,"metricsName": "annie_061906_margin_update","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10937481,"ownerUserName": "yi.shen","isGoalSet": "Y","goal": null },{ "id": 3974,"metricsId": 164,"metricsName": "annie_test","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 30700,"ownerUserName": "Arwen Li","isGoalSet": "Y","goal": null },{ "id": 3975,"metricsId": 136,"metricsName": "an_hongdou_061907_revenue","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3976,"metricsId": 137,"metricsName": "an_hongdou_061908_margin","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 10020815,"ownerUserName": "hongdou.shen","isGoalSet": "Y","goal": null },{ "id": 3977,"metricsId": 152,"metricsName": "an_ya_0627_margin_no_goal","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 3978,"metricsId": 151,"metricsName": "an_ya_0627_revenue_no_goal","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 8657395,"ownerUserName": "yaru.han","isGoalSet": "N","goal": null },{ "id": 3979,"metricsId": 162,"metricsName": "test metrics 2","metricsType": 2,"metricsTypeStr": "Margin","skuScope": null,"salesScope": null,"ownerUserId": 23907,"ownerUserName": "Julie","isGoalSet": "N","goal": null },{ "id": 3980,"metricsId": 161,"metricsName": "test metrics xx 77","metricsType": 1,"metricsTypeStr": "Revenue","skuScope": null,"salesScope": null,"ownerUserId": 26105,"ownerUserName": "Yanzhen Chen","isGoalSet": "N","goal": null } ]
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE id =69'

get_plan_list:
  - request:
      # 查询组合
      params_data: {
        "pageNum": 1,
        "pageSize": 20,
        "keyword": "06",
        "createTimeRange": "2024-06-22,2024-07-05"
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE plan_name like "%06%" and status!="D" and end_date>="2024-06-22" and start_date <="2024-07-05" ORDER BY id DESC'
  - request:
      # 查询第二页
      params_data: {
        "pageNum": 2,
        "pageSize": 20,
        "keyword": "",
        "createTimeRange": "2024-06-25,2024-07-15"
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE status!="D" and end_date>="2024-06-25" and start_date <="2024-07-15" ORDER BY id DESC LIMIT 20,20'
  - request:
      # 查询结果为空
      params_data: {
        "pageNum": 1,
        "pageSize": 20,
        "keyword": "new_plan",
        "createTimeRange": ""
      }
      sql: 'SELECT * FROM weee_merch.sp_planning WHERE plan_name like "%new_plan%" and status!="D" ORDER BY id DESC'
