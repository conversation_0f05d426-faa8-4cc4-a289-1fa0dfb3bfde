get_view_plan_list:
  - request:
      # viewUserId=10937481, status=created
      params_data: {
        "planningId": 74,
        "viewType": "org",
        "viewUserId": 10937481
      }
  - request:
      # viewUserId=10020815, status=completed
      params_data: {
        "planningId": 75,
        "viewType": "org",
        "viewUserId": 10020815
      }
  - request:
      # viewType = self
      params_data: {
        "planningId": 58,
        "viewType": "self",
        "viewUserId": 10937481
      }

update_review_content:
  - request:
      # 新增内容
      json_data: {
        "reviewUserId": 10937481,
        "comment": "test"
      }
      data: "69"
  - request:
      # 修改内容
      json_data: {
        "reviewUserId": 10937481,
        "comment": "test-6月份plan -- personal-review"
      }
      data: "75"
  - request:
      # 清空内容
      json_data: {
        "reviewUserId": 10937481,
        "comment": ""
      }
      data: "69"

get_chart_data:
  - request:
      params_data: {
        "startDate": "2024-06-15",
        "endDate": "2024-06-25"
      }
      data: "1/58/618"
  - request:
      params_data: {
        "startDate": "2024-06-15",
        "endDate": "2024-06-25"
      }
      data: "1/58/619"
  - request:
      params_data: {
        "startDate": "2024-06-15",
        "endDate": "2024-06-25"
      }
      data: "1/58/2304"

get_bar_ethnicity_data:
  - request:
      params_data: {
        "startDate": "2024-06-15",
        "endDate": "2024-06-25"
      }
      data: "2/58/618"

get_bar_tier_data:
  - request:
      params_data: {
        "breakdown": 2,
        "startDate": "2024-06-15",
        "endDate": "2024-06-25"
      }
      data: "2/58/618"