add_metrics_success:
  - request:
      # 新增revenue metrics
      json_data: {
        "metricsName": '01_test_revenue',
        "metricsType":1,
        "ownerUserId":10937481,
        "viewerIds":[10020815],
        "status":"A",
        "isGoalSet":"Y",
        "salesScope":"revenue sales scope",
        "skuScope":"revenue sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "01_test_revenue"'
  - request:
      # 新增margin metrics
      json_data: {
        "metricsName": "01_test_margin",
        "metricsType": 2,
        "ownerUserId": 10020815,
        "viewerIds": [],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "margin sales scope",
        "skuScope": "margin sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "01_test_margin"'
  - request:
      # 新增availability metrics
      json_data: {
        "metricsName": "01_test_availability",
        "metricsType": 3,
        "ownerUserId": 10937481,
        "viewerIds": [10020815,5955706],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "availability sales scope",
        "skuScope": "availability sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "01_test_availability"'
  - request:
      # 新增metrics,没有goal,scope
      json_data: {
        "metricsName": "01_test_no_goal",
        "metricsType": 2,
        "ownerUserId": 10937481,
        "viewerIds": [10020815,5955706,13341651],
        "status": "A",
        "isGoalSet": "N",
        "salesScope": '',
        "skuScope": ''
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "01_test_no_goal"'
  - request:
      # 新增Margin dollar
      json_data: {
        "metricsName": "01_test_dollar",
        "metricsType": 4,
        "ownerUserId": 10937481,
        "viewerIds": [ ],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "margin sales scope",
        "skuScope": "margin sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "01_test_dollar"'


add_metrics_fail:
  - request:
      # share to 和 owner 是同一个人
      json_data: {
        "metricsName": 'add_fail',
        "metricsType":1,
        "ownerUserId":10937481,
        "viewerIds":[10020815, 10937481],
        "status":"A",
        "isGoalSet":"Y",
        "salesScope":"revenue sales scope",
        "skuScope":"revenue sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title = "add_fail"'

update_metrics_success:
  - request:
      # 更新revenue metrics
      json_data: {
        "id": 134,
        "metricsName": 'annie_061905_revenue_update',
        "metricsType":1,
        "ownerUserId":10020815,
        "viewerIds":[],
        "status":"A",
        "isGoalSet":"N",
        "salesScope":"revenue sales scope",
        "skuScope":"revenue sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =134'
  - request:
      # 更新margin metrics
      json_data: {
        "id": 135,
        "metricsName": "annie_061906_margin_update",
        "metricsType": 2,
        "ownerUserId": 10937481,
        "viewerIds": [10020815],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "margin sales scope",
        "skuScope": ""
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =135'
  - request:
      # 更新availability metrics
      json_data: {
        "id": 199,
        "metricsName": "annie_061906_availability_update",
        "metricsType": 3,
        "ownerUserId": 10937481,
        "viewerIds": [10020815,5955706],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "availability sales scope",
        "skuScope": "availability sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =199'

update_metrics_fail:
  - request:
      # 更新 metrics type
      json_data: {
        "id": 159,
        "metricsName": "0708_hong_re",
        "metricsType": 3,
        "ownerUserId": 10020815,
        "viewerIds": [10937481],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "availability sales scope",
        "skuScope": "availability sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =159'
      message: 'Metrics type can not be change'
  - request:
      # 更新 metrics owner = share to person
      json_data: {
        "id": 169,
        "metricsName": "0708_hong_re",
        "metricsType": 1,
        "ownerUserId": 10937481,
        "viewerIds": [ 10937481 ],
        "status": "A",
        "isGoalSet": "Y",
        "salesScope": "availability sales scope",
        "skuScope": "availability sku scope"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =165'
      message: 'The owner user cannot be included in the list of shared viewers.'

get_metrics_list:
  - request:
      # 查询组合
      params_data: {
        "pageNum": 1,
        "pageSize": 20,
        "keyword": "dou",
        "metricsType": 3,
        "ownerUserId": "10020815"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title like "%dou%" and type =3 and owner_user_id=10020815 ORDER BY title'
  - request:
      # 查询第二页
      params_data: {
        "pageNum": 2,
        "pageSize": 20,
        "keyword": "pa",
        "metricsType": 3,
        "ownerUserId":""
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title like "%pa%" and type =3 ORDER BY title LIMIT 20,20'
  - request:
      # 查询metrics type和owner
      params_data: {
        "pageNum": 1,
        "pageSize": 20,
        "keyword": "",
        "metricsType": 1,
        "ownerUserId": "10937481"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE type =1 and owner_user_id =10937481 ORDER BY title'
  - request:
      # 查询结果为空
      params_data: {
        "pageNum": 1,
        "pageSize": 20,
        "keyword": "ab",
        "metricsType": 3,
        "ownerUserId": "3350546"
      }
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE title like "%ab%" and type =3 and owner_user_id=3350546 ORDER BY title'

get_single_metrics_detail:
  - request:
      ### 查看revenue metrics详情
      metrics_id: "169"
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =169'
  - request:
      ### 查看margin metrics详情
      metrics_id: "167"
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =167'
  - request:
      ### 查看availability metrics详情
      metrics_id: "202"
      sql: 'SELECT * FROM weee_merch.sp_metrics WHERE id =202'
