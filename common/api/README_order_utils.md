# 订单测试工具使用说明

## 概述

`OrderTestUtils` 是一个用于创建测试订单的工具类，提供了便捷的方法来创建订单作为其他测试用例的前置数据。

## 主要特性

- **地址自动管理**: 从配置文件中随机选择地址，确保整个测试套件运行期间不重复使用
- **简化调用**: 只需传入商品信息即可创建订单
- **异常处理**: 地址用尽或订单创建失败时抛出明确异常
- **线程安全**: 支持多线程环境下的并发调用

## 使用方法

### 基本用法

```python
from common.api.order_utils import OrderTestUtils

# 创建单商品订单
products = [{"product_id": 48464, "quantity": 1}]
order_id = OrderTestUtils.create_test_order(products=products)

# 创建多商品订单
products = [
    {"product_id": 48464, "quantity": 2},
    {"product_id": 87651, "quantity": 1}
]
order_id = OrderTestUtils.create_test_order(products=products)
```

### 在测试用例中使用

```python
import weeeTest
from common.api.order_utils import OrderTestUtils

class TestSomeFeature(weeeTest.TestCase):
    def test_feature_with_order(self):
        """测试需要订单数据的功能"""
        # 创建前置订单数据
        products = [{"product_id": 12345, "quantity": 1}]
        order_id = OrderTestUtils.create_test_order(products=products)
        
        # 使用订单ID进行后续测试
        # ... 你的测试逻辑
        assert order_id is not None
```

### 高级用法

```python
# 指定更多参数
order_id = OrderTestUtils.create_test_order(
    products=[{"product_id": 48464, "quantity": 1}],
    user_id=7226349,
    delivery_date="2024-12-25",
    language="en",
    platform="android",
    is_points_pay=False
)
```

## 方法说明

### create_test_order(products, **kwargs)

创建测试订单的主要方法。

**参数:**
- `products` (list, 必需): 商品列表，格式 `[{"product_id": xxx, "quantity": xxx}, ...]`
- `user_id` (int, 可选): 用户ID，默认 7226349
- `delivery_date` (str, 可选): 配送日期，格式 YYYY-MM-DD，默认为后天
- `language` (str, 可选): 语言，默认 "zh"
- `platform` (str, 可选): 平台，默认 "ios"
- `is_points_pay` (bool, 可选): 是否使用积分支付，默认 True

**返回:**
- `int/str`: 订单ID

**异常:**
- `ValueError`: 参数格式错误
- `Exception`: 地址用尽或订单创建失败

### get_address_usage_info()

获取地址使用情况信息。

**返回:**
```python
{
    "total_addresses": 5,
    "used_addresses": 2,
    "available_addresses": 3,
    "used_address_ids": ["address_001", "address_003"]
}
```

### reset_used_addresses()

重置已使用的地址列表（主要用于测试环境）。

## 地址配置

地址配置文件位于 `common/test_data/addresses.json`，包含多个预定义的收货地址。

### 配置文件格式

```json
{
  "addresses": [
    {
      "id": "address_001",
      "addrApt": "",
      "addrAddress": "1369 W Hubbard St APT 1W",
      "addrCity": "Chicago",
      "addrCountry": "2",
      "addrFirstname": "Alyssa",
      "addrLastname": "Myers",
      "addrState": "Illinois",
      "addrZipcode": "60642",
      "phone": "6502425661",
      "email": "<EMAIL>"
    }
  ]
}
```

## 注意事项

1. **地址不重复**: 在整个测试套件运行期间，每个地址只会被使用一次
2. **地址用尽**: 当所有地址都被使用后，再次调用会抛出异常
3. **线程安全**: 工具类使用锁机制确保多线程环境下的安全性
4. **错误处理**: 订单创建失败时会抛出详细的异常信息

## 故障排除

### 常见错误

1. **地址配置文件未找到**
   ```
   FileNotFoundError: 地址配置文件未找到: /path/to/addresses.json
   ```
   解决方案: 确保 `common/test_data/addresses.json` 文件存在

2. **所有地址已用尽**
   ```
   Exception: 所有地址已用尽！总共 5 个地址，已使用 5 个地址
   ```
   解决方案: 增加更多地址配置或调用 `reset_used_addresses()` 重置

3. **订单创建失败**
   ```
   Exception: 订单创建失败: API返回错误信息
   ```
   解决方案: 检查网络连接、API服务状态和参数有效性

## 扩展地址配置

如需添加更多地址，请编辑 `common/test_data/addresses.json` 文件，按照现有格式添加新的地址配置。
