import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath

class ECAuth(weeeTest.TestCase):

    def get_ec_auth_header(self, email="<EMAIL>", password="1234abcd",
                           device_id="2D1DFC5C-CE51-4E79-969E-014FA8753C65"):
        """获取EC系统的实时认证header，只返回authorization部分

        Args:
            email (str): 登录邮箱
            password (str): 登录密码
            device_id (str): 设备ID，用于请求头中的device-id字段

        Returns:
            dict: 包含authorization的header字典
        """
        # 先获取匿名token
        self.get(url='/ec/customer/login/token/generate')
        anon_token = jmespath(self.response, "object.token")

        if not anon_token:
            log.error(f'获取匿名token失败: {self.response}')
            return {}

        # 构建带匿名token的headers
        auth_headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "app-version": "null",
            "Authorization": f"Bearer {anon_token}",
            "B-Cookie": "",
            "Lang": "en",
            "Platform": "h5",
            "User-Agent": "WeeeApp",
            "weee-session-token": "",
            "Weee-Store": "cn",
            "Zipcode": "98011",
            "Weee-Zipcode": "98011",
            "special-tag": "et_automation",
            "device-id": device_id
        }

        # 获取会话令牌（可选，但建议添加）
        try:
            self.get(url='/ec/tracking/session_id', headers=auth_headers)
            session_token = jmespath(self.response, "object.weee_session_token")
            if session_token:
                auth_headers['weee-session-token'] = str(session_token)
                auth_headers['B-Cookie'] = str(session_token)
        except Exception as e:
            log.warning(f"获取会话令牌失败: {e}")

        # 登录数据
        data = {
            "channelFrom": "",
            "channelID": "",
            "email": email,
            "epCode": "",
            "ep_partner": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": False,
            "ftu_source": "",
            "ftu_url": "",
            "password": password,
            "referral_id": 0,
            "referrer_id": 0,
            "source": ""
        }

        # 使用匿名token作为header调用登录API
        self.post(url='/ec/customer/login/email', headers=auth_headers, json=data)

        # 从响应中提取token
        token = jmespath(self.response, "object.token")
        if token and len(token) > 0:
            log.info(f'EC登录成功，获取到token')
            # 只返回authorization部分
            return {'Authorization': 'Bearer ' + token}
        else:
            log.error(f'EC登录失败，未获取到token: {self.response}')
            return {}